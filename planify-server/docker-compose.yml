version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    command: redis-server --requirepass redis123
    volumes:
      - redis_data:/data
    profiles:
      - redis

  planify-server-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - '3333:3333'
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - PORT=3333
      - NODE_ENV=development
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER_DEV=${DB_USER_DEV:-root}
      - DB_PASSWORD_DEV=${DB_PASSWORD_DEV:-root}
      - DB_NAME_DEV=${DB_NAME_DEV:-planify_dev}
      - API_CURRENT_URL_DEV=${API_CURRENT_URL_DEV:-http://localhost:3334}
      - FRONTEND_URL_DEV=${FRONTEND_URL_DEV:-http://localhost:3000}
      - FROM_EMAIL=${FROM_EMAIL:-<EMAIL>}
      - BREVO_API_KEY=${BREVO_API_KEY:-your-key}
      - PLANAI_MODEL_CONNECTION=${PLANAI_MODEL_CONNECTION:-your-connection}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_USERNAME=default
      - REDIS_PASSWORD=redis123

volumes:
  redis_data:
    driver: local
