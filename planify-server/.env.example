NODE_ENV="development"
TIMEZONE=""
DATABASE_ENCRYPT="true"
DB_HOST=
DB_PORT=
DB_USER=
DB_PASSWORD=
DB_NAME=
PORT=3333

JWT_SECRET=
API_CURRENT_URL="http://localhost:3333"
PLANAI_MODEL_CONNECTION="https://ai.planify.app.br/"
PLANAI_SECRET= 
FRONTEND_URL=
FROM_EMAIL=
BREVO_API_KEY=

PAYMENT_GATEWAY_URL=
PAYMENT_GATEWAY_SECRET=

MINIO_ENDPOINT=
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=default
REDIS_PASSWORD=redis123