FROM node:21-alpine

WORKDIR /app
# Install build dependencies
RUN apk add --no-cache python3 make g++ git

# Copy package files
COPY package*.json ./

# Build arguments for environment variables
ARG PORT
# ARG NODE_ENV
ARG DB_HOST
ARG DB_PORT
ARG DB_USER
ARG DB_PASSWORD
ARG DB_NAME
ARG API_CURRENT_URL
ARG FRONTEND_URL
ARG FROM_EMAIL
ARG BREVO_API_KEY
ARG RABBIT_MQ_SERVICE
ARG PLANAI_MODEL_CONNECTION
ARG PAT_TOKEN
ARG REDIS_HOST
ARG REDIS_PORT
ARG REDIS_USERNAME
ARG REDIS_PASSWORD
ARG JWT_SECRET
ARG ENV
ARG SUPPORT_WEBHOOK_URL
ARG OTEL_EXPORTER_OTLP_ENDPOINT
ARG OTEL_SERVICE_NAME
ARG OTEL_RESOURCE_ATTRIBUTES
ARG MINIO_ENDPOINT
ARG MINIO_PORT
ARG MINIO_USE_SSL
ARG MINIO_ACCESS_KEY
ARG MINIO_SECRET_KEY
ARG MINIO_BUCKET
ARG PAYMENT_GATEWAY_URL
ARG PAYMENT_GATEWAY_SECRET
ARG LOGO_IMAGE_URL
ARG BUILD_DATE

# Configurar git para usar token
RUN git config --global url."https://${PAT_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

# Instalar dependências
RUN npm install -f

# Copy source code
COPY . .

# Set environment variables
ENV PORT=$PORT \
  # NODE_ENV=$NODE_ENV \
  DB_HOST=$DB_HOST \
  DB_PORT=$DB_PORT \
  DB_USER=$DB_USER \
  DB_PASSWORD=$DB_PASSWORD \
  DB_NAME=$DB_NAME \
  API_CURRENT_URL=$API_CURRENT_URL \
  FRONTEND_URL=$FRONTEND_URL \
  FROM_EMAIL=$FROM_EMAIL \
  BREVO_API_KEY=$BREVO_API_KEY \
  RABBIT_MQ_SERVICE=$RABBIT_MQ_SERVICE \
  PLANAI_MODEL_CONNECTION=$PLANAI_MODEL_CONNECTION \
  REDIS_HOST=$REDIS_HOST \
  REDIS_PORT=$REDIS_PORT \
  REDIS_USERNAME=$REDIS_USERNAME \
  REDIS_PASSWORD=$REDIS_PASSWORD \
  JWT_SECRET=$JWT_SECRET \
  ENV=$ENV \
  SUPPORT_WEBHOOK_URL=$SUPPORT_WEBHOOK_URL \
  OTEL_EXPORTER_OTLP_ENDPOINT=$OTEL_EXPORTER_OTLP_ENDPOINT \
  OTEL_SERVICE_NAME=$OTEL_SERVICE_NAME \
  OTEL_RESOURCE_ATTRIBUTES=$OTEL_RESOURCE_ATTRIBUTES \
  MINIO_ENDPOINT=$MINIO_ENDPOINT \
  MINIO_PORT=$MINIO_PORT \
  MINIO_USE_SSL=$MINIO_USE_SSL \
  MINIO_ACCESS_KEY=$MINIO_ACCESS_KEY \
  MINIO_SECRET_KEY=$MINIO_SECRET_KEY \
  MINIO_BUCKET=$MINIO_BUCKET \
  PAYMENT_GATEWAY_URL=$PAYMENT_GATEWAY_URL \
  PAYMENT_GATEWAY_SECRET=$PAYMENT_GATEWAY_SECRET \
  LOGO_IMAGE_URL=$LOGO_IMAGE_URL \
  BUILD_DATE=$BUILD_DATE

RUN npm run migrate
# Expose the application port
EXPOSE $PORT


CMD ["npm", "run", "start:dev"]