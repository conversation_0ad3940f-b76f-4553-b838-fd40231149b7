{"name": "plan-ai-server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "commit": "git add . && git cz && git push", "migrate": "npx sequelize-cli db:migrate", "migrate:undoAll": "npx sequelize-cli db:migrate:undo:all", "migrate:undo": "npx sequelize-cli db:migrate:undo --count 1"}, "dependencies": {"@getbrevo/brevo": "^2.1.1", "@nestjs/common": "^11.0.16", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.16", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^11.0.16", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/sequelize": "^11.0.0", "@nestjs/swagger": "^11.1.2", "@nestjs/throttler": "^6.4.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.56.1", "@opentelemetry/exporter-logs-otlp-http": "^0.57.2", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-logs": "^0.57.2", "@opentelemetry/sdk-node": "^0.57.2", "@opentelemetry/semantic-conventions": "^1.30.0", "@opentelemetry/winston-transport": "^0.10.1", "@planify/queues": "github:Planify-BR/queues-sdk-server", "@sendgrid/mail": "^8.1.3", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.4", "axios": "^1.8.2", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "express-basic-auth": "^1.2.1", "git-cz": "^4.9.0", "handlebars": "^4.7.8", "ioredis": "^5.6.1", "jwt-decode": "^4.0.0", "minio": "^8.0.3", "mysql": "^2.18.1", "mysql2": "^3.14.0", "nodemailer": "^6.9.13", "nodemailer-brevo-transport": "^2.1.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "plan-ai-server": "file:", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sequelize": "^6.36.0", "sequelize-typescript": "^2.1.6", "winston": "^3.17.0"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.0.16", "@types/express": "^5.0.1", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "@types/passport-google-oauth20": "^2.0.14", "@types/sequelize": "^4.28.20", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}