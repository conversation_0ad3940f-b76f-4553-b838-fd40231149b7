name: CI_CD

permissions:
  contents: write
  issues: write
  pull-requests: write
  id-token: write
  deployments: write
  actions: write
  checks: write
  statuses: write

on:
  push:
    branches:
      - dev
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  generate_version:
    runs-on: ubuntu-latest
    outputs:
      new_tag: ${{ steps.tag_version.outputs.new_tag }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN }}

      - name: Get current branch
        id: branch
        run: echo "branch=$(echo ${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}})" >> $GITHUB_OUTPUT

      - name: Generate SEMVER tag
        id: tag_version
        run: |
          # Define default versions for each environment
          if [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            default_version="0.0.5"
            env_prefix="dev-v"
          else
            default_version="1.1.9"
            env_prefix="v"  # prod não tem prefixo especial
          fi

          # Fetch all tags
          git fetch --tags

          # Get latest tag for this environment
          latest_tag=$(git tag -l "${env_prefix}*" | sort -V | tail -n 1)

          if [ -z "$latest_tag" ]; then
            # No tags found for this environment, use default
            latest_tag="${env_prefix}${default_version}"
          fi

          echo "Found latest tag for environment: $latest_tag"

          # Remove prefix to get just the version number
          version_number="${latest_tag#${env_prefix}}"

          # Extract version components
          IFS='.' read -r -a version_parts <<< "$version_number"
          major="${version_parts[0]}"
          minor="${version_parts[1]}"
          patch="${version_parts[2]:-0}"

          # Increment patch version
          new_patch=$((patch + 1))
          if [[ $new_patch -gt 9 ]]; then
            new_patch=0
            new_minor=$((minor + 1))
            if [[ $new_minor -gt 9 ]]; then
              new_minor=0
              new_major=$((major + 1))
            else
              new_major=$major
            fi
          else
            new_major=$major
            new_minor=$minor
          fi

          # Format new version with environment prefix
          new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"

          # Verify if new tag already exists
          while git rev-parse "$new_tag" >/dev/null 2>&1; do
            echo "Tag $new_tag already exists, incrementing..."
            new_patch=$((new_patch + 1))
            new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"
          done

          # Debug output
          echo "Current version: $latest_tag"
          echo "New version: $new_tag"

          # Set output
          echo "new_tag=${new_tag}" >> $GITHUB_OUTPUT

          # Configure git
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

          # Create and push tag
          git tag $new_tag
          git push origin $new_tag

  build_dev:
    needs: generate_version
    if: github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.PAT_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

      - name: Verify Docker Login
        run: docker info

      - name: Build Docker image for Dev
        run: |
          TAG=dev
          REPO_NAME="planify-server"
          REPO_OWNER="planify-br"
          REPO_NAME_LOWER=$(echo "$REPO_NAME" | tr '[:upper:]' '[:lower:]')
          docker build \
              --build-arg PAT_TOKEN="${{ secrets.PAT_TOKEN }}" \
              --build-arg PORT=3334 \
              --build-arg NODE_ENV="development" \
              --build-arg DB_HOST="${{ secrets.DB_HOST }}" \
              --build-arg DB_PORT="${{ secrets.DB_PORT }}" \
              --build-arg DB_USER="${{ secrets.DB_USER_DEV }}" \
              --build-arg DB_PASSWORD="${{ secrets.DB_PASSWORD_DEV }}" \
              --build-arg DB_NAME="${{ secrets.DB_NAME_DEV }}" \
              --build-arg API_CURRENT_URL="${{ secrets.API_CURRENT_URL_DEV }}" \
              --build-arg FRONTEND_URL="${{ secrets.FRONTEND_URL_DEV }}" \
              --build-arg FROM_EMAIL="${{ secrets.FROM_EMAIL }}" \
              --build-arg BREVO_API_KEY="${{ secrets.BREVO_API_KEY }}" \
              --build-arg PLANAI_MODEL_CONNECTION="${{ secrets.PLANAI_MODEL_CONNECTION }}" \
              --build-arg MINIO_ENDPOINT="${{ secrets.MINIO_ENDPOINT }}" \
              --build-arg MINIO_PORT="${{ secrets.MINIO_PORT }}" \
              --build-arg MINIO_USE_SSL="${{ secrets.MINIO_USE_SSL }}" \
              --build-arg MINIO_ACCESS_KEY="${{ secrets.MINIO_ACCESS_KEY }}" \
              --build-arg MINIO_SECRET_KEY="${{ secrets.MINIO_SECRET_KEY }}" \
              --build-arg MINIO_BUCKET="${{ secrets.MINIO_BUCKET_DEV }}" \
              --build-arg PAYMENT_GATEWAY_URL="${{ secrets.PAYMENT_GATEWAY_URL_DEV }}" \
              --build-arg PAYMENT_GATEWAY_SECRET="${{ secrets.PAYMENT_GATEWAY_SECRET }}" \
              --build-arg LOGO_IMAGE_URL="${{ secrets.LOGO_IMAGE_URL }}" \
              --build-arg REDIS_HOST="${{ secrets.REDIS_HOST_DEV }}" \
              --build-arg REDIS_PORT="${{ secrets.REDIS_PORT_DEV }}" \
              --build-arg REDIS_USERNAME="${{ secrets.REDIS_USERNAME }}" \
              --build-arg REDIS_PASSWORD="${{ secrets.REDIS_PASSWORD }}" \
              --build-arg JWT_SECRET="${{ secrets.JWT_SECRET }}" \
              --build-arg SUPPORT_WEBHOOK_URL="${{ secrets.SUPPORT_WEBHOOK_URL }}" \
              --build-arg ENV="development" \
              --build-arg OTEL_EXPORTER_OTLP_ENDPOINT="${{ secrets.OTEL_EXPORTER_OTLP_ENDPOINT }}" \
              --build-arg OTEL_SERVICE_NAME="planify-server" \
              --build-arg OTEL_RESOURCE_ATTRIBUTES="service.name=planify-server,service.version=1.0.0,deployment.environment=development" \
              --build-arg BUILD_DATE="$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
              -t ghcr.io/$REPO_OWNER/$REPO_NAME_LOWER:$TAG \
              .

      - name: Verify Build
        run: |
          if [ $? -ne 0 ]; then
            echo "Build failed!"
            exit 1
          fi

      - name: Push Docker image for Dev
        run: |
          TAG=dev
          REPO_NAME="planify-server"
          REPO_OWNER="planify-br"
          REPO_NAME_LOWER=$(echo "$REPO_NAME" | tr '[:upper:]' '[:lower:]')
          docker push ghcr.io/$REPO_OWNER/$REPO_NAME_LOWER:$TAG

  build_prod:
    needs: generate_version
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.PAT_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

      - name: Verify Docker Login
        run: docker info

      - name: Build Docker image for Prod
        run: |
          TAG=prod
          REPO_NAME="planify-server"
          REPO_OWNER="planify-br"
          REPO_NAME_LOWER=$(echo "$REPO_NAME" | tr '[:upper:]' '[:lower:]')
          docker build \
              --build-arg PAT_TOKEN="${{ secrets.PAT_TOKEN }}" \
              --build-arg PORT=3333 \
              --build-arg NODE_ENV="${{ secrets.NODE_ENV }}" \
              --build-arg DB_HOST="${{ secrets.DB_HOST }}" \
              --build-arg DB_PORT="${{ secrets.DB_PORT }}" \
              --build-arg DB_USER="${{ secrets.DB_USER }}" \
              --build-arg DB_PASSWORD="${{ secrets.DB_PASSWORD }}" \
              --build-arg DB_NAME="${{ secrets.DB_NAME }}" \
              --build-arg API_CURRENT_URL="${{ secrets.API_CURRENT_URL }}" \
              --build-arg FRONTEND_URL="${{ secrets.FRONTEND_URL }}" \
              --build-arg FROM_EMAIL="${{ secrets.FROM_EMAIL }}" \
              --build-arg BREVO_API_KEY="${{ secrets.BREVO_API_KEY }}" \
              --build-arg PLANAI_MODEL_CONNECTION="${{ secrets.PLANAI_MODEL_CONNECTION }}" \
              --build-arg MINIO_ENDPOINT="${{ secrets.MINIO_ENDPOINT }}" \
              --build-arg MINIO_PORT="${{ secrets.MINIO_PORT }}" \
              --build-arg MINIO_USE_SSL="${{ secrets.MINIO_USE_SSL }}" \
              --build-arg MINIO_ACCESS_KEY="${{ secrets.MINIO_ACCESS_KEY }}" \
              --build-arg MINIO_SECRET_KEY="${{ secrets.MINIO_SECRET_KEY }}" \
              --build-arg MINIO_BUCKET="${{ secrets.MINIO_BUCKET }}" \
              --build-arg PAYMENT_GATEWAY_URL="${{ secrets.PAYMENT_GATEWAY_URL }}" \
              --build-arg PAYMENT_GATEWAY_SECRET="${{ secrets.PAYMENT_GATEWAY_SECRET }}" \
              --build-arg LOGO_IMAGE_URL="${{ secrets.LOGO_IMAGE_URL }}" \
              --build-arg REDIS_HOST="${{ secrets.REDIS_HOST }}" \
              --build-arg REDIS_PORT="${{ secrets.REDIS_PORT }}" \
              --build-arg REDIS_USERNAME="${{ secrets.REDIS_USERNAME }}" \
              --build-arg REDIS_PASSWORD="${{ secrets.REDIS_PASSWORD }}" \
              --build-arg JWT_SECRET="${{ secrets.JWT_SECRET }}" \
              --build-arg SUPPORT_WEBHOOK_URL="${{ secrets.SUPPORT_WEBHOOK_URL }}" \
              --build-arg ENV="production" \
              --build-arg OTEL_EXPORTER_OTLP_ENDPOINT="${{ secrets.OTEL_EXPORTER_OTLP_ENDPOINT }}" \
              --build-arg OTEL_SERVICE_NAME="planify-server" \
              --build-arg OTEL_RESOURCE_ATTRIBUTES="service.name=planify-server,service.version=1.0.0,deployment.environment=production" \
              -t ghcr.io/$REPO_OWNER/$REPO_NAME_LOWER:$TAG \
              .

      - name: Verify Build
        run: |
          if [ $? -ne 0 ]; then
            echo "Build failed!"
            exit 1
          fi

      - name: Push Docker image for Prod
        run: |
          TAG=prod
          REPO_NAME="planify-server"
          REPO_OWNER="planify-br"
          REPO_NAME_LOWER=$(echo "$REPO_NAME" | tr '[:upper:]' '[:lower:]')
          docker push ghcr.io/$REPO_OWNER/$REPO_NAME_LOWER:$TAG

  deploy_dev:
    needs: [build_dev]
    if: github.event_name == 'push' && github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Dev VPS
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{secrets.SERVER_HOST}}
          username: ${{secrets.SERVER_USER}}
          password: ${{secrets.SERVER_PASS}}
          script: |
            TAG=dev
            REPO_NAME="planify-server"
            REPO_OWNER="planify-br"
            REPO_NAME_LOWER=$(echo "$REPO_NAME" | tr '[:upper:]' '[:lower:]')

            echo "Limpando cache do Docker..."
            docker system prune -af

            echo "Logando no GitHub Container Registry..."
            echo "${{ secrets.PAT_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            echo "Pulling the latest Docker image for $TAG..."
            docker pull ghcr.io/planify-br/planify-server:dev

            kubectl apply -f /home/<USER>/infrastructure/manifests/planify-server/dev/service.yml
            kubectl apply -f /home/<USER>/infrastructure/manifests/planify-server/dev/deployment.yml

            kubectl rollout restart deployment/planify-server-dev
          # kubectl rollout status deployment/planify-server-dev

          # docker rm -f planify-server-dev
          # docker run --name planify-server-dev --network=host -d ghcr.io/planify-br/planify-server:dev

          # docker rm -f planify-server-dev
          # docker run -d --name planify-server-dev -p 3334:3334 ghcr.io/planify-br/planify-server:dev

  deploy_prod:
    needs: [build_prod]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Production VPS
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{secrets.SERVER_HOST}}
          username: ${{secrets.SERVER_USER}}
          password: ${{secrets.SERVER_PASS}}
          script: |
            TAG=prod
            REPO_NAME="planify-server"
            REPO_OWNER="planify-br"
            REPO_NAME_LOWER=$(echo "$REPO_NAME" | tr '[:upper:]' '[:lower:]')
            echo "Logando no GitHub Container Registry..."
            echo "${{ secrets.PAT_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin


            echo "Pulling the latest Docker image for $TAG..."
            docker pull ghcr.io/planify-br/planify-server:prod

            kubectl apply -f /home/<USER>/infrastructure/manifests/planify-server/prod/service.yml
            kubectl apply -f /home/<USER>/infrastructure/manifests/planify-server/prod/deployment.yml

            kubectl rollout restart deployment/planify-server-prod
          # kubectl rollout status deployment/planify-server-prod

          # docker rm -f planify-server-prod
          # docker run --name planify-server-prod --network=host -d ghcr.io/planify-br/planify-server:prod

          # docker pull ghcr.io/$REPO_OWNER/$REPO_NAME_LOWER:$TAG --no-cache

  notify_discord:
    needs: [deploy_dev, deploy_prod]
    if: always()
    runs-on: ubuntu-latest
    env:
      DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK_URL }}
    steps:
      - name: Check Discord Webhook URL
        run: |
          if [ -z "$DISCORD_WEBHOOK_URL" ]; then
            echo "⚠️ DISCORD_WEBHOOK_URL não está configurado nos secrets"
            exit 1
          fi

      - name: Install jq
        run: |
          sudo apt-get update
          sudo apt-get install -y jq

      - name: Send Discord Notification
        shell: bash
        run: |
          echo "Preparando notificação do Discord..."

          # Escapar caracteres especiais na mensagem do commit
          COMMIT_MESSAGE=$(echo '${{ github.event.head_commit.message }}' | sed 's/"/\\"/g')
          COMMIT_AUTHOR='${{ github.event.head_commit.author.name }}'

          # Formatar a data para DD-MM-YYYY HH:MM
          FORMATTED_DATE=$(date -u +"%d-%m-%Y %H:%M")
          CURRENT_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

          # Determinar o ambiente com base na branch
          if [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            ENVIRONMENT="DEV"
            DEPLOY_RESULT="${{ needs.deploy_dev.result }}"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            ENVIRONMENT="PROD"
            DEPLOY_RESULT="${{ needs.deploy_prod.result }}"
          else
            ENVIRONMENT="UNKNOWN"
            DEPLOY_RESULT="skipped"
          fi

          echo "Ambiente detectado: $ENVIRONMENT"
          echo "Resultado do deploy: $DEPLOY_RESULT"

          # Verificando resultado dos jobs de deploy
          if [[ "$DEPLOY_RESULT" == "success" ]]; then
            echo "Deploy bem sucedido, enviando notificação de sucesso..."
            STATUS='🟢 Novo deploy de PLANIFY CORE finalizado!'
            DESCRIPTION='🚀 Serviço: PLANIFY CORE'
            COLOR=65280
          else
            echo "Deploy falhou, enviando notificação de erro..."
            STATUS='🔴 Erro ao realizar deploy de PLANIFY CORE!'
            DESCRIPTION='Serviço: PLANIFY CORE'
            COLOR=16711680
          fi

          # Construir o payload JSON usando jq para garantir um JSON válido
          PAYLOAD=$(jq -n \
            --arg title "$STATUS" \
            --arg desc "$DESCRIPTION" \
            --arg ambiente "$ENVIRONMENT" \
            --arg autor "$COMMIT_AUTHOR" \
            --arg alteracoes "$COMMIT_MESSAGE" \
            --arg data "$FORMATTED_DATE" \
            --arg timestamp "$CURRENT_TIME" \
            --argjson color "$COLOR" \
            '{
              embeds: [{
                title: $title,
                description: $desc,
                color: $color,
                timestamp: $timestamp,
                fields: [
                  {
                    name: "Ambiente",
                    value: $ambiente,
                    inline: false
                  },
                  {
                    name: "Autor",
                    value: $autor,
                    inline: false
                  },
                  {
                    name: "Alterações",
                    value: $alteracoes,
                    inline: false
                  },
                  {
                    name: "Data",
                    value: $data,
                    inline: false
                  }
                ]
              }]
            }')

          echo "Enviando notificação para o Discord..."

          # Enviar a requisição para o Discord e capturar resposta
          RESPONSE=$(curl -s -w "\n%{http_code}" -H "Content-Type: application/json" -X POST -d "${PAYLOAD}" "${DISCORD_WEBHOOK_URL}")
          HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
          BODY=$(echo "$RESPONSE" | sed '$d')

          if [[ "$HTTP_CODE" == 2* ]]; then
            echo "✅ Notificação enviada com sucesso! Código HTTP: $HTTP_CODE"
          else
            echo "❌ Falha ao enviar notificação para o Discord. Código HTTP: $HTTP_CODE"
            echo "Resposta do Discord: $BODY"
            exit 1
          fi
