name: CI_CD

permissions:
  contents: write
  issues: write
  pull-requests: write
  id-token: write
  deployments: write
  actions: write
  checks: write
  statuses: write

on:
  pull_request_target:
    types: [closed]
    branches:
      - dev
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  generate_version:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    outputs:
      new_tag: ${{ steps.tag_version.outputs.new_tag }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN }}

      - name: Get current branch
        id: branch
        run: echo "branch=$(echo ${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}})" >> $GITHUB_OUTPUT

      - name: Generate SEMVER tag
        id: tag_version
        run: |
          # Define default versions for each environment
          if [[ "${{ github.event.pull_request.base.ref }}" == "dev" ]]; then
            default_version="0.0.5"
            env_prefix="dev-v"
          else
            default_version="1.1.9"
            env_prefix="v"  # prod não tem prefixo especial
          fi

          # Fetch all tags
          git fetch --tags

          # Get latest tag for this environment
          latest_tag=$(git tag -l "${env_prefix}*" | sort -V | tail -n 1)

          if [ -z "$latest_tag" ]; then
            # No tags found for this environment, use default
            latest_tag="${env_prefix}${default_version}"
          fi

          echo "Found latest tag for environment: $latest_tag"

          # Remove prefix to get just the version number
          version_number="${latest_tag#${env_prefix}}"

          # Extract version components
          IFS='.' read -r -a version_parts <<< "$version_number"
          major="${version_parts[0]}"
          minor="${version_parts[1]}"
          patch="${version_parts[2]:-0}"

          # Increment patch version
          new_patch=$((patch + 1))
          if [[ $new_patch -gt 9 ]]; then
            new_patch=0
            new_minor=$((minor + 1))
            if [[ $new_minor -gt 9 ]]; then
              new_minor=0
              new_major=$((major + 1))
            else
              new_major=$major
            fi
          else
            new_major=$major
            new_minor=$minor
          fi

          # Format new version with environment prefix
          new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"

          # Verify if new tag already exists
          while git rev-parse "$new_tag" >/dev/null 2>&1; do
            echo "Tag $new_tag already exists, incrementing..."
            new_patch=$((new_patch + 1))
            new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"
          done

          # Debug output
          echo "Current version: $latest_tag"
          echo "New version: $new_tag"

          # Set output
          echo "new_tag=${new_tag}" >> $GITHUB_OUTPUT

          # Configure git
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

          # Create and push tag
          git tag $new_tag
          git push origin $new_tag

  deploy_dev:
    needs: [generate_version]
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'dev'
    runs-on: ubuntu-latest
    environment:
      name: dev
      url: https://dev.planify.app.br
    permissions:
      contents: read
      deployments: write
      pull-requests: write
      checks: write
      statuses: write

    steps:
      - name: Repository checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.PAT_TOKEN }}
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Deploy to Dev VPS
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{secrets.SERVER_HOST}}
          username: ${{secrets.SERVER_USER}}
          password: ${{secrets.SERVER_PASS}}
          script: |
            # Navigate to project directory
            cd /home/<USER>/planify-server

            # Update repository code
            git checkout dev --force
            git pull

            VERSION_NUMBER="${{ needs.generate_version.outputs.new_tag }}"
            if [[ $VERSION_NUMBER == dev-v* ]]; then
              VERSION_NUMBER=${VERSION_NUMBER#dev-v}
            else
              VERSION_NUMBER=${VERSION_NUMBER#v}
            fi

            # Build new image
            docker build \
              --build-arg PAT_TOKEN="${{ secrets.PAT_TOKEN }}" \
              --build-arg PORT=3334 \
              --build-arg NODE_ENV="development" \
              --build-arg DB_HOST="${{ secrets.DB_HOST }}" \
              --build-arg DB_PORT="${{ secrets.DB_PORT }}" \
              --build-arg DB_USER="${{ secrets.DB_USER_DEV }}" \
              --build-arg DB_PASSWORD="${{ secrets.DB_PASSWORD_DEV }}" \
              --build-arg DB_NAME="${{ secrets.DB_NAME_DEV }}" \
              --build-arg API_CURRENT_URL="${{ secrets.API_CURRENT_URL_DEV }}" \
              --build-arg FRONTEND_URL="${{ secrets.FRONTEND_URL_DEV }}" \
              --build-arg FROM_EMAIL="${{ secrets.FROM_EMAIL }}" \
              --build-arg BREVO_API_KEY="${{ secrets.BREVO_API_KEY }}" \
              --build-arg PLANAI_MODEL_CONNECTION="${{ secrets.PLANAI_MODEL_CONNECTION }}" \
              --build-arg MINIO_ENDPOINT="${{ secrets.MINIO_ENDPOINT }}" \
              --build-arg MINIO_PORT="${{ secrets.MINIO_PORT }}" \
              --build-arg MINIO_USE_SSL="${{ secrets.MINIO_USE_SSL }}" \
              --build-arg MINIO_ACCESS_KEY="${{ secrets.MINIO_ACCESS_KEY }}" \
              --build-arg MINIO_SECRET_KEY="${{ secrets.MINIO_SECRET_KEY }}" \
              --build-arg MINIO_BUCKET="${{ secrets.MINIO_BUCKET_DEV }}" \
              --build-arg PAYMENT_GATEWAY_URL="${{ secrets.PAYMENT_GATEWAY_URL_DEV }}" \
              --build-arg PAYMENT_GATEWAY_SECRET="${{ secrets.PAYMENT_GATEWAY_SECRET }}" \
              --build-arg LOGO_IMAGE_URL="${{ secrets.LOGO_IMAGE_URL }}" \
              --build-arg REDIS_HOST="${{ secrets.REDIS_HOST_DEV }}" \
              --build-arg REDIS_PORT="${{ secrets.REDIS_PORT_DEV }}" \
              --build-arg REDIS_USERNAME="${{ secrets.REDIS_USERNAME }}" \
              --build-arg REDIS_PASSWORD="${{ secrets.REDIS_PASSWORD }}" \
              --build-arg JWT_SECRET="${{ secrets.JWT_SECRET }}" \
              --build-arg ENV="dev" \
              --build-arg SUPPORT_WEBHOOK_URL="${{ secrets.SUPPORT_WEBHOOK_URL }}" \
              -t planify-server-dev:latest \
              .

            # Check if container exists
            if [ "$(docker ps -a | grep planify-server-dev-container)" ]; then
              echo "Container exists. Stopping and removing..."
              docker stop planify-server-dev-container
              docker rm planify-server-dev-container
            else
              echo "Container does not exist. Will create new one."
            fi

            # Run new container
            docker run -d \
              --name planify-server-dev-container \
              --restart always \
              -p 3334:3334 \
              -e PORT=3334 \
              -e NODE_ENV="development" \
              -e DB_HOST="${{ secrets.DB_HOST }}" \
              -e DB_PORT="${{ secrets.DB_PORT }}" \
              -e DB_USER="${{ secrets.DB_USER_DEV }}" \
              -e DB_PASSWORD="${{ secrets.DB_PASSWORD_DEV }}" \
              -e DB_NAME="${{ secrets.DB_NAME_DEV }}" \
              -e API_CURRENT_URL="${{ secrets.API_CURRENT_URL_DEV }}" \
              -e FRONTEND_URL="${{ secrets.FRONTEND_URL_DEV }}" \
              -e FROM_EMAIL="${{ secrets.FROM_EMAIL }}" \
              -e BREVO_API_KEY="${{ secrets.BREVO_API_KEY }}" \
              -e PLANAI_MODEL_CONNECTION="${{ secrets.PLANAI_MODEL_CONNECTION }}" \
              -e MINIO_ENDPOINT="${{ secrets.MINIO_ENDPOINT }}" \
              -e MINIO_PORT="${{ secrets.MINIO_PORT }}" \
              -e MINIO_USE_SSL="${{ secrets.MINIO_USE_SSL }}" \
              -e MINIO_ACCESS_KEY="${{ secrets.MINIO_ACCESS_KEY }}" \
              -e MINIO_SECRET_KEY="${{ secrets.MINIO_SECRET_KEY }}" \
              -e MINIO_BUCKET="${{ secrets.MINIO_BUCKET_DEV }}" \
              -e PAYMENT_GATEWAY_URL="${{ secrets.PAYMENT_GATEWAY_URL_DEV }}" \
              -e PAYMENT_GATEWAY_SECRET="${{ secrets.PAYMENT_GATEWAY_SECRET}}" \
              -e LOGO_IMAGE_URL="${{ secrets.LOGO_IMAGE_URL}}" \
              -e REDIS_HOST="${{ secrets.REDIS_HOST_DEV }}" \
              -e REDIS_PORT="${{ secrets.REDIS_PORT_DEV }}" \
              -e REDIS_USERNAME="${{ secrets.REDIS_USERNAME }}" \
              -e REDIS_PASSWORD="${{ secrets.REDIS_PASSWORD }}" \
              -e JWT_SECRET="${{ secrets.JWT_SECRET }}" \
              -e ENV="dev" \
              -e SUPPORT_WEBHOOK_URL="${{ secrets.SUPPORT_WEBHOOK_URL }}" \
              planify-server-dev:latest

            # Clean unused images
            docker image prune -f

  deploy_prod:
    needs: [generate_version]
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'main'
    runs-on: ubuntu-latest
    environment:
      name: prod
      url: https://board.planify.app.br
    permissions:
      contents: read
      deployments: write
      pull-requests: write
      checks: write
      statuses: write

    steps:
      - name: Repository checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.PAT_TOKEN }}
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Deploy to Production VPS
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{secrets.SERVER_HOST}}
          username: ${{secrets.SERVER_USER}}
          password: ${{secrets.SERVER_PASS}}
          script: |
            # Navigate to project directory
            cd /home/<USER>/planify-server

            # Update repository code
            git checkout main --force
            git pull

            VERSION_NUMBER="${{ needs.generate_version.outputs.new_tag }}"
            if [[ $VERSION_NUMBER == dev-v* ]]; then
              VERSION_NUMBER=${VERSION_NUMBER#dev-v}
            else
              VERSION_NUMBER=${VERSION_NUMBER#v}
            fi

            # Build new image
            docker build \
              --build-arg PAT_TOKEN="${{ secrets.PAT_TOKEN }}" \
              --build-arg PORT=3333 \
              --build-arg NODE_ENV="${{ secrets.NODE_ENV }}" \
              --build-arg DB_HOST="${{ secrets.DB_HOST }}" \
              --build-arg DB_PORT="${{ secrets.DB_PORT }}" \
              --build-arg DB_USER="${{ secrets.DB_USER }}" \
              --build-arg DB_PASSWORD="${{ secrets.DB_PASSWORD }}" \
              --build-arg DB_NAME="${{ secrets.DB_NAME }}" \
              --build-arg API_CURRENT_URL="${{ secrets.API_CURRENT_URL }}" \
              --build-arg FRONTEND_URL="${{ secrets.FRONTEND_URL }}" \
              --build-arg FROM_EMAIL="${{ secrets.FROM_EMAIL }}" \
              --build-arg BREVO_API_KEY="${{ secrets.BREVO_API_KEY }}" \
              --build-arg PLANAI_MODEL_CONNECTION="${{ secrets.PLANAI_MODEL_CONNECTION }}" \
              --build-arg MINIO_ENDPOINT="${{ secrets.MINIO_ENDPOINT }}" \
              --build-arg MINIO_PORT="${{ secrets.MINIO_PORT }}" \
              --build-arg MINIO_USE_SSL="${{ secrets.MINIO_USE_SSL }}" \
              --build-arg MINIO_ACCESS_KEY="${{ secrets.MINIO_ACCESS_KEY }}" \
              --build-arg MINIO_SECRET_KEY="${{ secrets.MINIO_SECRET_KEY }}" \
              --build-arg MINIO_BUCKET="${{ secrets.MINIO_BUCKET }}" \
              --build-arg PAYMENT_GATEWAY_URL="${{ secrets.PAYMENT_GATEWAY_URL }}" \
              --build-arg PAYMENT_GATEWAY_SECRET="${{ secrets.PAYMENT_GATEWAY_SECRET }}" \
              --build-arg LOGO_IMAGE_URL="${{ secrets.LOGO_IMAGE_URL }}" \
              --build-arg REDIS_HOST="${{ secrets.REDIS_HOST }}" \
              --build-arg REDIS_PORT="${{ secrets.REDIS_PORT }}" \
              --build-arg REDIS_USERNAME="${{ secrets.REDIS_USERNAME }}" \
              --build-arg REDIS_PASSWORD="${{ secrets.REDIS_PASSWORD }}" \
              --build-arg JWT_SECRET="${{ secrets.JWT_SECRET }}" \
              --build-arg SUPPORT_WEBHOOK_URL="${{ secrets.SUPPORT_WEBHOOK_URL }}" \
              --build-arg ENV="prod" \
              -t planify-server:latest \
              .

            # Check if container exists
            if [ "$(docker ps -a | grep planify-server-container)" ]; then
              echo "Container exists. Stopping and removing..."
              docker stop planify-server-container
              docker rm planify-server-container
            else
              echo "Container does not exist. Will create new one."
            fi

            # Run new container
            docker run -d \
              --name planify-server-container \
              --restart always \
              -p 3333:3333 \
              -e PORT=3333 \
              -e NODE_ENV="${{ secrets.NODE_ENV }}" \
              -e DB_HOST="${{ secrets.DB_HOST }}" \
              -e DB_PORT="${{ secrets.DB_PORT }}" \
              -e DB_USER="${{ secrets.DB_USER }}" \
              -e DB_PASSWORD="${{ secrets.DB_PASSWORD }}" \
              -e DB_NAME="${{ secrets.DB_NAME }}" \
              -e API_CURRENT_URL="${{ secrets.API_CURRENT_URL }}" \
              -e FRONTEND_URL="${{ secrets.FRONTEND_URL }}" \
              -e FROM_EMAIL="${{ secrets.FROM_EMAIL }}" \
              -e BREVO_API_KEY="${{ secrets.BREVO_API_KEY }}" \
              -e PLANAI_MODEL_CONNECTION="${{ secrets.PLANAI_MODEL_CONNECTION }}" \
              -e MINIO_ENDPOINT="${{ secrets.MINIO_ENDPOINT }}" \
              -e MINIO_PORT="${{ secrets.MINIO_PORT }}" \
              -e MINIO_USE_SSL="${{ secrets.MINIO_USE_SSL }}" \
              -e MINIO_ACCESS_KEY="${{ secrets.MINIO_ACCESS_KEY }}" \
              -e MINIO_SECRET_KEY="${{ secrets.MINIO_SECRET_KEY }}" \
              -e MINIO_BUCKET="${{ secrets.MINIO_BUCKET }}" \
              -e PAYMENT_GATEWAY_URL="${{ secrets.PAYMENT_GATEWAY_URL }}" \
              -e PAYMENT_GATEWAY_SECRET="${{ secrets.PAYMENT_GATEWAY_SECRET }}" \
              -e LOGO_IMAGE_URL="${{ secrets.LOGO_IMAGE_URL }}" \
              -e REDIS_HOST="${{ secrets.REDIS_HOST }}" \
              -e REDIS_PORT="${{ secrets.REDIS_PORT }}" \
              -e REDIS_USERNAME="${{ secrets.REDIS_USERNAME }}" \
              -e REDIS_PASSWORD="${{ secrets.REDIS_PASSWORD }}" \
              -e JWT_SECRET="${{ secrets.JWT_SECRET }}" \
              -e SUPPORT_WEBHOOK_URL="${{ secrets.SUPPORT_WEBHOOK_URL }}" \
              -e ENV="prod" \
              planify-server:latest

            # Clean unused images
            docker image prune -f

  notify_discord:
    needs: [generate_version, deploy_dev, deploy_prod]
    if: always() && (needs.deploy_dev.result == 'success' || needs.deploy_prod.result == 'success')
    runs-on: ubuntu-latest

    steps:
      - name: Send Discord Notification
        run: |
          if [[ "${{ github.event.pull_request.base.ref }}" == "dev" ]]; then
            ENVIRONMENT="DEV"
          else
            ENVIRONMENT="PROD"
          fi

          PR_TITLE="${{ github.event.pull_request.title }}"
          PR_AUTHOR="${{ github.event.pull_request.user.login }}"
          VERSION="${{ needs.generate_version.outputs.new_tag }}"
          CURRENT_TIME=$(date -u +"%Y-%m-%d")

          # Monta a mensagem primeiro
          MESSAGE=" Novo deploy de BACKEND ${ENVIRONMENT} finalizado!
          [VERSÃO]: ${VERSION}
          [AUTOR]: ${PR_AUTHOR}
          [ALTERAÇÕES]:
          ${PR_TITLE}
          [DATA]: ${CURRENT_TIME}"

          # Monta o payload usando jq corretamente
          PAYLOAD=$(jq -n \
            --arg msg "$MESSAGE" \
            '{
              username: "StatusReport BOT",
              content: $msg
            }'
          )

          # Envia para o Discord
          curl -H "Content-Type: application/json" -X POST -d "$PAYLOAD" ${{ secrets.DISCORD_WEBHOOK_URL }}
