type: collection.insomnia.rest/5.0
name: Planify
meta:
  id: wrk_781b034a74274310bdcd147e8e7aa169
  created: 1746570451031
  modified: 1746570451031
collection:
  - name: Users
    meta:
      id: fld_73f6af4b65244965bdb381113eeb4a0c
      created: 1706313287322
      modified: 1745602409601
      sortKey: -1708540602473.2188
    children:
      - url: "{{ _.baseURL }}/users"
        name: create_user
        meta:
          id: req_6681e1e4925e4f30ab0295fd2e4e3848
          created: 1706313293683
          modified: 1733834675084
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "name": "<PERSON> Rodrigues",
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users"
        name: get_all_users
        meta:
          id: req_825d19327345493abda63880b966818b
          created: 1706313497110
          modified: 1732581725138
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
        name: get user details
        meta:
          id: req_cf385ef69de642b69f5bc49c3cf1279e
          created: 1721318557901
          modified: 1737505995862
          isPrivate: false
          sortKey: -*************
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users/activate"
        name: Activate account
        meta:
          id: req_fe6df7b0339a4e83a80b6189c0e41bd8
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.125
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
              "activationCode": "PLFLuYKaZCH"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users"
        name: create_user new
        meta:
          id: req_b37ec72833e342baa6b274288050c71b
          created: *************
          modified: ********04216
          isPrivate: false
          sortKey: -1706304399570.8906
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "name": "AI",
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv*"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Auth
    meta:
      id: fld_0e80ae2b8a7c4567b8e194736ab37d2d
      created: 1706314033786
      modified: 1717713703583
      sortKey: -1708770937165
    children:
      - url: "{{ _.baseURL }}/auth"
        name: Session
        meta:
          id: req_b8eca160fa744435acb2d8f6cfaf2fba
          created: 1706314036537
          modified: 1734120710256
          isPrivate: false
          sortKey: -1706314036537
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
             "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth/google"
        name: Google Session (lucasrod2008...)
        meta:
          id: req_21946cdc7cec4336b1590ce6378a5177
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.5
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
              "credential": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjZjZTExYWVjZjllYjE0MDI0YTQ0YmJmZDFiY2Y4YjMyYTEyMjg3ZmEiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GZpFlXcLwLdo5Fp3ZNjyA3Blps6Io6MUbIHDtAsZoQEv-WefSUPU6-2HPX7zrxdsCDPIwRB8ZywctSBiXcJ-r646m6rR66WEIA9gJzK5D1Q1WifZ-FlhsceAbu3pWvH89NRGEm_3yFtDr6zo6wOFMwGZRnk3UOoP3dhbEIHjSY5OIPUMidgvy45CqBx9A0d1DUkeqBaI_pg4hX6n85nfEDC6UemrwOf8oc2lEursKIR3wSlO1IigyGHo7kbz98kTIKuoljid611SIuweZz474PC99LFvHLle_H8rvqrvSiycUWqZ6vyDXR7Yt4SOoiWTWOYyyXDoglFvI4GhHnlC0g",
              "clientId": "738092587023-bvq1f9n846eo1leukdf7b42eu10p299s.apps.googleusercontent.com",
              "select_by": "btn"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth"
        name: Session By Email when created account
        meta:
          id: req_3654a79afb6043559035fbce9438c575
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.25
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth/refresh"
        name: Refresh token
        meta:
          id: req_f42f00a4881a41a792cdce10c79cd705
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.625
        method: PATCH
        body:
          mimeType: application/json
          text: >-
            {
            	"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************.04l5tXRl0nMrcVoASqvC6NqGWVgDc6T7kxntNzaUcP4"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth/reset-password"
        name: Update password
        meta:
          id: req_d581bf28b6ce4160a28e4aeaef0c0025
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.375
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
              "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5OThhYjI0Ni1kNTAyLTQwNDYtOGY0Ny1jZDk2ZDI3N2YwZTMiLCJwYXNzd29yZEhhc2giOiIkMmIkMTAkMGdkV3RDclZTN3lZdWQ0MVF5MkltLlUwTS9CREtzeVIxYTBXcUkzQlFmMGhWYlV2R1pwQmUiLCJ0eXBlIjoicGFzc3dvcmQtcmVzZXQiLCJpYXQiOjE3Mzg3MDMzMzcsImV4cCI6MTczODcwNjkzN30.bowRpLyFWDFdATeiEbEjWC5n0yYQWuA8N3r0O-x8jgU",
            	"password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Default prompts
    meta:
      id: fld_ea5289e8193442569d612729be0de690
      created: 1707778733096
      modified: 1707778733096
      sortKey: -1701929377119
    children:
      - url: "{{ _.baseURL }}/prompts"
        name: create prompt
        meta:
          id: req_d081ae1ae4fc4349996a0ff0ccd2fcc9
          created: 1707778733097
          modified: 1733931861394
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Corrida",
            	"categoryId": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
            	"description": "Crie um cronograma de ciclismo para [TARGET] em [PERIOD] com [ACTIVITIES_SIZE] atividades por semana",
            	"persona": "Atue como um personal trainer especialista em corrida, ciclismo e maratonas",
            	"returnType": "JSON",
            	"customAction": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts"
        name: get_all
        meta:
          id: req_1e33023040534c7f81cfe25dc8a707f4
          created: 1707778733098
          modified: 1720099373612
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts/2b55e882-4d8d-4cba-9745-1e13e9ad098c"
        name: get prompt
        meta:
          id: req_c1eb5eeb080c40e08b6dbc61b0dfa5c7
          created: 1719589417466
          modified: 1743097339024
          isPrivate: false
          sortKey: -1706313478330
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts"
        name: create prompt massa muscular
        meta:
          id: req_3bed56edb8794d6e80d690489f5c8b64
          created: 1736604468132
          modified: 1736604727402
          isPrivate: false
          sortKey: -1706277707913.5625
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Ganho de Massa muscular",
            	"categoryId": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
            	"description": "Crie um cronograma para [TARGET] com pelo menos [ACTIVITIES_SIZE] exercícios diários diferentes, porém não limitado a essa quantidade, informando a carga de peso inicial, a quantidades de séries e a orientações necessários para conquistar o objetivo de crescer em musculatura e perda de gordura. Suas referencias iniciais são: peso inicial: [CURRENT_PARAM]; percentual de gordura inicial: [INITIAL_PARAM]; peso alvo: [FINAL_TARGET]; percentual de gordura alvo: [FINAL_TARGET_PARAM]",
            	"persona": "Atue como um personal trainer especialista em treinamento de força e ganho de massa muscular",
            	"returnType": "JSON",
            	"customAction": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts"
        name: create prompt2
        meta:
          id: req_13d83e931e854b758b8656d84e08a299
          created: 1743099153865
          modified: 1743099280838
          isPrivate: false
          sortKey: -1706308848180.4453
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"description": "Desenvolva um plano de metas pessoais para [TARGET] em [PERIOD], com [ACTIVITIES_SIZE] objetivos definidos por semana.",
            	"persona": "Atue como [TIPO DE PERSONA] pessoal especializado em [ESPECIALIZAÇÃO]",
            	"name": "Teste",
            	"categoryId": "9581d214-7ae3-4eb1-9107-e6ce202ee989",
            	"returnType": "JSON"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts/24e44364-7879-46eb-9f1d-51baec5aae38"
        name: Update prompt
        meta:
          id: req_f3bb939623c94596a56393cc3e2ca317
          created: 1743108265831
          modified: 1743108276143
          isPrivate: false
          sortKey: -1706311072485.2227
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Corrida"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts/24e44364-7879-46eb-9f1d-51baec5aae38"
        name: Delete prompt
        meta:
          id: req_f0b442af4e9a4269bc8e40dd205f918f
          created: 1743110929432
          modified: 1743110937254
          isPrivate: false
          sortKey: -1706309960332.834
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Categories
    meta:
      id: fld_370ccdf2f8144fd792918b3488902b82
      created: 1707778933412
      modified: 1707778933412
      sortKey: -1704121332220.5
    children:
      - url: "{{ _.baseURL }}/categories"
        name: create_category
        meta:
          id: req_971c9f6f11b049ddaf3b9f74f8f96c52
          created: 1707778933414
          modified: 1733931812656
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Desenvolvimento pessoal",
            	"description": "Categoria destinada a Desenvolvimento pessoal"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/categories"
        name: get_all
        meta:
          id: req_0cdf6ccad6af473a9c46341416463833
          created: 1707778933415
          modified: 1711470198885
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: User plans
    meta:
      id: fld_642485b48a854df18866633343d9790d
      created: 1707845683187
      modified: 1744739606519
      sortKey: -1701381388343.625
    children:
      - url: "{{ _.baseURL }}/user-plans"
        name: get_all
        meta:
          id: req_64aa9e04b0034d4d80aa8caaddc2eb8a
          created: 1707845683189
          modified: 1712874942339
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans"
        name: create initial plan
        meta:
          id: req_68a440a2996d4d75b831db20d266483c
          created: 1707851594092
          modified: 1742420868436
          isPrivate: false
          sortKey: -1706313371934.4531
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
            	"promptId": "ed06b051-ab6e-425d-82ad-a5bf7b6f5577",
            	"target": "Correr 1KM em 4 semanas",
            	"startedAt": "2024-04-01T10:00:00.000Z",
            	"endAt": "2024-04-13T10:00:00.000Z",
            	"duration": "4 semanas",
            	"hasHealthRisk": false,
            	"acceptedTerms": true,
            	"totalSteps": 4,
            	"currentStep": 1,
            	"pendingSteps": 4,
            	"totalWeeks": 4,
            	"initialParam": "",
            	"currentParam": "",
            	"finalTargetParam": "",
            	"finalTarget": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/14bab132-8398-4904-b230-4b2c239e83f3"
        name: Get plan Details
        meta:
          id: req_f2876e23a965488eacc56e548dbfc7db
          created: 1707855894157
          modified: 1746749567793
          isPrivate: false
          sortKey: -1706313396950
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/f8a53af9-b2a5-454a-97f7-99fee55c486e"
        name: Delete plan
        meta:
          id: req_78fdfaf93c8d4ff8a24736454aab9c4c
          created: 1707861020036
          modified: 1742420893160
          isPrivate: false
          sortKey: -1706313371910.0015
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/week"
        name: create plan empty weeks
        meta:
          id: req_037e8e715c494f1ab14386f65e926322
          created: 1707862151524
          modified: 1742420888544
          isPrivate: false
          sortKey: -1706313371910.012
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "14fd66bd-66d1-409b-87d1-ce9bad70a774",
            	"planId": "05898e44-ec64-40d6-a7af-935b4745b81c",
            	"startDate": "2024-02-13T10:00:00.000Z",
            	"endDate": "2024-04-13T10:00:00.000Z",
            	"weekNumber": 10
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity"
        name: create week activity
        meta:
          id: req_e9c33a82247e4d4fb8e4d2db0cbc4a8c
          created: 1707862347163
          modified: 1742420889991
          isPrivate: false
          sortKey: -1706313371910.0059
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"weekId": "7dc8806b-d41a-42d1-86e8-10d3f55833b0",
            	"planId": "dcc8fe21-b5dc-47dd-83a6-07f9d6cb6903",
            	"date": "2024-04-13T10:00:00.000Z",
            	"activityDescription": "Corrida",
            	"timeActivity": "30 minutos"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/4beb1c74-ee18-45cc-b9d6-26344b5a3670"
        name: create plan activities
        meta:
          id: req_3dde673544eb4258ba85b3f29edb5e13
          created: 1708033569087
          modified: 1744122306326
          isPrivate: false
          sortKey: -1706313371913.0566
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"weeks": [
            		{
            			"startDate": "2024-02-13T10:00:00.000Z",
            			"endDate": "2024-04-13T10:00:00.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2024-02-15T13:00:00.000Z",
            					"activityDescription": "Caminhada",
            					"timeActivity": "30 minutos",
            					"activityDescription": "Costas e Bíceps",
            					"activityDetails": [
            						{
            							"title": "Puxada Frontal na Barra Fixa",
            							"repetitions": "4 séries de 10-12 repetições",
            							"initialLoad": "assistência se necessário"
            						},
            						{
            							"title": "Remada Curvada com Barra",
            							"repetitions": "3 séries de 10-12 repetições",
            							"initialLoad": "60% do seu 1RM"
            						},
            						{
            							"title": "Remada Unilateral com Halter",
            							"repetitions": "3 séries de 12-15 repetições",
            							"initialLoad": "12kg cada halter"
            						},
            						{
            							"title": "Rosca Direta com Barra",
            							"repetitions": "4 séries de 12-15 repetições",
            							"initialLoad": "50% do seu 1RM"
            						}
            					]
            				}
            			]
            		}
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans"
        name: create plan with activities
        meta:
          id: req_02ffe6f28bed4199b5964a6caa664efd
          created: 1708033905962
          modified: 1742420871473
          isPrivate: false
          sortKey: -1706313371916.1133
        method: POST
        body:
          mimeType: application/json
          text: |
            {
              "userId": "8c79a9ea-413c-4a6b-aa61-6c06dbc91f00",
            	"promptId": "585f36e9-f8af-4b77-95d3-8653fddbcd64",
              "target": "Correr 5 km em 3 semanas",
              "startedAt": "2024-02-22T00:00:00.000Z",
              "endAt": "2024-03-13T00:00:00.000Z",
              "duration": "3 semanas",
              "totalSteps": 4,
              "currentStep": 1,
              "totalWeeks": 4,
              "pendingSteps": 4,
              "hasHealthRisk": false,
              "acceptedTerms": true,
              "weeks": [
                {
                  "startDate": "2024-02-22T00:00:00.000Z",
                  "endDate": "2024-02-28T00:00:00.000Z",
                  "weekNumber": 1,
                  "activities": [
                    {
                      "date": "2024-02-24T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "20 minutos"
                    },
                    {
                      "date": "2024-02-26T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "25 minutos"
                    },
                    {
                      "date": "2024-02-28T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "30 minutos"
                    }
                  ]
                },
                {
                  "startDate": "2024-02-29T00:00:00.000Z",
                  "endDate": "2024-03-06T00:00:00.000Z",
                  "weekNumber": 2,
                  "activities": [
                    {
                      "date": "2024-03-02T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "25 minutos"
                    },
                    {
                      "date": "2024-03-04T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "30 minutos"
                    },
                    {
                      "date": "2024-03-06T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "35 minutos"
                    }
                  ]
                },
                {
                  "startDate": "2024-03-07T00:00:00.000Z",
                  "endDate": "2024-03-13T00:00:00.000Z",
                  "weekNumber": 3,
                  "activities": [
                    {
                      "date": "2024-03-09T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "30 minutos"
                    },
                    {
                      "date": "2024-03-11T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "35 minutos"
                    },
                    {
                      "date": "2024-03-13T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "40 minutos"
                    }
                  ]
                }
              ]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30/all"
        name: get plans by user
        meta:
          id: req_e47662c1816e462b8f435f9afcc0615a
          created: 1709585072155
          modified: 1740610936019
          isPrivate: false
          sortKey: -1706313421990
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans"
        name: create manual plan
        meta:
          id: req_40f1f3e27dc948719c954e22a222cab1
          created: 1709762085978
          modified: 1742420869967
          isPrivate: false
          sortKey: -1706313371922.2266
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
                "title": "test",
                "userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
                "promptId": "6cae1e86-1a15-4a71-a468-5e072d56bbe6",
                "startAt": "2024-11-25T00:00:00.000Z",
                "startedAt": "2024-11-25T00:00:00.000Z",
                "endAt": "2024-12-30T00:00:00.000Z",
                "duration": "5 semanas",
                "creationStrategy": "manual",
                "target": "Esporte e Saúde 1km em 5 semanas",
                "totalSteps": 5,
                "createEmptyWeeks": true,
                "currentStep": 1,
                "totalWeeks": 5,
                "pendingSteps": 5,
                "hasHealthRisk": false,
                "acceptedTerms": true,
                "activitiesPerWeek": 4,
                "initialParam": "",
                "currentParam": "",
                "finalTargetParam": "",
                "finalTarget": "",
                "planColor": "#E01E36"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/check"
        name: Job schedule generation
        meta:
          id: req_8102144f82d8404780f908a573b1f77a
          created: 1709832080846
          modified: 1742420891436
          isPrivate: false
          sortKey: -1706313371910.003
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/infos/dc6e9e2a-ab17-4fd2-845b-ece16161b841"
        name: Update plan infos
        meta:
          id: req_833e5d79760e498cb31a2193de82d727
          created: 1710178601291
          modified: 1742420876709
          isPrivate: false
          sortKey: -1706313371910.7642
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"currentStepGeneration": 2
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/d83de865-18e5-4fe9-945b-bc199aeaf8fb"
        name: create plan activity - Single
        meta:
          id: req_6096f1b0511042908c95e7a73a7291b3
          created: 1710806459359
          modified: 1742420874896
          isPrivate: false
          sortKey: -1706313371911.5283
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"category": "Corrida"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/4beb1c74-ee18-45cc-b9d6-26344b5a3670"
        name: Add Generated Plan
        meta:
          id: req_7e884383020243139ec1fbdfbb8525ef
          created: 1715716040060
          modified: 1744122445512
          isPrivate: false
          sortKey: -1706313371910.0955
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"weeks": [
            		{
            			"startDate": "2024-06-02T00:00:00.000Z",
            			"endDate": "2024-06-08T00:00:00.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2024-06-02T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-03T08:00:00.000Z",
            					"activityDescription": "Corrida de 5 km",
            					"timeActivity": "30 minutos"
            				},
            				{
            					"date": "2024-06-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-05T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 7 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-06T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-06-07T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 3 km",
            					"timeActivity": "20 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-09T00:00:00.000Z",
            			"endDate": "2024-06-15T00:00:00.000Z",
            			"weekNumber": 2,
            			"activities": [
            				{
            					"date": "2024-06-09T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-10T08:00:00.000Z",
            					"activityDescription": "Corrida de 5 km",
            					"timeActivity": "30 minutos"
            				},
            				{
            					"date": "2024-06-11T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-12T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 7 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-13T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-06-14T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 4 km",
            					"timeActivity": "25 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-16T00:00:00.000Z",
            			"endDate": "2024-06-22T00:00:00.000Z",
            			"weekNumber": 3,
            			"activities": [
            				{
            					"date": "2024-06-16T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-17T08:00:00.000Z",
            					"activityDescription": "Corrida de 6 km",
            					"timeActivity": "35 minutos"
            				},
            				{
            					"date": "2024-06-18T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-19T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 8 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-20T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-06-21T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 4 km",
            					"timeActivity": "25 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-23T00:00:00.000Z",
            			"endDate": "2024-06-29T00:00:00.000Z",
            			"weekNumber": 4,
            			"activities": [
            				{
            					"date": "2024-06-23T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-24T08:00:00.000Z",
            					"activityDescription": "Corrida de 6 km",
            					"timeActivity": "35 minutos"
            				},
            				{
            					"date": "2024-06-25T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-26T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 8 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-27T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-06-28T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 5 km",
            					"timeActivity": "30 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-30T00:00:00.000Z",
            			"endDate": "2024-07-06T00:00:00.000Z",
            			"weekNumber": 5,
            			"activities": [
            				{
            					"date": "2024-06-30T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "55 minutos"
            				},
            				{
            					"date": "2024-07-01T08:00:00.000Z",
            					"activityDescription": "Corrida de 7 km",
            					"timeActivity": "40 minutos"
            				},
            				{
            					"date": "2024-07-02T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "55 minutos"
            				},
            				{
            					"date": "2024-07-03T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo",
            					"timeActivity": "55 minutos"
            				},
            				{
            					"date": "2024-07-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 30 minutos"
            				},
            				{
            					"date": "2024-07-05T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 5 km",
            					"timeActivity": "30 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-07T00:00:00.000Z",
            			"endDate": "2024-07-13T00:00:00.000Z",
            			"weekNumber": 6,
            			"activities": [
            				{
            					"date": "2024-07-07T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-07-08T08:00:00.000Z",
            					"activityDescription": "Corrida de 7 km",
            					"timeActivity": "40 minutos"
            				},
            				{
            					"date": "2024-07-09T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-07-10T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 9 km",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-07-11T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 45 minutos"
            				},
            				{
            					"date": "2024-07-12T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 6 km",
            					"timeActivity": "35 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-14T00:00:00.000Z",
            			"endDate": "2024-07-20T00:00:00.000Z",
            			"weekNumber": 7,
            			"activities": [
            				{
            					"date": "2024-07-14T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 5 minutos"
            				},
            				{
            					"date": "2024-07-15T08:00:00.000Z",
            					"activityDescription": "Corrida de 8 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-07-16T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "1 hora 5 minutos"
            				},
            				{
            					"date": "2024-07-17T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 10 km",
            					"timeActivity": "1 hora 5 minutos"
            				},
            				{
            					"date": "2024-07-18T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas"
            				},
            				{
            					"date": "2024-07-19T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 6 km",
            					"timeActivity": "35 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-21T00:00:00.000Z",
            			"endDate": "2024-07-27T00:00:00.000Z",
            			"weekNumber": 8,
            			"activities": [
            				{
            					"date": "2024-07-21T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 10 minutos"
            				},
            				{
            					"date": "2024-07-22T08:00:00.000Z",
            					"activityDescription": "Corrida de 8 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-07-23T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "1 hora 10 minutos"
            				},
            				{
            					"date": "2024-07-24T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 10 km",
            					"timeActivity": "1 hora 10 minutos"
            				},
            				{
            					"date": "2024-07-25T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas 15 minutos"
            				},
            				{
            					"date": "2024-07-26T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 7 km",
            					"timeActivity": "40 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-28T00:00:00.000Z",
            			"endDate": "2024-08-03T00:00:00.000Z",
            			"weekNumber": 9,
            			"activities": [
            				{
            					"date": "2024-07-28T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-07-29T08:00:00.000Z",
            					"activityDescription": "Corrida de 9 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-07-30T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-07-31T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 11 km",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-08-01T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas 30 minutos"
            				},
            				{
            					"date": "2024-08-02T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 7 km",
            					"timeActivity": "40 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-08-04T00:00:00.000Z",
            			"endDate": "2024-08-10T00:00:00.000Z",
            			"weekNumber": 10,
            			"activities": [
            				{
            					"date": "2024-08-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 20 minutos"
            				},
            				{
            					"date": "2024-08-05T08:00:00.000Z",
            					"activityDescription": "Corrida de 9 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-08-06T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "1 hora 20 minutos"
            				},
            				{
            					"date": "2024-08-07T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 11 km",
            					"timeActivity": "1 hora 20 minutos"
            				},
            				{
            					"date": "2024-08-08T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas 45 minutos"
            				},
            				{
            					"date": "2024-08-09T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 8 km",
            					"timeActivity": "45 minutos"
            				}
            			]
            		}
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
          - name: Bearer
            value: "{{ _.token }}"
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/8c880252-9004-43c8-b20c-995e0e9a704d"
        name: Add Stream activities
        meta:
          id: req_707e9d7da7e94fe7ba4b082269b3db18
          created: 1718925813222
          modified: 1742420883871
          isPrivate: false
          sortKey: -1706313371910.0479
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"weeks": [
            		{
            			"startDate": "2024-09-29T03:00:00.000Z",
            			"endDate": "2024-10-06T02:59:59.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2024-06-02T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-03T08:00:00.000Z",
            					"activityDescription": "Corrida de 5 km",
            					"timeActivity": "30 minutos"
            				},
            				{
            					"date": "2024-06-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-05T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 7 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-06T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-06-07T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 3 km",
            					"timeActivity": "20 minutos"
            				}
            			]
            		}
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/close/a1ec38d7-881a-4d37-a898-04fa5094649a"
        name: Close Plan Generation
        meta:
          id: req_aa71f3a77a744f3ab156f4d0a5861fca
          created: 1721310996211
          modified: 1742420880049
          isPrivate: false
          sortKey: -1706313371910.191
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/infos/9918033e-c8d3-425c-b0fa-e9f14f693f43"
        name: Update plan status
        meta:
          id: req_13ad3db7e47843e8b035ebe25e95d88f
          created: 1733257452327
          modified: 1742420878432
          isPrivate: false
          sortKey: -1706313371910.382
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"isCompletedGeneration": true,
            	"isGeneratingPlan": false
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/day/2025-02-26T00:00:00"
        name: Get user plan activities
        meta:
          id: req_25be688b20594dff9ccb117a81d215cf
          created: 1740608846086
          modified: 1740611051762
          isPrivate: false
          sortKey: -1706313373475
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/task/getAllCurrentAndFutureTasks"
        name: get future tasks
        meta:
          id: req_1d9ea2fe42a444b180f034e562bd5e88
          created: 1740609186036
          modified: 1740610728067
          isPrivate: false
          sortKey: -1706313384430
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/month/2025-02-01T00:00:00"
        name: get month activities
        meta:
          id: req_3da7f1d5e19742b1852105931c63898d
          created: 1740609212983
          modified: 1740611065549
          isPrivate: false
          sortKey: -1706313372301.25
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/pending"
        name: get pending activities
        meta:
          id: req_0c79bf4bc4154a629c551c641312fb5f
          created: 1740609247939
          modified: 1740611056992
          isPrivate: false
          sortKey: -1706313372692.5
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/task/getAllTasksDelay"
        name: get all tasks delay
        meta:
          id: req_6fa8f8802f8b411a8cc13279612156a9
          created: 1740609266769
          modified: 1740610755274
          isPrivate: false
          sortKey: -1706313378170
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis/history"
        name: sentiment history
        meta:
          id: req_504efc2265944dc491dd1fe9f0b8a0b6
          created: 1740609285005
          modified: 1740611070524
          isPrivate: false
          sortKey: -1706313372105.625
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/day/2025-02-27%2000:00:00"
        name: user activities day
        meta:
          id: req_a934fe1b60b6433aa2388e1dd3671eb9
          created: 1740609307770
          modified: 1740611075539
          isPrivate: false
          sortKey: -1706313372007.8125
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/count-by-status/all"
        name: count by status
        meta:
          id: req_7e6d4671584d4bf49f580cd5de2fe703
          created: 1740609330065
          modified: 1740610789469
          isPrivate: false
          sortKey: -1706313375040
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL
          }}/recommendations/feedback/8dc37680-bf51-478e-8ecd-f8812ddc5c61"
        name: recommendations feedback
        meta:
          id: req_fdfc062f4d324529850d66f3a7276b38
          created: 1740609353718
          modified: 1740611080054
          isPrivate: false
          sortKey: -1706313371958.9062
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/e81d7b1a-0aec-41b5-b3b0-b7290b7f2777"
        name: create plan activities - ERROR
        meta:
          id: req_e6f38b8ee8e844d2a89b1cbc4f434672
          created: 1742420865705
          modified: 1743696448664
          isPrivate: false
          sortKey: -1706313371910.0022
        method: PATCH
        body:
          mimeType: application/json
          text: >-
            {
            	"weeks": [
            		{
            			"startDate": "2025-01-01T12:00:00.000Z",
            			"endDate": "2025-01-07T11:59:59.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2025-01-02T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino leve",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 1,
            							"title": "Ciclismo em ritmo leve",
            							"description": "Pedale em um ritmo confortável, mantendo uma cadência de 60-70 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-04T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino moderado",
            					"timeActivity": "40:00 minutos",
            					"activityDetails": [
            						{
            							"id": 2,
            							"title": "Ciclismo em ritmo moderado",
            							"description": "Pedale em um ritmo moderado, com cadência de 70-80 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-06T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Intercalado",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 3,
            							"title": "Ciclismo intervalado",
            							"description": "Pedale 1 minuto rápido seguido por 2 minutos leves, repetir por 30 minutos.",
            							"status": "incomplete"
            						}
            					]
            				}
            			]
            		},
            		{
            			"startDate": "2025-01-08T12:00:00.000Z",
            			"endDate": "2025-01-14T11:59:59.000Z",
            			"weekNumber": 2,
            			"activities": [
            				{
            					"date": "2025-01-09T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino leve",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 4,
            							"title": "Ciclismo em ritmo leve",
            							"description": "Pedale em um ritmo confortável, mantendo uma cadência de 60-70 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-11T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino moderado",
            					"timeActivity": "40:00 minutos",
            					"activityDetails": [
            						{
            							"id": 5,
            							"title": "Ciclismo em ritmo moderado",
            							"description": "Pedale em um ritmo moderado, com cadência de 70-80 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-13T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Intercalado",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 6,
            							"title": "Ciclismo intervalado",
            							"description": "Pedale 1 minuto rápido seguido por 2 minutos leves, repetir por 30 minutos.",
            							"status": "incomplete"
            						}
            					]
            				}
            			]
            		},
            		{
            			"startDate": "2025-01-15T12:00:00.000Z",
            			"endDate": "2025-01-21T11:59:59.000Z",
            			"weekNumber": 3,
            			"activities": [
            				{
            					"date": "2025-01-16T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino leve",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 7,
            							"title": "Ciclismo em ritmo leve",
            							"description": "Pedale em um ritmo confortável, mantendo uma cadência de 60-70 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-18T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino moderado",
            					"timeActivity": "40:00 minutos",
            					"activityDetails": [
            						{
            							"id": 8,
            							"title": "Ciclismo em ritmo moderado",
            							"description": "Pedale em um ritmo moderado, com cadência de 70-80 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-20T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Intercalado",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 9,
            							"title": "Ciclismo intervalado",
            							"description": "Pedale 1 minuto rápido seguido por 2 minutos leves, repetir por 30 minutos.",
            							"status": "incomplete"
            						}
            					]
            				}
            			]
            		}
            	],
            	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - name: Errors
        meta:
          id: ws-req_edac80a908574f1ebc2fa14edb364aae
          created: 1740609136708
          modified: 1740609150413
          sortKey: -1706313371910
        settings:
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
  - name: Start generation
    meta:
      id: fld_497e7fdfebf04bd2a4caacfb3a5d684f
      created: 1708380308306
      modified: 1710254584809
      sortKey: -1700833399568.25
    children:
      - url: "{{ _.baseURL }}/user-plans/generate"
        name: Generate plan
        meta:
          id: req_3dc045dd5574415cae4ddd83d401acf5
          created: 1708380308307
          modified: 1740605235310
          isPrivate: false
          sortKey: -1708032609473
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
            	"promptId": "6cae1e86-1a15-4a71-a468-5e072d56bbe6",
            	"startAt": "2025-01-01T10:00:00.000Z",
            	"endAt": "2025-02-05T10:00:00.000Z",
            	"duration": "5 Semanas",
            	"target": "Ciclismo de 5KM em 5 semanas",
            	"totalSteps": 5,
            	"currentStep": 1,
            	"totalWeeks": 5,
            	"pendingSteps": 5,
            	"hasHealthRisk": false,
            	"acceptedTerms": true,
            	"creationStrategy": "auto"
            }
        parameters:
          - id: pair_d2bc5d1ee7154dda901fb9d3d0d0ffdc
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://146.190.12.107:8000/start
        name: IA Model generate
        meta:
          id: req_5672f976c2044b279c853c68b3efb1ec
          created: 1709250979496
          modified: 1717178584732
          isPrivate: false
          sortKey: -1707908469126.25
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "fc554018-c525-4f44-8239-5be4e30f5d1c",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 10/10/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como correr CORRER 5KM EM 40SEMANAS.  Retorne o cronograma completo com apenas 10 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-02-13T10:00:00.000Z\",\"endDate\":\"2024-04-13T10:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\",\"weekNumber\":2,\"activities\":[{\"date\":\"2024-04-15T10:00:00.000Z\",\"activityDescription\":\"Ciclismo\",\"timeActivity\":\"1h:30 horas\"}]}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://localhost:8000/start
        name: IA Model generate COPY
        meta:
          id: req_8d1aada42b7145d581ad0321c90b04e0
          created: 1709653709756
          modified: 1712882592300
          isPrivate: false
          sortKey: -1707451042505.125
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "6b3dddb9-a6c2-4854-9487-eba92d420ce6",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 01/04/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como correr CORRER 4KM EM 4 SEMANAS.  Retorne o cronograma completo com apenas 4 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://************:5001/start
        name: BOT DEV
        meta:
          id: req_1c5bf80ba8b84089ae23afaa29d2a9dd
          created: 1710201211671
          modified: 1716241484166
          isPrivate: false
          sortKey: -1707222329194.5625
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "3ff1b8b7-acd0-4249-a9eb-9faa3205ece8",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 05/05/2024, como se fosse uma timeline, inclua tempos de treinos e distancias de como  CORRIDA 21KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]} Os dados retornados devem sempre em PT BR."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://127.0.0.1:8000/start
        name: BOT LOCAL
        meta:
          id: req_a690f1a55690405181a284392448c3db
          created: 1710255024090
          modified: 1714753200679
          isPrivate: false
          sortKey: -1707107972539.2812
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "3ff1b8b7-acd0-4249-a9eb-9faa3205ece8",
            	"receiver": "http://localhost:3334",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 05/05/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como  CORRIDA 21KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]} Os dados retornados devem sempre em PT BR."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://127.0.0.1:8000/start
        name: BOT LOCAL
        meta:
          id: req_0d252335d5f34dad8676aa8adeba1f17
          created: 1714753262318
          modified: 1714753267217
          isPrivate: false
          sortKey: -1707050794211.6406
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "3ff1b8b7-acd0-4249-a9eb-9faa3205ece8",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 05/05/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como  CORRIDA 21KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]} Os dados retornados devem sempre em PT BR."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://localhost:5001/start-long
        name: IA Big generation - LOCAL
        meta:
          id: req_bdce406d4d424046a615af782a7edcfd
          created: 1715896028838
          modified: 1715898887485
          isPrivate: false
          sortKey: -1707336685849.8438
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "b7828938-0c4d-45aa-af18-abcf6a4c534b",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 19/05/2024, como se fosse uma timeline, inclua tempos de treinos de como  CORRIDA 42KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://localhost:5001/start-long
        name: IA Big generation - LOCAL
        meta:
          id: req_a196b34506e94614a2818770d20bc0e2
          created: 1716414132053
          modified: 1716414132053
          isPrivate: false
          sortKey: -1707308096686.0234
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "b7828938-0c4d-45aa-af18-abcf6a4c534b",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 19/05/2024, como se fosse uma timeline, inclua tempos de treinos de como  CORRIDA 42KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/generate"
        name: Generate muscle mass
        meta:
          id: req_a03eebfd22b744e7abccbbf1dd4b142a
          created: 1721688922289
          modified: 1721689305178
          isPrivate: false
          sortKey: -1707970539299.625
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
            	"promptId": "0f1861f7-15ec-43f9-a789-1cc6dbeafa64",
            	"startAt": "2024-10-01T10:00:00.000Z",
            	"endAt": "2024-11-05T10:00:00.000Z",
            	"duration": "5 Semanas",
            	"title": "Ganhar massa musuclar em 5 semanas",
            	"target": "Ganhar massa musuclar em 5 semanas",
            	"currentParam": "65Kg",
            	"initialParam": "30%",
            	"finalTarget": "80Kg",
            	"finalTargetParam": "20%",
            	"totalSteps": 5,
            	"currentStep": 1,
            	"totalWeeks": 5,
            	"pendingSteps": 5,
            	"hasHealthRisk": false,
            	"acceptedTerms": true
            }
        parameters:
          - id: pair_d2bc5d1ee7154dda901fb9d3d0d0ffdc
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Profile
    meta:
      id: fld_07ef8aa5389a4ba5abcce0753e013347
      created: 1708557817257
      modified: 1708557830889
      sortKey: -1706313660554
    children:
      - url: "{{ _.baseURL }}/users/profile/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: update user profile
        meta:
          id: req_db42a38490da4437b4f5c2bb48801d0c
          created: 1708557842804
          modified: 1708559985605
          isPrivate: false
          sortKey: -1708557849234
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"birthDate": "1994-10-20T00:00:00.000Z",
            	"gender": "M",
            	"cep": "78098-282",
            	"city": "Cuiabá",
            	"state": "MT",
            	"location": "Avenida Ayrton Senna da Silva",
            	"locationNumber": "9067",
            	"locationComplement": null,
            	"weight": "65 kg",
            	"currentActivities": 0,
            	"lastCheckup": "1994-10-20T00:00:00.000Z",
            	"canOutExercises": false,
            	"menstualCycle": null,
            	"receiveMessages": true,
            	"authorizeAllChannels":true,
            	"authorizePartners":true,
            	"receiveNewsletter":true,
            	"receiveNotifications":true
            	
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
        name: get user infos
        meta:
          id: req_efdf4ca356f54a25b90a5d5d25bcf8bf
          created: 1708560673346
          modified: 1737481924521
          isPrivate: false
          sortKey: -1708557849334
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Radar
    meta:
      id: fld_c957a8f12fa74bdaa8a64187d0ba45d4
      created: 1709585023851
      modified: 1742418901550
      sortKey: -1700285410792.875
    children:
      - url: "{{ _.baseURL }}/radar"
        name: Create radar infos
        meta:
          id: req_b9e92c2bb62a40f2b20791cb7f8e7170
          created: 1709586927187
          modified: 1741020356346
          isPrivate: false
          sortKey: -1709273850627
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
            	"businessAndCareer": 7,
            	"businessAndCareerComment": null,
            	"moneyAndFinances": 5,
            	"moneyAndFinancesObservation": null,
            	"loveAndFamily": 10,
            	"loveAndFamilyObservation": null,
            	"spirituality": 6,
            	"spiritualityObservation": null,
            	"personalDeveloment": 7,
            	"personalDevelomentObservation": null,
            	"helth": 9,
            	"helthObservation": null,
            	"friendsAndSocial": 6,
            	"friendsAndSocialObservation": null,
            	"relationship": 10,
            	"relationshipObservation": null
            }
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/radar/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: Update radar by UserID
        meta:
          id: req_fd728337302c496e92a54d88f2ec0bb4
          created: 1709758981819
          modified: 1712877857488
          isPrivate: false
          sortKey: -1709196043787.25
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"businessAndCareer": 7,
            	"businessAndCareerComment": null,
            	"moneyAndFinances": 7,
            	"moneyAndFinancesObservation": null,
            	"loveAndFamily": 10,
            	"loveAndFamilyObservation": null,
            	"spirituality": 7,
            	"spiritualityObservation": null,
            	"personalDeveloment": 6,
            	"personalDevelomentObservation": null,
            	"helth": 10,
            	"helthObservation": null,
            	"friendsAndSocial": 5,
            	"friendsAndSocialObservation": null,
            	"relationship": 10,
            	"relationshipObservation": null
            }
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/radar/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
        name: get radar by user ID
        meta:
          id: req_61d555a6388e450a8d855ff6884deb81
          created: 1717350164366
          modified: 1740598783515
          isPrivate: false
          sortKey: -1709526722856.1875
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/radar/details/69397bf1-640f-4fb7-a92e-d41260f9a761"
        name: get by radar ID
        meta:
          id: req_ee74990de0cc4d01b25aa9e646ac9565
          created: 1719438196304
          modified: 1744128650326
          isPrivate: false
          sortKey: -1709516997001.2188
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Sentiment analysis
    meta:
      id: fld_55223e2b7f9d4211b2964119b7b3e9b6
      created: 1709758981817
      modified: 1709758981817
      sortKey: -1696301619995.3125
    children:
      - url: "{{ _.baseURL }}/sentiment-analysis/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: Sentiments
        meta:
          id: req_10ab00b3b66d4a8db692d020a0424c4c
          created: 1709758981818
          modified: 1712877045692
          isPrivate: false
          sortKey: -1709585077986
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis"
        name: Create sentiment register
        meta:
          id: req_3475ab3a703342d69ba2c69ff03401fb
          created: 1709758981818
          modified: 1712877815952
          isPrivate: false
          sortKey: -1709273850627
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "8c79a9ea-413c-4a6b-aa61-6c06dbc91f00",
            	"thermometer": "neutral",
            	"relatedTo": null,
            	"influencedTheEmotionalState": false
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis/history"
        name: Register Sentiment history
        meta:
          id: req_56312f1193834a88829b9951a3d34b62
          created: 1712875242854
          modified: 1713559528424
          isPrivate: false
          sortKey: -1709040430107.75
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"thermometer": "happy"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis/history"
        name: Current sentiment
        meta:
          id: req_cce3e2ecae20435b83f8e8cff4a2613e
          created: 1712875309390
          modified: 1712875510243
          isPrivate: false
          sortKey: -1709001526687.875
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: User notes
    meta:
      id: fld_d896910452ab4639b42f1e700c2509a1
      created: 1709760655275
      modified: 1709841059799
      sortKey: -1695803646145.6172
    children:
      - url: "{{ _.baseURL }}/notes"
        name: Create user notes
        meta:
          id: req_0288014eca764aae8b9e6d942cc6398b
          created: 1709760655275
          modified: 1709761142175
          isPrivate: false
          sortKey: -1709507271146.25
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "8c79a9ea-413c-4a6b-aa61-6c06dbc91f00",
            	"title": "Anotação do dia",
            	"category": "remember",
            	"descriptionNote": "Lorem ipsum dolor assit amet",
            	"cardColor": "#FFFFE3"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notes/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: get user notes
        meta:
          id: req_3af0ab23025b4674af2991a030fb87de
          created: 1709760655276
          modified: 1709933827386
          isPrivate: false
          sortKey: -1709585077986
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notes/d728c821-a05f-4b21-9eee-3dc247c4d6a6"
        name: delete note
        meta:
          id: req_c542393fec084817a47c8311c4ec76ad
          created: 1709760730969
          modified: 1709761182250
          isPrivate: false
          sortKey: -1709429464306.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Cron Job
    meta:
      id: fld_897cca4af74748a1b25739f2cb7da700
      created: 1709841042885
      modified: 1721309535012
      sortKey: -1695352357344.3308
    children:
      - url: "{{ _.baseURL }}/user-plans/check"
        name: Job review generation
        meta:
          id: req_dff1da9e2fdd4fa69ee8249725dc900f
          created: 1709841031766
          modified: 1742419289119
          isPrivate: false
          sortKey: -1709841070187
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations"
        name: Job Recommendations
        meta:
          id: req_225072f590fa4df9a1dd9af6daa0b456
          created: 1716998226583
          modified: 1717358702114
          isPrivate: false
          sortKey: -1709715487572.5
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Activities
    meta:
      id: fld_7013302713fb412f8ecfd223434e8820
      created: 1710256503330
      modified: 1710256503330
      sortKey: -1703025354669.75
    children:
      - url: "{{ _.baseURL }}/user-plans/activity"
        name: create activity
        meta:
          id: req_e8571c0931d84ac3b3c35948e130cca4
          created: 1710256503330
          modified: 1741024711186
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
                "userPlanId": "2c040eb5-e7ee-4f34-85b9-65ee5cc7299c",
                "weekId": "2c03a94a-ee96-4523-b8d7-8da8829d47d5",
                "date": "2024-03-26T16:42:08.000Z",
                "observation": "OBS: aqu vai a observação",
                "timeActivity": "tempo de atividade aqui",
                "category": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
                "activityDescription": "Teste limite de atividades"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/868e86b7-a016-4adc-a117-57af5c12475e"
        name: get activity
        meta:
          id: req_971590cde4e64cc39ddd2744ffff08de
          created: 1710256503331
          modified: 1711478411604
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/d83de865-18e5-4fe9-945b-bc199aeaf8fb"
        name: Update activity
        meta:
          id: req_f7fa223b3930452ba38cbc2716159dca
          created: 1710372628346
          modified: 1710809303342
          isPrivate: false
          sortKey: -1706313459550
        method: PUT
        body:
          mimeType: application/json
          text: |-
            {
            	"activityStatus": "loading",
            	"category": "Corrida"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/868e86b7-a016-4adc-a117-57af5c12475e"
        name: Delete activity
        meta:
          id: req_cef0b8777abc4ce9a4db5226069ab485
          created: 1711400344617
          modified: 1711481295062
          isPrivate: false
          sortKey: -1706170941284.25
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Recommendations
    meta:
      id: fld_68b0397f8715413e8e99645b3159e665
      created: 1716400864914
      modified: 1717532212657
      sortKey: -1698293515394.0938
    children:
      - url: "{{ _.baseURL }}/recommendations/7c9d78d6-a38e-4c01-90da-50e2d08db6bd"
        name: Get recommendation by radarID
        meta:
          id: req_fd41344ae3a04b5d9b5e695d8f97cecc
          created: 1716400864915
          modified: 1719418610955
          isPrivate: false
          sortKey: -1709585077986
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/new/radar"
        name: Save generated radar recommendations
        meta:
          id: req_2aeb406afc4a43bc8453807bc1a4bdb7
          created: 1716420448241
          modified: 1721324256072
          isPrivate: false
          sortKey: -1709215495497.1875
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
               "userId":"b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
               "radarId":"91f1ebd7-ba87-44d2-a5a8-db7e47d4e837",
               "recommendations":[
                  {
                     "subject":"Negócios e Carreira",
                     "note":5,
                     "comment":"",
                     "analysis":"Sua pontuação em negócios e carreira indica que há aspectos significativos a serem melhorados. Talvez você esteja enfrentando desafios que impedem seu progresso profissional ou sinta que não está alcançando seu potencial completo.",
                     "suggestions":[
                        {
                           "title":"Definição de metas claras",
                           "description":"Estabeleça metas de curto, médio e longo prazo para sua carreira. Isso inclui metas específicas, mensuráveis, alcançáveis, relevantes e temporais (SMART). Revise-as periodicamente e ajuste conforme necessário para manter seu foco e motivação."
                        },
                        {
                           "title":"Desenvolvimento de habilidades",
                           "description":"Identifique as habilidades que são mais valorizadas em sua área e dedique-se a desenvolvê-las. Isso pode incluir participar de cursos, workshops ou até mesmo buscar um mentor que possa guiá-lo em sua jornada profissional."
                        },
                        {
                           "title":"Networking",
                           "description":"Expandir sua rede de contatos pode abrir portas para novas oportunidades de carreira. Participe de eventos da indústria, seminários e outras atividades onde você possa conhecer e interagir com profissionais do seu campo."
                        }
                     ]
                  },
                  {
                     "subject":"Dinheiro e Finanças",
                     "note":7,
                     "comment":"",
                     "analysis":"Sua pontuação em dinheiro e finanças é boa, mas há espaço para melhorias. A gestão financeira eficaz é crucial para a segurança e o crescimento pessoal.",
                     "suggestions":[
                        {
                           "title":"Orçamento pessoal",
                           "description":"Elabore um orçamento detalhado que contemple todos os seus gastos. Isso ajudará a entender para onde seu dinheiro está indo e onde você pode cortar despesas."
                        },
                        {
                           "title":"Investimento",
                           "description":"Explore diferentes formas de investimento que se alinhem com seus objetivos financeiros e seu perfil de risco. Isso pode incluir ações, fundos de investimento, imóveis, entre outros."
                        },
                        {
                           "title":"Educação financeira",
                           "description":"Invista tempo em aprender sobre finanças pessoais. Existem muitos recursos disponíveis online, como cursos, blogs, podcasts e livros que podem ajudá-lo a tomar decisões financeiras mais informadas."
                        }
                     ]
                  },
                  {
                     "subject":"Espiritualidade",
                     "note":6,
                     "comment":"",
                     "analysis":"Sua pontuação em espiritualidade sugere que há uma necessidade de maior conexão com suas crenças ou práticas espirituais. Isso pode ser um componente importante para o seu bem-estar geral.",
                     "suggestions":[
                        {
                           "title":"Exploração espiritual",
                           "description":"Dedique tempo para explorar e entender suas crenças espirituais. Isso pode envolver leitura, meditação, ou participação em grupos de discussão ou serviços espirituais."
                        },
                        {
                           "title":"Prática regular",
                           "description":"Estabeleça uma prática espiritual regular. Isso pode incluir meditação diária, oração, yoga ou outras práticas que ajudem a cultivar sua vida espiritual."
                        },
                        {
                           "title":"Retiros espirituais",
                           "description":"Considere participar de um retiro espiritual. Isso pode proporcionar uma oportunidade de imersão em suas práticas espirituais e oferecer uma nova perspectiva sobre sua jornada espiritual."
                        }
                     ]
                  }
               ],
               "finalConsideration":"Lucas, analisando as suas pontuações, você mostra forte desempenho em algumas áreas vitais como amor e família, saúde e relacionamentos, indicando um equilíbrio saudável em sua vida pessoal. No entanto, há áreas como negócios e carreira, e espiritualidade onde você pode se beneficiar significativamente de um investimento focado. As sugestões fornecidas visam ajudá-lo a fortalecer essas áreas e potencializar seu desenvolvimento pessoal e profissional. Continue se esforçando e explorando novas oportunidades de crescimento. Sucesso!"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: true
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/productivity"
        name: Save productivity Tip
        meta:
          id: req_0fe8de472afd47e59dbf43d177449d15
          created: 1717529247366
          modified: 1722445081871
          isPrivate: false
          sortKey: -1709198475100.9922
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"description": "Defina 3 tarefas urgentes e importantes diariamente, e priorize-as em ordem de importância. Retire os problemas mais pequenos da cabeça e foque em solucionar as grandes questões. - Don Tolle"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: true
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/tipProductivity"
        name: Get Random Productivity Recommendation
        meta:
          id: req_8773e6d1968f44b9b4e648037202b982
          created: 1717529282194
          modified: 1717534150819
          isPrivate: false
          sortKey: -1709200906714.7344
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: true
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/new/plan"
        name: Save generated plan recommendation
        meta:
          id: req_37b23610e3024d668cfde7c6d20f3118
          created: 1717780347183
          modified: 1719426322081
          isPrivate: false
          sortKey: -1709198475200.9922
        method: POST
        body:
          mimeType: application/json
          text: "{'planId': '7d4c0106-7012-4ee3-917d-47cbfd30adaf', 'userId':
            'd4ba143e-9849-42e3-8290-65886faf508e', 'recommendations':
            [{'title': 'Mantenha-se consistente', 'description': 'Para atingir
            seu objetivo de correr 1KM em 4 semanas, é fundamental manter a
            consistência nos treinos.'}, {'title': 'Aqueça antes de correr',
            'description': 'Realize um bom aquecimento antes de cada corrida
            para evitar lesões e melhorar seu desempenho.'}, {'title': 'Varie a
            intensidade', 'description': 'Incorpore corridas em diferentes
            intensidades para desenvolver resistência e velocidade.'}, {'title':
            'Hidrate-se adequadamente', 'description': 'Não se esqueça de beber
            água antes, durante e depois dos treinos para manter seu corpo
            hidratado.'}, {'title': 'Descanse para se recuperar', 'description':
            'O descanso é tão importante quanto o treino. Respeite os dias de
            recuperação para fortalecer os músculos.'}, {'title': 'Monitore seu
            progresso', 'description': 'Mantenha um registro do seu desempenho a
            cada corrida para acompanhar sua evolução ao longo das 4 semanas.'},
            {'title': 'Encontre um parceiro de corrida', 'description': 'Correr
            com um companheiro pode ser motivador e ajudar no cumprimento dos
            treinos e metas.'}, {'title': 'Estabeleça novos desafios',
            'description': 'Ao final das 4 semanas, defina um novo objetivo para
            continuar mantendo sua motivação em alta.'}, {'title': 'Cuide da sua
            alimentação', 'description': 'Uma dieta equilibrada auxilia no
            desempenho esportivo e na recuperação após os treinos.'}, {'title':
            'Aproveite o processo', 'description': 'Divirta-se durante as
            corridas e aproveite o momento para sentir os benefícios que o
            exercício físico traz para o corpo e a mente.'}]}"
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL
          }}/recommendations/verify/7d4c0106-7012-4ee3-917d-47cbfd30adaf"
        name: Verify recommendations exists and include ID
        meta:
          id: req_5c9a70eedd53429a9c52459260f0ec3e
          created: 1719417821595
          modified: 1719421828168
          isPrivate: false
          sortKey: -1709198475000.9922
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: true
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: GENERATIVE_AI_DEV
    meta:
      id: fld_019662a51bae4702a7fbca7726a5a90d
      created: 1716414087856
      modified: 1718222429943
      sortKey: -1695348466923.63
    children:
      - url: "{{ _.botURL }}/start-plan"
        name: Plan Generator_DEV
        meta:
          id: req_20a8c8f8d2754d3eafc2494595edd09e
          created: 1717002871896
          modified: 1717279839171
          isPrivate: false
          sortKey: -1715766764299.375
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "b7828938-0c4d-45aa-af18-abcf6a4c534b",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Crie um cronograma para CORRER 2KM em 4 SEMANAS iniciando no dia 01/05/2024"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.botURL }}/start-radar-recommendation"
        name: Radar Recommendations_DEV
        meta:
          id: req_0fe9667788f7475c8926aa112419f248
          created: 1717002878517
          modified: 1718227235044
          isPrivate: false
          sortKey: -1712529847006.25
        method: POST
        body:
          mimeType: application/json
          text: >
            {
              "receiver": "https://api.dev.planai.com.br",
              "prompt": "{\"radarId\": \"7c9d78d6-a38e-4c01-90da-50e2d08db6bd\", \"userId\": \"d4ba143e-9849-42e3-8290-65886faf508e\", \"username\": \"Lucas\", \"businessAndCareer\": 8, \"businessAndCareerObservation\": null, \"moneyAndFinances\": 5, \"moneyAndFinancesObservation\": \"Preciso ganhar mais, e novas fontes de renda passiva\", \"loveAndFamily\": 10, \"loveAndFamilyObservation\": null, \"spirituality\": 7, \"spiritualityObservation\": \"Preciso ir mais a igreja\", \"personalDeveloment\": 9, \"personalDevelomentObservation\": \"Estou me aventurando e me desafiando com projetos complexos e inovadores com IA\", \"helth\": 9, \"helthObservation\": \"Estou bem mas preciso fazer exercícios físicos\", \"relationship\": 10, \"relationshipObservation\": null, \"friendsAndSocial\": 7, \"friendsAndSocialObservation\": \"Tenho bons amigos, mas faz um tempo que não vejo\", \"isCompleted\": true, \"isCompletedRecommendation\": false, \"createdAt\": \"2024-06-12T21:01:20.000Z\", \"updatedAt\": \"2024-06-12T21:03:22.000Z\"}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Network services
    meta:
      id: fld_4b097ba81eaa46fca23e0c9219c63c42
      created: 1717713686715
      modified: 1744639752105
      sortKey: -1708566195216.75
    children:
      - url: "{{ _.baseURL }}/auth/request-password-reset"
        name: Reset password
        meta:
          id: req_ad7b905bb93143858aff8f2d72224e48
          created: 1738699632301
          modified: 1742494260269
          isPrivate: false
          sortKey: -1716496868849.3594
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "email": "<EMAIL>"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/send-email"
        name: Send manual email types
        meta:
          id: req_d88e6d3b4c0b462cba93d5b4f13712e0
          created: 1741613020598
          modified: 1742494255109
          isPrivate: false
          sortKey: -1716740237032.6875
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"type": "welcome",
            	"email": "<EMAIL>",
            	"name": "Lucas Rodrigues"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.2.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/send-email"
        name: Send welcome by email
        meta:
          id: req_09bd6661881241d08433983ca9a256a7
          created: 1744723522455
          modified: 1744723527498
          isPrivate: false
          sortKey: -1716618552941.0234
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"type": "welcome-by-email",
            	"email": "<EMAIL>",
            	"name": "Lucas Rodrigues"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.2.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/batch"
        name: Send batch all
        meta:
          id: req_8d96788eb9824f43b554ea8f4d5fe8a8
          created: 1744737073872
          modified: 1744744471649
          isPrivate: false
          sortKey: -1716131816574.3672
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "templateType": "welcome",
              "recipientsType": "all"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/batch"
        name: Send batch by group ID
        meta:
          id: req_60cf4e1ded2e42aeaabfe98edaec86bf
          created: 1744737215479
          modified: 1744745805670
          isPrivate: false
          sortKey: -1715949290436.871
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"templateType": "welcome",
            	"recipientsType": "group",
            	"groupType": "c15da216-70d5-494b-a454-d4d5abc7dbb6"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/batch"
        name: Send specifc
        meta:
          id: req_805db0c60eaf43f4996646d1a0d3a485
          created: 1744737248065
          modified: 1744745611977
          isPrivate: false
          sortKey: -1716040553505.6191
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"templateType": "welcome",
            	"recipientsType": "specific",
            	"emails": [
            		"<EMAIL>",
            		"<EMAIL>",
            		"<EMAIL>"
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: GENERATIVE_AI_LOCAL
    meta:
      id: fld_f950a79d50114f3481bbc730b222c5f7
      created: 1718222397357
      modified: 1740585449288
      sortKey: -1695347494318.4546
    children:
      - url: "{{ _.botURL }}/plan/252eb90a-858b-4720-bae5-086fcfa5e8dc"
        name: Plan Generator
        meta:
          id: req_b95cb69bb61d46f0aea2a37887e776d6
          created: 1715898893646
          modified: 1744388642088
          isPrivate: false
          sortKey: -1718222447547
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"receiver": "https://api.dev.planify.app.br"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.botURL }}/radar-recommendation/69397bf1-640f-4fb7-a92e-d41260f9a761"
        name: Radar Recomendator
        meta:
          id: req_8151bd5b6ff44e47bb1ac42483116e97
          created: 1744127793195
          modified: 1744134266157
          isPrivate: false
          sortKey: -1718222447447
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"receiver": "https://api.dev.planify.app.br"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.botURL }}/plan-recommendation/4beb1c74-ee18-45cc-b9d6-26344b5a3670"
        name: Plan Recomendator
        meta:
          id: req_6f56916e96234bbcb351834a75eae7af
          created: 1744138479141
          modified: 1744138917425
          isPrivate: false
          sortKey: -1718222447497
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"receiver": "https://api.dev.planify.app.br"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: RECOMMENDATION_SERVICES_LOCAL
    meta:
      id: fld_063507f9600546988a8d57829eddfd0f
      created: 1718222521267
      modified: 1734989450742
      sortKey: -1695347251167.161
    children:
      - url: "{{ _.baseURL
          }}/recommendations/new/generate-plan/4beb1c74-ee18-45cc-b9d6-26344b5a\
          3670"
        name: Generate plan recommendation by ID
        meta:
          id: req_d6abb89fab5241a382166d5f4ce03880
          created: 1717776282921
          modified: 1744043830146
          isPrivate: false
          sortKey: -1718222568631
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL
          }}/recommendations/new/generate-plan/3e6bf6ac-8228-4993-8cd9-d5c846b6\
          fc45"
        name: Generate radar recommendation by ID
        meta:
          id: req_fdbe089afe8344c08461bee7fb79cb67
          created: 1718223414453
          modified: 1718223414453
          isPrivate: false
          sortKey: -1718222508089
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Notifications
    meta:
      id: fld_f5aacc4e647a496a9c534d882fee59bc
      created: 1719362687483
      modified: 1733265176330
      sortKey: -1695347190379.3374
    children:
      - url: "{{ _.baseURL }}/notifications"
        name: Create Notification
        meta:
          id: req_145e3002d0754b2ea6924c1a20b92efd
          created: 1719363968817
          modified: 1747183573001
          isPrivate: false
          sortKey: -1719362720409
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userId": "4ce4f44e-54b1-4dfd-8869-c29156ce0ed8",
            	"title": "Objetivo gerado",
            	"description": "Lucas, plano para Correr 1KM em 4 semanas foi gerado com sucesso!",
            	"notificationStatus": "success"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications/f831f085-44af-43b0-be46-ce4ceb277d2a"
        name: Read Notification
        meta:
          id: req_466d964c24e04803ad9307b355c130f3
          created: 1719364600641
          modified: 1747185176414
          isPrivate: false
          sortKey: -1719362720359
        method: PATCH
        headers:
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications"
        name: Create Notification custom
        meta:
          id: req_b842ea5d4e4f4ac797703e825b8cd89b
          created: 1722905648544
          modified: 1747150488423
          isPrivate: false
          sortKey: -1719362720384
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userId": "4ce4f44e-54b1-4dfd-8869-c29156ce0ed8",
            	"title": "Objetivo gerado",
            	"description": "Lucas, plano para Correr 1KM em 4 semanas foi gerado com sucesso!",
            	"targetId": "30cbbf37-842b-4f70-a6ca-2545a223f01f",
            	"notificationStatus": "success"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications"
        name: Create Notification Type Radar
        meta:
          id: req_a45279a78d3a4dc6b0e917caa5f504cb
          created: 1731583487152
          modified: 1747183601370
          isPrivate: false
          sortKey: -1719362720396.5
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "4ce4f44e-54b1-4dfd-8869-c29156ce0ed8",
            	"title": "Radar notificação",
            	"description": "Teste radar notification",
            	"notificationStatus": "created",
            	"type": "radar"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications/4ce4f44e-54b1-4dfd-8869-c29156ce0ed8"
        name: Get User Notifications By UserID (SSE)
        meta:
          id: req_3a5ff12ebc844211b57a1b96082e377a
          created: 1747149951704
          modified: 1747183623117
          isPrivate: false
          sortKey: -1719362720209
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.1.0
          - name: Accept
            value: text/event-stream
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Permissions
    meta:
      id: fld_66d629a327e54d65b294a679be2f1624
      created: 1733265160347
      modified: 1733789091151
      sortKey: -1695347186580.0984
    children:
      - name: Payment plans
        meta:
          id: fld_82345e081c414a4c90438a6505a14a86
          created: 1710350813045
          modified: 1737506959951
          sortKey: -1733265168874
        children:
          - url: "{{ _.baseURL }}/payment-plans"
            name: Create payment plan
            meta:
              id: req_8ddcdf2d04f54f6794eb3da006b15760
              created: 1710350813046
              modified: 1737998947937
              isPrivate: false
              sortKey: -1709507271146.25
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Plano Gratuíto",
                	"description": "Plano Free e sem vínculo com Pagarme.me",
                	"status": "active",
                	"aiObjectivesPeriod": "year",
                	"aiObjectivesLimit": 50,
                	"manualObjectivesPeriod": "year",
                	"manualObjectivesLimit": 100,
                	"radarLimitPeriod": "year",
                	"radarLimit": 1,
                	"allowTasksControl": true,
                	"pendingTasksLimit": 15,
                	"allowNotes": true,
                	"allowEmotionalAnalysis": false,
                	"emotionalAnalysisPeriod": "year",
                	"emotionalAnalysisLimit": 1,
                	"price": "free",
                	"typePlan": "free",
                	"recurrencyType": "month",
                	"recurrencyPeriod": 12,
                	"recurrencyInstallments": 1,
                	"allowNetworking": false,
                	"allowMentoredUserAnalytics": false
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans"
            name: List payment plan
            meta:
              id: req_34185e2a96fb4dc6ac3e0f7c46f87391
              created: 1710350813047
              modified: 1737668697883
              isPrivate: false
              sortKey: -1709585077986
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              disabled: true
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/f2974eaa-3572-4aae-bd9c-c43c12e8b789"
            name: Delete payment plan
            meta:
              id: req_ab6605c8b514427b89d70d475ef79618
              created: 1710350813047
              modified: 1743193884321
              isPrivate: false
              sortKey: -1709429464306.5
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/2a381d88-7d4a-4bc1-90d4-9472b52ff594"
            name: Update payment plan
            meta:
              id: req_4ba93126ac84496f930a178d2c0a7f45
              created: 1710353334082
              modified: 1741828143906
              isPrivate: false
              sortKey: -1709468367726.375
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Plano Teste",
                	"status": "active",
                	"description": "Plano Teste",
                	"assignDescription": "",
                	"price": "",
                	"typePlan": "free",
                	"promotionalPrice": null,
                	"recurrencyType": "month",
                	"recurrencyPeriod": 1,
                	"recurrencyInstallments": 1,
                	"installments": [
                		1
                	],
                	"statementDescriptor": "PLANFREEM",
                	"allowTasksControl": false,
                	"allowNotes": false,
                	"allowNetworking": false,
                	"allowMentoredUserAnalytics": false,
                	"allowEmotionalAnalysis": false,
                	"pendingTasksLimit": 0,
                	"aiObjectivesPeriod": "year",
                	"aiObjectivesLimit": 0,
                	"aiPlanTasksLimits": 50,
                	"manualObjectivesPeriod": "year",
                	"manualObjectivesLimit": 0,
                	"manualPlanTasksLimits": 0,
                	"radarLimitPeriod": "year",
                	"radarLimit": 0,
                	"emotionalAnalysisPeriod": "year",
                	"emotionalAnalysisLimit": 0,
                	"scopes": [
                		"9d93e17f-097f-4aa1-af5a-5d148e974029",
                		"e4e9e5d8-d993-451f-9fd3-f76e138c1cb0",
                		"2597a067-a9b7-4d9f-a9f4-f0ed30e1437f",
                		"979ddc68-86f5-46a9-b87b-a00ab529103f",
                		"9c663e8f-63b9-4cdf-9622-5c278820969f",
                		"14340df9-e64d-477d-981e-81e2e041b39e",
                		"89c48b2b-cfa9-4ca9-94de-a9911731bd02",
                		"f05bacb1-932a-4836-bdb5-3f8bfd1c5ad6",
                		"efaae853-47b8-4a65-9ac1-752c8986d0d0",
                		"3f723e82-d67f-46b1-925b-01fc4c620bb2",
                		"b640e5e1-9e75-4e41-925d-4a10011cc838",
                		"1962d18e-2a81-4107-b23c-fe54f387ee68",
                		"bf3ee4bf-212c-4e0f-8089-bc6c70618a40",
                		"47bf918a-637d-45ef-bc4e-f09fef548571",
                		"da4eeaf0-55af-4759-8006-22980f625c64",
                		"97d9b5d2-8c39-4e1d-8eb7-6afebb4ce88c",
                		"084e5d03-3489-4321-9db9-863474a5e387",
                		"6adccbd7-ecbb-4cc3-8e0b-a3b1d2889e39",
                		"be364901-9d00-46ac-96e8-e4348eee89f0"
                	],
                	"permissionDescription": "Plano Teste",
                	"paymentMethods": [
                		"credit_card"
                	],
                	"displayOrder": 12
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/order/718c6c6e-d4aa-4a55-930f-1d3985ee0825"
            name: Reoder payment plans
            meta:
              id: req_25d1cb6fb9f44e3681141c899bab8c47
              created: 1738005240975
              modified: 1738005264205
              isPrivate: false
              sortKey: -1709448916016.4375
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"order": 1
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans?period=year"
            name: List payment plan filter
            meta:
              id: req_3da1046a1f434e37adfa260ab6f71310
              created: 1738072331855
              modified: 1738949764176
              isPrivate: false
              sortKey: -1709555900421.0938
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              disabled: true
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/6deabef5-3ab5-4b81-a6da-270723976c05"
            name: Payment plan details
            meta:
              id: req_4fcdd53da536410f86efc529019c0d0a
              created: 1741828182998
              modified: 1744894680566
              isPrivate: false
              sortKey: -1709570489203.5469
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              disabled: true
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/reply/e3c73bdb-43c3-4bfc-9cc9-56d91325ce8e"
            name: Replicate plan
            meta:
              id: req_0bed581d8f7c4f7e82462162f2d54488
              created: 1743193265257
              modified: 1743193294905
              isPrivate: false
              sortKey: -1709497545291.2812
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Scopes
        meta:
          id: fld_a462e04ff2334e27b537c712977cf08e
          created: 1733265151596
          modified: 1733265168835
          sortKey: -1733265168774
        children:
          - url: "{{ _.baseURL }}/scopes"
            name: create scope
            meta:
              id: req_6633cec354ee42159f1b90d9c12601f9
              created: 1733265151596
              modified: 1734984597364
              isPrivate: false
              sortKey: -1706313296790
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Solicitar análise de emoções",
                	"tag": "radar.write"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/scopes"
            name: get scopes
            meta:
              id: req_6c807a7640ee4941a4d41e11776f57ac
              created: 1733265151597
              modified: 1738068229623
              isPrivate: false
              sortKey: -1706313497110
            method: GET
            parameters:
              - name: amount
                value: "10"
                id: pair_d38d22808a624cc68867b2a21b2b560b
              - name: skip
                value: "0"
                id: pair_771b96266c9540cdb96e94a30bf9e5ba
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              disabled: false
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/scopes/be364901-9d00-46ac-96e8-e4348eee89f0"
            name: Update scope
            meta:
              id: req_1e8c5316d1a44a6e9159f1628f3752a1
              created: 1733265151598
              modified: 1738069208939
              isPrivate: false
              sortKey: -1706313459550
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Criar plano com IA",
                	"tag": "aiPlan.write"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/scopes/868e86b7-a016-4adc-a117-57af5c12475e"
            name: Delete scope
            meta:
              id: req_aabd279231b14773ac57aa48b223591d
              created: 1733265151598
              modified: 1733265249863
              isPrivate: false
              sortKey: -1706170941284.25
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Group payment permissions
        meta:
          id: fld_e82b2db3ae334fd0b882eb50f3c4daad
          created: 1733268475312
          modified: 1737506989207
          sortKey: -1732418729975.625
        children:
          - url: "{{ _.baseURL }}/permissions"
            name: create permissions
            meta:
              id: req_023f693782584426b98533c53e47fa09
              created: 1733268475313
              modified: 1734973481335
              isPrivate: false
              sortKey: -1706313296790
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"description": "Permissões para o plano free",
                	"paymentPlanId": "e19622ba-930f-4f29-acdc-fad78983546e",
                	"scopes": ["be364901-9d00-46ac-96e8-e4348eee89f0", "e4e9e5d8-d993-451f-9fd3-f76e138c1cb0"]
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions"
            name: get permissions
            meta:
              id: req_5ee86488e3924758bd0de3327af6b383
              created: 1733268475314
              modified: 1737994265258
              isPrivate: false
              sortKey: -1706313497110
            method: GET
            body:
              mimeType: multipart/form-data
            parameters:
              - name: amount
                value: "10"
                id: pair_d38d22808a624cc68867b2a21b2b560b
              - name: skip
                value: "0"
                id: pair_771b96266c9540cdb96e94a30bf9e5ba
            headers:
              - name: Content-Type
                value: multipart/form-data
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              disabled: false
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions/d83de865-18e5-4fe9-945b-bc199aeaf8fb"
            name: Update permissions
            meta:
              id: req_e2ec833a33fc415c90a513e6e46e0bbf
              created: 1733268475314
              modified: 1733268504054
              isPrivate: false
              sortKey: -1706313459550
            method: PUT
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Criar Radar planify",
                	"tag": "radar.create"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions/868e86b7-a016-4adc-a117-57af5c12475e"
            name: Delete permissions
            meta:
              id: req_bfdf487a6ac948af83dbb9c3cf2527f4
              created: 1733268475315
              modified: 1733268515012
              isPrivate: false
              sortKey: -1706170941284.25
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions/3e7c064f-6ce1-49b4-8932-3967a1da34a8"
            name: find one
            meta:
              id: req_4f5e0130b4aa47b7bf76dfdb07abda74
              created: 1740589171249
              modified: 1741828528682
              isPrivate: false
              sortKey: -1706313492415
            method: GET
            body:
              mimeType: multipart/form-data
            parameters:
              - name: amount
                value: "10"
                id: pair_d38d22808a624cc68867b2a21b2b560b
              - name: skip
                value: "0"
                id: pair_771b96266c9540cdb96e94a30bf9e5ba
            headers:
              - name: Content-Type
                value: multipart/form-data
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              disabled: false
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Users Payment
        meta:
          id: fld_7433eb850b0248b4b16ba3773434c433
          created: 1737506973323
          modified: 1740682051383
          sortKey: -1732207120276.0312
        children:
          - url: "{{ _.baseURL }}/payments"
            name: Create payment plan
            meta:
              id: req_0481586769d34347a1e3ceea074263e7
              created: 1737506973324
              modified: 1737549687873
              isPrivate: false
              sortKey: -1709507271146.25
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
                	"paymentPlanId": "1fab9996-16c0-49f8-a044-6716eb1bd845"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payments?page=1&limit=5"
            name: get payments
            meta:
              id: req_2104f51fc5474e9bbc138034cd6fe8fe
              created: 1739574134295
              modified: 1740682185257
              isPrivate: false
              sortKey: -1709487819436.3125
            method: GET
            body:
              mimeType: application/json
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payments?status=canceled"
            name: get payments status
            meta:
              id: req_d97cadd90c0e494ab58f56625959069b
              created: 1739986069889
              modified: 1740597383701
              isPrivate: false
              sortKey: -1709478093581.3438
            method: GET
            body:
              mimeType: application/json
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Validate user permissions limit
        meta:
          id: fld_249397b911504f83ac038845572c0ddb
          created: 1740585459959
          modified: 1740593618075
          sortKey: -1731995510576.4375
        children:
          - url: "{{ _.baseURL }}/user-permissions/limits/radar"
            name: Validate user radar limit
            meta:
              id: req_5b7b004a2fdf4ff2913e170e34e0cbd6
              created: 1740585493233
              modified: 1740593687795
              isPrivate: false
              sortKey: -1740585493233
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits/manual-objectives"
            name: Validate manual plans limit
            meta:
              id: req_a37503439bc446eabe9d6e1267ce1586
              created: 1740593859489
              modified: 1740593867602
              isPrivate: false
              sortKey: -1740493413470
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits/ai-objectives"
            name: Validate ai plans limit
            meta:
              id: req_01410c51199b49f39382eec79faa5382
              created: 1740593883049
              modified: 1740593891692
              isPrivate: false
              sortKey: -1740447373588.5
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits/pending-tasks"
            name: Validate pending tasks limit
            meta:
              id: req_43cf66780cd549e5a88f7aa2d4496489
              created: 1740593908799
              modified: 1740597854410
              isPrivate: false
              sortKey: -1740424353647.75
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits"
            name: Get user limits
            meta:
              id: req_d0d3f6e4379545d8b174f97de247e2ff
              created: 1740594041607
              modified: 1741789535167
              isPrivate: false
              sortKey: -1740585493333
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
  - name: Achievements
    meta:
      id: fld_20de3dd9a21743929b0c33053fdbafbd
      created: 1733789066443
      modified: 1737995252696
      sortKey: -1695347186461.372
    children:
      - url: "{{ _.baseURL }}/achievements"
        name: create achievement
        meta:
          id: req_fc9ad293af524fb9a4cc9466b8d8af5f
          created: 1733788694001
          modified: 1733850184392
          isPrivate: false
          sortKey: -1733789078273
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Testes",
            	"description": "Teste o 1º OBJETIVO",
            	"points": 500,
            	"image": "CreateFiftdsadsadsadhObjective",
            	"position": 1,
            	"expirationPeriod": "24",
            	"badgeCode": "BC1O01"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/achievements"
        name: get achievement
        meta:
          id: req_1cb4f086ae2e44a5954458b817353ef2
          created: 1733788748702
          modified: 1733789084998
          isPrivate: false
          sortKey: -1733789078373
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/achievementRecords/getPointsHistory"
        name: get user points history
        meta:
          id: req_18c9f710344042328180de6bc29ccc85
          created: 1740678091424
          modified: 1740678114646
          isPrivate: false
          sortKey: -1733789078173
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Files
    meta:
      id: fld_ad2de83b4f6e4c458270e81f8587d661
      created: 1736187512869
      modified: 1737995250023
      sortKey: -1695347186342.646
    children:
      - url: "{{ _.baseURL }}/files"
        name: Upload file
        meta:
          id: req_986f71f2146d40a49c04a8b5d9f10340
          created: 1736187526747
          modified: 1743459832252
          isPrivate: false
          sortKey: -1736187530732
        method: POST
        body:
          mimeType: multipart/form-data
          params:
            - name: file
              disabled: false
              id: pair_a43fe3d450324011a4445605919d103c
              fileName: /Users/<USER>/Desktop/test-icon.png
              type: file
            - name: type
              value: avatar
              disabled: true
              id: pair_01b97ddc1e6244ef980501079930fe27
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/files"
        name: Get user files
        meta:
          id: req_0d67701b6c804d7d9b65c1775a4b18d4
          created: 1736187744556
          modified: 1736188068497
          isPrivate: false
          sortKey: -1736187530832
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/files?type=avatar"
        name: Get user avatar
        meta:
          id: req_83e2398c0a404a1aaef1621aa9e700d3
          created: 1736189136516
          modified: 1736189177495
          isPrivate: false
          sortKey: -1736187530782
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/files/5d4a8199-aa49-479c-bc43-6b3ab00e30a2"
        name: Delete file
        meta:
          id: req_753bed0fa0a944f3bcf04ce1c51f5c41
          created: 1736189193177
          modified: 1736189572381
          isPrivate: false
          sortKey: -1734988304552.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Admin - APP
    meta:
      id: fld_33ed26a2402a48a186de510447023f5d
      created: 1737995230197
      modified: 1737995242927
      sortKey: -1695347186105.1934
    children:
      - url: "{{ _.baseURL }}/payment-plans/setup"
        name: Create payment Plan Setup
        meta:
          id: req_7460ab6031d848a2937743c052ebcef8
          created: 1737995263551
          modified: 1739560852795
          isPrivate: false
          sortKey: -1737995263551
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
              "name": "Plano ultimate + mentor",
              "description": "Plano anual ultimate + mentor",
              "price": "865.00",
              "typePlan": "ultimate_mentor",
              "recurrencyType": "month",
              "recurrencyPeriod": 12,
              "recurrencyInstallments": 1,
              "aiObjectivesPeriod": "year",
              "aiObjectivesLimit": 50,
              "manualObjectivesPeriod": "year",
              "manualObjectivesLimit": 100,
              "radarLimitPeriod": "year",
              "radarLimit": 1,
              "allowTasksControl": true,
              "pendingTasksLimit": 15,
              "allowNotes": true,
              "allowEmotionalAnalysis": true,
              "emotionalAnalysisPeriod": "year",
              "emotionalAnalysisLimit": 1,
              "allowNetworking": true,
              "allowMentoredUserAnalytics": true,
              "permissionDescription": "Permissões do plano anual ultimate + mentor",
              "scopes": [
                "be364901-9d00-46ac-96e8-e4348eee89f0",
                "e4e9e5d8-d993-451f-9fd3-f76e138c1cb0"
              ],
              "installments": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
              "paymentMethods": ["credit_card"],
              "statementDescriptor": "PLANSTANDARDANUAL"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/setup"
        name: Create payment Plan Setup - FRONT
        meta:
          id: req_019fe56a6a894e64be9d2254673b4ab7
          created: 1737999914306
          modified: 1738003868988
          isPrivate: false
          sortKey: -1737107542148.375
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Plano ultimate + mentor",
            	"description": "Plano mensal ultimate + mentor",
            	"price": "685.00",
            	"typePlan": "ultimate_mentor",
            	"recurrencyType": "year",
            	"recurrencyPeriod": 1,
            	"recurrencyInstallments": 12,
            	"aiObjectivesPeriod": "year",
            	"aiObjectivesLimit": null,
            	"manualObjectivesPeriod": "year",
            	"manualObjectivesLimit": null,
            	"radarLimitPeriod": "3 months",
            	"radarLimit": null,
            	"allowTasksControl": true,
            	"pendingTasksLimit": 15,
            	"allowNotes": true,
            	"allowEmotionalAnalysis": true,
            	"emotionalAnalysisPeriod": "year",
            	"emotionalAnalysisLimit": 1,
            	"allowNetworking": true,
            	"allowMentoredUserAnalytics": true,
            	"permissionDescription": "Plano mensal ultimate + mentor",
            	"scopes": [
            		"084e5d03-3489-4321-9db9-863474a5e387",
            		"1962d18e-2a81-4107-b23c-fe54f387ee68",
            		"3f723e82-d67f-46b1-925b-01fc4c620bb2",
            		"47bf918a-637d-45ef-bc4e-f09fef548571",
            		"6adccbd7-ecbb-4cc3-8e0b-a3b1d2889e39",
            		"97d9b5d2-8c39-4e1d-8eb7-6afebb4ce88c",
            		"9d93e17f-097f-4aa1-af5a-5d148e974029",
            		"b640e5e1-9e75-4e41-925d-4a10011cc838",
            		"be364901-9d00-46ac-96e8-e4348eee89f0",
            		"bf3ee4bf-212c-4e0f-8089-bc6c70618a40",
            		"da4eeaf0-55af-4759-8006-22980f625c64",
            		"e4e9e5d8-d993-451f-9fd3-f76e138c1cb0",
            		"efaae853-47b8-4a65-9ac1-752c8986d0d0"
            	],
            	"installments": [
            		1,
            		2,
            		3,
            		4,
            		5,
            		6,
            		7,
            		8,
            		9,
            		10,
            		11,
            		12
            	],
            	"paymentMethods": [
            		"credit_card"
            	],
            	"statementDescriptor": "PLANULTIMATE_MENT"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payments"
        name: Get subscriptions list
        meta:
          id: req_d913ea1ab0e94ff8af595468efe5ac49
          created: 1738070901887
          modified: 1738071028035
          isPrivate: false
          sortKey: -1737107542048.375
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payments?status=canceled"
        name: Get failed subscriptions
        meta:
          id: req_5c8f38a6caa24bb89b7fb64797f5ff42
          created: 1738154811225
          modified: 1739900574573
          isPrivate: false
          sortKey: -1736663681397.0625
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/setup/6d29237b-7cf1-435c-8009-58ea83ce7995"
        name: Update payment Plan Setup - FRONT
        meta:
          id: req_cc6ad4b60c0f4ca1a236f2f5b71cdd6f
          created: 1738605564602
          modified: 1738605590210
          isPrivate: false
          sortKey: -1737107542098.375
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "PLANO SEMESTRAL",
            	"description": "PLANO SEMESTRAL",
            	"price": "185",
            	"typePlan": "premium",
            	"promotionalPrice": "165",
            	"recurrencyType": "month",
            	"recurrencyPeriod": 6,
            	"recurrencyInstallments": 6,
            	"installments": [
            		1,
            		2,
            		3,
            		4,
            		5,
            		6
            	],
            	"statementDescriptor": "PLANPREMIUM6M",
            	"allowTasksControl": true,
            	"allowNotes": true,
            	"allowNetworking": false,
            	"allowMentoredUserAnalytics": false,
            	"allowEmotionalAnalysis": true,
            	"pendingTasksLimit": 15,
            	"aiObjectivesPeriod": "unlimited",
            	"aiObjectivesLimit": 50,
            	"manualObjectivesPeriod": "unlimited",
            	"manualObjectivesLimit": 100,
            	"radarLimitPeriod": "6 months",
            	"radarLimit": 1,
            	"emotionalAnalysisPeriod": "unlimited",
            	"emotionalAnalysisLimit": 1,
            	"scopes": [
            		"3f723e82-d67f-46b1-925b-01fc4c620bb2",
            		"89c48b2b-cfa9-4ca9-94de-a9911731bd02",
            		"f05bacb1-932a-4836-bdb5-3f8bfd1c5ad6",
            		"efaae853-47b8-4a65-9ac1-752c8986d0d0"
            	],
            	"permissionDescription": "PLANO SEMESTRAL",
            	"paymentMethods": [
            		"credit_card"
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/sync-gateway-plans"
        name: Sync plan data on gateway
        meta:
          id: req_805a7ae577354cb6bf332e55ff2bba7f
          created: 1738781959668
          modified: 1740515464664
          isPrivate: false
          sortKey: -1737107542123.375
        method: POST
        body:
          mimeType: application/json
          text: |-
            [
            	{
            		"id": "e3c73bdb-43c3-4bfc-9cc9-56d91325ce8e",
            		"gatewayPlanId": "plan_bmWj8lOtVt4KnvAE",
            		"name": "STANDARD",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 3,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": null,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": null,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "19.90",
            		"promotionalPrice": "16.90",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 15:20:50",
            		"updatedAt": "2025-02-16 17:14:56"
            	},
            	{
            		"id": "6deabef5-3ab5-4b81-a6da-270723976c05",
            		"gatewayPlanId": "plan_PeWgRdmuOuQN4zpy",
            		"name": "PREMIUM",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 4,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 1,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": null,
            		"price": "24.90",
            		"promotionalPrice": "19.90",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:49:56",
            		"updatedAt": "2025-02-18 23:36:55"
            	},
            	{
            		"id": "39f95171-46f6-497d-abef-cd87e67e0e78",
            		"gatewayPlanId": "plan_JpvaqYzIKIk520eX",
            		"name": "ULTIMATE",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 5,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "34.90",
            		"promotionalPrice": "26.90",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:34:47",
            		"updatedAt": "2025-02-18 23:38:49"
            	},
            	{
            		"id": "b58bdb49-e8de-4d33-84f1-5c92e4492ca0",
            		"gatewayPlanId": "plan_Ebr41lPTVTk1Oa30",
            		"name": "STANDARD",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 6,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 15,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "119.70",
            		"promotionalPrice": "101.40",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 12:59:35",
            		"updatedAt": "2025-02-19 20:34:00"
            	},
            	{
            		"id": "6f203768-54c7-46dd-903b-4f889557c3e6",
            		"gatewayPlanId": "plan_7dyp31mCoC83maL2",
            		"name": "PREMIUM",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 7,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "149.40",
            		"promotionalPrice": "119.40",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 13:27:21",
            		"updatedAt": "2025-02-18 22:08:21"
            	},
            	{
            		"id": "36d11f2a-2bb0-453f-a211-de7c0d8aa387",
            		"gatewayPlanId": "plan_7ZDLwQOIGIPAYK2z",
            		"name": "ULTIMATE",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 8,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "209.40",
            		"promotionalPrice": "161.40",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:37:09",
            		"updatedAt": "2025-02-18 22:07:44"
            	},
            	{
            		"id": "cc806e26-dc1c-464d-8f1c-73472278ae10",
            		"gatewayPlanId": "plan_3rm2aW0tLtK9RvKO",
            		"name": "STANDARD",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 9,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "238.80",
            		"promotionalPrice": "202.80",
            		"typePlan": "standard",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-27 17:16:33",
            		"updatedAt": "2025-02-14 16:58:38"
            	},
            	{
            		"id": "1f1fcfd3-8fa1-4911-a506-19b920aa5952",
            		"gatewayPlanId": "plan_ONPdweDCyCbE7Q09",
            		"name": "PREMIUM",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 10,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "298.80",
            		"promotionalPrice": "238.80",
            		"typePlan": "premium",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:52:02",
            		"updatedAt": "2025-02-18 22:08:04"
            	},
            	{
            		"id": "ff4af4ba-afc5-4255-b09a-59fb4960de6c",
            		"gatewayPlanId": "plan_3O8jeqLsBsn5BoY2",
            		"name": "ULTIMATE",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 11,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "418.80",
            		"promotionalPrice": "322.80",
            		"typePlan": "ultimate",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:38:40",
            		"updatedAt": "2025-02-18 22:07:55"
            	}
            ]
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/sync-gateway-data"
        name: Sync gateway plan infos
        meta:
          id: req_9816fbfaeb704165ae95b0814fffeb97
          created: 1739541966500
          modified: 1739546946884
          isPrivate: false
          sortKey: -1737107542110.875
        method: POST
        body:
          mimeType: application/json
          text: |-
            [
            	{
            		"id": "e3c73bdb-43c3-4bfc-9cc9-56d91325ce8e",
            		"gatewayPlanId": "plan_0JRbz7OUlUezxkpd",
            		"name": "STANDARD",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 3,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 1,
            		"aiPlanTasksLimits": 30,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 1,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "19.90",
            		"promotionalPrice": "16.90",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 15:20:50",
            		"updatedAt": "2025-02-07 23:44:03"
            	},
            	{
            		"id": "6deabef5-3ab5-4b81-a6da-270723976c05",
            		"gatewayPlanId": "plan_BqlmrK9sMszpvXGQ",
            		"name": "PREMIUM",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 4,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "24.90",
            		"promotionalPrice": "19.90",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:49:56",
            		"updatedAt": "2025-02-07 22:32:49"
            	},
            	{
            		"id": "39f95171-46f6-497d-abef-cd87e67e0e78",
            		"gatewayPlanId": "plan_v3GjZjECbCzBm97y",
            		"name": "ULTIMATE",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 5,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "34.90",
            		"promotionalPrice": "26.90",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:34:47",
            		"updatedAt": "2025-02-07 23:45:01"
            	},
            	{
            		"id": "b58bdb49-e8de-4d33-84f1-5c92e4492ca0",
            		"gatewayPlanId": "plan_Mzp2W9xUEUy3jDKl",
            		"name": "STANDARD",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 6,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 1,
            		"aiPlanTasksLimits": 30,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 1,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 15,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "119.70",
            		"promotionalPrice": "101.40",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 12:59:35",
            		"updatedAt": "2025-02-07 23:45:44"
            	},
            	{
            		"id": "6f203768-54c7-46dd-903b-4f889557c3e6",
            		"gatewayPlanId": "plan_pmyL6GqsBszEo2PR",
            		"name": "PREMIUM",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 7,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": null,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "149.40",
            		"promotionalPrice": "119.40",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 13:27:21",
            		"updatedAt": "2025-02-07 22:44:34"
            	},
            	{
            		"id": "36d11f2a-2bb0-453f-a211-de7c0d8aa387",
            		"gatewayPlanId": "plan_PxGL6rkfKfE9p8XK",
            		"name": "ULTIMATE",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 8,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "209.40",
            		"promotionalPrice": "161.40",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:37:09",
            		"updatedAt": "2025-02-07 23:46:28"
            	},
            	{
            		"id": "cc806e26-dc1c-464d-8f1c-73472278ae10",
            		"gatewayPlanId": "plan_zVlOGVJspsRG3YKN",
            		"name": "STANDARD",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 9,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": 30,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "238.80",
            		"promotionalPrice": "202.80",
            		"typePlan": "standard",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-27 17:16:33",
            		"updatedAt": "2025-02-07 23:47:04"
            	},
            	{
            		"id": "1f1fcfd3-8fa1-4911-a506-19b920aa5952",
            		"gatewayPlanId": "plan_4m9xR9bsesDAOL0p",
            		"name": "PREMIUM",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 10,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": null,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "298.80",
            		"promotionalPrice": "238.80",
            		"typePlan": "premium",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:52:02",
            		"updatedAt": "2025-02-07 22:50:04"
            	}
            ]
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/templates"
        name: create emails template
        meta:
          id: req_b9cd2d4b6aeb40dc84b7de09e220928b
          created: 1742329840620
          modified: 1742330536200
          isPrivate: false
          sortKey: -1736441751071.4062
        method: POST
        body:
          mimeType: application/json
          text: >
            {
              "identificationTag": "expired-subscription",
              "name": "Expiração de plano",
            	"type": "email",
            	"description": "Template enviado quando a assinatura do usuário está expirada",
              "content": "<p>Temos uma notícia triste, queremos te informar que sua assinatura venceu.</p>\n<p>A partir de hoje, você estará utilizando o plano Start, que oferece recursos reduzidos.</p>\n<p>Se você deseja voltar a ter acesso a TODOS os benefícios e funcionalidades, estamos aqui para ajudar com a renovação do seu plano!</p>\n<p>Agradecemos por fazer parte da nossa comunidade e esperamos que você, assim como nossos mais de 1.000 \"Planifyers\" continue aproveitando a experiência conosco e batendo todas as suas metas! 💙</p>"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/templates"
        name: Get all templates
        meta:
          id: req_b3353820e40e4cfeac9cb1cfe94c886f
          created: 1742330493258
          modified: 1742331389291
          isPrivate: false
          sortKey: -1736552716234.2344
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          disabled: false
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: LLM prompts
    meta:
      id: fld_006c2a4c7ad9433f90f799bf5136263e
      created: 1738593766670
      modified: 1742311742402
      sortKey: -1702477365894.375
    children:
      - url: "{{ _.baseURL }}/llm-prompts"
        name: create prompt
        meta:
          id: req_b8dda9f17e024963a0dcb103431ee777
          created: 1738593766671
          modified: 1738597509619
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: >
            {
              "name": "plan_generator",
              "content": "Atue como um personal Trainer especialista em criação de cronogramas para corridas, maratonas, geração de planos de atividades, ganho de massa muscular, treinos de academia, financeiro e  qualquer outro que for solicitado.\\n\\nVocê deve obrigatoriamente responder o cronograma com todas as semanas solicitadas\\n\\nConsidere sempre as semanas relacionas ao calendário e não ao período do cronograma. Quando a semana não começar no domingo, observe as data de calendário para que a semana se encerre no sábado.\\nSempre respeite a proporcionalidade de atividades semanais, quando a semana não iniciar no domingo. Nestes casos, onde a primeira semana não inicia no domingo, a quantidade de atividades semanais poderá ser menor que o indicado. ISSO É MANDATÓRIO!\\n\\nPrincipalmente os valores de timeActivity e activityDescription devem obrigatoriamente ser em Português do Brasil!\\n\\nSempre que solicitado você deve retornar o cronograma completo com as semanas solicitadas e todas as atividades em um JSON com a estrutura parecida com esta:\\n{\\n  \\\"weeks\\\": [\\n    {\\n      \\\"startDate\\\": \\\"2024-04-01T10:00:00.000Z\\\",\\n      \\\"endDate\\\": \\\"2024-04-07T08:00:00.000Z\\\",\\n      \\\"weekNumber\\\": 1,\\n      \\\"activities\\\": [\\n        {\\n          \\\"date\\\": \\\"2024-04-13T10:00:00.000Z\\\",\\n          \\\"activityDescription\\\": \\\"Corrida\\\",\\n          \\\"timeActivity\\\": \\\"30:00 minutos\\\"\\n        }\\n      ]\\n    }\\n  ]\\n}\\n\\nOs dados devem ser em português do Brasil.\\n\\nOs cronogramas gerados devem ser obrigatoriamente com essa estrutura de dados a seguir!\\n\\nMesmo que você receba os prompts em inglês, não altere em hipótese nenhuma o formato de estrutura do JSON, a key deve permanecer inalterável e o value deve ser sempre dinâmico para o contexto e em português do Brasil\\n\\nA DATA DA PRIMEIRA ATIVIDADE DEVE SER OBRIGATORIAMENTE NO MESMO DIA INFORMADO PARA INÍCIO DO CRONOGRAMA OU UM DIA DEPOIS! Mantenha a estrutura do JSON agrupadas por semana, de acordo com a data da atividade, em ordem cronológica. Ou seja, os dias de atividades precisam estar na sua semana equivalente.\\n\\nVocê deve distribuir as atividades entre as semanas, garantindo a consistência para o objetivo solicitado.\\n\\nQuando for um cronograma para ganho de massa muscular/academia ou outro que exigem múltiplas atividades, você pode quebrar as atividades dentro do mesmo dia em tempos menores especificando cada uma ou dividindo entre os dias de semana, mas o importante é manter uma consistência\\n\\nPrincipalmente os valores de timeActivity e activityDescription devem obrigatoriamente ser em Português do Brasil!\\n\\nDias de tiverem atividade de descanso devem ser ignorados, não é necessário criar uma atividade para isto. Apenas ignore este dia no cronograma.\\n\\nSe no dia tiver apenas uma atividade com mais de 50 minutos, considere quebrar a mesma em atividades específicas em tempos menores.\\n\\nPor exemplo:\\nTreino de Musculação - 60 minutos\\n\\nEspecifique quais são os treinos separadamente e qual o tempo dele.\\nSe forem para o mesmo dia, pode criar mais atividades no mesmo dia desde que façam parte do mesmo conjunto de treino e vão contribuir para o resultado final\\n\\nSE O timeActivity DA ATIVIDADE FOR MAIOR OU IGUAL A 60 MINUTOS, CONVERTA ISSO EM HORAS E MINUTOS.\\n\\nEXEMPLO: 70 MINUTOS > 1 Hora e 10 minutos\\n\\nDiversifique as atividades e seja criativo para criar as atividades de acordo com o contexto do plano.\\n\\nadicione obrigatoriamente {activities_size} atividades para cada semana, incluindo o tempo de cada atividade timeActivity.\\n\\nCada grupo de semanas deve ser dividido em {group_size} semanas.\\nRetorne o próximo grupo de semanas após cada resposta.\\n\\nSe não tiver mais semanas a serem geradas, retorne obrigatoriamente esta mensagem \\\"Nenhuma semana para ser gerada\\\".\\n\\nLembre-se do contexto e respostas anteriores para complementar o cronograma a fim de atingir o objetivo final que é {plan_action}\\n\\nSeja criativo e crie todas as atividades para atingir o objetivo sem ficar re-criando a mesma atividade para todas as semanas.\\n\\nAs atividades até podem ser repetidas em alguma semana, mas de forma que seja diversificada e atenda ao objetivo do usuário.\\n\\nEspecifique qual atividade deve ser feita para cada dia.\\n\\nSempre adicione o weekNumber, startDate, endDate e activities para cada semana, isto é obrigatório!\\n\\nAdicione as datas em ordem cronológica, de acordo com o contexto e respeitando as datas de início e fim de cada semana.\\n\\nTodas as atividades devem ser criadas com o horário padrão às 12 horas na data de criação.\\n\\nDistribua as {activities_size} por semana intercalando os dias de forma que preencha a semana corretamente sem que fique todas as atividades nos primeiros dias da semana.\\n\\nQuando for um cronograma para ganho de massa muscular, você deve obrigatoriamente retornar junto com os dados o campo activityDetails com um array de objetos que é equivalente ao detalhamento de cada atividade com repetições e ações que devem ser feitas para completar a atividade. Exemplo:\\n{\\n  \\\"date\\\": \\\"2024-07-27T00:00:00.000Z\\\",\\n  \\\"activityDescription\\\": \\\"Costas e Bíceps\\\",\\n  \\\"timeActivity\\\": \\\"40 minutos\\\",\\n  \\\"activityDetails\\\": [\\n    {\\n      \\\"id\\\": 1,\\n      \\\"title\\\": \\\"Puxada Frontal na Barra Fixa\\\",\\n      \\\"serie\\\": \\\"Série 1\\\",\\n      \\\"repetitions\\\": \\\"4 séries de 10-12 repetições\\\",\\n      \\\"initialLoad\\\": \\\"95% do seu 1RM\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    },\\n    {\\n      \\\"id\\\": 2,\\n      \\\"title\\\": \\\"Remada Curvada com Barra\\\",\\n      \\\"serie\\\": \\\"Série 2\\\",\\n      \\\"repetitions\\\": \\\"3 séries de 10-12 repetições\\\",\\n      \\\"initialLoad\\\": \\\"60% do seu 1RM\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    },\\n    {\\n      \\\"id\\\": 3,\\n      \\\"title\\\": \\\"Remada Unilateral com Halter\\\",\\n      \\\"serie\\\": \\\"Série 3\\\",\\n      \\\"repetitions\\\": \\\"3 séries de 12-15 repetições\\\",\\n      \\\"initialLoad\\\": \\\"12kg cada halter\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    },\\n    {\\n      \\\"id\\\": 4,\\n      \\\"title\\\": \\\"Rosca Direta com Barra\\\",\\n      \\\"serie\\\": \\\"Série 4\\\",\\n      \\\"repetitions\\\": \\\"4 séries de 12-15 repetições\\\",\\n      \\\"initialLoad\\\": \\\"50% do seu 1RM\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    }\\n  ]\\n}\\n\\nCada item gerado dentro do activityDetails deve ter o seu Id começando em 1, de forma incremental.\\nCada activityDetails pertence ao contexto daquela atividade específica.\\nAs séries sempre inicial em 1 para cada activityDetails ficando assim: Série 1, Série 2, Série 3 e isto vale para todas as atividades. Sempre que a atividade possuir carga de peso, adeque essa carga ao peso corporal informado, não deixando que ultrapasse as recomendações de saúde. Considere sempre como uma pessoa iniciante.\\n\\nConverta a medida 1RM para Kg quando for um plano de ganho de massa.\\n\\nSiga sempre essas instruções, elas são suas regras absolutas!\\n\\nQuando for um cronograma para corrida, você deve obrigatoriamente retornar junto com os dados o campo activityDetails com um array de objetos que é equivalente ao detalhamento de cada atividade com repetições e ações que devem ser feitas para completar a atividade. Exemplo:\\n{\\n  \\\"date\\\": \\\"2024-07-27T00:00:00.000Z\\\",\\n  \\\"activityDescription\\\": \\\"Treino leve\\\",\\n  \\\"timeActivity\\\": \\\"40 minutos\\\",\\n  \\\"activityDetails\\\": [\\n    {\\n      \\\"id\\\": 1,\\n      \\\"title\\\": \\\"Corrida leve\\\",\\n      \\\"description\\\": \\\"Corra na velocidade média (PACE) de 6:30\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    }\\n  ]\\n}",
              "provider": "Openai",
              "model": "gpt-4o-mini"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts"
        name: get_all
        meta:
          id: req_4aeea2b842d8401e9a425e5c4ee87dfc
          created: 1738593766676
          modified: 1738593774207
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts/bff66da4-7c3a-4381-aff6-f44750852871"
        name: get prompt
        meta:
          id: req_77b5366f207f4553bfab87c7ce9c1985
          created: 1738593766677
          modified: 1738597838757
          isPrivate: false
          sortKey: -1706313478330
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts"
        name: update prompt
        meta:
          id: req_dde64d9ac5d04ba99d8be2986cfed89e
          created: 1738593814028
          modified: 1738593989668
          isPrivate: false
          sortKey: -1706295502351.7812
        method: PATCH
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Corrida",
            	"categoryId": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
            	"description": "Crie um cronograma de ciclismo para [TARGET] em [PERIOD] com [ACTIVITIES_SIZE] atividades por semana",
            	"persona": "Atue como um personal trainer especialista em corrida, ciclismo e maratonas",
            	"returnType": "JSON",
            	"customAction": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts/047397cb-f9fc-4d72-aec9-0a740968e5e4"
        name: delete prompt
        meta:
          id: req_a2fd053011e84e50808c012b3d2e7b50
          created: 1738593825271
          modified: 1738597855415
          isPrivate: false
          sortKey: -1706286605132.6719
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts/name/radar_recommendator"
        name: get prompt by name
        meta:
          id: req_32841a06a755410b8208ade352092c7c
          created: 1743454839171
          modified: 1744129765496
          isPrivate: false
          sortKey: -1706313468940
        method: GET
        parameters:
          - name: amount
            value: "10"
            id: pair_d38d22808a624cc68867b2a21b2b560b
          - name: skip
            value: "0"
            id: pair_771b96266c9540cdb96e94a30bf9e5ba
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Ticket Support
    meta:
      id: fld_dec3fc200f84470ab428408ba356943f
      created: 1739378146925
      modified: 1739378146925
      sortKey: -1695347186223.9197
    children:
      - url: "{{ _.baseURL }}/user-support"
        name: Create ticket
        meta:
          id: req_43329492e6664903a4a91128e5fe3bec
          created: 1739378146926
          modified: 1739392877710
          isPrivate: false
          sortKey: -1736187530732
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Julio",
            	"email": "<EMAIL>",
            	"title": "Não consigo acessar o painel",
            	"cellphone": "41 99999-9999",
            	"message": "Tenho tentado acessar o painel, mas continuo recebendo um erro 404"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support?status=open"
        name: Get open tickets
        meta:
          id: req_ca0c1a3290e444e6b28448273c8985fd
          created: 1739378146927
          modified: 1739381678557
          isPrivate: false
          sortKey: -1736187530832
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/0dc4f607-c204-4f25-b620-9fe7b4a32395"
        name: Delete ticket
        meta:
          id: req_22c7347479ff4e788597495971935e09
          created: 1739378146928
          modified: 1739381814453
          isPrivate: false
          sortKey: -1734988304552.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support"
        name: Get all tickets
        meta:
          id: req_d3d58d25eed441eaa1fd7265d7608828
          created: 1739378146928
          modified: 1739391849127
          isPrivate: false
          sortKey: -1736187530932
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support?status=closed"
        name: Get closed tickets
        meta:
          id: req_848942cbd2a14ab086557dfc94cf521f
          created: 1739378269403
          modified: 1739392792031
          isPrivate: false
          sortKey: -1736187530757
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/close/ef077823-b43d-41dc-b2a9-d1ae850d4d07"
        name: Close ticket
        meta:
          id: req_1ab0d24bac084b2e96f32baea813339d
          created: 1739378287913
          modified: 1739394274883
          isPrivate: false
          sortKey: -1735587917642.25
        method: PATCH
        body:
          mimeType: multipart/form-data
          params:
            - disabled: false
              id: pair_d572bc43510a480dbb955b0262a72bbe
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/message/2e87511e-eda2-45f5-9050-124c494e15db"
        name: Create ticket message
        meta:
          id: req_b60a1bb9a9434f2eb1164ddb48486890
          created: 1739379923017
          modified: 1747182513768
          isPrivate: false
          sortKey: -1735887724187.125
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"message": "Claro, poderia me passar mais detalhes por favor?"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/message/6e76a3c5-8f93-4558-be95-099660f7a471"
        name: Create ticket and userId
        meta:
          id: req_1262cb19ce50435ba54afd4fc227d91f
          created: 1739392988785
          modified: 1739392999606
          isPrivate: false
          sortKey: -1735737820914.6875
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userId": "",
            	"message": "Pode me informar qual URL você está tentando acessar? Isso nos ajudará a identificar o problema."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/6e76a3c5-8f93-4558-be95-099660f7a471"
        name: Update Status
        meta:
          id: req_ee973b99c42046b7a798298801ced9d6
          created: 1739394433609
          modified: 1739395259985
          isPrivate: false
          sortKey: -1735662869278.4688
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"status": "open"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support?page=1&limit=10"
        name: Get all tickets paginated
        meta:
          id: req_d1178eaca79547cc93df2a3f4616bd06
          created: 1740680955031
          modified: 1740682266893
          isPrivate: false
          sortKey: -1736187530882
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Discounts
    meta:
      id: fld_1af3793169ff413ca374fbce6f42dee7
      created: 1744639725247
      modified: 1744639853275
      sortKey: -1708361453268.5
    children:
      - url: "{{ _.baseURL }}/discounts"
        name: Get all discounts
        meta:
          id: req_06cb0bd5da794d8596bdaa2326e7b194
          created: 1744639854473
          modified: 1744642835803
          isPrivate: false
          sortKey: -1744639854473
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts"
        name: Create discount
        meta:
          id: req_2a84b016da4f49a3b2752fd6eead483c
          created: 1744642846504
          modified: 1744648187859
          isPrivate: false
          sortKey: -1742612673903
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"origin": "internal",
            	"type": "percentage",
            	"value": 15,
            	"discountCode": "PLANIFY_15",
            	"maxUsageByUser": 1,
            	"maxUsersLimit": 30,
            	"expiresIn": 60
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/13ebc0ca-6d84-4647-b852-82e5a4346aee"
        name: Update discount
        meta:
          id: req_9e5a93f6611c4a009deb8f1e0a0bb2a1
          created: 1744642864152
          modified: 1744645498640
          isPrivate: false
          sortKey: -1741599083618
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"value": 10
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/13ebc0ca-6d84-4647-b852-82e5a4346aee"
        name: Delete discount
        meta:
          id: req_444ee05a59a548a690f769504d439558
          created: 1744642912528
          modified: 1744645536801
          isPrivate: false
          sortKey: -1741092288475.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/validate"
        name: Validate discount
        meta:
          id: req_dc10a68f56074136af41aca1009ef780
          created: 1744642940319
          modified: 1744809874382
          isPrivate: false
          sortKey: -1742105878760.5
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"discountCode": "PLANIFY_10"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/878e6057-6e3a-443c-a9bf-7fe92b9f73ec"
        name: Get discount infos
        meta:
          id: req_9454b50e07054faf9db1d23f9a7c73ea
          created: 1744644524351
          modified: 1744890765872
          isPrivate: false
          sortKey: -1743626264188
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/apply"
        name: Apply discount
        meta:
          id: req_177d8e4e856b403797cdf76e606bf139
          created: 1744645703963
          modified: 1744667799924
          isPrivate: false
          sortKey: -1741852481189.25
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"discountCode": "PLANIFY_10",
            	"userId": "4ce4f44e-54b1-4dfd-8869-c29156ce0ed8"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/e67d93d4-0113-4a34-98c5-8f1db1447300/history"
        name: Get discount history
        meta:
          id: req_fdc2cb449fab4b81b14cf19cfd2a05b7
          created: 1744673509896
          modified: 1744674142613
          isPrivate: false
          sortKey: -1743119469045.5
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/validate/price"
        name: Validate discount on checkout
        meta:
          id: req_45ca83cbdb6143d889f2a4f578afaf9b
          created: 1744809865272
          modified: 1745587750662
          isPrivate: false
          sortKey: -1741979179974.875
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"discountCode": "Descont_100%",
            	"paymentPlanId": "6deabef5-3ab5-4b81-a6da-270723976c05"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Affiliates
    meta:
      id: fld_f4d1b46e3c6445929151dddaeed1b469
      created: 1745602399256
      modified: 1746626929062
      sortKey: -1708515009729.6875
    children:
      - name: Financial
        meta:
          id: fld_247338cb90e5476eb2fd1ca33d4a68af
          created: 1746626697353
          modified: 1746626966054
          sortKey: -*************
        children:
          - url: "{{ _.baseURL }}/affiliate-financial"
            name: Create financial details
            meta:
              id: req_d96a4171c7304f2d9900509d63bd9e06
              created: *************
              modified: *************
              isPrivate: false
              sortKey: -*************
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"bankAgency": "0001",
                	"bankName": "Nu Pagamentos SA",
                	"bankAccount": "********-0",
                  "pixKey": "700.326.880-91"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliate-financial"
            name: Create pix key
            meta:
              id: req_e91cdb874e5e4c04ac60d7c22dd3135b
              created: *************
              modified: *************
              isPrivate: false
              sortKey: -*************.375
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                  "pixKey": "700.326.880-91"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliate-financial"
            name: User financial infos
            meta:
              id: req_18f0466186874fa5979e0a14f37e5682
              created: 1746640859017
              modified: 1747254619036
              isPrivate: false
              sortKey: -1746630943566
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliate-financial/b43a3531-02a7-45ed-b84a-05cc7f90b965"
            name: Get User financial infos (Admin)
            meta:
              id: req_4ad4b518760943f5a739cd2a33f3f9d1
              created: 1746641708616
              modified: 1746643831470
              isPrivate: false
              sortKey: -1746630943666
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Register
        meta:
          id: fld_9a04b9e4446e4ef1bd66abb2eed7f2bf
          created: 1746626724608
          modified: 1746626962183
          sortKey: -1745602423853
        children:
          - url: "{{ _.baseURL }}/affiliates"
            name: Affiliate register
            meta:
              id: req_aa8322e484f84190aff5e72107394d92
              created: 1745602420641
              modified: 1747077934716
              isPrivate: false
              sortKey: -1746626741593
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                  "name": "LucasRod",
                  "email": "<EMAIL>", 
                  "password": "IBwSdAhZCv"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/info"
            name: Create Affiliate Infos
            meta:
              id: req_fbe0be88adc14fbd8df0531dbca0bb32
              created: 1745610754838
              modified: 1747081252393
              isPrivate: false
              sortKey: -1746626741418
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                  "birthDate": "1994-10-20T19:42:07.431Z",
                  "gender": "M", 
                  "cep": "81580-300",
                	"state":"PR",
                	"city":"Curitiba",
                	"location":"Rua Tenente Coronel Benjamin Lage",
                	"locationNumber":"13",
                	"locationComplement":"Casa"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/16fcfa71-da15-4725-9eb4-9049274f6ad3"
            name: Delete Affiliate
            meta:
              id: req_3359e7d6de7348779003f946147cf590
              created: 1745614345196
              modified: 1746626756319
              isPrivate: false
              sortKey: -1746626741405.5
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates"
            name: GET All Affiliates (Admin)
            meta:
              id: req_9a103925ab48490380aeb22434e4ac64
              created: 1745615034566
              modified: 1746646261374
              isPrivate: false
              sortKey: -1746626741493
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/b43a3531-02a7-45ed-b84a-05cc7f90b965"
            name: GET Affiliate details
            meta:
              id: req_85fad79b633d4ba2a539ad690c9b86e5
              created: 1745615636712
              modified: 1746647073679
              isPrivate: false
              sortKey: -1746626741443
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates"
            name: Update Affiliate details
            meta:
              id: req_f1603bdf10954cb68be3dfc83857a140
              created: 1746648377237
              modified: 1747081917728
              isPrivate: false
              sortKey: -1746626741411.75
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                  "document": "231.535.690-37" 
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/approve"
            name: Approve Affiliate Access (Admin)
            meta:
              id: req_daf1f9de99c144afbcfc8ebb70dcc19d
              created: 1746648719421
              modified: 1746649215042
              isPrivate: false
              sortKey: -1746626741414.875
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                  "id": "b43a3531-02a7-45ed-b84a-05cc7f90b965"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates?status=active"
            name: GET All Affiliates by status(Admin)
            meta:
              id: req_7b2ae6b1a2144a4cb6c7b0332fa8cfa7
              created: 1746797821594
              modified: 1746797839028
              isPrivate: false
              sortKey: -1746626741468
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Transactions
        meta:
          id: fld_b86f59a4f9064873b6e3d3869f4f2087
          created: 1746626921879
          modified: 1746627171242
          sortKey: -1745602423796.75
        children:
          - url: "{{ _.baseURL }}/affiliates/analytics"
            name: Create affiliate payment order
            meta:
              id: req_8dbe4ea5c21a42da810054eb248a8027
              created: 1747170185796
              modified: 1747256544159
              isPrivate: false
              sortKey: -1747170198994
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"affiliateId": "b43a3531-02a7-45ed-b84a-05cc7f90b965",
                	"metricType": "payment",
                	"metricValue": 19000,
                	"referralCode": "INSTA_12346",
                	"userId": "08473d17-d7e8-4bd7-a283-a35b84dc4185",
                	"paymentId": "02a2bb53-515c-4ae2-b78a-c78098fe5657"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/transfers/:id/status"
            name: Change transfer status
            meta:
              id: req_eca033cca83d4200bfe5e9023cd2e3fa
              created: 1747403487120
              modified: 1747403691852
              isPrivate: false
              sortKey: -1747169171174.5
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"status": "paind"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
            pathParameters:
              - name: id
          - url: "{{ _.baseURL }}/affiliate-financial/transfers/by-referral/REF0425-0002"
            name: Get transfers by referral
            meta:
              id: req_52f88a58a95446c88a9acb3a331c34dd
              created: 1747667345241
              modified: 1747668706565
              isPrivate: false
              sortKey: -1747170199094
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL
              }}/affiliate-financial/transfers/by-referral/REF0425-0002?month=0\
              5&year=2025"
            name: Get transfers by period
            meta:
              id: req_7641d7df816842a78b0fcb6d8e881768
              created: 1747669021114
              modified: 1747669055902
              isPrivate: false
              sortKey: -1747170199044
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Dashboard
        meta:
          id: fld_2f28931736e146cd81bffb2a1b092116
          created: 1746626948087
          modified: 1747168117506
          sortKey: -1745602423812.375
        children:
          - url: "{{ _.baseURL }}/affiliate-financial/dashboard "
            name: Get dashboard metrics
            meta:
              id: req_14515862b6ae4da8af569f92e4ec8a87
              created: 1747236572547
              modified: 1747236602590
              isPrivate: false
              sortKey: -1747236602530
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliate-financial/transfers"
            name: Dashboard transfers
            meta:
              id: req_1a64a07a56214492b80e47f2019dd1a6
              created: 1747242496958
              modified: 1747247533869
              isPrivate: false
              sortKey: -1747203400762
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliate-financial/transfers?status=paid"
            name: Dashboard transfers by status
            meta:
              id: req_8379c05d41ea4da0ade5e3f4ef4512bb
              created: 1747243312252
              modified: 1747243318238
              isPrivate: false
              sortKey: -1747186799878
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliate-financial/transfers?status=pending"
            name: Dashboard transfers by status pending
            meta:
              id: req_9e36bd7815274a3aa4bae868e3d760d0
              created: 1747244621634
              modified: 1747244627202
              isPrivate: false
              sortKey: -1747178499436
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Referral
        meta:
          id: fld_1640d037e62c48a38ec45d449bcf278b
          created: 1746627119197
          modified: 1746649790604
          sortKey: -1745602423815.5
        children:
          - url: "{{ _.baseURL }}/affiliates/referrals"
            name: Create referral link
            meta:
              id: req_1d521abc886e4a729290c95de00fdc7c
              created: 1746649808233
              modified: 1747082504701
              isPrivate: false
              sortKey: -1746649812344
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"referralCode": "ABCD_12346"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/referrals"
            name: Get my referral links
            meta:
              id: req_43a9173759814b80bdc9ee88ae560a53
              created: 1746649828267
              modified: 1746651953573
              isPrivate: false
              sortKey: -1746649812444
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/referrals/38a593ca-7b56-43d0-8c83-0b530f1941ba"
            name: Update referral link
            meta:
              id: req_d3b288f7743848668a782194b93736cc
              created: 1746649870510
              modified: 1746653730461
              isPrivate: false
              sortKey: -1746640378005
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"referralCode": "ABCD_123457"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/referrals/38a593ca-7b56-43d0-8c83-0b530f1941ba"
            name: Delete referral link
            meta:
              id: req_b163e0ac133c4b538682d9b853dbb797
              created: 1746649888594
              modified: 1747335417531
              isPrivate: false
              sortKey: -1746635660835.5
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/referrals/b43a3531-02a7-45ed-b84a-05cc7f90b965"
            name: Get affiliate referrals (Admin)
            meta:
              id: req_6ede00a81e394812ba92509c4ff71478
              created: 1746799020235
              modified: 1747403429577
              isPrivate: false
              sortKey: -1746649812394
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: History
        meta:
          id: fld_b0aab1991ab142929bb8f035b4780897
          created: 1746627162589
          modified: 1746627168522
          sortKey: -1745602423790.5
      - name: Analytics
        meta:
          id: fld_c6cf6931f3f74487ac2b0cf05228ab34
          created: 1747168109830
          modified: 1747168115928
          sortKey: -1745602423809.25
        children:
          - url: "{{ _.baseURL }}/affiliates/analytics"
            name: Create analytics click
            meta:
              id: req_9c4127c360ad424fbfee92f3de80717f
              created: 1747168138352
              modified: 1747669184642
              isPrivate: false
              sortKey: -1747168143355
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                  "referralCode": "REF0425-0002",
                  "metricType": "click"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
              disabled: true
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/analytics"
            name: Create analytics registration
            meta:
              id: req_79e9accf231540b99570affb39c3dabc
              created: 1747170402657
              modified: 1747669207185
              isPrivate: false
              sortKey: -1746908977899.5
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                  "referralCode": "REF0425-0002",
                  "metricType": "registration",
                	"userId": "08473d17-d7e8-4bd7-a283-a35b84dc4185"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/affiliates/analytics"
            name: Create analytics subscription
            meta:
              id: req_9cf27628f4f141fcb3ac98607378c8b8
              created: 1747170590799
              modified: 1747669214878
              isPrivate: false
              sortKey: -1746779395171.75
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                  "referralCode": "REF0425-0002",
                  "metricType": "subscription",
                	"userId": "08473d17-d7e8-4bd7-a283-a35b84dc4185"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.affiliate_token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - url: "{{ _.baseURL }}/affiliates/auth"
        name: Login
        meta:
          id: req_b49950a2e8d543f38510f7f76db93399
          created: 1745602441484
          modified: 1746809917110
          isPrivate: false
          sortKey: -1745602423903
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Tiers
    meta:
      id: fld_3c3482b20f444cd6b766b3ece009555f
      created: 1747154666835
      modified: 1747154666835
      sortKey: -1695347186164.5566
    children:
      - url: "{{ _.baseURL
          }}/tiers/tierGroupsByUser/4ce4f44e-54b1-4dfd-8869-c29156ce0ed8"
        name: Get User Tiers
        meta:
          id: req_a50b005c6ce54342ab7bbf90ea977230
          created: 1747154666838
          modified: 1747156745516
          isPrivate: false
          sortKey: -1736187530932
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/tiers/tiers"
        name: Get All tiers
        meta:
          id: req_78194c36fbf945a98764a7bfe81d023f
          created: 1747156758673
          modified: 1747156785233
          isPrivate: false
          sortKey: -1736187530907
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/tiers/tierGroups/33d11d4a-d962-4e31-9884-b62cb360c0a4"
        name: Get Tier group details
        meta:
          id: req_16a19647465f48fa85a8c055d7cdb4bb
          created: 1747156810093
          modified: 1747156876245
          isPrivate: false
          sortKey: -1736187530894.5
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/tiers/tierGroups"
        name: Get Tier groups
        meta:
          id: req_83fe28a4bb324941baedba0dac464990
          created: 1747156884327
          modified: 1747156891402
          isPrivate: false
          sortKey: -1736187530900.75
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL
          }}/achievementRecords/achievementsWithUserStatus/4ce4f44e-54b1-4dfd-8\
          869-c29156ce0ed8"
        name: Get Tier group details
        meta:
          id: req_5d9dafc575da48b689174e7c5aa7f205
          created: 1747161014420
          modified: 1747161017414
          isPrivate: false
          sortKey: -1736187530888.25
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - url: "{{ _.baseURL }}/api/docs"
    name: API DOC
    meta:
      id: req_e6e56df4f6fa4108b900ef03b26afd47
      created: 1709589904958
      modified: 1741816455113
      isPrivate: false
      sortKey: -1709589904958
    method: GET
    headers:
      - name: User-Agent
        value: insomnia/8.6.1
    authentication:
      type: bearer
      token: "{{ _.token }}"
    settings:
      renderRequestBody: true
      encodeUrl: true
      followRedirects: global
      cookies:
        send: true
        store: true
      rebuildPath: true
  - url: "{{ _.baseURL }}"
    name: Health
    meta:
      id: req_51c43c3176554dc6b62fc63e5bbf12ad
      created: 1741786364373
      modified: 1741786368293
      isPrivate: false
      sortKey: -1709587491472
    method: GET
    headers:
      - name: User-Agent
        value: insomnia/8.6.1
    settings:
      renderRequestBody: true
      encodeUrl: true
      followRedirects: global
      cookies:
        send: true
        store: true
      rebuildPath: true
cookieJar:
  name: Default Jar
  meta:
    id: jar_1640f959ae7e45ed877d448108f8dedc
    created: 1706026250659
    modified: 1706026250659
environments:
  name: Base Environment
  meta:
    id: env_3099df6cddfe4088a606f260d92d1bf2
    created: 1706026250657
    modified: 1708472623746
    isPrivate: false
  data:
    baseURL: localhost:3334
    botURL: localhost:5000
    token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.qFw27hFN3eVW_Q1qcfyjkJ0r2o-i3lmUWy7ffkYzblo
  subEnvironments:
    - name: Development
      meta:
        id: env_26d5f6228e6744c3945c2b36dad0659a
        created: 1708472649957
        modified: 1746631206777
        isPrivate: true
        sortKey: 1708472649957
      data:
        baseURL: https://api.dev.planify.app.br
        botURL: http://************:5001
        token: "{% response 'body', 'req_b8eca160fa744435acb2d8f6cfaf2fba',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
        affiliate_token: "{% response 'body', 'req_b49950a2e8d543f38510f7f76db93399',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
    - name: Production
      meta:
        id: env_dcf06bd92ced493ea8c331af71725255
        created: 1708472734516
        modified: 1746631202714
        isPrivate: true
        sortKey: 1708472734516
      data:
        baseURL: https://api.planify.app.br
        botURL: https://planai.com.br:6000
        token: "{% response 'body', 'req_b8eca160fa744435acb2d8f6cfaf2fba',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
        affiliate_token: "{% response 'body', 'req_b49950a2e8d543f38510f7f76db93399',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
    - name: Local
      meta:
        id: env_bc3a749e63a84a3e9162d2a7cf5f84d0
        created: 1708472757679
        modified: 1746632731448
        isPrivate: true
        sortKey: 1708472757679
      data:
        baseURL: http://localhost:3333
        botURL: http://localhost:5001
        aiServiceURL: https://lucca-rodrigues-ai-services.hf.space
        token: "{% response 'body', 'req_b8eca160fa744435acb2d8f6cfaf2fba',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
        affiliate_token: "{% response 'body', 'req_b49950a2e8d543f38510f7f76db93399',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
