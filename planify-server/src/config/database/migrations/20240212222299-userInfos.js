'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('UserInfos', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      birthDate: {
        type: Sequelize.DATE,
        defaultValue: null,
      },
      gender: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      cep: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      state: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      city: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      location: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      locationNumber: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      locationComplement: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      cellphone: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      phone: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      weight: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      currentActivitiesWeek: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      lastCheckup: {
        type: Sequelize.DATE,
        defaultValue: null,
      },
      menstualCycle: {
        type: Sequelize.DATE,
        defaultValue: null,
      },
      canOutExercises: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      receiveMessages: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      authorizeAllChannels: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      authorizePartners: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      receiveNewsletter: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      receiveNotifications: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      profileImage: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      activityLevel: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      termsAccepted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      termsAcceptedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      financialEducationLevel: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      investorProfile: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('UserInfos');
  },
};
