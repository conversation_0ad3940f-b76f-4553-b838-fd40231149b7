/* eslint-disable prettier/prettier */
'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Notifications', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      isRead: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      hasBeenViewed: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      readAt: {
        allowNull: true,
        type: Sequelize.DATE,
      },
      notificationStatus: {
        type: Sequelize.ENUM(
          'deleted',
          'updated',
          'created',
          'delayed',
          'default',
          'error',
          'success',
          'delActivity',
          'delPlan',
          'completedActivity',
          'completedPlan',
          'planDelayed',
          'partiallyCompletedPlan',
        ),
        allowNull: true,
        defaultValue: 'default',
      },
      targetId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      targetType: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      readCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      notificationDate: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null,
      },
      targetDate: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('Notifications');
  },
};
