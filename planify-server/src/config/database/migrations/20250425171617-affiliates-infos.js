'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('AffiliatesInfos', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        references: {
          model: 'Affiliates',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      birthDate: {
        type: Sequelize.DATE,
        defaultValue: null,
      },
      gender: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      cep: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      state: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      city: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      location: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      locationNumber: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      locationComplement: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      profileImage: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('AffiliatesInfos');
  },
};
