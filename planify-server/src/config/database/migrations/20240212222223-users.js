/* eslint-disable prettier/prettier */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Users', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      name: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      email: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      userAccountStatus: {
        type: Sequelize.ENUM('active', 'pending', 'inactive'),
        allowNull: false,
        defaultValue: 'pending',
      },
      pendingStatusReason: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      userType: {
        type: Sequelize.ENUM(
          'user',
          'admin',
          'partner',
          'mentor',
          'affiliate',
          'custom',
        ),
        defaultValue: 'user',
      },
      activationCode: {
        type: Sequelize.TEXT,
        defaultValue: null,
      },
      sharedCode: {
        type: Sequelize.TEXT,
        defaultValue: null,
      },
      isValidatedAccount: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      password: {
        type: Sequelize.STRING,
        defaultValue: null,
      },
      cpf: {
        type: Sequelize.STRING,
        defaultValue: null,
        unique: true,
      },
      isPasswordSubmited: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      isResetPassword: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      paymentPlanId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'PaymentPlans',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      lastAccess: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('Users');
  },
};
