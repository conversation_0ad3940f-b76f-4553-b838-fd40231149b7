'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Discounts', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      ownerId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      origin: {
        type: Sequelize.ENUM(
          'internal',
          'external',
          'mentor',
          'user',
          'affiliates',
          'custom',
        ),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM('percentage', 'fixed'),
        allowNull: true,
        defaultValue: 'percentage',
      },
      value: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active',
      },
      discountCode: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      allowInFirstPurchase: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      maxUsageByUser: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
      },
      maxUsersLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
      },
      expiresIn: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      expirationAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Discounts');
  },
};
