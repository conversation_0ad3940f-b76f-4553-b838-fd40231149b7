'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('PaymentPlans', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      gatewayPlanId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      assignDescription: {
        type: Sequelize.TEXT,
        allowNull: true,
        defaultValue: null,
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'draft'),
        allowNull: true,
        defaultValue: 'active',
      },

      displayOrder: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },

      // AI Objectives
      aiObjectivesPeriod: {
        type: Sequelize.ENUM('6 months', 'year', 'unlimited', 'custom'),
        allowNull: true,
        defaultValue: 'year',
      },
      aiObjectivesLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 50,
      },
      aiPlanTasksLimits: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 50,
      },

      // Manual Objectives
      manualObjectivesPeriod: {
        type: Sequelize.ENUM('6 months', 'year', 'unlimited', 'custom'),
        allowNull: true,
        defaultValue: 'year',
      },
      manualObjectivesLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 100,
      },
      manualPlanTasksLimits: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 50,
      },

      // Radar
      radarLimitPeriod: {
        type: Sequelize.ENUM('3 months', '6 months', 'year', 'custom'),
        allowNull: true,
        defaultValue: 'year',
      },
      radarLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
      },

      // Tasks
      allowTasksControl: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      pendingTasksLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 15,
      },

      // Notes
      allowNotes: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },

      // Emotional Analysis
      allowEmotionalAnalysis: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      emotionalAnalysisPeriod: {
        type: Sequelize.ENUM('6 months', 'year', 'unlimited', 'custom'),
        allowNull: true,
        defaultValue: 'year',
      },
      emotionalAnalysisLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
      },

      price: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: 'free',
      },
      promotionalPrice: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      typePlan: {
        type: Sequelize.ENUM(
          'free',
          'start',
          'standard',
          'premium',
          'ultimate',
          'ultimate_mentor',
        ),
        allowNull: false,
        defaultValue: 'free',
      },
      recurrencyType: {
        type: Sequelize.ENUM('month', 'year'),
        allowNull: false,
        defaultValue: 'month',
      },
      recurrencyPeriod: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },
      recurrencyInstallments: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },

      // Networking
      allowNetworking: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },

      allowMentoredUserAnalytics: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },

      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('PaymentPlans');
  },
};
