/* eslint-disable prettier/prettier */
'use strict';

// id UUID [primary key]
// tierName VARCHAR
// createdAt TIMESTAMP
// tierOrder int
// isFinalTier bool
// scoreStart VARCHAR
// scoreEnd VARCHAR
// TierGroupId UUID
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Tier', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      TierGroupId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'TierGroup',
          key: 'id',
        },
      },
      tierName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      tierOrder: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      isFinalTier: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
      },
      scoreStart: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      scoreEnd: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      badgeImage: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      thumbnailImage: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('Tier');
  },
};
