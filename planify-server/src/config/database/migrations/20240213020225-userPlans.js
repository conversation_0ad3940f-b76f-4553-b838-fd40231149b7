/* eslint-disable prettier/prettier */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('UserPlans', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      promptId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Prompts',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      target: {
        allowNull: false,
        type: Sequelize.STRING,
      },
      title: {
        allowNull: true,
        type: Sequelize.STRING,
        defaultValue: null,
      },
      startedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      isGeneratingPlan: {
        allowNull: true,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isCompleted: {
        allowNull: true,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isCompletedGeneration: {
        allowNull: true,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      currentStepGeneration: {
        allowNull: true,
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      duration: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      totalSteps: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      currentStep: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      totalWeeks: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      pendingSteps: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      hasHealthRisk: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      acceptedTerms: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      endAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      creationStrategy: {
        type: Sequelize.ENUM('auto', 'manual'),
        allowNull: true,
        defaultValue: 'auto',
      },
      planStatus: {
        type: Sequelize.ENUM(
          'inProgress',
          'delayed',
          'completed',
          'inCreation',
          'waiting',
          'partiallyCompleted',
        ),
        allowNull: false,
        defaultValue: 'inProgress',
      },
      activitiesPerWeek: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 3,
      },
      currentParam: {
        allowNull: true,
        type: Sequelize.TEXT,
      },
      initialParam: {
        allowNull: true,
        type: Sequelize.TEXT,
      },
      finalTarget: {
        allowNull: true,
        type: Sequelize.TEXT,
      },
      finalTargetParam: {
        allowNull: true,
        type: Sequelize.TEXT,
      },
      planColor: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      participantAge: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      weeklyActivityFrequency: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      activityLevel: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      planSchemaValues: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Campos específicos por tipo de plano (armazenados em JSON)',
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('UserPlans');
  },
};
