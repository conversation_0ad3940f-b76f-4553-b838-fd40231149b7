/* eslint-disable prettier/prettier */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Radar', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      businessAndCareer: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      businessAndCareerObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      moneyAndFinances: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      moneyAndFinancesObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      moneyAndFinances: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      moneyAndFinancesObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      loveAndFamily: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      loveAndFamilyObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      spirituality: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      spiritualityObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      personalDeveloment: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      personalDevelomentObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      helth: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      helthObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      relationship: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      relationshipObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      friendsAndSocial: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      friendsAndSocialObservation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      isCompleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      isCompletedRecommendation: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Radar');
  },
};
