'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Templates', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
      },
      identificationTag: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      type: {
        type: Sequelize.ENUM('email', 'whatsapp', 'sms', 'push', 'custom'),
        allowNull: true,
        defaultValue: 'custom',
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'draft'),
        allowNull: true,
        defaultValue: 'active',
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      parameterizations: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('Templates');
  },
};
