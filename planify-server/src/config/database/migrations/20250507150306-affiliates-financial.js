'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('AffiliatesFinancial', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      affiliateId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Affiliates',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      bankAgency: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      bankName: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      bankAccount: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      pixKey: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('AffiliatesFinancial');
  },
};
