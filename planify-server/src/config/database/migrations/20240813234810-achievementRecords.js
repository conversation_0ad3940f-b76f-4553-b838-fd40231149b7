'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('AchievementRecords', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
        },
      },
      points: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      achievementId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Achievements',
          key: 'id',
        },
      },
      unlockedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      referenceRecord: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null,
      },
      expirationStatus: {
        type: Sequelize.INTEGER(1),
        allowNull: false,
        defaultValue: 0,
      },
      type: {
        type: Sequelize.ENUM('credit', 'debit'),
        allowNull: true,
        defaultValue: null,
      },
      operationHistory: {
        type: Sequelize.ENUM(
          'earnPoints',
          'lostPoints',
          'expirePoints',
          'redemptionPoints',
        ),
        allowNull: true,
        defaultValue: null,
      },
      expirationDate: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('AchievementRecords');
  },
};
