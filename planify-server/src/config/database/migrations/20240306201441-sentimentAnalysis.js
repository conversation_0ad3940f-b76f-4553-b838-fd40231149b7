/* eslint-disable prettier/prettier */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('SentimentAnalysis', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      thermometer: {
        type: Sequelize.ENUM(
          'laughing',
          'happy',
          'neutral',
          'confused',
          'sad',
          'crying',
          'angry',
          'no_emotion',
        ),
        allowNull: true,
        defaultValue: null,
      },
      relatedTo: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      influencedTheEmotionalState: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('SentimentAnalysis');
  },
};
