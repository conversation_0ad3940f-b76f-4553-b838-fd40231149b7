'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Negotiations', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM('percentage', 'fixed'),
        allowNull: true,
      },
      value: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      recurrencyType: {
        type: Sequelize.ENUM('limited', 'unlimited'),
        allowNull: true,
        defaultValue: 'limited',
      },
      recurrencyPeriod: {
        type: Sequelize.ENUM('monthly', 'yearly', 'first_payment'),
        allowNull: true,
        defaultValue: 'monthly',
      },
      recurrencyLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active',
      },
      allowDefault: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.createTable('NegotiationAffiliates', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      negotiationId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Negotiations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      affiliateId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Affiliates',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex(
      'NegotiationAffiliates',
      ['negotiationId', 'affiliateId'],
      {
        unique: true,
        name: 'negotiation_affiliate_unique',
      },
    );

    await queryInterface.createTable('NegotiationPaymentPlans', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      negotiationId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Negotiations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      paymentPlanId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'PaymentPlans',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      type: {
        type: Sequelize.ENUM('percentage', 'fixed'),
        allowNull: true,
      },
      value: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      recurrencyType: {
        type: Sequelize.ENUM('limited', 'unlimited'),
        allowNull: true,
      },
      recurrencyPeriod: {
        type: Sequelize.ENUM('monthly', 'yearly', 'first_payment'),
        allowNull: true,
      },
      recurrencyLimit: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex(
      'NegotiationPaymentPlans',
      ['negotiationId', 'paymentPlanId'],
      {
        unique: true,
        name: 'negotiation_paymentplan_unique',
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('NegotiationPaymentPlans');
    await queryInterface.dropTable('NegotiationAffiliates');
    await queryInterface.dropTable('Negotiations');
  },
};
