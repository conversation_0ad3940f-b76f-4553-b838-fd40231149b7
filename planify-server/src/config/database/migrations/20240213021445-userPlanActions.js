'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('UserPlanActions', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      planId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'UserPlans',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      description: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      startedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      duration: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      hasHealthRisk: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      acceptedTerms: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      endAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      isBlocked: {
        type: Sequelize.TINYINT,
        allowNull: true,
        defaultValue: null,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('UserPlanActions');
  },
};
