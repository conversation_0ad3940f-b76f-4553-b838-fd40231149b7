/* eslint-disable prettier/prettier */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('PlanActionDays', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      weekId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'PlanActionWeeks',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      userPlanId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'UserPlans',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        defaultValue: null,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'Users',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        defaultValue: null,
      },
      activityDescription: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      activityDetails: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      observation: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      timeActivity: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      activityStatus: {
        type: Sequelize.ENUM(
          'loading',
          'pending',
          'done',
          'in_progress',
          'partially_completed',
        ),
        allowNull: false,
        defaultValue: 'loading',
      },
      activityColor: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      date: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('PlanActionDays');
  },
};
