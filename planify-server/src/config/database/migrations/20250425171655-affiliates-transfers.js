'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Transfers', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      affiliateId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Affiliates',
          key: 'id',
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
      },
      amount: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM(
          'pending',
          'approved',
          'paid',
          'rejected',
          'failed',
          'canceled',
          'refunded',
        ),
        allowNull: false,
        defaultValue: 'pending',
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      paymentId: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      referralCode: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      paymentReceiptUrl: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Transfers');
  },
};
