/* eslint-disable prettier/prettier */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { SentimentAnalysisService } from './sentiment-analysis.service';
import { CreateSentimentAnalysisDto } from './dto/create-sentiment-analysis.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { CreateSentimentHistorysDto } from './dto/create-sentiment-history.dto';

@ApiTags('Sentiment Analysis')
@Controller('sentiment-analysis')
export class SentimentAnalysisController {
  constructor(
    private readonly sentimentAnalysisService: SentimentAnalysisService,
  ) {}

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createSentimentAnalysisDto: CreateSentimentAnalysisDto) {
    return this.sentimentAnalysisService.create(createSentimentAnalysisDto);
  }

  @UseGuards(AuthGuard)
  @Patch('/history')
  history(
    @Body() sentimentHistory: CreateSentimentHistorysDto,
    @Req() token: string,
  ) {
    return this.sentimentAnalysisService.createHistory(sentimentHistory, token);
  }

  @UseGuards(AuthGuard)
  @Get('/history')
  getHistory(@Req() token: string) {
    return this.sentimentAnalysisService.getHistory(token);
  }

  @UseGuards(AuthGuard)
  @Get(':userId')
  findByUser(
    @Param('userId') userId: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ) {
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    return this.sentimentAnalysisService.findByUser(
      userId,
      pageNumber,
      limitNumber,
    );
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.sentimentAnalysisService.remove(id);
  }

  @UseGuards(AuthGuard)
  @Patch('/update/:id')
  update(
    @Req() token: string,
    @Body() updateSentimentAnalysisDto: CreateSentimentHistorysDto,
  ) {
    return this.sentimentAnalysisService.updateHistoryById(
      token,
      updateSentimentAnalysisDto,
    );
  }
}
