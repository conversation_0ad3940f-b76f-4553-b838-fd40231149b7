import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';
import { EThermometer } from 'src/data/@types/EThermometer';

export class CreateSentimentAnalysisDto {
  @ApiProperty()
  @IsString()
  userId: string;

  @ApiProperty({ enum: EThermometer, enumName: 'EThermometer' })
  @IsEnum(EThermometer)
  @IsOptional()
  thermometer: EThermometer | null;

  @ApiProperty()
  @IsOptional()
  relatedTo: string | null;

  @ApiProperty()
  @IsBoolean()
  influencedTheEmotionalState: boolean;
}
