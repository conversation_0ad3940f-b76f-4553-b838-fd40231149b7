import {
  Table,
  Column,
  <PERSON>,
  PrimaryKey,
  DataType,
  Foreign<PERSON>ey,
  CreatedAt,
  UpdatedAt,
  BelongsTo,
  <PERSON><PERSON>ult, // Importar BelongsTo
} from 'sequelize-typescript';
import { SentimentAnalysis } from './sentiment-analysis.entity';
import { SentimentHistory } from './sentiment-history.entity';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'SentimentAnalysisHistory',
})
export class SentimentAnalysisHistory extends Model<SentimentAnalysisHistory> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({
    type: DataType.UUID,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @ForeignKey(() => SentimentAnalysis)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  sentimentAnalysisId: string;

  @BelongsTo(() => SentimentAnalysis, { onDelete: 'CASCADE' })
  sentimentAnalysis: SentimentAnalysis;

  @ForeignKey(() => SentimentHistory)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  sentimentHistoryId: string;

  @BelongsTo(() => SentimentHistory, { onDelete: 'CASCADE' })
  sentimentHistory: SentimentHistory;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;
}
