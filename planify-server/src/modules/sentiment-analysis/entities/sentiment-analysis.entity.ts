import {
  Model,
  Column,
  DataType,
  ForeignKey,
  Table,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'SentimentAnalysis',
})
export class SentimentAnalysis extends Model<SentimentAnalysis> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  thermometer: string | null;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  relatedTo: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  influencedTheEmotionalState: boolean;
}
