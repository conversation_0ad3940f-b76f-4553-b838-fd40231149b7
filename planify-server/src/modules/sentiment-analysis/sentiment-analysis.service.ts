/* eslint-disable prettier/prettier */
import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateSentimentAnalysisDto } from './dto/create-sentiment-analysis.dto';
import { InjectModel } from '@nestjs/sequelize';
import { SentimentAnalysis } from './entities/sentiment-analysis.entity';
import { User } from '../users/entities/user.entity';
import { SentimentHistory } from './entities/sentiment-history.entity';
import { formatInTimeZoneBrazil, getUserId } from 'src/utils';
import { CreateSentimentHistorysDto } from './dto/create-sentiment-history.dto';
import { Op } from 'sequelize';
import { SentimentAnalysisHistory } from './entities/sentiment-analysis-history.entity';
import { subMinutes } from 'date-fns';

@Injectable()
export class SentimentAnalysisService {
  constructor(
    @InjectModel(SentimentAnalysis)
    private sentimentsModel: typeof SentimentAnalysis,
    @InjectModel(SentimentHistory)
    private sentimentsHistoryModel: typeof SentimentHistory,
    @InjectModel(SentimentAnalysisHistory)
    private sentimentsAnalysisHistoryModel: typeof SentimentAnalysisHistory,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async createHistory(sentimentDto: CreateSentimentHistorysDto, token: string) {
    try {
      const loggedUserId = getUserId(token);

      if (!loggedUserId) {
        throw new NotFoundException('Invalid user');
      }

      const user = await this.userModel.findByPk(loggedUserId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (!sentimentDto.thermometer) {
        throw new NotFoundException('Thermometer value is required');
      }

      const currentDate = new Date();
      const fortyFiveMinutesAgo = new Date(Date.now() - 45 * 60 * 1000);
      // const oneMinuteAgo = new Date(Date.now() - 1 * 60 * 1000);

      const oneMinuteAgo = subMinutes(
        new Date(formatInTimeZoneBrazil(new Date())),
        1,
      );

      // Verificar se já existe um registro recente
      const existingSentiment = await this.sentimentsHistoryModel.findOne({
        where: {
          userId: loggedUserId,
          createdAt: { [Op.gt]: fortyFiveMinutesAgo },
        },
        order: [['createdAt', 'DESC']],
      });

      if (existingSentiment) {
        // Verificar se o registro existente foi criado há menos de um minuto
        if (existingSentiment.createdAt > oneMinuteAgo) {
          // Atualizar o registro existente
          existingSentiment.thermometer = sentimentDto.thermometer.toString();
          existingSentiment.createdAt = formatInTimeZoneBrazil(
            currentDate,
          ) as unknown as Date;
          await existingSentiment.save();
          return existingSentiment;
        }
      }

      // Criar um novo registro
      const newSentiment = await this.sentimentsHistoryModel.create({
        userId: loggedUserId,
        thermometer: sentimentDto.thermometer.toString(),
        createdAt: formatInTimeZoneBrazil(currentDate) as unknown as Date,
      });

      await this.sentimentsAnalysisHistoryModel.create({
        userId: loggedUserId,
        sentimentAnalysisId: null,
        sentimentHistoryId: newSentiment.id,
        createdAt: formatInTimeZoneBrazil(currentDate) as unknown as Date,
      });

      return newSentiment;
    } catch (error) {
      // Lidar com erros
      console.error('Error in createHistory:', error.message || error);
      throw new NotFoundException('Failed to create sentiment history');
    }
  }

  async getHistory(token: string) {
    const loggedUser = getUserId(token);

    const user = await this.userModel.findOne({
      where: { id: loggedUser },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const currentDay = new Date();
    currentDay.setHours(0, 0, 0, 0);

    const sentiment = await this.sentimentsAnalysisHistoryModel.findOne({
      where: {
        createdAt: { [Op.gte]: formatInTimeZoneBrazil(currentDay) },
      },
      include: [
        {
          model: this.sentimentsHistoryModel,
          where: {
            userId: loggedUser,
          },
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return sentiment;
  }

  async create(sentimentDto: CreateSentimentAnalysisDto) {
    const currentDate = new Date();

    try {
      const user = await this.userModel.findByPk(sentimentDto?.userId);

      if (!user) {
        throw new Error('User not found');
      }

      const transaction =
        await this.sentimentsAnalysisHistoryModel.sequelize.transaction();

      try {
        const sentimentInfo = await this.sentimentsModel.create(
          {
            ...sentimentDto,
            thermometer: sentimentDto?.thermometer?.toString() ?? null,
            createdAt: formatInTimeZoneBrazil(currentDate) as unknown as Date,
          },
          { transaction },
        );

        let sentimentHistory = null;

        if (
          sentimentDto.thermometer &&
          sentimentDto.thermometer !== 'no_emotion'
        ) {
          sentimentHistory = await this.sentimentsHistoryModel.create(
            {
              userId: sentimentDto.userId,
              thermometer: sentimentDto.thermometer.toString(),
              createdAt: formatInTimeZoneBrazil(currentDate) as unknown as Date,
            },
            { transaction },
          );
        }

        await this.sentimentsAnalysisHistoryModel.create(
          {
            userId: sentimentDto.userId,
            sentimentAnalysisId: sentimentInfo.id,
            sentimentHistoryId: sentimentHistory?.id,
            createdAt: formatInTimeZoneBrazil(currentDate) as unknown as Date,
          },
          { transaction },
        );

        await transaction.commit();

        return sentimentInfo;
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  async findByUser(userId: string, page: number, limit: number) {
    const user = await this.userModel.findByPk(userId);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const offset = (page - 1) * limit;

    const { rows: sentimentsList, count: totalItems } =
      await this.sentimentsAnalysisHistoryModel.findAndCountAll({
        where: { userId },
        include: [
          {
            model: this.sentimentsModel,
            attributes: ['influencedTheEmotionalState', 'relatedTo'],
          },
          {
            model: this.sentimentsHistoryModel,
            attributes: ['thermometer'],
          },
        ],
        attributes: { exclude: ['userId', 'updatedAt'] },
        order: [['createdAt', 'DESC']],
        limit,
        offset,
      });

    return {
      currentPage: page,
      totalPages: Math.ceil(totalItems / limit),
      totalItems,
      data: sentimentsList,
    };
  }

  async remove(id: string) {
    const sentimentAnalysisHistory =
      await this.sentimentsAnalysisHistoryModel.findByPk(id);

    if (!sentimentAnalysisHistory) {
      throw new NotFoundException('Sentiment not found');
    }

    const { sentimentAnalysisId, sentimentHistoryId } =
      sentimentAnalysisHistory;

    // Start a transaction to ensure atomicity
    const transaction =
      await this.sentimentsAnalysisHistoryModel.sequelize.transaction();

    try {
      // Delete the sentiment analysis history entry first
      await this.sentimentsAnalysisHistoryModel.destroy({
        where: { id },
        transaction,
      });

      // Delete the related sentiment analysis entry
      if (sentimentAnalysisId) {
        await this.sentimentsModel.destroy({
          where: { id: sentimentAnalysisId },
          transaction,
        });
      }

      // Delete the related sentiment history entry
      if (sentimentHistoryId) {
        await this.sentimentsHistoryModel.destroy({
          where: { id: sentimentHistoryId },
          transaction,
        });
      }

      // Commit the transaction
      await transaction.commit();

      return sentimentAnalysisHistory;
    } catch (error) {
      // Rollback the transaction in case of error
      await transaction.rollback();
      throw error;
    }
  }

  // async updateHistoryById(
  //   id: string,
  //   token: string,
  //   sentimentDto: CreateSentimentHistorysDto,
  // ) {
  //   const loggedUserId = getUserId(token);

  //   console.log('loggedUserId', loggedUserId);
  //   console.log('sentimentDto', sentimentDto);
  //   console.log('id', id);
  //   console.log('token', token);

  //   if (!loggedUserId) {
  //     throw new NotFoundException('Invalid user');
  //   }

  //   const user = await this.userModel.findByPk(loggedUserId);
  //   if (!user) {
  //     throw new NotFoundException('User not found');
  //   }

  //   const sentimentHistory = await this.sentimentsHistoryModel.findByPk(id);

  //   if (!sentimentHistory) {
  //     throw new NotFoundException('Sentiment history not found');
  //   }

  //   sentimentHistory.thermometer = sentimentDto.thermometer.toString();
  //   await sentimentHistory.save();

  //   return sentimentHistory;
  // }

  async updateHistoryById(
    token: string,
    sentimentDto: CreateSentimentHistorysDto,
  ) {
    const loggedUserId = getUserId(token);

    if (!loggedUserId) {
      console.error('Invalid user ID from token');
      throw new NotFoundException('Invalid user');
    }

    const user = await this.userModel.findByPk(loggedUserId);
    if (!user) {
      console.error('User not found with ID:', loggedUserId);
      throw new NotFoundException('User not found');
    }

    const sentimentHistory = await this.sentimentsHistoryModel.findOne({
      where: { userId: loggedUserId },
      order: [['createdAt', 'DESC']],
    });

    if (!sentimentHistory) {
      console.error('Sentiment history not found for user ID:', loggedUserId);
      throw new NotFoundException('Sentiment history not found');
    }

    sentimentHistory.thermometer = sentimentDto.thermometer.toString();
    await sentimentHistory.save();

    console.log('Sentiment history updated for user ID:', loggedUserId);
    return sentimentHistory;
  }
}
