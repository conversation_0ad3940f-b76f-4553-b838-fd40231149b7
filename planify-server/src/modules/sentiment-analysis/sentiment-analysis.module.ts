import { <PERSON><PERSON><PERSON>, forwardRef } from '@nestjs/common';
import { SentimentAnalysisService } from './sentiment-analysis.service';
import { SentimentAnalysisController } from './sentiment-analysis.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { SentimentAnalysis } from './entities/sentiment-analysis.entity';
import { User } from '../users/entities/user.entity';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { SentimentHistory } from './entities/sentiment-history.entity';
import { SentimentAnalysisHistory } from './entities/sentiment-analysis-history.entity';

@Module({
  imports: [
    SequelizeModule.forFeature([
      SentimentAnalysis,
      SentimentHistory,
      User,
      SentimentAnalysisHistory,
    ]),
    forwardRef(() => AuthModule),
    JwtModule,
  ],
  controllers: [SentimentAnalysisController],
  providers: [SentimentAnalysisService],
})
export class SentimentAnalysisModule {}
