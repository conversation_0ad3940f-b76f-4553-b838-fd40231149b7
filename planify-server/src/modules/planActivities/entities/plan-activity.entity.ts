import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { TActivityStatus } from 'src/data/@types/planAction';
import { SubCategory } from 'src/modules/subCategory/entities/subCategory.entity';
import { UserPlan } from 'src/modules/user-plans/entities/user-plan.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { PlanWeek } from 'src/modules/weeks/entities/plan-weeks.entity';

@Table({
  tableName: 'PlanActionDays',
})
export class PlanActivity extends Model<PlanActivity> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => PlanWeek)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  weekId: string;

  @BelongsTo(() => PlanWeek, {
    onDelete: 'CASCADE',
    hooks: true,
  })
  planWeek: PlanWeek;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @ForeignKey(() => UserPlan)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userPlanId: string;

  @BelongsTo(() => UserPlan)
  userPlan: UserPlan;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  activityDescription: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  activityDetails: any;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  observation: string;

  @ForeignKey(() => SubCategory)
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  category: string;

  @BelongsTo(() => SubCategory)
  subCategory: SubCategory;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  timeActivity: string;

  @Column({
    type: DataType.STRING,
    defaultValue: 'loading',
  })
  activityStatus: TActivityStatus;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  date: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  activityColor: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isBlocked: boolean | null;
}
