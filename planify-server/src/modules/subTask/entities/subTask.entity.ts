import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { TTaskStatus } from 'src/data/@types/taskStatus';
import { Task } from 'src/modules/task/entities/task.entity';

@Table({
  tableName: 'SubTask',
})
export class SubTask extends Model<SubTask> {
  @ForeignKey(() => Task)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  taskId: string;

  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.STRING,
    defaultValue: 'pending',
  })
  status: TTaskStatus;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  position: number;
}
