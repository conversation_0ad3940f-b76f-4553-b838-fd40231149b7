/* eslint-disable prettier/prettier */
import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Patch,
  Delete,
  Param,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { ApiTags } from '@nestjs/swagger';
import { CreateTaskDto } from './dto/create.subTask.dto';
import { SubTaskService } from './task.service';
import { UpdateStatusSubTaskDto } from './dto/updateStatus.subTask.dto';

@ApiTags('Tasks')
@Controller('subTask')
export class SubTaskController {
  constructor(private readonly subTaskService: SubTaskService) {}

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() taskDto: CreateTaskDto) {
    return this.subTaskService.create(taskDto);
  }

  @UseGuards(AuthGuard)
  @Get('all/:id')
  findAll(@Param('id') id: string) {
    return this.subTaskService.findAll(id);
  }

  @UseGuards(AuthGuard)
  @Post('updateTaskStatus')
  updateSubtaskStatus(@Body() updateStatusSubTaskDto: UpdateStatusSubTaskDto) {
    return this.subTaskService.updateSubtaskStatus(updateStatusSubTaskDto);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.subTaskService.remove(id);
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() taskDto: CreateTaskDto) {
    return this.subTaskService.update(id, taskDto);
  }

  @UseGuards(AuthGuard)
  @Patch('/move/:id')
  async moveTask(
    @Param('id') id: string,
    @Body('newPosition') newPosition: number,
  ) {
    return this.subTaskService.moveSubTask(id, newPosition);
  }
}
