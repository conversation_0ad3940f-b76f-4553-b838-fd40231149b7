/* eslint-disable prettier/prettier */
import { Injectable, NotFoundException } from '@nestjs/common';
import { SubTask } from './entities/subTask.entity';
import { InjectModel } from '@nestjs/sequelize';
import { CreateTaskDto } from './dto/create.subTask.dto';
import { UpdateStatusSubTaskDto } from './dto/updateStatus.subTask.dto';
import { Op } from 'sequelize';

@Injectable()
export class SubTaskService {
  constructor(
    @InjectModel(SubTask)
    private taskSubModel: typeof SubTask,
  ) {}

  async create(taskDto: CreateTaskDto) {
    await this.taskSubModel.increment('position', {
      by: 1,
      where: { taskId: taskDto.taskId },
    });

    return await this.taskSubModel.create({
      ...taskDto,
      position: 1,
    });
  }

  async findAll(taskId: string) {
    return await this.taskSubModel.findAll({
      where: {
        taskId,
      },
      order: [['position', 'ASC']],
    });
  }

  findOne(id: number) {
    return `This action returns a #${id} task`;
  }

  async update(id: string, taskDto: CreateTaskDto) {
    const task = await this.taskSubModel.findByPk(id);

    if (!task) {
      throw new NotFoundException('Tarefa não encontrada');
    }

    task.title = taskDto.title;
    await task.save();

    return task;
  }

  async remove(id: string) {
    const task = await this.taskSubModel.findByPk(id);

    if (!task) {
      throw new NotFoundException('Tarefa não encontrada');
    }

    await task.destroy();
    return task;
  }

  async updateSubtaskStatus(updateStatusSubTaskDto: UpdateStatusSubTaskDto) {
    const { id, status } = updateStatusSubTaskDto;

    if (!id) {
      throw new NotFoundException('ID da sub-tarefa é obrigatório');
    }

    const subTask = await this.taskSubModel.findByPk(id);

    if (!subTask) {
      throw new NotFoundException('Sub-tarefa não encontrada');
    }

    subTask.status = status;
    await subTask.save();

    return subTask;
  }

  async moveSubTask(id: string, newPosition: number) {
    const subTask = await this.taskSubModel.findByPk(id);

    if (!subTask) {
      throw new NotFoundException('Sub-tarefa não encontrada');
    }

    const { taskId } = subTask;

    const currentPosition = subTask.position;

    // Atualizar as posições das outras notas
    if (newPosition < currentPosition) {
      await this.taskSubModel.increment('position', {
        by: 1,
        where: {
          taskId,
          position: {
            [Op.gte]: newPosition,
            [Op.lt]: currentPosition,
          },
        },
      });
    } else {
      await this.taskSubModel.decrement('position', {
        by: 1,
        where: {
          taskId,
          position: {
            [Op.gt]: currentPosition,
            [Op.lte]: newPosition,
          },
        },
      });
    }

    subTask.position = newPosition;
    await subTask.save();

    return subTask;
  }
}
