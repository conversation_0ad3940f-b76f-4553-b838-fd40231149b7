import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';

import { User } from '../users/entities/user.entity';
import { SubTaskController } from './subTask.controller';
import { SubTaskService } from './task.service';
import { SubTask } from './entities/subTask.entity';

@Module({
  imports: [
    SequelizeModule.forFeature([SubTask, User]),
    forwardRef(() => AuthModule),
    JwtModule,
  ],
  controllers: [SubTaskController],
  providers: [SubTaskService],
  exports: [SubTaskService],
})
export class SubTaskModule {}
