import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CreateAffiliateDto,
  UpdateAffiliateDto,
  CreateAffiliateInfoDto,
} from './dto/affiliate.dto';
import { Affiliate } from './entities/affiliate.entity';
import { AffiliateInfo } from './entities/affiliate-info.entity';
import { Referral } from './entities/referral.entity';
import { Analytic } from './entities/analytic.entity';
import { createModuleLogger } from 'src/utils/moduleLogger';
import {
  CreateReferralDto,
  UpdateReferralDto,
  CreateAnalyticDto,
} from './dto/transfer-referral.dto';
import { hash } from 'bcrypt';
import { Op } from 'sequelize';
import { envs } from 'src/utils/envsProxy';
import { getAffiliateId, getUserId } from 'src/utils';
import { UsersService } from '../users/users.service';
import { paginateResults } from 'src/utils/pagination';
import { AffiliateFinancialService } from '../affiliate-financial/affiliate-financial.service';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class AffiliatesService {
  private readonly logger = createModuleLogger(AffiliatesService.name);
  private readonly CACHE_TTL = 48 * 60 * 60;

  constructor(
    @InjectModel(Affiliate)
    private readonly affiliateModel: typeof Affiliate,
    @InjectModel(AffiliateInfo)
    private readonly affiliateInfoModel: typeof AffiliateInfo,
    @InjectModel(Referral)
    private readonly referralModel: typeof Referral,
    @InjectModel(Analytic)
    private readonly analyticModel: typeof Analytic,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => AffiliateFinancialService))
    private readonly affiliateFinancialService: AffiliateFinancialService,
    private readonly redisService: RedisService,
  ) {}

  private async clearAffiliateCaches(affiliateId: string): Promise<void> {
    setTimeout(async () => {
      try {
        await this.redisService.delByPattern(`affiliate:*:${affiliateId}*`);
        this.logger.info(
          `Cache limpo em background para o afiliado ${affiliateId}`,
        );
      } catch (error) {
        this.logger.error(
          `Erro ao limpar cache em background para o afiliado ${affiliateId}:`,
          error,
        );
      }
    }, 0);
  }

  private async updateAffiliateCache(affiliateId: string): Promise<void> {
    setTimeout(async () => {
      try {
        if (this.affiliateFinancialService) {
          await this.affiliateFinancialService['updateCacheAfterChanges'](
            affiliateId,
          );
          this.logger.info(
            `Cache do afiliado ${affiliateId} atualizado em background`,
          );
        }

        const referralsCacheKey = this.getReferralsCacheKey(affiliateId);
        const referralsData = await this.fetchAffiliateLinkData(affiliateId);
        await this.redisService.update(
          referralsCacheKey,
          referralsData,
          this.CACHE_TTL,
        );
      } catch (error) {
        this.logger.error(
          `Erro ao atualizar cache em background para o afiliado ${affiliateId}:`,
          error,
        );
      }
    }, 0);
  }

  private getReferralsCacheKey(affiliateId: string): string {
    return `affiliate:referrals:${affiliateId}`;
  }

  async findByEmail(email: string, includePassword = false) {
    const options: any = {
      where: { email },
    };

    if (!includePassword) {
      options.attributes = {
        exclude: ['password'],
      };
    }

    const affiliate = await this.affiliateModel.findOne(options);

    return affiliate;
  }

  async findByDocument(document: string, includePassword = false) {
    const options: any = {
      where: { document },
    };

    if (!includePassword) {
      options.attributes = {
        exclude: ['password'],
      };
    }

    const affiliate = await this.affiliateModel.findOne(options);

    return affiliate;
  }

  async findAll(status?: string, page: number = 1, limit: number = 10) {
    try {
      const whereConditions = status ? { status } : {};

      const paginatedResults = await paginateResults(
        { page, limit },
        {
          model: this.affiliateModel,
          whereConditions,
          orderOptions: [['createdAt', 'DESC']],
          attributes: {
            exclude: ['password'],
          },
        },
      );

      if (!paginatedResults.data || paginatedResults.data.length === 0) {
        return paginatedResults;
      }

      const affiliatesWithAnalytics = [];

      for (const affiliate of paginatedResults.data) {
        const [
          clicksCount,
          registrationsCount,
          subscriptionsCount,
          paymentsCount,
          paymentsSum,
        ] = await Promise.all([
          this.analyticModel.count({
            where: { affiliateId: affiliate.id, metricType: 'click' },
          }),
          this.analyticModel.count({
            where: { affiliateId: affiliate.id, metricType: 'registration' },
          }),
          this.analyticModel.count({
            where: { affiliateId: affiliate.id, metricType: 'subscription' },
          }),
          this.analyticModel.count({
            where: { affiliateId: affiliate.id, metricType: 'payment' },
          }),
          this.analyticModel.sum('metricValue', {
            where: { affiliateId: affiliate.id, metricType: 'payment' },
          }),
        ]);

        const affiliateJson = affiliate.toJSON();
        const affiliateWithAnalytics = {
          ...affiliateJson,
          clicks: clicksCount,
          registrations: registrationsCount,
          subscriptions: subscriptionsCount,
          payments: paymentsCount,
          revenue: paymentsSum || 0,
        };
        affiliatesWithAnalytics.push(affiliateWithAnalytics);
      }

      paginatedResults.data = affiliatesWithAnalytics;

      return paginatedResults;
    } catch (error) {
      this.logger.error(`Erro ao buscar afiliados: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async findOne(id: string, includeDeleted = false) {
    try {
      const affiliate = await this.affiliateModel.findByPk(id, {
        attributes: {
          exclude: ['password'],
        },
        paranoid: !includeDeleted,
      });

      if (!affiliate) {
        this.logger.error(`Afiliado não encontrado: ${id}`);
        throw new NotFoundException(`Afiliado não encontrado ${id}`);
      }

      const info = await this.affiliateInfoModel.findOne({
        where: { id: affiliate.id },
      });

      const [
        clicksCount,
        registrationsCount,
        subscriptionsCount,
        paymentsCount,
        paymentsSum,
        referrals,
      ] = await Promise.all([
        this.analyticModel.count({
          where: { affiliateId: id, metricType: 'click' },
        }),
        this.analyticModel.count({
          where: { affiliateId: id, metricType: 'registration' },
        }),
        this.analyticModel.count({
          where: { affiliateId: id, metricType: 'subscription' },
        }),
        this.analyticModel.count({
          where: { affiliateId: id, metricType: 'payment' },
        }),
        this.analyticModel.sum('metricValue', {
          where: { affiliateId: id, metricType: 'payment' },
        }),
        this.referralModel.findAll({
          where: { affiliateId: id },
          order: [['createdAt', 'DESC']],
        }),
      ]);

      if (!referrals || referrals.length === 0) {
        return {
          ...affiliate.toJSON(),
          info: info ? info.toJSON() : null,
          clicks: clicksCount,
          registrations: registrationsCount,
          subscriptions: subscriptionsCount,
          payments: paymentsCount,
          revenue: paymentsSum || 0,
          referrals: [],
        };
      }

      const referralCodes = referrals.map((ref) => ref.referralCode);
      const { referralMetrics } = await this.getReferralMetrics(
        id,
        referralCodes,
      );

      const referralsWithMetrics = this.processReferralsWithMetrics(
        referrals,
        referralMetrics,
      );

      return {
        ...affiliate.toJSON(),
        info: info ? info.toJSON() : null,
        clicks: clicksCount,
        registrations: registrationsCount,
        subscriptions: subscriptionsCount,
        payments: paymentsCount,
        revenue: paymentsSum || 0,
        referrals: referralsWithMetrics,
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar afiliado: ${error.message}`);
      throw error;
    }
  }

  private processReferralsWithMetrics(referrals, referralMetrics) {
    const {
      clicksMap,
      registrationsMap,
      subscriptionsMap,
      paymentsMap,
      revenuesMap,
    } = referralMetrics;

    return referrals.map((referral) => {
      const referralCode = referral.referralCode;
      return {
        ...referral.toJSON(),
        clicks: clicksMap.get(referralCode) || 0,
        registrations: registrationsMap.get(referralCode) || 0,
        subscriptions: subscriptionsMap.get(referralCode) || 0,
        payments: paymentsMap.get(referralCode) || 0,
        revenue: revenuesMap.get(referralCode) || 0,
      };
    });
  }

  async findByCode(code: string) {
    const affiliate = await this.affiliateModel.findOne({
      where: { affiliateCode: code },
    });

    if (!affiliate) {
      this.logger.error(`Código de afiliado não encontrado: ${code}`);
      throw new NotFoundException(`Código de afiliado não encontrado ${code}`);
    }

    return affiliate;
  }

  async createReferral(referralDto: CreateReferralDto) {
    try {
      return await this.referralModel.create(referralDto);
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException(error);
    }
  }

  async generateAffiliateCode() {
    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = String(now.getFullYear()).slice(-2);
    const dateCode = `${month}${year}`;

    const lastAffiliate = await this.affiliateModel.findOne({
      where: {
        affiliateCode: {
          [Op.like]: `REF${dateCode}-%`,
        },
      },
      order: [['createdAt', 'DESC']],
    });

    let nextSequence = 1;
    if (lastAffiliate?.affiliateCode) {
      const lastSequence = parseInt(
        lastAffiliate.affiliateCode.split('-')[1],
        10,
      );
      nextSequence = lastSequence + 1;
    }

    const sequenceCode = String(nextSequence).padStart(4, '0');

    return `REF${dateCode}-${sequenceCode}`;
  }

  async create(affiliateDto: CreateAffiliateDto) {
    try {
      if (!affiliateDto.affiliateCode) {
        affiliateDto.affiliateCode = await this.generateAffiliateCode();
      }

      const hashedPassword = await hash(affiliateDto.password, 10);

      const affiliate = await this.affiliateModel.create({
        ...affiliateDto,
        password: hashedPassword,
      });

      await this.createReferral({
        affiliateId: affiliate.id,
        referralCode: affiliate.affiliateCode,
        referralUrl: `${envs.REFERRAL_URL}sign-up?ref=${affiliate.affiliateCode}`,
      });

      const affiliateResponse = affiliate.get({ plain: true });
      delete affiliateResponse.password;

      return affiliateResponse;
    } catch (error) {
      this.logger.error('Erro ao criar afiliado:', error);
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestException(
          `O usuário com este e-mail ou documento já existe`,
        );
      }
      throw new BadRequestException(`Erro ao criar afiliado: ${error.message}`);
    }
  }

  async createAffiliateInfo(infoDto: CreateAffiliateInfoDto, token) {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        this.logger.error('Tentativa de acesso sem ID de afiliado válido');
        throw new UnauthorizedException('Acesso não autorizado para afiliados');
      }

      const affiliate = await this.affiliateModel.findByPk(affiliateId);
      if (!affiliate) {
        throw new NotFoundException(
          `Afiliado não encontrado para o ID ${affiliateId}`,
        );
      }

      if (infoDto.id) {
        delete infoDto.id;
      }

      const [info, created] = await this.affiliateInfoModel.upsert({
        ...infoDto,
        id: affiliateId,
      });

      this.logger.debug(
        `Informações de afiliado ${created ? 'criadas' : 'atualizadas'} com sucesso para: ${affiliateId}`,
      );

      return info;
    } catch (error) {
      this.logger.error(
        `Erro ao criar/atualizar informações do afiliado: ${error.message}`,
      );
      throw new BadRequestException(
        `Erro ao criar/atualizar informações: ${error.message}`,
      );
    }
  }

  async update(affiliateDto: UpdateAffiliateDto, token) {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        this.logger.warn(
          `Usuário não autorizado ao atualizar dados do afiliado ${affiliateId}`,
        );
        throw new UnauthorizedException({
          message: `Usuário não autorizado ao atualizar dados do afiliado ${affiliateId}`,
        });
      }

      const affiliate = await this.findOne(affiliateId);

      if (!affiliate) {
        throw new NotFoundException({
          message: `Afiliado não encontrado para o ID ${affiliateId}`,
        });
      }

      if (affiliateDto.password) {
        affiliateDto.password = await hash(affiliateDto.password, 10);
      }

      if ('id' in affiliateDto) {
        delete affiliateDto['id'];
      }

      await this.affiliateModel.update(affiliateDto, {
        where: { id: affiliateId },
      });

      const updatedAffiliate = await this.findOne(affiliateId);

      return updatedAffiliate;
    } catch (error) {
      this.logger.error(`Erro ao atualizar afiliado: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async remove(token) {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        this.logger.error('Tentativa de acesso sem ID de afiliado válido');
        throw new UnauthorizedException({
          message: 'Acesso não autorizado para afiliados',
        });
      }

      const affiliate = await this.affiliateModel.findByPk(affiliateId);

      if (!affiliate) {
        throw new NotFoundException({
          message: `Afiliado não encontrado para o ID ${affiliateId}`,
        });
      }

      affiliate.status = 'inactive';
      await affiliate.save();

      await this.affiliateModel.destroy({
        where: { id: affiliateId },
      });

      return { message: 'Conta desativada com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover afiliado: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async approve(id: string, token) {
    try {
      if (!id) {
        this.logger.error('Tentativa de aprovar afiliado sem ID válido');
        throw new BadRequestException({
          message: 'ID do afiliado não fornecido',
        });
      }

      const loggedUser = getUserId(token);

      if (!loggedUser) {
        this.logger.error(
          'Tentativa de aprovar afiliado sem estar autenticado',
        );
        throw new UnauthorizedException({
          message: 'Você precisa estar autenticado para realizar esta ação',
        });
      }

      const user = await this.usersService.findOne(loggedUser);

      if (!user || user.userType !== 'admin') {
        this.logger.error(
          `Usuário ${loggedUser} tentou aprovar afiliado sem permissão de admin`,
        );
        throw new UnauthorizedException({
          message: 'Você não tem permissão para aprovar afiliados',
        });
      }

      const affiliate = await this.affiliateModel.findByPk(id);

      if (!affiliate) {
        this.logger.error(`Afiliado não encontrado com ID: ${id}`);
        throw new NotFoundException({
          message: `Afiliado não encontrado com ID: ${id}`,
        });
      }

      affiliate.status = 'active';
      await affiliate.save();

      this.logger.debug(
        `Afiliado ${id} aprovado com sucesso pelo admin ${loggedUser}`,
      );
      return { message: 'Afiliado aprovado com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao aprovar afiliado: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async removeByAdmin(id: string) {
    try {
      const affiliate = await this.affiliateModel.findByPk(id);

      if (!affiliate) {
        throw new NotFoundException({
          message: `Afiliado não encontrado para o ID ${id}`,
        });
      }

      affiliate.status = 'inactive';
      await affiliate.save();

      await this.affiliateModel.destroy({
        where: { id },
      });

      return { message: 'Afiliado removido com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover afiliado (admin): ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  private async getReferralMetrics(
    affiliateId: string,
    referralCodes: string[],
  ) {
    const [
      clicksCount,
      registrationsCount,
      subscriptionsCount,
      paymentsCount,
      paymentsSum,
      allClicks,
      allRegistrations,
      allSubscriptions,
      allPayments,
      allRevenues,
    ] = await Promise.all([
      this.analyticModel.count({
        where: { affiliateId, metricType: 'click' },
      }),
      this.analyticModel.count({
        where: { affiliateId, metricType: 'registration' },
      }),
      this.analyticModel.count({
        where: { affiliateId, metricType: 'subscription' },
      }),
      this.analyticModel.count({
        where: { affiliateId, metricType: 'payment' },
      }),
      this.analyticModel.sum('metricValue', {
        where: { affiliateId, metricType: 'payment' },
      }),
      this.analyticModel.findAll({
        attributes: [
          'referralCode',
          [this.analyticModel.sequelize.fn('COUNT', '*'), 'count'],
        ],
        where: {
          affiliateId,
          referralCode: { [Op.in]: referralCodes },
          metricType: 'click',
        },
        group: ['referralCode'],
        raw: true,
      }),
      this.analyticModel.findAll({
        attributes: [
          'referralCode',
          [this.analyticModel.sequelize.fn('COUNT', '*'), 'count'],
        ],
        where: {
          affiliateId,
          referralCode: { [Op.in]: referralCodes },
          metricType: 'registration',
        },
        group: ['referralCode'],
        raw: true,
      }),
      this.analyticModel.findAll({
        attributes: [
          'referralCode',
          [this.analyticModel.sequelize.fn('COUNT', '*'), 'count'],
        ],
        where: {
          affiliateId,
          referralCode: { [Op.in]: referralCodes },
          metricType: 'subscription',
        },
        group: ['referralCode'],
        raw: true,
      }),
      this.analyticModel.findAll({
        attributes: [
          'referralCode',
          [this.analyticModel.sequelize.fn('COUNT', '*'), 'count'],
        ],
        where: {
          affiliateId,
          referralCode: { [Op.in]: referralCodes },
          metricType: 'payment',
        },
        group: ['referralCode'],
        raw: true,
      }),
      this.analyticModel.findAll({
        attributes: [
          'referralCode',
          [
            this.analyticModel.sequelize.fn(
              'SUM',
              this.analyticModel.sequelize.col('metricValue'),
            ),
            'sum',
          ],
        ],
        where: {
          affiliateId,
          referralCode: { [Op.in]: referralCodes },
          metricType: 'payment',
        },
        group: ['referralCode'],
        raw: true,
      }),
    ]);

    const clicksMap = new Map(
      allClicks.map((item) => [item.referralCode, parseInt(item['count'])]),
    );
    const registrationsMap = new Map(
      allRegistrations.map((item) => [
        item.referralCode,
        parseInt(item['count']),
      ]),
    );
    const subscriptionsMap = new Map(
      allSubscriptions.map((item) => [
        item.referralCode,
        parseInt(item['count']),
      ]),
    );
    const paymentsMap = new Map(
      allPayments.map((item) => [item.referralCode, parseInt(item['count'])]),
    );
    const revenuesMap = new Map(
      allRevenues.map((item) => [
        item.referralCode,
        parseFloat(item['sum']) || 0,
      ]),
    );

    return {
      globalMetrics: {
        clicks: clicksCount,
        registrations: registrationsCount,
        subscriptions: subscriptionsCount,
        payments: paymentsCount,
        revenue: paymentsSum || 0,
      },
      referralMetrics: {
        clicksMap,
        registrationsMap,
        subscriptionsMap,
        paymentsMap,
        revenuesMap,
      },
    };
  }

  async findAllReferrals(token) {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        throw new UnauthorizedException({
          message: `Token obrigatório para buscar Link de afiliados`,
        });
      }

      const cacheKey = this.getReferralsCacheKey(affiliateId);

      return this.redisService.getOrSet(
        cacheKey,
        () => this.fetchAffiliateLinkData(affiliateId),
        this.CACHE_TTL,
      );
    } catch (error) {
      this.logger.error(`Erro ao buscar links de afiliado: ${error.message}`);

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      return {
        referrals: [],
        clicks: 0,
        registrations: 0,
        subscriptions: 0,
        payments: 0,
        revenue: 0,
      };
    }
  }

  private async fetchAffiliateLinkData(affiliateId: string) {
    try {
      const affiliate = await this.affiliateModel.findByPk(affiliateId, {
        attributes: {
          exclude: ['password'],
        },
      });

      if (!affiliate) {
        throw new NotFoundException(`Afiliado não encontrado ${affiliateId}`);
      }

      const referrals = await this.referralModel.findAll({
        where: {
          affiliateId: affiliateId,
          deletedAt: null,
        },
        include: [
          {
            model: Affiliate,
            attributes: ['id', 'name', 'email', 'affiliateCode'],
          },
        ],
        order: [['createdAt', 'DESC']],
      });

      if (!referrals || referrals.length === 0) {
        return {
          referrals: [],
          clicks: 0,
          registrations: 0,
          subscriptions: 0,
          payments: 0,
          revenue: 0,
        };
      }

      const referralCodes = referrals.map((ref) => ref.referralCode);

      const { globalMetrics, referralMetrics } = await this.getReferralMetrics(
        affiliateId,
        referralCodes,
      );

      const referralsWithMetrics = this.processReferralsWithMetrics(
        referrals,
        referralMetrics,
      );

      return {
        referrals: referralsWithMetrics,
        clicks: globalMetrics.clicks,
        registrations: globalMetrics.registrations,
        subscriptions: globalMetrics.subscriptions,
        payments: globalMetrics.payments,
        revenue: globalMetrics.revenue,
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar links de afiliado: ${error.message}`);

      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      return {
        referrals: [],
        clicks: 0,
        registrations: 0,
        subscriptions: 0,
        payments: 0,
        revenue: 0,
      };
    }
  }

  async createReferralForAffiliate(
    createReferralDto: CreateReferralDto,
    token,
  ) {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        throw new UnauthorizedException({
          message: `Token obrigatório para criar um link de afiliado`,
        });
      }

      const affiliate = await this.affiliateModel.findByPk(affiliateId);

      if (!affiliate) {
        throw new NotFoundException(
          `Afiliado não encontrado com o ID ${affiliateId}`,
        );
      }

      createReferralDto.affiliateId = affiliateId;

      if (!createReferralDto.referralCode) {
        const now = new Date();
        const randomString = Math.random()
          .toString(36)
          .substring(2, 8)
          .toUpperCase();
        createReferralDto.referralCode = `RF${now.getFullYear()}${randomString}`;
      }

      createReferralDto.referralUrl = `${envs.REFERRAL_URL}/register?ref=${createReferralDto.referralCode}`;

      const existingReferral = await this.referralModel.findOne({
        where: {
          referralCode: createReferralDto.referralCode,
          deletedAt: null,
        },
      });

      if (existingReferral) {
        throw new BadRequestException(
          `O Código de afiliado ${createReferralDto.referralCode} já existe`,
        );
      }

      const referral = await this.referralModel.create({
        affiliateId: createReferralDto.affiliateId,
        referralCode: createReferralDto.referralCode,
        referralUrl: createReferralDto.referralUrl,
      });

      await this.updateAffiliateCache(affiliateId);

      return referral;
    } catch (error) {
      this.logger.error(`Erro ao criar Link de afiliado: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async updateReferral(
    id: string,
    updateReferralDto: UpdateReferralDto,
    token,
  ) {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        throw new UnauthorizedException({ message: `Token inválido` });
      }

      const referral = await this.referralModel.findByPk(id);

      if (!referral) {
        throw new NotFoundException({
          message: `Link de afiliado não encontrado com o ID ${id}`,
        });
      }

      if (referral.affiliateId !== affiliateId) {
        throw new UnauthorizedException({
          message:
            'Você não tem permissão para atualizar este link de afiliado',
        });
      }

      if (
        updateReferralDto.affiliateId &&
        updateReferralDto.affiliateId !== affiliateId
      ) {
        delete updateReferralDto.affiliateId;
      }

      if (
        updateReferralDto.referralCode &&
        updateReferralDto.referralCode !== referral.referralCode
      ) {
        const existingReferral = await this.referralModel.findOne({
          where: {
            referralCode: updateReferralDto.referralCode,
            deletedAt: null,
          },
        });

        if (existingReferral) {
          throw new BadRequestException(
            `O Código de afiliado ${updateReferralDto.referralCode} já existe`,
          );
        }

        updateReferralDto.referralUrl = `${envs.REFERRAL_URL}/register?ref=${updateReferralDto.referralCode}`;
      }

      await referral.update(updateReferralDto);

      await this.updateAffiliateCache(affiliateId);

      return referral;
    } catch (error) {
      this.logger.error(`Erro ao atualizar Link de afiliado: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async removeReferral(id: string, token) {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        throw new UnauthorizedException({
          message: `Token obrigatório para remover um link de afiliado`,
        });
      }

      const referral = await this.referralModel.findByPk(id);

      if (!referral) {
        throw new NotFoundException({
          message: `Link de afiliado não encontrado com o ID ${id}`,
        });
      }

      if (referral.affiliateId !== affiliateId) {
        throw new UnauthorizedException({
          message: 'Você não tem permissão para excluir este link de afiliado',
        });
      }

      await referral.destroy();
      await this.updateAffiliateCache(affiliateId);

      return { success: true };
    } catch (error) {
      this.logger.error(`Erro ao remover Link de afiliado: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async findReferralsByAffiliateId(affiliateId: string) {
    try {
      const affiliate = await this.affiliateModel.findByPk(affiliateId);
      if (!affiliate) {
        throw new NotFoundException(`Afiliado não encontrado: ${affiliateId}`);
      }

      const referrals = await this.referralModel.findAll({
        where: { affiliateId },
        order: [['createdAt', 'DESC']],
      });

      if (!referrals || referrals.length === 0) {
        return {
          referrals: [],
          clicks: 0,
          registrations: 0,
          subscriptions: 0,
          payments: 0,
          revenue: 0,
        };
      }

      const referralCodes = referrals.map((ref) => ref.referralCode);

      const { globalMetrics, referralMetrics } = await this.getReferralMetrics(
        affiliateId,
        referralCodes,
      );

      const referralsWithMetrics = this.processReferralsWithMetrics(
        referrals,
        referralMetrics,
      );

      return {
        referrals: referralsWithMetrics,
        clicks: globalMetrics.clicks,
        registrations: globalMetrics.registrations,
        subscriptions: globalMetrics.subscriptions,
        payments: globalMetrics.payments,
        revenue: globalMetrics.revenue,
      };
    } catch (error) {
      this.logger.error(
        `Erro ao buscar referrals do afiliado: ${error.message}`,
      );
      throw error;
    }
  }

  async getAfiliateComission(affiliateId: string) {
    return 1000;
  }

  async createAnalytic(createAnalyticDto: CreateAnalyticDto) {
    try {
      if (!createAnalyticDto.referralCode) {
        throw new BadRequestException('Código de referência é obrigatório');
      }

      const referral = await this.findReferralByCode(
        createAnalyticDto.referralCode,
      );

      if (!referral) {
        this.logger.warn(
          `Tentativa de criar análise com código de referência inválido: ${createAnalyticDto.referralCode}`,
        );
        throw new NotFoundException(
          `Código de referência não encontrado: ${createAnalyticDto.referralCode}`,
        );
      }

      const affiliateId = referral.affiliateId;

      if (
        createAnalyticDto.metricType === 'payment' &&
        !createAnalyticDto.metricValue
      ) {
        createAnalyticDto.metricValue =
          await this.getAfiliateComission(affiliateId);
        this.logger.info(
          `Valor de comissão calculado para payment: ${createAnalyticDto.metricValue}`,
        );
      }

      if (createAnalyticDto.userId && createAnalyticDto.metricType) {
        const existingAnalytic = await this.analyticModel.findOne({
          where: {
            affiliateId,
            userId: createAnalyticDto.userId,
            referralCode: createAnalyticDto.referralCode,
            metricType: createAnalyticDto.metricType,
            ...(createAnalyticDto.metricType === 'payment' &&
            createAnalyticDto.metricValue
              ? { metricValue: createAnalyticDto.metricValue }
              : {}),
            ...(createAnalyticDto.metricType === 'payment' &&
            createAnalyticDto.paymentId
              ? { paymentId: createAnalyticDto.paymentId }
              : {}),
          },
        });

        if (existingAnalytic) {
          this.logger.info(
            `Analítica idempotente encontrada para afiliado=${affiliateId}, userId=${createAnalyticDto.userId}, tipo=${createAnalyticDto.metricType}`,
          );
          return existingAnalytic;
        }
      }

      const metric = await this.analyticModel.create({
        ...createAnalyticDto,
        affiliateId,
      });

      if (createAnalyticDto.metricType === 'payment') {
        await this.affiliateFinancialService.createTransfer({
          affiliateId,
          amount: createAnalyticDto.metricValue,
          userId: createAnalyticDto.userId,
          paymentId: createAnalyticDto.paymentId,
          referralCode: createAnalyticDto.referralCode,
        });

        // Atualizar todos os caches relevantes em background
        setTimeout(async () => {
          try {
            // Atualizar cache de transferências de todos os status
            if (this.affiliateFinancialService) {
              if (this.affiliateFinancialService['updateCacheAfterChanges']) {
                await this.affiliateFinancialService['updateCacheAfterChanges'](
                  affiliateId,
                );
                this.logger.info(
                  `Cache de transferências atualizado após criação de analytics de pagamento para ${affiliateId}`,
                );
              }
            }
          } catch (error) {
            this.logger.error(
              `Erro ao atualizar cache de transferências após analytics: ${error.message}`,
            );
          }
        }, 0);

        await this.updateAffiliateCache(affiliateId);
      } else {
        // Para outros tipos de métricas, atualizar apenas o cache de afiliado
        await this.updateAffiliateCache(affiliateId);
      }

      return metric;
    } catch (error) {
      this.logger.error(`Erro ao criar análise: ${error.message}`);
      throw new BadRequestException(error);
    }
  }

  async findReferralByCode(referralCode: string) {
    try {
      const referral = await this.referralModel.findOne({
        where: {
          referralCode,
          deletedAt: null,
        },
      });

      if (!referral) {
        return null;
      }

      return referral;
    } catch (error) {
      this.logger.error(`Erro ao buscar referral por código: ${error.message}`);
      return null;
    }
  }
}
