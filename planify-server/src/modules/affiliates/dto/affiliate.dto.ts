import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEmail,
  IsEnum,
  MinLength,
  IsOptional,
  IsUUID,
  IsDateString,
} from 'class-validator';

export class CreateAffiliateDto {
  @ApiProperty({
    description: 'Nome do afiliado',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Email do afiliado',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Documento do afiliado (CPF/CNPJ)',
    required: false,
  })
  @IsString()
  @IsOptional()
  document?: string;

  @ApiProperty({
    description: 'Telefone do afiliado',
    required: false,
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({
    description: 'Senha do afiliado',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'Status do afiliado',
    required: false,
    enum: ['active', 'inactive', 'pending'],
    default: 'pending',
  })
  @IsEnum(['active', 'inactive', 'pending'])
  @IsOptional()
  status?: string;

  @ApiProperty({
    description: 'Código de afiliado',
    required: false,
  })
  @IsString()
  @IsOptional()
  affiliateCode?: string;
}

export class UpdateAffiliateDto extends PartialType(CreateAffiliateDto) {}

export class CreateAffiliateInfoDto {
  @ApiProperty({
    description: 'ID do afiliado (obtido automaticamente do token)',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Data de nascimento',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  birthDate?: Date;

  @ApiProperty({
    description: 'Gênero',
    required: false,
  })
  @IsString()
  @IsOptional()
  gender?: string;

  @ApiProperty({
    description: 'CEP',
    required: false,
  })
  @IsString()
  @IsOptional()
  cep?: string;

  @ApiProperty({
    description: 'Estado',
    required: false,
  })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiProperty({
    description: 'Cidade',
    required: false,
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({
    description: 'Endereço',
    required: false,
  })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({
    description: 'Número',
    required: false,
  })
  @IsString()
  @IsOptional()
  locationNumber?: string;

  @ApiProperty({
    description: 'Complemento',
    required: false,
  })
  @IsString()
  @IsOptional()
  locationComplement?: string;

  @ApiProperty({
    description: 'Imagem de perfil (URL)',
    required: false,
  })
  @IsString()
  @IsOptional()
  profileImage?: string;
}

export class UpdateAffiliateInfoDto extends PartialType(
  CreateAffiliateInfoDto,
) {}

export class AffiliateAuthDto {
  @ApiProperty({
    description: 'Email do afiliado',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Senha do afiliado',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}
