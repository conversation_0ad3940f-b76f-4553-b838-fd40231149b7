import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsUUID,
  IsNumber,
  IsEnum,
  Min,
  IsInt,
  IsOptional,
} from 'class-validator';

export class CreateTransferDto {
  @ApiProperty({
    description: 'ID do afiliado',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  affiliateId: string;

  @ApiProperty({
    description: 'Valor da transferência',
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(0.01)
  amount: number;

  @ApiProperty({
    description: 'Status da transferência',
    required: false,
    enum: [
      'pending',
      'approved',
      'paid',
      'rejected',
      'failed',
      'canceled',
      'refunded',
    ],
    default: 'pending',
  })
  @IsEnum([
    'pending',
    'approved',
    'paid',
    'rejected',
    'failed',
    'canceled',
    'refunded',
  ])
  @IsNotEmpty()
  status: string;
}

export class UpdateTransferDto extends PartialType(CreateTransferDto) {}

export class CreateReferralDto {
  @ApiProperty({
    description: 'ID do afiliado (preenchido automaticamente)',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  affiliateId?: string;

  @ApiProperty({
    description: 'Código de referência',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  referralCode: string;

  @ApiProperty({
    description: 'URL de referência (preenchida automaticamente)',
    required: false,
  })
  @IsString()
  @IsOptional()
  referralUrl?: string;
}

export class UpdateReferralDto extends PartialType(CreateReferralDto) {
  @ApiProperty({
    description: 'Data de inativação do referral',
    required: false,
    type: Date,
    nullable: true,
  })
  deletedAt?: Date;
}

export class CreateAnalyticDto {
  @ApiProperty({
    description:
      'ID do afiliado (opcional, preenchido automaticamente via referralCode)',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  affiliateId?: string;

  @ApiProperty({
    description:
      'Código do afiliado (usado para identificar origem da métrica)',
    required: false,
    example: 'INSTA_12346',
  })
  @IsString()
  @IsOptional()
  referralCode?: string;

  @ApiProperty({
    description:
      'ID do usuário (obrigatório para métricas do tipo registration e subscription)',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description:
      'ID do pagamento (obrigatório apenas para métricas do tipo payment)',
    required: false,
    example: '02a2bb53-515c-4ae2-b78a-c78098fe5657',
  })
  @IsUUID()
  @IsOptional()
  paymentId?: string;

  @ApiProperty({
    description: 'Tipo de métrica',
    enum: ['click', 'registration', 'subscription', 'payment'],
    example: 'click',
    required: true,
  })
  @IsEnum(['click', 'registration', 'subscription', 'payment'])
  @IsNotEmpty()
  metricType: string;

  @ApiProperty({
    description:
      'Valor da métrica (obrigatório apenas para payment, para outros tipos é definido automaticamente ou pode ser nulo)',
    example: 1690.0,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  metricValue?: number;
}
