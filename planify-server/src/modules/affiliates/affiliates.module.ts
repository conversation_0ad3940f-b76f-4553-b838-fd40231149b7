import { Module, forwardRef } from '@nestjs/common';
import { AffiliatesService } from './affiliates.service';
import { AffiliatesController } from './affiliates.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { JwtModule } from '@nestjs/jwt';
import { Affiliate } from './entities/affiliate.entity';
import { AffiliateInfo } from './entities/affiliate-info.entity';
import { Referral } from './entities/referral.entity';
import { Analytic } from './entities/analytic.entity';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { AffiliateFinancialModule } from '../affiliate-financial/affiliate-financial.module';
import { RedisModule } from '../redis/redis.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Affiliate, AffiliateInfo, Referral, Analytic]),
    JwtModule,
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    forwardRef(() => AffiliateFinancialModule),
    RedisModule,
  ],
  controllers: [AffiliatesController],
  providers: [AffiliatesService],
  exports: [AffiliatesService],
})
export class AffiliatesModule {}
