import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AffiliatesService } from './affiliates.service';
import {
  CreateAffiliateDto,
  UpdateAffiliateDto,
  AffiliateAuthDto,
  CreateAffiliateInfoDto,
} from './dto/affiliate.dto';
import { AuthService } from '../auth/auth.service';
import { AuthGuard } from '../auth/auth.guard';
import { Permissions } from '../auth/permissions';
import { PermissionsGuard } from '../auth/permission.guard';
import {
  CreateReferralDto,
  UpdateReferralDto,
  CreateAnalyticDto,
} from './dto/transfer-referral.dto';

@ApiTags('affiliates')
@Controller('affiliates')
export class AffiliatesController {
  constructor(
    private readonly affiliatesService: AffiliatesService,
    private readonly authService: AuthService,
  ) {}

  @Post('auth')
  @ApiOperation({ summary: 'Autenticar afiliado' })
  @ApiResponse({ status: 200, description: 'Afiliado autenticado com sucesso' })
  @ApiResponse({ status: 401, description: 'Credenciais inválidas' })
  @ApiResponse({ status: 404, description: 'Afiliado não encontrado' })
  auth(@Body() authDto: AffiliateAuthDto) {
    return this.authService.authAffiliate(authDto.email, authDto.password);
  }

  @Post()
  @ApiOperation({ summary: 'Criar um novo afiliado' })
  @ApiResponse({ status: 201, description: 'Afiliado criado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(@Body() createAffiliateDto: CreateAffiliateDto) {
    return this.affiliatesService.create(createAffiliateDto);
  }

  @Post('info')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Criar ou atualizar informações de perfil do afiliado',
  })
  @ApiResponse({
    status: 201,
    description: 'Informações criadas/atualizadas com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Afiliado não encontrado' })
  createInfo(
    @Body() createAffiliateInfoDto: CreateAffiliateInfoDto,
    @Req() token,
  ) {
    return this.affiliatesService.createAffiliateInfo(
      createAffiliateInfoDto,
      token,
    );
  }

  @Get()
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Listar todos os afiliados (apenas admin)' })
  @ApiResponse({ status: 200, description: 'Lista de afiliados' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 403, description: 'Sem permissões suficientes' })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status do afiliado',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página',
    type: Number,
  })
  findAll(
    @Query('status') status?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.affiliatesService.findAll(status, page, limit);
  }

  @Get('referrals')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Listar todos os referrals do afiliado autenticado',
  })
  @ApiResponse({ status: 200, description: 'Lista de referrals' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 403, description: 'Sem permissões suficientes' })
  findAllReferrals(@Req() token) {
    return this.affiliatesService.findAllReferrals(token);
  }

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Buscar um afiliado pelo Token' })
  async findOne(@Param('id') id: string) {
    return this.affiliatesService.findOne(id);
  }

  @Post('approve')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Aprovar um afiliado' })
  @ApiResponse({ status: 200, description: 'Afiliado aprovado com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 403, description: 'Sem permissões suficientes' })
  @ApiResponse({ status: 404, description: 'Afiliado não encontrado' })
  approve(@Body('id') id: string, @Req() token) {
    return this.affiliatesService.approve(id, token);
  }

  @Patch()
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Atualizar dados do afiliado autenticado' })
  @ApiResponse({ status: 200, description: 'Afiliado atualizado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Afiliado não encontrado' })
  update(@Body() affiliateDto: UpdateAffiliateDto, @Req() token) {
    return this.affiliatesService.update(affiliateDto, token);
  }

  @Delete()
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Desativar conta do afiliado autenticado' })
  @ApiResponse({ status: 200, description: 'Afiliado removido com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Afiliado não encontrado' })
  remove(@Req() token) {
    return this.affiliatesService.remove(token);
  }

  @Delete('admin/:id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remover um afiliado (Apenas Admin)' })
  @ApiResponse({ status: 200, description: 'Afiliado removido com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 403, description: 'Sem permissões suficientes' })
  @ApiResponse({ status: 404, description: 'Afiliado não encontrado' })
  removeByAdmin(@Param('id') id: string) {
    return this.affiliatesService.removeByAdmin(id);
  }

  @Post('referrals')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Criar um novo link de afiliado',
    description:
      'Cria um novo link de afiliado usando apenas o código de referência informado. Os campos affiliateId e referralUrl são preenchidos automaticamente.',
  })
  @ApiResponse({
    status: 201,
    description: 'Link de afiliado criado com sucesso',
  })
  @ApiResponse({
    status: 400,
    description: 'Código de referência já existe ou é inválido',
  })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  createReferral(@Body() createReferralDto: CreateReferralDto, @Req() token) {
    return this.affiliatesService.createReferralForAffiliate(
      createReferralDto,
      token,
    );
  }

  @Patch('referrals/:id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Atualizar um referral' })
  @ApiResponse({ status: 200, description: 'Referral atualizado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Referral não encontrado' })
  updateReferral(
    @Param('id') id: string,
    @Body() updateReferralDto: UpdateReferralDto,
    @Req() token,
  ) {
    return this.affiliatesService.updateReferral(id, updateReferralDto, token);
  }

  @Delete('referrals/:id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Inativar um referral (soft delete)' })
  @ApiResponse({ status: 200, description: 'Referral inativado com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Referral não encontrado' })
  removeReferral(@Param('id') id: string, @Req() token) {
    return this.affiliatesService.removeReferral(id, token);
  }

  @Get('referrals/:id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Listar todos os referrals de um afiliado específico pelo ID',
  })
  @ApiResponse({ status: 200, description: 'Lista de referrals' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 403, description: 'Sem permissões suficientes' })
  @ApiResponse({ status: 404, description: 'Afiliado não encontrado' })
  findReferralsByAffiliateId(@Param('id') id: string) {
    return this.affiliatesService.findReferralsByAffiliateId(id);
  }

  @Post('analytics')
  @ApiOperation({
    summary: 'Registrar métricas de analytics para um afiliado',
    description:
      'Registra métricas como cliques, registros, assinaturas ou pagamentos relacionados ao programa de afiliados. Para pagamentos (payment), o metricValue e paymentId são obrigatórios. Para os demais tipos, o metricValue pode ser nulo. Para registration, subscription e payment, o userId é obrigatório. O referralCode é utilizado para identificar a origem da métrica e rastrear qual link de referência foi utilizado. Aceita affiliateId ou referralCode para identificar o afiliado.',
  })
  @ApiResponse({ status: 201, description: 'Métrica registrada com sucesso' })
  @ApiResponse({
    status: 400,
    description:
      'Dados inválidos, falta de parâmetros obrigatórios ou registro duplicado',
  })
  @ApiResponse({
    status: 404,
    description: 'Afiliado ou código de referência não encontrado',
  })
  createAnalytic(@Body() createAnalyticDto: CreateAnalyticDto) {
    return this.affiliatesService.createAnalytic(createAnalyticDto);
  }
}
