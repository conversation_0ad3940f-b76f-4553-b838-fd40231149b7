import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  DeletedAt,
} from 'sequelize-typescript';
import { Affiliate } from './affiliate.entity';

@Table({
  tableName: 'Referrals',
  paranoid: true,
})
export class Referral extends Model<Referral> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Affiliate)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  affiliateId: string;

  @BelongsTo(() => Affiliate)
  affiliate: Affiliate;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  referralCode: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  referralUrl: string;

  @DeletedAt
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deletedAt: Date;
}
