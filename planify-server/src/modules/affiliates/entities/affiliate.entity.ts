import {
  Column,
  Model,
  Table,
  DataType,
  HasOne,
  HasMany,
  DeletedAt,
} from 'sequelize-typescript';
import { AffiliateInfo } from './affiliate-info.entity';
import { Referral } from './referral.entity';
import { Transfer } from '../../affiliate-financial/entities/transfer.entity';
import { Analytic } from './analytic.entity';

@Table({
  tableName: 'Affiliates',
  paranoid: true,
})
export class Affiliate extends Model<Affiliate> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    unique: true,
  })
  document: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  password: string;

  @Column({
    type: DataType.ENUM('active', 'inactive', 'pending'),
    allowNull: false,
    defaultValue: 'pending',
  })
  status: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  affiliateCode: string;

  @DeletedAt
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deletedAt: Date;

  @HasOne(() => AffiliateInfo)
  info: AffiliateInfo;

  @HasMany(() => Referral)
  referrals: Referral[];

  @HasMany(() => Transfer, {
    foreignKey: 'affiliateId',
    sourceKey: 'id',
  })
  transfers: Transfer[];

  @HasMany(() => Analytic)
  analytics: Analytic[];
}
