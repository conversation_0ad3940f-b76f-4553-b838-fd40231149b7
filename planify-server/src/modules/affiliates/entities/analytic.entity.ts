import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Affiliate } from './affiliate.entity';

@Table({
  tableName: 'Analytics',
})
export class Analytic extends Model<Analytic> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Affiliate)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  affiliateId: string;

  @BelongsTo(() => Affiliate)
  affiliate: Affiliate;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  referralCode: string;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  paymentId: string;

  @Column({
    type: DataType.ENUM('click', 'registration', 'subscription', 'payment'),
    allowNull: false,
    defaultValue: 'click',
  })
  metricType: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
  })
  metricValue: number;
}
