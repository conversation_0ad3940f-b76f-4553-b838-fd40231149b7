/* eslint-disable prettier/prettier */
import { Controller, Post, Body, UseGuards, Get } from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { SettingsService } from './settings.service';
import { CreateSettingsDto } from './dto/create.settings.dto'; 

@ApiTags('Settings')
@Controller('settings') 
@ApiBearerAuth()
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() settingsDto: CreateSettingsDto) {
    return this.settingsService.create(settingsDto);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.settingsService.findAll();
  }
}
