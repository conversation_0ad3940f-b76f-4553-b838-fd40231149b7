/* eslint-disable prettier/prettier */
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Settings } from './entities/settings.entity';
import { CreateSettingsDto } from './dto/create.settings.dto';

@Injectable()
export class SettingsService {
  constructor(
    @InjectModel(Settings)
    private SettingsModel: typeof Settings,
  ) {

  }

  async create(settingsDto: CreateSettingsDto) {
    try {
      return await this.SettingsModel.create(settingsDto);
    } catch (error) {
      throw new BadRequestException('Error creating settings', error);
    }
  }

  async findAll() {
    try {
      return await this.SettingsModel.findAll();
    } catch (error) {
      throw new BadRequestException('Error finding settings', error);
    }
  }
}
