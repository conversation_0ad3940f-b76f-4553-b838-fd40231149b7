import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { Task } from './entities/task.entity';
import { TaskController } from './task.controller';
import { TaskService } from './task.service';
import { User } from '../users/entities/user.entity';
import { UserPermissionsModule } from '../userPermissions/userPermissions.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Task, User]),
    forwardRef(() => AuthModule),
    forwardRef(() => UserPermissionsModule),
    JwtModule, 
  ],
  controllers: [TaskController],
  providers: [TaskService],
  exports: [TaskService],
})
export class TaskModule {}
