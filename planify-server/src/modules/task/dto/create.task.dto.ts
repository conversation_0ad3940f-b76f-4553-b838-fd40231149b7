import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class CreateTaskDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  file?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  reminderDatetime?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  completionDate?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description?: string;
}
