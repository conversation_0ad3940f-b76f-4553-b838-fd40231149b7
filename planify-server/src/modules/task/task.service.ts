/* eslint-disable prettier/prettier */
import { Injectable, NotFoundException } from '@nestjs/common';
import { Task } from './entities/task.entity';
import { InjectModel } from '@nestjs/sequelize';
import { CreateTaskDto } from './dto/create.task.dto';
import { getUserId } from 'src/utils';
import { SubTask } from '../subTask/entities/subTask.entity';
import { UpdateStatusTaskDto } from './dto/updateStatus.task.dto';
import { Op } from 'sequelize';
import { endOfDay, startOfDay } from 'date-fns';
@Injectable()
export class TaskService {
  constructor(
    @InjectModel(Task)
    private taskModel: typeof Task,
  ) {}

  async create(taskDto: CreateTaskDto, token: string) {
    const loggedUser = getUserId(token);

    await this.taskModel.increment('position', {
      by: 1,
      where: { userId: loggedUser },
    });

    return await this.taskModel.create({
      ...taskDto,
      userId: loggedUser,
      position: 1,
    });
  }

  async findAll(token: string) {
    const loggedUser = getUserId(token);

    const tasks = await this.taskModel.findAll({
      where: {
        userId: loggedUser,
      },
      include: {
        model: SubTask,
        as: 'subTasks',
      },
      order: [['position', 'ASC']],
    });

    tasks.forEach((task) => {
      task.subTasks.sort((a, b) => a.position - b.position);
    });

    return tasks;
  }

  findOne(id: number) {
    return `This action returns a #${id} task`;
  }

  async remove(id: string) {
    if (!id) {
      throw new NotFoundException('ID da tarefa é obrigatório');
    }

    const task = await this.taskModel.findByPk(id, { include: SubTask });

    if (!task) {
      throw new NotFoundException('Tarefa não encontrada');
    }

    const transaction = await this.taskModel.sequelize.transaction();

    try {
      await SubTask.destroy({
        where: {
          taskId: id,
        },
        transaction,
      });

      await this.taskModel.destroy({
        where: {
          id,
        },
        transaction,
      });

      await transaction.commit();

      return task;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async updateTaskStatus({ id, status }: UpdateStatusTaskDto) {
    if (!id) {
      throw new NotFoundException('ID da tarefa é obrigatório');
    }

    const task = await this.taskModel.findByPk(id, { include: SubTask });

    if (!task) {
      throw new NotFoundException('Tarefa não encontrada');
    }
    const transaction = await this.taskModel.sequelize.transaction();

    try {
      await SubTask.update(
        { status },
        {
          where: {
            taskId: id,
          },
          transaction,
        },
      );

      await task.update({ status }, { transaction });

      await transaction.commit();

      return task;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async updateTaskStatusOnly({ id, status }: UpdateStatusTaskDto) {
    if (!id) {
      throw new NotFoundException('ID da tarefa é obrigatório');
    }

    const task = await this.taskModel.findByPk(id, { include: SubTask });

    if (!task) {
      throw new NotFoundException('Tarefa não encontrada');
    }

    await task.update({ status });

    return task;
  }

  async update(id: string, taskDto: CreateTaskDto) {
    if (!id) {
      throw new NotFoundException('ID da tarefa é obrigatório');
    }

    const task = await this.taskModel.findByPk(id);

    if (!task) {
      throw new NotFoundException('Tarefa não encontrada');
    }

    await task.update(taskDto);

    return task;
  }

  async moveTask(taskId: string, newPosition: number, token: string) {
    const loggedUserId = getUserId(token);

    if (!loggedUserId) {
      throw new NotFoundException('Usuário inválido');
    }

    const task = await this.taskModel.findOne({
      where: { id: taskId, userId: loggedUserId },
    });

    if (!task) {
      throw new NotFoundException('Tarefa não encontrada');
    }

    const currentPosition = task.position;

    // Atualizar as posições das outras notas
    if (newPosition < currentPosition) {
      await this.taskModel.increment('position', {
        by: 1,
        where: {
          userId: loggedUserId,
          position: {
            [Op.gte]: newPosition,
            [Op.lt]: currentPosition,
          },
        },
      });
    } else if (newPosition > currentPosition) {
      // Movendo para baixo
      await this.taskModel.decrement('position', {
        by: 1,
        where: {
          userId: loggedUserId,
          position: {
            [Op.lte]: newPosition,
            [Op.gt]: currentPosition,
          },
        },
      });
    }

    task.position = newPosition;
    await task.save();

    return task;
  }

  async getCountTasksPendingOrDelay(token: string) {
    const loggedUser = getUserId(token);
    const today = startOfDay(new Date());

    const whereConditions: any = {
      userId: loggedUser,
      status: {
        [Op.or]: ['pending', 'delay'],
      },
      [Op.and]: [
        {
          createdAt: {
            [Op.lt]: today,
          },
        },
        {
          [Op.or]: [
            { createdAt: null },
            {
              createdAt: {
                [Op.lt]: today,
              },
            },
          ],
        },
      ],
    };

    const count = await this.taskModel.count({
      where: whereConditions,
    });

    return count;
  }

  async getAllCurrentAndFutureTasks(token: string, createdAt?: Date) {
    const loggedUser = getUserId(token);
    const today = startOfDay(new Date());

    const pendingOrDelayedCount = await this.getCountTasksPendingOrDelay(token);

    const whereConditions: any = {
      userId: loggedUser,
      status: {
        [Op.or]: ['pending', 'delay'],
      },
      createdAt: {
        [Op.gte]: today,
      },
    };

    if (createdAt) {
      const startOfDayCreatedAt = startOfDay(createdAt);
      const endOfDayCreatedAt = endOfDay(createdAt);

      whereConditions.createdAt = {
        [Op.between]: [startOfDayCreatedAt, endOfDayCreatedAt],
      };
    }

    const tasks = await this.taskModel.findAll({
      where: whereConditions,
      include: {
        model: SubTask,
        as: 'subTasks',
      },
      order: [['completionDate', 'ASC']],
    });

    return { tasks, pendingOrDelayedCount };
  }

  async getAllTasksDelay(token: string, createdAt?: Date) {
    const loggedUser = getUserId(token);
    const today = startOfDay(new Date());

    try {
      const whereConditions: any = {
        userId: loggedUser,
        status: {
          [Op.or]: ['pending', 'delay'],
        },
        [Op.and]: [
          {
            createdAt: {
              [Op.lt]: today,
            },
          },
          {
            [Op.or]: [
              { createdAt: null },
              {
                createdAt: {
                  [Op.lt]: today,
                },
              },
            ],
          },
        ],
      };

      if (createdAt) {
        const startOfDayCreatedAt = startOfDay(createdAt);
        const endOfDayCreatedAt = endOfDay(createdAt);

        whereConditions[Op.and].push({
          createdAt: {
            [Op.between]: [startOfDayCreatedAt, endOfDayCreatedAt],
          },
        });
      }

      const tasks = await this.taskModel.findAll({
        where: whereConditions,
        include: {
          model: SubTask,
          as: 'subTasks',
        },
      });

      return tasks;
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw new Error('Failed to fetch tasks');
    }
  }

  getAllTasksByStatus(token: string, status?: string, createdAt?: Date) {
    const loggedUser = getUserId(token);

    const whereConditions: any = {
      userId: loggedUser,
      status: status || 'completed',
    };

    if (createdAt) {
      const startOfDayCreatedAt = startOfDay(createdAt);
      const endOfDayCreatedAt = endOfDay(createdAt);

      whereConditions.createdAt = {
        [Op.between]: [startOfDayCreatedAt, endOfDayCreatedAt],
      };
    }

    return this.taskModel.findAll({
      where: whereConditions,
      include: {
        model: SubTask,
        as: 'subTasks',
      },
    });
  }

  async countPendingTasks(id: string): Promise<number> {
    const count = await this.taskModel.count({
      where: {
        userId: id,
        status: 'pending',
      },
    });

    return count;
  }
}
