import {
  Column,
  Model,
  Table,
  DataType,
  Foreign<PERSON>ey,
  HasMany,
} from 'sequelize-typescript';
import { TTaskStatus } from 'src/data/@types/taskStatus';
import { SubTask } from 'src/modules/subTask/entities/subTask.entity';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'Task',
})
export class Task extends Model<Task> {
  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.STRING,
    defaultValue: 'pending',
  })
  status: TTaskStatus;

  @HasMany(() => SubTask, { onDelete: 'CASCADE' })
  subTasks: SubTask[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  position: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  file?: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  reminderDatetime?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  completionDate?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description?: string;
}
