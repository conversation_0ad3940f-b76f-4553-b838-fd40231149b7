/* eslint-disable prettier/prettier */
import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Delete,
  Param,
  Patch,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { ApiTags } from '@nestjs/swagger';
import { TaskService } from './task.service';
import { CreateTaskDto } from './dto/create.task.dto';
import { UpdateStatusTaskDto } from './dto/updateStatus.task.dto';
import { PermissionsGuard } from '../auth/permission.guard';
import { RequirePermission, TUserPermissionType, TUserScopes, UserPermissionsGuard } from '../userPermissions/userPermissions.guard';
import { Permissions } from '../auth/permissions';

@ApiTags('Tasks')
@Controller('task')
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @UseGuards(AuthGuard, PermissionsGuard, UserPermissionsGuard)
  @Permissions(TUserScopes.PENDINGTODO_WRITE)
  @RequirePermission(TUserPermissionType.PENDINGTODO)
  @Post()
  create(@Body() taskDto: CreateTaskDto, @Req() token: string) {
    return this.taskService.create(taskDto, token);
  }

  @UseGuards(AuthGuard)
  @Get('all')
  findAll(@Req() token: string) {
    return this.taskService.findAll(token);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.taskService.remove(id);
  }

  @UseGuards(AuthGuard)
  @Post('updateTaskStatus')
  updateSubtaskStatus(@Body() updateStatusSubTaskDto: UpdateStatusTaskDto) {
    return this.taskService.updateTaskStatus(updateStatusSubTaskDto);
  }

  @UseGuards(AuthGuard)
  @Post('updateTaskStatusOnly')
  updateTaskStatusOnly(@Body() updateStatusSubTaskDto: UpdateStatusTaskDto) {
    return this.taskService.updateTaskStatusOnly(updateStatusSubTaskDto);
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() taskDto: CreateTaskDto) {
    return this.taskService.update(id, taskDto);
  }

  @UseGuards(AuthGuard)
  @Patch('/move/:taskId')
  async moveTask(
    @Req() token: string,
    @Param('taskId') taskId: string,
    @Body('newPosition') newPosition: number,
  ) {
    return this.taskService.moveTask(taskId, newPosition, token);
  }

  @UseGuards(AuthGuard)
  @Get('getAllCurrentAndFutureTasks')
  async getAllCurrentAndFutureTasks(
    @Req() token: string,
    @Query('createdAt') createdAt?: Date,
  ) {
    return this.taskService.getAllCurrentAndFutureTasks(token, createdAt);
  }

  @UseGuards(AuthGuard)
  @Get('getAllTasksDelay')
  async getAllTasksDalay(
    @Req() token: string,
    @Query('createdAt') createdAt?: Date,
  ) {
    return this.taskService.getAllTasksDelay(token, createdAt);
  }

  @UseGuards(AuthGuard)
  @Get('getAllTasksByStatus')
  async getAllTasksByStatus(
    @Req() token: string,
    @Query('status') status?: string,
    @Query('createdAt') createdAt?: Date,
  ) {
    return this.taskService.getAllTasksByStatus(token, status, createdAt);
  }
}
