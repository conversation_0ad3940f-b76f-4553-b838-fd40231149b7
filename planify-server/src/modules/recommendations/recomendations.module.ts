import { Module, forwardRef } from '@nestjs/common';
import { RecomendationsService } from './recommendations.service';
import { RecomendationsController } from './recomendations.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { RadarRecomendation } from './entities/radarRecomendation.entity';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { RadarModule } from '../radar/radar.module';
import { UsersModule } from '../users/users.module';
import { ProductivityTips } from './entities/productivityTips.entity';
import { PlanRecommendations } from './entities/planRecommendation.entity';
import { UserPlan } from '../user-plans/entities/user-plan.entity';
import { TipFeedback } from './entities/tipFeedback.entity';
import { User } from '../users/entities/user.entity';
// import { UserPlansModule } from '../user-plans/user-plans.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      RadarRecomendation,
      ProductivityTips,
      PlanRecommendations,
      UserPlan,
      TipFeedback,
      User,
    ]),
    forwardRef(() => AuthModule),
    JwtModule,
    forwardRef(() => RadarModule),
    forwardRef(() => UsersModule),
    // forwardRef(() => UserPlansModule),
  ],
  controllers: [RecomendationsController],
  providers: [RecomendationsService],
  exports: [RecomendationsService],
})
export class RecommendationsModule {}
