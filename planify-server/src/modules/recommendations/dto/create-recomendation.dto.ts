import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateRadarRecomendationDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  radarId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty()
  // @IsJSON()
  @IsNotEmpty()
  recommendations: any;

  @ApiProperty()
  @IsString()
  @IsOptional()
  finalConsideration?: string;

  // @ApiProperty()
  // @IsString()
  // expirationDate?: string;
}
