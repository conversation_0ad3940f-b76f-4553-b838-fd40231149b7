/* eslint-disable prettier/prettier */
import {
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { CreateRadarRecomendationDto } from './dto/create-recomendation.dto';
import { RadarRecomendation } from './entities/radarRecomendation.entity';
import { InjectModel } from '@nestjs/sequelize';
import { addDays } from 'date-fns';
import { RadarService } from '../radar/radar.service';
import { UsersService } from '../users/users.service';
import axios from 'axios';
import { ProductivityTips } from './entities/productivityTips.entity';
import { Sequelize } from 'sequelize';
import { PlanRecommendations } from './entities/planRecommendation.entity';
import { UserPlan } from '../user-plans/entities/user-plan.entity';
import { Op } from 'sequelize';
import { TipFeedback } from './entities/tipFeedback.entity';
import { User } from '../users/entities/user.entity';
import { CreateTipFeedbackDto } from './dto/create-tipFeedback';
import { getUserId } from 'src/utils';

@Injectable()
export class RecomendationsService {
  constructor(
    @Inject(forwardRef(() => RadarService))
    private radarService: RadarService,
    private usersService: UsersService,
    @InjectModel(RadarRecomendation)
    private radarRecommendationModel: typeof RadarRecomendation,
    @InjectModel(UserPlan)
    private userPlanModel: typeof UserPlan,
    @InjectModel(PlanRecommendations)
    private planRecommendationsModel: typeof PlanRecommendations,
    @InjectModel(ProductivityTips)
    private productivityTipsModel: typeof ProductivityTips,
    @InjectModel(TipFeedback)
    private tipFeedbackModel: typeof TipFeedback,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}
  async create(data: CreateRadarRecomendationDto) {
    const radarAlwreadyExists = await this.radarService.findById(data?.radarId);
    const user = await this.usersService.findOne(data?.userId);

    if (!user) {
      throw new NotFoundException({ message: 'User not found' });
    }

    if (!radarAlwreadyExists) {
      throw new NotFoundException({ message: 'Radar not found' });
    }
    const formatedData = {
      ...data,
      recommendation: JSON.stringify(data?.recommendations),
      finalConsideration: data?.finalConsideration,
      expirationDate: String(addDays(new Date(), 180).toISOString()),
    };

    const recommendation =
      await this.radarRecommendationModel.create(formatedData);

    await this.radarService.updateStatus(recommendation?.radarId, {
      isCompleted: true,
      isCompletedRecommendation: true,
    });

    return recommendation;
  }

  async generateRecommendation(radarId) {
    const API_CURRENT_URL = process.env.API_CURRENT_URL;
    const PLANAI_MODEL_CONNECTION = process.env.PLANAI_MODEL_CONNECTION;

    axios
      .post(`${PLANAI_MODEL_CONNECTION}/radar-recommendation/${radarId}`, {
        receiver: API_CURRENT_URL,
      })
      .then(() => {})
      .catch(() => {});

    return {
      message: 'Your recommendation is being generated. Please wait a moments.',
    };
  }

  async generatePlanRecommendation(planId: string) {
    const API_CURRENT_URL = process.env.API_CURRENT_URL;
    const PLANAI_MODEL_CONNECTION = process.env.PLANAI_MODEL_CONNECTION;

    const plan = await this.userPlanModel.findOne({
      where: { id: planId },
    });

    if (!plan) {
      throw new NotFoundException({ message: 'Plan id not found' });
    }

    axios
      .post(`${PLANAI_MODEL_CONNECTION}/plan-recommendation/${planId}`, {
        receiver: API_CURRENT_URL,
      })
      .then(() => {})
      .catch(() => {});

    return {
      message: 'Your recommendation is being generated. Please wait a moments.',
    };
  }

  async findOne(radarId: string) {
    const response = await this.radarRecommendationModel.findOne({
      where: { radarId: radarId },
    });

    if (!response) {
      throw new NotFoundException({ message: 'recommendation not found' });
    }

    const formatedData = {
      ...response?.dataValues,
      recommendation: JSON.parse(response?.recommendation) ?? null,
    };

    return formatedData ?? [];
  }

  async getProductivityTips() {
    const tip = await this.productivityTipsModel.findOne({
      order: [Sequelize.fn('RAND')],
    });

    return tip;
  }

  async createProductivityTips(description: string) {
    const tip = await this.productivityTipsModel.create({
      description: description,
      status: 'active',
    });

    return tip;
  }

  async createPlanRecommendations(data) {
    const planAlreadyExists = await this.planRecommendationsModel.findOne({
      where: { planId: data?.planId },
    });

    if (planAlreadyExists) {
      throw new UnauthorizedException({
        message: 'This plan recommendation already exists',
      });
    }

    const formatedData = {
      userId: data?.userId,
      planId: data?.planId,
      recommendation: data?.recommendations,
      finalConsideration: data?.finalConsideration,
    };

    const recommendation =
      await this.planRecommendationsModel.create(formatedData);

    return recommendation;
  }

  async jobCheckRecommendations() {
    const penddingGenerations = await this.radarService.findAllPendings();

    penddingGenerations.forEach(async (radar) => {
      await this.generateRecommendation(radar?.id);
    });

    return {
      message: 'Job already started successfully!',
    };
  }

  async findRecommendationExists(id: string) {
    const radarRecommendation = await this.radarRecommendationModel.findOne({
      where: {
        [Op.or]: [{ id: id }, { radarId: id }],
      },
    });

    const planRecommendation = await this.planRecommendationsModel.findOne({
      where: {
        [Op.or]: [{ id: id }, { planId: id }],
      },
    });

    if (!radarRecommendation && !planRecommendation) {
      throw new NotFoundException({ message: 'Recommendation not found' });
    } else {
      return;
    }
  }

  async createFeedback(data: CreateTipFeedbackDto, token: string) {
    const loggedUser = getUserId(token);

    const user = await this.userModel.findOne({ where: { id: loggedUser } });

    if (!user) {
      throw new NotFoundException({ message: 'User not found' });
    }

    const tip = await this.productivityTipsModel.findOne({
      where: { id: data.tipId },
    });

    if (!tip) {
      throw new NotFoundException({ message: 'Tip not found' });
    }

    const transaction = await this.tipFeedbackModel.sequelize.transaction();

    try {
      const feedback = await this.tipFeedbackModel.create(
        {
          userId: loggedUser,
          tipId: data.tipId,
          feedback: data.feedback,
        },
        { transaction },
      );

      if (data.feedback === true) {
        await tip.increment('likeCount', { by: 1, transaction });
      } else if (data.feedback === false) {
        await tip.increment('dislikeCount', { by: 1, transaction });
      }

      await transaction.commit();
      return feedback;
    } catch (error) {
      await transaction.rollback();
      throw new NotFoundException({ message: 'Error on create feedback' });
    }
  }

  async getFeedback(id: string, token: string) {
    const loggedUser = getUserId(token);

    const user = await this.userModel.findOne({ where: { id: loggedUser } });

    if (!user) {
      throw new NotFoundException({ message: 'User not found ' });
    }

    const feedback = await this.tipFeedbackModel.findOne({
      where: {
        tipId: id,
        userId: user?.id,
      },
    });

    return feedback;
  }
}
