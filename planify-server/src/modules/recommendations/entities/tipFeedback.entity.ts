/* eslint-disable prettier/prettier */
import { Column, Model, Table, DataType } from 'sequelize-typescript';

@Table({
  tableName: 'TipFeedback',
})
export class TipFeedback extends Model<TipFeedback> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  tipId: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  feedback: boolean;
}
