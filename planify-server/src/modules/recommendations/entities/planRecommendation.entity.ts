import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { UserPlan } from 'src/modules/user-plans/entities/user-plan.entity';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'PlanRecommendations',
})
export class PlanRecommendations extends Model<PlanRecommendations> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @ForeignKey(() => UserPlan)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  planId: string;

  @Column({
    type: DataType.JSON,
    allowNull: false,
  })
  recommendation: any;
}
