import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { Radar } from 'src/modules/radar/entities/radar.entity';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'RadarRecommendations',
})
export class RadarRecomendation extends Model<RadarRecomendation> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Radar)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  radarId: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.JSON,
    allowNull: false,
  })
  recommendation: any;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  finalConsideration?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  expirationDate: string;
}
