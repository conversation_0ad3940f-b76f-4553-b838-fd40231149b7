import { Column, Model, Table, DataType } from 'sequelize-typescript';
import { TProctivityTipStatus } from 'src/data/@types/recommendations';

@Table({
  tableName: 'ProductivityTips',
})
export class ProductivityTips extends Model<ProductivityTips> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  description: any;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  status: TProctivityTipStatus;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  dislikeCount: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  likeCount: number;
}
