import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Req,
} from '@nestjs/common';
import { RecomendationsService } from './recommendations.service';
import { CreateRadarRecomendationDto } from './dto/create-recomendation.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { CreateTipFeedbackDto } from './dto/create-tipFeedback';

@ApiTags('Recommendations')
@Controller('recommendations')
export class RecomendationsController {
  constructor(private readonly recomendationsService: RecomendationsService) {}

  @Get('/')
  findAll() {
    return this.recomendationsService.jobCheckRecommendations();
  }

  @Get('/tipProductivity')
  getProductivity() {
    return this.recomendationsService.getProductivityTips();
  }

  @Post('/productivity')
  createProductivity(@Body() data: any) {
    return this.recomendationsService.createProductivityTips(data?.description);
  }

  @Post('/new/radar')
  create(@Body() data: CreateRadarRecomendationDto) {
    return this.recomendationsService.create(data);
  }

  @Post('/new/plan')
  createPlan(@Body() data) {
    return this.recomendationsService.createPlanRecommendations(data);
  }

  @Get(':radarId')
  findOne(@Param('radarId') radarId: string) {
    return this.recomendationsService.findOne(radarId);
  }

  @Post('/new/generate-plan/:planId')
  generatePlanRecommendation(@Param('planId') planId: string) {
    return this.recomendationsService.generatePlanRecommendation(planId);
  }

  @Post('/generate/:radarId')
  generate(@Param('radarId') radarId: string) {
    return this.recomendationsService.generateRecommendation(radarId);
  }

  @Get('/verify/:id')
  findRecommendationExists(@Param('id') id: string) {
    return this.recomendationsService.findRecommendationExists(id);
  }

  @UseGuards(AuthGuard)
  @Post('/feedback')
  createFeedback(@Body() data: CreateTipFeedbackDto, @Req() token: string) {
    return this.recomendationsService.createFeedback(data, token);
  }

  @UseGuards(AuthGuard)
  @Get('/feedback/:id')
  getFeedback(@Param('id') id: string, @Req() token: string) {
    return this.recomendationsService.getFeedback(id, token);
  }
}
