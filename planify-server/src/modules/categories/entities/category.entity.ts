import { Column, Model, Table, DataType, HasMany } from 'sequelize-typescript';
import { Prompt } from 'src/modules/prompts/entities/prompt.entity';

@Table({
  tableName: 'Categories',
})
export class Category extends Model<Category> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  position: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  description: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  color: string;

  @HasMany(() => Prompt)
  prompts: Prompt[];
}
