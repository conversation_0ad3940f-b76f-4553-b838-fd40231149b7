/* eslint-disable prettier/prettier */
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { AchievementRecords } from './entities/achievementRecords.entity';
import { Achievement } from '../achievements/entities/achievement.entity';
import { TNotificationType } from 'src/data/@types/TNotificationType';
import { CreateAchievementHistoryDto } from './dto/create-AchievementHistory.dto';
import { getUserId } from 'src/utils';
import { Op, Sequelize } from 'sequelize';
import { AchievementConditionValidationService } from './achievementConditionValidation.service';
import { NotificationsService } from '../notifications/notifications.service';
import { QueuesService } from '../queues/queues.service';
import { QUEUE_NAMES } from '../queues/constants';
import { paginateResults } from 'src/utils/pagination';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { AchievementsService } from '../achievements/achievements.service';

@Injectable()
export class AchievementRecordsService {
  private logger = createModuleLogger(AchievementRecordsService.name);

  constructor(
    @InjectModel(AchievementRecords)
    private achievementRecordsModel: typeof AchievementRecords,
    @InjectModel(Achievement)
    private achievementModel: typeof Achievement,
    private readonly queuesService: QueuesService,
    @Inject(forwardRef(() => NotificationsService))
    private notificationsService: NotificationsService,
    @Inject(forwardRef(() => AchievementConditionValidationService))
    private medalConditionService: AchievementConditionValidationService,
    @Inject(forwardRef(() => AchievementsService))
    private achievementsService: AchievementsService,
  ) {}

  async create(achievementDto: CreateAchievementHistoryDto) {
    this.logger.info('Creating achievement:', achievementDto);

    const transaction =
      await this.achievementRecordsModel.sequelize.transaction();

    try {
      const achievement =
        await this.achievementsService.getAchievementByBadgeCode(
          achievementDto.badgeCode,
        );

      if (!achievement) {
        this.logger.error('Achievement not found:', achievementDto.badgeCode);
        throw new Error(`Achievement not found: ${achievementDto.badgeCode}`);
      }

      this.logger.info('Found achievement:', {
        id: achievement.id,
        name: achievement.name,
        badgeCode: achievement.badgeCode,
      });

      const isAlreadyUnlocked = await this.achievementRecordsModel.findOne({
        where: {
          userId: achievementDto.userId,
          achievementId: achievement.id,
        },
      });

      if (isAlreadyUnlocked) {
        this.logger.info('Achievement already unlocked:', {
          userId: achievementDto.userId,
          achievementId: achievement.id,
          badgeCode: achievement.badgeCode,
        });
        await transaction.rollback();
        return false;
      }

      const achievementRecords = await this.achievementRecordsModel.create(
        {
          points: achievement.points,
          userId: achievementDto.userId,
          unlockedAt: new Date(),
          achievementId: achievement.id,
          type: 'credit',
          operationHistory: 'earnPoints',
          expirationDate: new Date(
            new Date().setMonth(
              new Date().getMonth() + Number(achievement.expirationPeriod),
            ),
          ),
        },
        { transaction },
      );

      this.logger.info('Achievement record created:', {
        id: achievementRecords.id,
        userId: achievementRecords.userId,
        achievementId: achievementRecords.achievementId,
      });

      const achievementData = achievement;

      await this.notificationsService.create({
        userId: achievementDto.userId,
        title: 'Você ganhou uma nova medalha',
        description: `Parabéns você ganhou a medalha ${achievementData.name} você ganhou ${achievementData.points} pontos`,
        notificationStatus: TNotificationType.CREATED,
        targetType: 'achievement',
      });

      await transaction.commit();
      this.logger.info('Achievement transaction committed successfully');

      const milestones =
        await this.medalConditionService.checkPointsAchievementAndTier(
          achievementDto.userId,
        );

      if (milestones && milestones.length > 0) {
        this.logger.info('Additional milestones triggered:', milestones);
        for (const milestone of milestones) {
          await this.queuesService.publish(QUEUE_NAMES.ACHIEVEMENT, {
            userId: achievementDto.userId,
            badgeCode: milestone.badgeCode,
          });
        }
      }

      return achievementRecords;
    } catch (error) {
      this.logger.error('Error creating achievement:', error);
      await transaction.rollback();
      throw error;
    }
  }

  async handleAchievementCreation(achievementDto: CreateAchievementHistoryDto) {
    this.logger.info('Handling achievement creation:', achievementDto);

    try {
      const result = await this.create(achievementDto);
      this.logger.info('Achievement created successfully:', {
        userId: achievementDto.userId,
        badgeCode: achievementDto.badgeCode,
      });
      return result;
    } catch (error) {
      this.logger.error('Error in handleAchievementCreation:', error);
      throw error;
    }
  }

  async findAll() {
    const achievements = await this.achievementRecordsModel.findAll();
    return achievements;
  }

  async getAchievementByUserId(userId: string) {
    const achievements = await this.achievementRecordsModel.findAll({
      where: { userId },
      include: [
        {
          model: Achievement,
          attributes: ['name', 'description', 'image'],
        },
      ],
    });

    return achievements;
  }

  async getAllAchievementsWithUserStatus(userId: string) {
    const allAchievements = await this.achievementsService.findAll();

    const userAchievements = await this.achievementRecordsModel.findAll({
      where: { userId },
      include: [{ model: Achievement }],
    });

    const userAchievementIds = userAchievements.map(
      (record) => record.achievementId,
    );

    const achievementsWithStatus = allAchievements.map((achievement) => {
      const isUnlocked = userAchievementIds.includes(achievement.id);
      const userAchievement = userAchievements.find(
        (record) => record.achievementId === achievement.id,
      );

      return {
        id: achievement.id,
        name: achievement.name,
        description: achievement.description,
        points: isUnlocked ? userAchievement.points : achievement.points,
        image: achievement.image,
        unlockedAt: isUnlocked ? userAchievement.unlockedAt : null,
        isUnlocked,
        position: achievement.position,
      };
    });

    achievementsWithStatus.sort((a, b) => a.position - b.position);

    return achievementsWithStatus;
  }

  async getPointsHistory(
    token: string,
    period: number,
    type: string = 'all',
    page: number,
    limit: number,
  ) {
    const loggedUser = getUserId(token);
    const now = new Date();
    if (period <= 0) throw new Error('O período deve ser maior que zero.');

    const periodStart = new Date();
    periodStart.setDate(now.getDate() - period);

    const allowedTypes = ['credit', 'debit', 'expired', 'rescued', 'all'];
    if (!allowedTypes.includes(type)) {
      throw new BadRequestException(
        `Tipo inválido. Tipos permitidos: ${allowedTypes.join(', ')}`,
      );
    }

    const whereConditions: any = {
      userId: loggedUser,
      // unlockedAt: { [Op.between]: [periodStart, now] },
    };

    if (type !== 'all') {
      const typeConditions = {
        credit: { type: 'credit' },
        debit: { type: 'debit' },
        expired: { expirationDate: { [Op.lt]: now } },
        rescued: { operationHistory: 'redeemPoints' },
      };
      Object.assign(whereConditions, typeConditions[type]);
    }

    try {
      const response = await paginateResults(
        { page, limit },
        {
          model: this.achievementRecordsModel,
          whereConditions,
          includeOptions: [
            {
              model: Achievement,
              attributes: ['name', 'description', 'image'],
            },
          ],
          orderOptions: [['createdAt', 'DESC']],
          transformResult: (record) => ({
            id: record.id,
            userId: record.userId,
            points: record.points,
            achievementId: record.achievementId,
            unlockedAt: record.unlockedAt,
            type: record.type,
            expirationDate: record.expirationDate,
            achievementDetails: record.achievement,
            operationHistory: record.operationHistory,
            createdAt: record.createdAt,
          }),
        },
      );
      return response;
    } catch (error) {
      throw new BadRequestException(
        `Erro ao recuperar o histórico de pontos: ${error.message}`,
      );
    }
  }

  async getPointsSummary(token: string, period?: number) {
    const loggedUser = getUserId(token);

    const periodCondition = period ? `AND unlockedAt > :periodStart` : '';

    const query = `
      SELECT
        SUM(points) AS totalPoints,
        SUM(CASE WHEN type = 'credit' AND expirationDate > NOW() THEN points ELSE 0 END) AS credits,
        SUM(CASE WHEN type = 'debit' AND expirationDate > NOW() THEN points ELSE 0 END) AS debits,
        SUM(CASE WHEN expirationDate < NOW() THEN points ELSE 0 END) AS expired,
        SUM(CASE WHEN operationHistory = 'redeemPoints' AND expirationDate > NOW() THEN points ELSE 0 END) AS rescued
      FROM AchievementRecords
      WHERE userId = :userId
      ${periodCondition}
    `;

    const replacements = {
      userId: loggedUser,
      ...(period && {
        periodStart: new Date(
          new Date().setDate(new Date().getDate() - period),
        ),
      }),
    };

    const [results] = await this.achievementRecordsModel.sequelize.query(
      query,
      {
        replacements,
      },
    );

    return results[0];
  }

  async jobCheckExpiredAchievements() {
    const now = new Date();

    const transaction =
      await this.achievementRecordsModel.sequelize.transaction();
    try {
      const allAchievements = await this.achievementRecordsModel.findAll({
        where: {
          expirationDate: {
            [Op.lt]: now,
          },
          expirationStatus: 0,
        },
      });

      for (const achievement of allAchievements) {
        await this.achievementRecordsModel.create({
          points: -achievement.points,
          userId: achievement.userId,
          achievementId: achievement.achievementId,
          type: 'debit',
          operationHistory: 'expirePoints',
          referenceRecord: achievement.id,
          expirationStatus: 3,
        });

        await achievement.update({ expirationStatus: 1 });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      this.logger.error(error.message);
      throw new BadRequestException(
        `Erro ao verificar conquistas expiradas: ${error.message}`,
      );
    }
  }

  async getAchievementPointsByUserIdRaw(userId: string): Promise<number> {
    const sequelize = this.achievementRecordsModel.sequelize;
    const [results] = await sequelize.query(
      `SELECT SUM(points) as totalPoints FROM AchievementRecords WHERE userId = :userId`,
      {
        replacements: { userId },
        type: 'SELECT',
      },
    );

    return results && results['totalPoints']
      ? parseInt(results['totalPoints'], 10)
      : 0;
  }
}
