/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserPlan } from '../user-plans/entities/user-plan.entity';
import { PlanActivity } from '../planActivities/entities/plan-activity.entity';
import { Radar } from '../radar/entities/radar.entity';
import { AchievementRecords } from './entities/achievementRecords.entity';

// Concluiu o 1º RADAR

// Upgrade STANDARD
// Upgrade PREMIUM
// Upgrade ULTIMATE

@Injectable()
export class AchievementConditionValidationService {
  constructor(
    @InjectModel(UserPlan)
    private userPlanModel: typeof UserPlan,
    @InjectModel(PlanActivity)
    private planActivityModel: typeof PlanActivity,
    @InjectModel(Radar)
    private radarModel: typeof Radar,
    @InjectModel(AchievementRecords)
    private achievementRecordsModel: typeof AchievementRecords,
  ) {}

  // Criou o 1º OBJETIVO, Criou o 2º OBJETIVO, Criou o 5º OBJETIVO
  async checkObjectiveCreationAchievement(userId: string) {
    const createdObjectivesCount = await this.userPlanModel.count({
      where: { userId, deletedAt: null },
    });

    const milestones = {
      1: 'BC1O01',
      2: 'BC1O02',
      5: 'BC1O03',
    };

    return milestones[createdObjectivesCount] || null;
  }

  // Concluiu o 1º OBJETIVO, Concluiu o 2º OBJETIVO, Concluiu o 5º OBJETIVO, Concluiu o 10º OBJETIVO
  async checkCompletedPlanAchievement(userId: string) {
    const completedPlansCount = await this.userPlanModel.count({
      where: { userId, planStatus: 'completed' },
    });

    const milestones = {
      1: 'BCO001',
      2: 'BCO002',
      5: 'BC5O01',
      10: 'BC10O1',
    };

    return milestones[completedPlansCount] || null;
  }

  // 1ª tarefa concluída, 50 tarefas concluídas, 100 tarefas concluídas
  async checkMilestoneAchievement(userId: string) {
    const completedActivitiesCount = await this.planActivityModel.count({
      where: { userId, activityStatus: 'done' },
    });

    const milestones = {
      1: 'BC1T01',
      50: 'BC50T1',
      100: 'BC100T',
    };

    return milestones[completedActivitiesCount] || null;
  }

  async checkPointsAchievementAndTier(userId: string) {
    const points = await this.achievementRecordsModel.sum('points', {
      where: { userId },
    });

    if (!points) return null;

    const milestones = [
      { badgeCode: 'BA1K01', condition: points >= 1000 },
      { badgeCode: 'BA5K01', condition: points >= 5000 },
      { badgeCode: 'BA10K1', condition: points >= 10000 },
    ].filter((milestone) => milestone.condition);

    return milestones;
  }
}
