/* eslint-disable prettier/prettier */
import { ApiTags } from '@nestjs/swagger';
import { AchievementRecordsService } from './achievementRecords.service';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { CreateAchievementHistoryDto } from './dto/create-AchievementHistory.dto';

@ApiTags('achievementRecords')
@Controller('achievementRecords')
export class AchievementRecordsController {
  constructor(
    private readonly achievementsService: AchievementRecordsService,
  ) {}
  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.achievementsService.findAll();
  }

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() achievementDto: CreateAchievementHistoryDto) {
    return this.achievementsService.create(achievementDto);
  }

  @UseGuards(AuthGuard)
  @Get('getAllAchievementByUserId/:userId')
  getAchievementByUserId(@Param('userId') userId: string) {
    return this.achievementsService.getAchievementByUserId(userId);
  }

  @UseGuards(AuthGuard)
  @Get('achievementsWithUserStatus/:userId')
  getAllAchievementsWithUserStatus(@Param('userId') userId: string) {
    return this.achievementsService.getAllAchievementsWithUserStatus(userId);
  }

  @UseGuards(AuthGuard)
  @Get('getPointsHistory')
  async getPointsHistory(
    @Req() token: string,
    @Query('period') period: number,
    @Query('type') type: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.achievementsService.getPointsHistory(
      token,
      period,
      type,
      page,
      limit,
    );
  }

  @UseGuards(AuthGuard)
  @Get('pointsSummary')
  async getPointsSummary(
    @Req() token: string,
    @Query('period') period: number,
  ) {
    return this.achievementsService.getPointsSummary(token, period);
  }
}
