/* eslint-disable prettier/prettier */
import {
  Column,
  Model,
  Table,
  ForeignKey,
  DataType,
  BelongsTo,
} from 'sequelize-typescript';
import { Achievement } from 'src/modules/achievements/entities/achievement.entity';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'AchievementRecords',
})
export class AchievementRecords extends Model<AchievementRecords> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @ForeignKey(() => Achievement)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  achievementId: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  points: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  unlockedAt: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  operationHistory: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  referenceRecord: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  type: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expirationDate: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  expirationStatus: number;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Achievement)
  achievement: Achievement;
}
