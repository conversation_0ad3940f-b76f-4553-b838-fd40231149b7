/* eslint-disable prettier/prettier */
import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { AchievementRecords } from './entities/achievementRecords.entity';
import { AchievementRecordsController } from './achievementRecords.controller';
import { AchievementRecordsService } from './achievementRecords.service';
import { Achievement } from '../achievements/entities/achievement.entity';
import { UserPlan } from '../user-plans/entities/user-plan.entity';
import { AchievementConditionValidationService } from './achievementConditionValidation.service';
import { PlanActivity } from '../planActivities/entities/plan-activity.entity';
import { Radar } from '../radar/entities/radar.entity';
import { Tier } from '../tiers/entities/Tier.entity';
import { TierGroup } from '../tiers/entities/tierGroup.entity';
import { NotificationsModule } from '../notifications/notifications.module';
import { User } from '../users/entities/user.entity';
import { QueuesModule } from '../queues/queues.module';
import { TiersModule } from '../tiers/tiers.module';
import { AchievementsModule } from '../achievements/achievements.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      AchievementRecords,
      Achievement,
      UserPlan,
      PlanActivity,
      Radar,
      Tier,
      TierGroup,
      User,
    ]),
    forwardRef(() => AuthModule),
    JwtModule,
    forwardRef(() => NotificationsModule),
    forwardRef(() => QueuesModule),
    forwardRef(() => TiersModule),
    forwardRef(() => AchievementsModule),
  ],
  controllers: [AchievementRecordsController],
  providers: [AchievementRecordsService, AchievementConditionValidationService],
  exports: [
    AchievementRecordsService,
    AchievementConditionValidationService,
    SequelizeModule,
  ],
})
export class AchievementRecordsModule {}
