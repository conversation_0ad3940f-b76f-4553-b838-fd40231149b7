import { Post, Req, UseGuards, Controller, Get } from '@nestjs/common';
import { UserPermissionsService } from './userPermissions.service';
import { AuthGuard } from '../auth/auth.guard';
import { Request } from 'express';

@Controller('user-permissions/limits')
export class UserPermissionsController {
  constructor(
    private readonly userPermissionsService: UserPermissionsService,
  ) { }

  @UseGuards(AuthGuard)
  @Get()
  async getLimits(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.verifyUserLimits(token);
  }

  @UseGuards(AuthGuard)
  @Get("/by-user")
  async getLimitsByUserId(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.getLimitsByUserId(token);
  }

  @Post('radar')
  async create(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.validateRadarLimits(token);
  }

  @Post('ai-objectives')
  async createAiObjectives(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.validateAiObjectivesLimits(token);
  }

  @Post('/ai-objectives/by-user')
  async checkAvailableObjectivesLimit(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.checkAvailableObjectivesLimit(token, 'ai');
  }

  @Post('manual-objectives')
  async createManualObjectives(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.validateManualObjectivesLimits(token);
  }

  @Post('/manual-objectives/by-user')
  async checkAvailableManualObjectives(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.checkAvailableObjectivesLimit(token, 'manual');
  }

  @Post('pending-tasks')
  async createPendingTasks(@Req() req: Request) {
    const token = req.headers.authorization;
    return this.userPermissionsService.validatePendingTasksLimits(token);
  }

  // @Post('tasks-control')
  // async createTasksControl(@Req() req: Request) {
  //   const token = req.headers.authorization;
  //   return this.userPermissionsService.validateTasksControlLimits(token);
  // }

  @Post('objective-tasks/:planId')
  async createObjectiveTasks(@Req() req: Request) {
    const token = req.headers.authorization;
    const { planId } = req.params;

    return this.userPermissionsService.validateObjectiveTasksLimit(
      planId,
      token,
    );
  }
}
