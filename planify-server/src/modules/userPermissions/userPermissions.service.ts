/* eslint-disable prettier/prettier */
import {
  Injectable,
  UnauthorizedException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ThrottlerException } from '@nestjs/throttler';
import { UsersService } from '../users/users.service';
import { AuthService } from '../auth/auth.service';
import { PaymentsService } from '../payments/payments.service';
import { RadarService } from '../radar/radar.service';
import { TaskService } from '../task/task.service';
import { PermissionsService } from '../permissions/permissions.service';
import { PaymentPlansService } from '../payment-plans/payment-plans.service';
import { PERMISSION_LIMIT_TYPES } from './@types/permissionLimits';
import { UserPlansService } from '../user-plans/user-plans.service';
import { TCreationStrategy } from 'src/data/@types/TCreationStrategy';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { capitalize, ObjectiveType } from 'src/utils/capitalize';

@Injectable()
export class UserPermissionsService {
  private logger = createModuleLogger(UserPermissionsService.name);
  constructor(
    private authService: AuthService,
    private usersService: UsersService,
    private paymentsService: PaymentsService,
    private paymentPlansService: PaymentPlansService,
    private radarService: RadarService,
    private userTasksService: TaskService,
    private permissionService: PermissionsService,
    @Inject(forwardRef(() => UserPlansService))
    private userPlansService: UserPlansService,
  ) { }

  async getCurrentUser(tokenOrUserId: string) {
    let userId = tokenOrUserId;

    // Check if it's a JWT token (contains dots)
    if (typeof tokenOrUserId === 'string' && tokenOrUserId.includes('.')) {
      try {
        const decodedToken = this.authService.decodeToken(tokenOrUserId);
        userId = decodedToken?.userId || null;
      } catch (error) {
        this.logger.error(`Error decoding JWT token: ${error.message}`);
      }
    }

    // Handle Bearer token case
    if (
      typeof tokenOrUserId === 'string' &&
      tokenOrUserId.startsWith('Bearer ')
    ) {
      const token = tokenOrUserId.replace('Bearer ', '');
      try {
        const decodedToken = this.authService.decodeToken(token);
        userId = decodedToken?.userId || null;
      } catch (error) {
        this.logger.error(`Error decoding Bearer token: ${error.message}`);
      }
    }

    if (!userId || typeof userId !== 'string') {
      this.logger.info(`Invalid user ID format: ${userId}`);
      throw new UnauthorizedException({
        message: 'Invalid token format',
      });
    }

    const user = await this.usersService.findOne(userId);

    if (!user) {
      this.logger.info(`User not found with ID ${userId}`);
      throw new UnauthorizedException({
        message: 'Invalid or expired token',
      });
    }

    return user;
  }

  async getCurrentSubscription(userId: string) {
    const subscription = await this.paymentsService.getUserPaymentPlan(userId);
    if (!subscription) {
      this.logger.info(`'User does not have an active subscription ${userId}`);
      throw new UnauthorizedException({
        message: 'User does not have an active subscription',
      });
    }

    const permissionsData: any =
      await this.permissionService.findByPaymentPlanId(
        subscription.paymentPlanId,
      );

    const planData = await this.paymentPlansService.findOne(
      subscription.paymentPlanId,
    );

    return {
      planData,
      subscription,
      permissionsData,
    };
  }

  async verifyUserLimits(tokenOrUserId: string) {
    const user = await this.getCurrentUser(tokenOrUserId);
    // if (user) {
    //   userId = user.id;
    // }

    // if (!user) {
    //   const userData = await getUserId({ user: { userId: tokenOrUserId } });
    //   userId = userData.userId;
    // }
    const userId = user.id;
    const { planData, subscription, permissionsData } =
      await this.getCurrentSubscription(userId);

    // 0 === nenhum item permitido"
    // >= 1 quantidade permitida
    // null === itens ilimitados

    const limitFields = {
      userId,
      subscriptionPlan: {
        name: planData?.name,
        period: planData?.recurrencyPeriod,
        recurrence: planData?.recurrencyType,
        createdAt: subscription?.createdAt,
        expirationAt: subscription?.expirationAt,
      },

      // AI
      aiObjectives: {
        period: planData?.aiObjectivesPeriod,
        maxLimit: planData?.aiObjectivesLimit ?? 0,
      },
      aiObjectiveTasks: {
        period: planData.aiObjectivesPeriod,
        maxLimit: planData.aiPlanTasksLimits ?? 0,
      },

      // MANUAL
      manualObjectives: {
        period: planData?.manualObjectivesPeriod,
        maxLimit: planData?.manualObjectivesLimit ?? 0,
      },
      manualObjectiveTasks: {
        period: planData.manualObjectivesPeriod,
        maxLimit: planData.manualPlanTasksLimits ?? 0,
      },

      // RADAR
      radar: {
        period: planData?.radarLimitPeriod,
        maxLimit: planData?.radarLimit ?? 0,
      },

      pendingTasks: {
        period: null,
        maxLimit: planData.pendingTasksLimit ?? 0,
      },

      scopes: [...permissionsData.scopes],
    };

    // this.logger.info('[USER LIMIT FIELDS]: ', limitFields);

    return limitFields;
  }

  async getSubscriptionByPaymentId(paymentId: string): Promise<any> {
    const permissionsData: any =
      await this.permissionService.findByPaymentPlanId(
        paymentId,
      );

    const planData = await this.paymentPlansService.findOne(
      paymentId,
    );

    return {
      planData,
      permissionsData,
    };
  }


  async getLimitsByUserId(tokenOrUserId: string) {
    const user = await this.getCurrentUser(tokenOrUserId);

    if (!user) {
      throw new UnauthorizedException({
        message: 'Invalid or expired token',
      });
    }

    const subscription = await this.getSubscriptionByPaymentId(user.paymentPlanId);

    if (!subscription) {
      throw new UnauthorizedException({
        message: 'User does not have an active subscription',
      });
    }

    const { permissionsData, planData } = subscription;

    const limitFields = {
      userId: user.id,
      subscriptionPlan: {
        name: planData?.name,
        period: planData?.recurrencyPeriod,
        recurrence: planData?.recurrencyType,
      },

      // AI
      aiObjectives: {
        period: planData?.aiObjectivesPeriod,
        maxLimit: planData?.aiObjectivesLimit ?? 0,
      },
      aiObjectiveTasks: {
        period: planData.aiObjectivesPeriod,
        maxLimit: planData.aiPlanTasksLimits ?? 0,
      },

      // MANUAL
      manualObjectives: {
        period: planData?.manualObjectivesPeriod,
        maxLimit: planData?.manualObjectivesLimit ?? 0,
      },
      manualObjectiveTasks: {
        period: planData.manualObjectivesPeriod,
        maxLimit: planData.manualPlanTasksLimits ?? 0,
      },

      // RADAR
      radar: {
        period: planData?.radarLimitPeriod,
        maxLimit: planData?.radarLimit ?? 0,
      },

      pendingTasks: {
        period: null,
        maxLimit: planData.pendingTasksLimit ?? 0,
      },

      scopes: [...permissionsData.scopes],
    };


    return limitFields;
  }


  async validateManualObjectivesLimits(tokenOrUserId: string) {
    const { userId, manualObjectives } =
      await this.verifyUserLimits(tokenOrUserId);

    const manualObjectivesCreatedSize =
      await this.userPlansService.findManualObjectivesByPeriod(
        manualObjectives.period,
        userId,
      );

    const createdSize = Number(manualObjectivesCreatedSize);
    const maxLimit = Number(manualObjectives.maxLimit);

    if (
      manualObjectives.period &&
      manualObjectives.maxLimit &&
      createdSize >= maxLimit
    ) {
      this.logger.info('[USER MANUAL PLANS LIMITS]: ', {
        createdSize,
        maxLimit,
      });
      throw new ThrottlerException(
        'User has reached the limit of manual objectives created',
      );
    }
    const response = {
      availableManualObjectivesLimit:
        maxLimit - createdSize || PERMISSION_LIMIT_TYPES.NONE,
    };

    this.logger.info('[ RESPONSE LIMIT]', response);

    return response;
  }

  async validateAiObjectivesLimits(tokenOrUserId: string) {
    const { userId, aiObjectives } = await this.verifyUserLimits(tokenOrUserId);

    const aiObjectivesCreatedSize =
      await this.userPlansService.findAiObjectivesByPeriod(
        aiObjectives.period,
        userId,
      );

    const createdSize = Number(aiObjectivesCreatedSize);
    const maxLimit = Number(aiObjectives.maxLimit);

    if (
      aiObjectives.period &&
      aiObjectives.maxLimit &&
      createdSize >= maxLimit
    ) {
      this.logger.info('[USER AI PLANS LIMITS]: ', { createdSize, maxLimit });
      throw new ThrottlerException(
        'User has reached the limit of ai objectives created',
      );
    }

    const response = {
      availableAiObjectivesLimit:
        maxLimit - createdSize || PERMISSION_LIMIT_TYPES.NONE,
    };

    this.logger.info('[ RESPONSE LIMIT]', response);
    return response;
  }

  async validateRadarLimits(tokenOrUserId: string) {
    const { userId, radar } = await this.getLimitsByUserId(tokenOrUserId);

    const radarCreatedSize = await this.radarService.findRadarByPeriod(
      radar.period,
      userId,
    );
    const createdSize = Number(radarCreatedSize);
    const maxLimit = Number(radar.maxLimit);

    if (radar.period && radar.maxLimit && createdSize >= maxLimit) {
      this.logger.info('[USER RADAR LIMITS]: ', { createdSize, maxLimit });
      throw new ThrottlerException(
        'User has reached the limit of radar created',
      );
    }
    const response = {
      availableRadarLimit:
        maxLimit - createdSize || PERMISSION_LIMIT_TYPES.NONE,
    };

    this.logger.info('[ RESPONSE LIMIT]', response);

    return response;
  }

  async validatePendingTasksLimits(tokenOrUserId: string) {
    const res = await this.verifyUserLimits(tokenOrUserId);

    const { userId, pendingTasks } = res;
    if (pendingTasks.maxLimit === null) {
      // Limite ilimitado, não há necessidade de validação
      return { availablePendingTasksLimit: null };
    }

    const pendingTasksCreatedSize =
      await this.userTasksService.countPendingTasks(userId);
    const createdSize = Number(pendingTasksCreatedSize);
    const maxLimit = Number(pendingTasks.maxLimit);

    if (createdSize >= maxLimit) {
      this.logger.info('[USER PENDING TASKS LIMITS]: ', {
        createdSize,
        maxLimit,
      });
      throw new ThrottlerException(
        'User has reached the limit of pending tasks created',
      );
    }

    const response = {
      availablePendingTasksLimit:
        maxLimit - createdSize || PERMISSION_LIMIT_TYPES.NONE,
    };

    this.logger.info('[ RESPONSE LIMIT]', response);
    return response;
  }

  async validateObjectiveTasksLimit(planId: string, tokenOrUserId: string) {
    const { manualObjectiveTasks, aiObjectiveTasks } =
      await this.getLimitsByUserId(tokenOrUserId);

    const { activitiesSize, planDetails } =
      await this.userPlansService.getPlanTasksSize(planId);

    let createdSize = 0;
    let maxLimit = 0;
    if (planDetails.creationStrategy === TCreationStrategy.MANUAL) {
      createdSize = Number(activitiesSize);
      maxLimit = Number(manualObjectiveTasks.maxLimit);
    }

    if (planDetails.creationStrategy === TCreationStrategy.AUTO) {
      createdSize = Number(activitiesSize);
      maxLimit = Number(aiObjectiveTasks.maxLimit);
    }

    if (
      (planDetails &&
        manualObjectiveTasks.period &&
        manualObjectiveTasks.maxLimit &&
        createdSize >= maxLimit) ||
      (planDetails &&
        aiObjectiveTasks.period &&
        aiObjectiveTasks.maxLimit &&
        createdSize >= maxLimit)
    ) {
      this.logger.info('[USER TASKS LIMITS]: ', { createdSize, maxLimit });
      throw new ThrottlerException(
        'User has reached the limit of tasks created',
      );
    }

    if (planDetails) {
      const response = {
        typePlan: planDetails.creationStrategy,
        createdSize: activitiesSize,
        availableTasksLimit:
          maxLimit - createdSize || PERMISSION_LIMIT_TYPES.NONE,
      };

      this.logger.info('[ RESPONSE]', response);

      return response;
    }
  }

  async checkAvailableObjectivesLimit(
    tokenOrUserId: string,
    objectiveType: ObjectiveType,
  ) {
    const { userId, aiObjectives, manualObjectives } = await this.getLimitsByUserId(tokenOrUserId);


    const objectives = objectiveType === 'ai' ? aiObjectives : manualObjectives;
    const maxLimit = Number(objectives.maxLimit);
    const period = objectives.period;

    if (!period || !maxLimit) {
      return {
        [`available${capitalize(objectiveType)}ObjectivesLimit`]: PERMISSION_LIMIT_TYPES.NONE,
      };
    }

    const createdCount = Number(
      await (objectiveType === 'ai'
        ? this.userPlansService.findAiObjectivesByPeriod(period, userId)
        : this.userPlansService.findManualObjectivesByPeriod(period, userId)),
    );

    if (createdCount >= maxLimit) {
      this.logger.info(`[USER ${objectiveType.toUpperCase()} OBJECTIVES LIMIT EXCEEDED]`, {
        createdCount,
        maxLimit,
      });
      throw new ThrottlerException(
        `User has reached the limit of ${objectiveType} objectives created`,
      );
    }

    const availableLimit = maxLimit - createdCount;

    const response = {
      [`available${capitalize(objectiveType)}ObjectivesLimit`]: availableLimit,
    };

    this.logger.info(`[AVAILABLE ${objectiveType.toUpperCase()} OBJECTIVES LIMIT]`, response);
    return response;
  }

  async checkAvailablePendingTasksLimit(tokenOrUserId: string) {
    const { userId, pendingTasks } = await this.getLimitsByUserId(tokenOrUserId);
    const maxLimit = pendingTasks.maxLimit;

    // Limite ilimitado (sem restrição)
    if (maxLimit === null || maxLimit === undefined) {
      return { availablePendingTasksLimit: null };
    }

    const createdCount = Number(
      await this.userTasksService.countPendingTasks(userId),
    );

    const numericLimit = Number(maxLimit);

    if (createdCount >= numericLimit) {
      this.logger.info('[USER PENDING TASKS LIMIT EXCEEDED]', {
        createdCount,
        maxLimit: numericLimit,
      });
      throw new ThrottlerException(
        'User has reached the limit of pending tasks created',
      );
    }

    const availableLimit = numericLimit - createdCount;

    const response = {
      availablePendingTasksLimit: availableLimit || PERMISSION_LIMIT_TYPES.LIMITED,
    };

    this.logger.info('[AVAILABLE PENDING TASKS LIMIT]', response);
    return response;
  }


}
