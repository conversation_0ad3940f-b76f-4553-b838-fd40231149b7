/* eslint-disable prettier/prettier */
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  SetMetadata,
} from '@nestjs/common';
import { ThrottlerException } from '@nestjs/throttler';
import { Reflector } from '@nestjs/core';
import { UserPermissionsService } from './userPermissions.service';
import { getUserIdFromToken } from 'src/utils';
import { createModuleLogger } from 'src/utils/moduleLogger';

export enum TUserPermissionType {
  CREATE_AI_PLAN = 'CREATE_AI_PLAN',
  CREATE_MANUAL_PLAN = 'CREATE_MANUAL_PLAN',
  CREATE_RADAR = 'CREATE_RADAR',
  CREATE_OBJECTIVE_TASKS = 'CREATE_OBJECTIVE_TASKS',
  PENDINGTODO = 'PENDINGTODO',
}

export enum TUserScopes {
  ADMIN = 'admin',

  TICKET_CLOSE = 'ticket.stop',
  TICKET_READ = 'ticket.read',
  TICKET_WRITE = 'ticket.write',

  MANUAL_PLAN_READ = 'manualPlan.read',
  MANUAL_PLAN_WRITE = 'manualPlan.write',

  AI_PLAN_READ = 'aiPlan.read',
  AI_PLAN_WRITE = 'aiPlan.write',

  RADAR_READ = 'radar.read',
  RADAR_WRITE = 'radar.write',

  PLAN_READ = 'plan.read',

  NETWORK_READ = 'network.read',
  NETWORK_WRITE = 'network.write',
  NETWORK_DELETE = 'network.delete',

  TASK_READ = 'task.read',
  TASK_WRITE = 'task.write',
  TASK_DELETE = 'task.delete',
  TASK_UPDATE = 'task.update',

  PENDINGTODO_WRITE = 'pendingTodo.write',

  EMOTIONAL_DIARY_READ = 'emotionalDiary.read',

  EMOTIONAL_ANALYSIS_READ = 'emotionalAnalysis.read',
  EMOTIONAL_ANALYSIS_WRITE = 'emotionalAnalysis.write',
}

export interface IPermissionOptions {
  type: TUserPermissionType;
  scopes: string[];
}

export const USER_PERMISSION_KEY = 'user_permission';
export const RequirePermission = (
  type: TUserPermissionType,
  ...scopes: string[]
) => SetMetadata(USER_PERMISSION_KEY, { type, scopes });

@Injectable()
export class UserPermissionsGuard implements CanActivate {
  private logger = createModuleLogger(UserPermissionsGuard.name);
  constructor(
    private reflector: Reflector,
    private userPermissionsService: UserPermissionsService,
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const options = this.reflector.getAllAndOverride<IPermissionOptions>(
      USER_PERMISSION_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!options) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = request.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      this.logger.warn('Token não fornecido');
      throw new UnauthorizedException('Token não fornecido');
    }

    try {
      const userId = getUserIdFromToken(token);

      if (!userId) {
        this.logger.warn('Token inválido ou expirado');
        throw new UnauthorizedException('Token inválido ou expirado');
      }

      const { scopes: userScopes } =
        await this.userPermissionsService.getLimitsByUserId(userId);

      if (!userScopes || !Array.isArray(userScopes)) {
        this.logger.warn('Usuário não possui permissões definidas');
        throw new UnauthorizedException(
          'Usuário não possui permissões definidas',
        );
      }

      const hasRequiredScopes = options.scopes.every((scope) =>
        userScopes.includes(scope),
      );

      if (!hasRequiredScopes) {
        this.logger.warn('Usuário não possui as permissões necessárias');
        throw new UnauthorizedException(
          'Usuário não possui as permissões necessárias',
        );
      }

      switch (options.type) {
        case TUserPermissionType.CREATE_AI_PLAN:
          await this.userPermissionsService.checkAvailableObjectivesLimit(token, 'ai');
          break;
        case TUserPermissionType.CREATE_MANUAL_PLAN:
          await this.userPermissionsService.checkAvailableObjectivesLimit(token, 'manual');
          break;
        case TUserPermissionType.CREATE_RADAR:
          await this.userPermissionsService.validateRadarLimits(userId);
          break;
        case TUserPermissionType.CREATE_OBJECTIVE_TASKS:
          await this.userPermissionsService.validateObjectiveTasksLimit(
            request.body.userPlanId,
            userId,
          );
          break;
        case TUserPermissionType.PENDINGTODO:
          await this.userPermissionsService.checkAvailablePendingTasksLimit(userId);
          break;
        default:
          throw new UnauthorizedException('Tipo de permissão inválido');
      }

      return true;
    } catch (error) {
      if (error instanceof ThrottlerException) {
        this.logger.error(error.message);
        throw new ThrottlerException(error.message);
      } else {
        this.logger.error(error.message);
        throw new UnauthorizedException(error.message);
      }
    }
  }
}
