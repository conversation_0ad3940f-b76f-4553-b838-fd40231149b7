import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { UsersModule } from '../users/users.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';
import { User } from '../users/entities/user.entity';
import { Permission } from '../permissions/entities/permission.entity';
import { Scope } from '../scopes/entities/scope.entity';
import { QueuesModule } from '../queues/queues.module';
import { UserPermissionsService } from './userPermissions.service';
import { Payment } from '../payments/entities/payment.entity';
import { UserPermissionsController } from './userPermissions.controller';
import { TaskModule } from '../task/task.module';
import { RadarModule } from '../radar/radar.module';
import { AuthModule } from '../auth/auth.module';
import { PaymentsModule } from '../payments/payments.module';
import { PermissionsModule } from '../permissions/permissions.module';
import { UserPlansModule } from '../user-plans/user-plans.module';
import { UserPermissionsGuard } from './userPermissions.guard';

@Module({
  imports: [
    SequelizeModule.forFeature([Payment, User, Permission, Scope]),
    forwardRef(() => UsersModule),
    forwardRef(() => PaymentPlansModule),
    forwardRef(() => QueuesModule),
    forwardRef(() => TaskModule),
    forwardRef(() => AuthModule),
    forwardRef(() => PaymentsModule),
    forwardRef(() => PermissionsModule),
    forwardRef(() => RadarModule),
    forwardRef(() => UserPlansModule),
  ],
  controllers: [UserPermissionsController],
  providers: [UserPermissionsService, UserPermissionsGuard],
  exports: [UserPermissionsService, UserPermissionsGuard, SequelizeModule],
})
export class UserPermissionsModule {}
