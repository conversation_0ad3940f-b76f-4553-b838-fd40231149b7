import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Payment } from './entities/payment.entity';
import { PaymentsController } from './payments.controller';
import { PaymentsService } from './payments.service';
import { UsersModule } from '../users/users.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';
import { User } from '../users/entities/user.entity';
import { Permission } from '../permissions/entities/permission.entity';
import { Scope } from '../scopes/entities/scope.entity';
import { QueuesModule } from '../queues/queues.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Payment, User, Permission, Scope]),
    forwardRef(() => UsersModule),
    forwardRef(() => PaymentPlansModule),
    forwardRef(() => QueuesModule),
  ],
  controllers: [PaymentsController],
  providers: [PaymentsService],
  exports: [PaymentsService, SequelizeModule],
})
export class PaymentsModule {}
