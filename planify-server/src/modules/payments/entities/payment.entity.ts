import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'Payments',
})
export class Payment extends Model<Payment> {
  @Column({
    type: DataType.STRING,
    primaryKey: true,
  })
  id!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customerId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customerDocument!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  cardId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  gatewayPlanId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  discountId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  transactionId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  subscriptionId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  paymentPlanId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  hookType!: string;

  @Column({
    type: DataType.DECIMAL(19, 2),
    allowNull: false,
  })
  amount!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  status!: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  installments!: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  cycleReference!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  transactionDate!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  log!: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expirationAt!: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  reason!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  brand!: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  canceledAt!: Date;

  @ForeignKey(() => User)
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  userId!: string;

  @BelongsTo(() => User)
  user: User;
}
