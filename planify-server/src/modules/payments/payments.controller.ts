/* eslint-disable prettier/prettier */
import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Query,
} from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { AuthGuard } from '../auth/auth.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';
import { ApiTags } from '@nestjs/swagger';
import { ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('payments')
@ApiBearerAuth()
@Controller('payments')
@UseGuards(AuthGuard)
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  async create(@Req() token: string, @Body() data: any) {
    return this.paymentsService.create(data, token);
  }

  @Get()
  @UseGuards(PermissionsGuard)
  @Permissions('admin')
  async findAll(
    @Query('status') status?: 'paid' | 'failed' | 'pending' | 'canceled',
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.paymentsService.findAll(status, page, limit);
  }

  @Get('/payment-history')
  @UseGuards(PermissionsGuard)
  async getPaymentHistory(
    @Req() token: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: 'paid' | 'failed' | 'pending' | 'canceled', 
  ) {
    return this.paymentsService.getPaymentHistory(token, page, limit, status);
  }
}
