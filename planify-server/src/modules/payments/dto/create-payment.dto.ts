import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import {
  TPaymentHistory,
  TPaymentMethods,
  TStatusPlan,
} from 'src/data/@types/paymentPlan';

export class CreatePaymentDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  paymentPlanId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  discountId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  statusCurrentPlan?: TStatusPlan;

  @ApiProperty()
  @IsString()
  @IsOptional()
  observation?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  duoDate?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  paymentDay?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  paymentNumber?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  currentPayment?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  originalPrice?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  currentPrice?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  paymentStatus?: TPaymentHistory;

  @ApiProperty()
  @IsString()
  @IsOptional()
  paymentMethod?: TPaymentMethods;

  @ApiProperty()
  @IsString()
  @IsOptional()
  allowedUntil?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  quantity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalPayment?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  brand?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  canceledAt?: string;
}
