/* eslint-disable prettier/prettier */
import {
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { paginateResults } from 'src/utils/pagination';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { InjectModel } from '@nestjs/sequelize';
import { getUserId } from 'src/utils';
import { PaymentPlansService } from '../payment-plans/payment-plans.service';
import { Payment } from './entities/payment.entity';
import { UsersService } from '../users/users.service';
import { Permission } from '../permissions/entities/permission.entity';
import { Scope } from '../scopes/entities/scope.entity';
import * as crypto from 'crypto';
import { Op } from 'sequelize';
import { QueuesService } from '../queues/queues.service';
import { QUEUE_NAMES } from '../queues/constants';
import { createModuleLogger } from 'src/utils/moduleLogger';

@Injectable()
export class PaymentsService {
  private logger = createModuleLogger(PaymentsService.name);

  constructor(
    @Inject(forwardRef(() => PaymentPlansService))
    private readonly paymentPlansService: PaymentPlansService,
    @InjectModel(Payment)
    private paymentModel: typeof Payment,
    @InjectModel(Permission)
    private permissionModel: typeof Permission,
    @InjectModel(Scope)
    private scopeModel: typeof Scope,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => QueuesService))
    private readonly queuesService: QueuesService,
  ) {}

  async getUserPaymentPlan(userId: string) {
    return await this.paymentModel.findOne({
      where: {
        userId: userId,
        status: 'paid',
      },
      order: [['createdAt', 'DESC']],
    });
  }

  async getUserPlanPermissions(userId: string) {
    const user = await this.usersService.findOne(userId);
    if (!user || !user.paymentPlanId) {
      this.logger.warn(
        `Usuário não possui um plano de pagamento ativo ${userId}`,
      );
      return [];
    }

    const permissions = await this.permissionModel.findAll({
      where: {
        paymentPlanId: user.paymentPlanId,
        status: 'active',
      },
    });

    const scopeTags = [];

    for (const permission of permissions) {
      if (permission.scopes?.length) {
        const scopes = await this.scopeModel.findAll({
          where: {
            id: permission.scopes,
          },
          attributes: ['tag'],
        });
        scopeTags.push(...scopes.map((scope) => scope.tag));
      }
    }

    return [...new Set(scopeTags)];
  }

  async create(data: CreatePaymentDto, token: any) {
    const userId = getUserId(token);

    if (!userId) {
      this.logger.warn(`User not found ${userId}`);
      throw new NotFoundException({ message: 'User not found' });
    }

    const user = await this.usersService.findOne(userId);

    if (!user) {
      this.logger.warn(`User not found ${userId}`);
      throw new NotFoundException({ message: 'User not found' });
    }

    const { paymentPlanId } = data;

    const plan = await this.paymentPlansService.findOne(paymentPlanId);
    if (!plan) {
      this.logger.warn(`paymentPlanId not found ${paymentPlanId}`);
      throw new NotFoundException({ message: 'paymentPlanId not found' });
    }

    await this.usersService.updateUserPaymentPlan(userId, paymentPlanId);

    const permissions = await this.permissionModel.findAll({
      where: {
        paymentPlanId,
        status: 'active',
      },
    });

    const updatePermissions = await Promise.all(
      permissions.map(async (permission) => {
        if (permission.scopes?.length) {
          const scopes = await this.scopeModel.findAll({
            where: {
              id: permission.scopes,
            },
            attributes: ['tag'],
          });
          return {
            ...permission.toJSON(),
            permissions: scopes.map((scope) => scope.tag),
          };
        }
        return permission.toJSON();
      }),
    );

    await this.paymentModel.create({
      id: crypto.randomUUID(),
      userId,
      paymentPlanId,
      amount: Number(plan.price),
      status: 'active',
      hookType: 'subscription',
      transactionDate: new Date().toISOString(),
    });

    await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION, {
      userId: userId,
      type: 'success',
      title: 'Pagamento Confirmado',
      description: 'Seu pagamento foi confirmado com sucesso!',
    });

    return updatePermissions;
  }

  async findAll(
    status?: 'paid' | 'failed' | 'pending' | 'canceled',
    page: number = 1,
    limit: number = 10,
  ) {
    const whereConditions = status ? { status } : {};

    const paginatedPayments = await paginateResults(
      { page, limit },
      {
        model: this.paymentModel,
        whereConditions,
        orderOptions: [['createdAt', 'DESC']],
        attributes: {
          include: ['expirationAt'],
        },
      },
    );

    const paymentsWithDetails = await Promise.all(
      paginatedPayments.data.map(async (payment) => {
        let user = null;
        let paymentPlan = null;
        try {
          user = await this.usersService.findOne(payment.userId);
        } catch (err) {
          if (err?.constructor?.name !== 'NotFoundException') throw err;
          user = null;
        }
        try {
          paymentPlan = await this.paymentPlansService.findOne(
            payment.paymentPlanId,
          );
        } catch (err) {
          paymentPlan = null;
        }

        const shouldIncludePaymentPlan =
          paymentPlan && !paymentPlan.name?.includes('VIEW');

        const paymentJson = payment.toJSON();

        return {
          ...paymentJson,
          user: user
            ? {
                id: user.id,
                name: user.name,
              }
            : null,
          paymentPlan: shouldIncludePaymentPlan
            ? {
                id: paymentPlan.id,
                name: paymentPlan.name,
                price: paymentPlan.price,
                promotionalPrice: paymentPlan.promotionalPrice,
                typePlan: paymentPlan.typePlan,
                recurrencyType: paymentPlan.recurrencyType,
                recurrencyPeriod: paymentPlan.recurrencyPeriod,
                recurrencyInstallments: paymentPlan.recurrencyInstallments,
              }
            : null,
        };
      }),
    );

    return {
      data: paymentsWithDetails,
      totalRecords: paginatedPayments.totalRecords,
      totalPages: paginatedPayments.totalPages,
      currentPage: paginatedPayments.currentPage,
      pageSize: paginatedPayments.pageSize,
    };
  }

  async checkExpiredSubscriptions() {
    try {
      this.logger.info(
        '[PaymentsService] Verificando assinaturas expiradas...',
      );

      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const expiredPayments = await this.paymentModel.findAll({
        where: {
          status: 'paid',
          expirationAt: {
            [Op.lt]: new Date(),
            [Op.gt]: oneHourAgo,
          },
        },
      });

      if (expiredPayments.length === 0) {
        this.logger.info(
          '[PaymentsService] Nenhuma assinatura expirada encontrada na última hora',
        );
        return;
      }

      this.logger.info(
        `[PaymentsService] Encontradas ${expiredPayments.length} assinaturas expiradas`,
      );

      const allPaymentPlans = await this.paymentPlansService.findAll();
      const PLAN_START = allPaymentPlans.find((plan) =>
        plan.name?.includes('START'),
      );

      const PLAN_VIEW = allPaymentPlans.find((plan) =>
        plan.name?.includes('VIEW'),
      );

      if (!PLAN_START || !PLAN_VIEW) {
        this.logger.error(
          '[PaymentsService] Plano de pagamento padrão não encontrado',
        );
        return;
      }

      for (const payment of expiredPayments) {
        try {
          const paymentPlan = await this.paymentPlansService.findOne(
            payment.paymentPlanId,
          );
          const isSubscriptionPlan = paymentPlan?.recurrencyType === 'month';

          if (isSubscriptionPlan) {
            payment.status = 'expired';
            await payment.save();

            const hasNewPayment = await this.paymentModel.findOne({
              where: {
                userId: payment.userId,
                paymentPlanId: payment.paymentPlanId,
                status: 'paid',
                expirationAt: {
                  [Op.gt]: payment.expirationAt,
                },
              },
            });

            if (hasNewPayment) {
              this.logger.info(
                `[PaymentsService] Usuário ${payment.userId} já possui novo pagamento ativo`,
              );
              continue;
            }
          }

          if (paymentPlan.name === 'START') {
            await this.usersService.updateUserPaymentPlan(
              payment.userId,
              PLAN_VIEW.id,
            );
          } else if (paymentPlan.name === 'VIEW') {
            const newPayment = {
              id: crypto.randomUUID(),
              userId: payment.userId,
              paymentPlanId: paymentPlan.id,
              amount: 0,
              status: 'paid',
              hookType: 'renewal',
              transactionDate: new Date().toISOString(),
              expirationAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 ano
            };
            await this.paymentModel.create(newPayment);
          } else {
            await this.usersService.updateUserPaymentPlan(
              payment.userId,
              PLAN_START.id,
            );
            const newPayment = {
              id: crypto.randomUUID(),
              userId: payment.userId,
              paymentPlanId: PLAN_START.id,
              amount: 0,
              status: 'paid',
              hookType: 'downgrade',
              transactionDate: new Date().toISOString(),
              expirationAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
            };
            await this.paymentModel.create(newPayment);
          }

          await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION, {
            userId: payment.userId,
            type: 'plan',
            title: 'Plano Expirado',
            description:
              'Seu plano expirou. Faça uma nova assinatura para continuar aproveitando os recursos premium.',
          });

          this.logger.info(
            `[PaymentsService] Usuário ${payment.userId} movido para plano de downgrade`,
          );
        } catch (error) {
          this.logger.error(
            `[PaymentsService] Erro ao processar downgrade do usuário ${payment.userId}:`,
            error,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        '[PaymentsService] Erro ao verificar assinaturas expiradas:',
        error,
      );
    }
  }

  async checkExpiringSubscriptions() {
    try {
      this.logger.info(
        '[PaymentsService] Verificando assinaturas próximas do vencimento...',
      );

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const thirtyDaysFromNow = new Date(
        today.getTime() + 30 * 24 * 60 * 60 * 1000,
      );
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
      tomorrow.setHours(0, 0, 0, 0);

      const expiringPayments = await this.paymentModel.findAll({
        where: {
          status: 'paid',
          expirationAt: {
            [Op.lte]: thirtyDaysFromNow,
            [Op.gt]: today,
          },
        },
      });

      this.logger.info(
        `[PaymentsService] Encontrados ${expiringPayments.length} pagamentos expirando nos próximos 30 dias`,
      );

      for (const payment of expiringPayments) {
        const user = await this.usersService.findOne(payment.userId);
        if (!user) continue;

        const paymentPlan = await this.paymentPlansService.findOne(
          payment.paymentPlanId,
        );

        if (
          paymentPlan?.recurrencyType === 'month' &&
          paymentPlan?.recurrencyPeriod === 1
        ) {
          this.logger.info(
            `[PaymentsService] Assinatura mensal do usuário ${payment.userId}, não precisa ser notificado`,
          );
          continue;
        }

        const expirationDate = new Date(payment.expirationAt);
        expirationDate.setHours(0, 0, 0, 0);

        const daysUntilExpiration = Math.ceil(
          (expirationDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
        );

        if (daysUntilExpiration > 30) {
          this.logger.info(
            `[PaymentsService] Existem assinaturas ativas que ainda não estão no período de expiração`,
          );
          continue;
        }

        let emailType:
          | 'expires-this-month'
          | 'expires-15-days'
          | 'expires-5-days'
          | 'expires-1-day'
          | null = null;

        if (expirationDate.getTime() === tomorrow.getTime()) {
          emailType = 'expires-1-day';
        } else if (daysUntilExpiration === 5) {
          emailType = 'expires-5-days';
        } else if (daysUntilExpiration === 15) {
          emailType = 'expires-15-days';
        } else if (daysUntilExpiration === 30) {
          emailType = 'expires-this-month';
        }

        if (emailType) {
          await this.queuesService.publish(QUEUE_NAMES.EMAIL, {
            type: emailType,
            data: {
              email: user.email,
              name: user.name,
              expirationDate: payment.expirationAt,
              daysUntilExpiration: daysUntilExpiration,
            },
          });

          this.logger.info(
            `[PaymentsService] Email de vencimento enviado para ${user.email} - Tipo: ${emailType} - Dias até expiração: ${daysUntilExpiration}`,
          );
        }
      }

      this.logger.info(
        '[PaymentsService] Verificação de assinaturas próximas do vencimento concluída',
      );
    } catch (error) {
      this.logger.error(
        '[PaymentsService] Erro ao verificar assinaturas próximas do vencimento:',
        error,
      );
    }
  }

  async getPaymentHistory(
    token: string,
    page: number = 1,
    limit: number = 10,
    status?: 'paid' | 'failed' | 'pending' | 'canceled' | 'expired',
  ) {
    const userId = getUserId(token);
    const whereConditions: any = { userId };

    if (status) {
      whereConditions.status = status;
    }

    const paginatedPayments = await paginateResults(
      { page, limit },
      {
        model: this.paymentModel,
        whereConditions,
        orderOptions: [['createdAt', 'DESC']],
        attributes: {
          include: ['expirationAt'],
        },
      },
    );

    const paymentsWithDetails = await Promise.all(
      paginatedPayments.data.map(async (payment) => {
        const paymentPlan = await this.paymentPlansService.findOne(
          payment.paymentPlanId,
        );

        const formatedAmout = payment?.amount / 100;

        return {
          id: payment?.id ?? null,
          amount: formatedAmout ?? null,
          status: payment?.status ?? null,
          installment: payment?.installments ?? null,
          transactionDate: payment?.transactionDate ?? null,
          transactionId: payment?.transactionId ?? null,
          expirationAt: payment?.expirationAt ?? null,
          paymentPlan: paymentPlan
            ? {
                name: paymentPlan.name,
                typePlan: paymentPlan.typePlan,
              }
            : null,
        };
      }),
    );

    return {
      data: paymentsWithDetails,
      totalRecords: paginatedPayments.totalRecords,
      totalPages: paginatedPayments.totalPages,
      currentPage: paginatedPayments.currentPage,
      pageSize: paginatedPayments.pageSize,
    };
  }
}
