import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit {
  private readonly logger = new Logger(RedisService.name);
  private client: Redis;
  private static isInitialized = false;
  private readonly DEFAULT_TTL = 48 * 60 * 60;

  constructor() {}

  async onModuleInit() {
    if (RedisService.isInitialized) {
      return;
    }

    try {
      this.logger.log('Iniciando conexão com Redis para cache...');
      await this.initializeClient();
      this.logger.log('Conexão com Redis para cache estabelecida com sucesso');
      this.logger.log('Configurações:');
      this.logger.log(`- Host: ${process.env.REDIS_HOST || 'localhost'}`);
      this.logger.log(`- Port: ${process.env.REDIS_PORT || '6379'}`);
      this.logger.log(`- Username: ${process.env.REDIS_USERNAME || 'default'}`);
      this.logger.log(`- Cache TTL padrão: ${this.DEFAULT_TTL}s (48 horas)`);
      RedisService.isInitialized = true;
    } catch (error) {
      this.logger.error('❌ ERRO AO CONECTAR COM REDIS PARA CACHE:', error);
      RedisService.isInitialized = false;
      throw error;
    }
  }

  private async initializeClient() {
    if (!this.client) {
      this.client = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        username: process.env.REDIS_USERNAME || 'default',
        password: process.env.REDIS_PASSWORD || 'redis123',
      });

      this.client.on('error', (error) => {
        this.logger.error('Erro na conexão Redis:', error);
      });
    }
  }

  async get(key: string): Promise<string | null> {
    try {
      await this.ensureConnection();
      return await this.client.get(key);
    } catch (error) {
      this.logger.error(`Erro ao obter valor para chave ${key}:`, error);
      return null;
    }
  }

  async set(key: string, value: string, ttlSeconds?: number): Promise<boolean> {
    try {
      await this.ensureConnection();
      const expiration = ttlSeconds || this.DEFAULT_TTL;
      await this.client.set(key, value, 'EX', expiration);
      return true;
    } catch (error) {
      this.logger.error(`Erro ao definir valor para chave ${key}:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      await this.ensureConnection();
      await this.client.del(key);
      return true;
    } catch (error) {
      this.logger.error(`Erro ao excluir chave ${key}:`, error);
      return false;
    }
  }

  async delByPattern(pattern: string): Promise<boolean> {
    try {
      await this.ensureConnection();
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(...keys);
      }
      return true;
    } catch (error) {
      this.logger.error(`Erro ao excluir chaves com padrão ${pattern}:`, error);
      return false;
    }
  }

  /**
   * Obtém ou define um valor em cache
   * @param key Chave do cache
   * @param fetchFunction Função para buscar o valor caso não exista em cache
   * @param ttlSeconds Tempo de vida do cache em segundos (opcional, padrão: 48h)
   * @returns Valor do cache
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttlSeconds?: number,
  ): Promise<T> {
    try {
      await this.ensureConnection();

      const cachedValue = await this.client.get(key);

      if (cachedValue) {
        this.logger.debug(`Cache hit: ${key}`);
        return JSON.parse(cachedValue);
      }

      this.logger.debug(`Cache miss: ${key} - buscando dados...`);
      const result = await fetchFunction();

      const expiration = ttlSeconds || this.DEFAULT_TTL;
      await this.client.set(key, JSON.stringify(result), 'EX', expiration);

      return result;
    } catch (error) {
      this.logger.error(`Erro ao buscar/definir cache para ${key}:`, error);
      return fetchFunction();
    }
  }

  /**
   * Atualiza o valor em cache
   * @param key Chave do cache
   * @param newValue Novo valor
   * @param ttlSeconds Tempo de vida do cache em segundos (opcional, padrão: 48h)
   * @returns true se sucesso, false se falha
   */
  async update<T>(
    key: string,
    newValue: T,
    ttlSeconds?: number,
  ): Promise<boolean> {
    try {
      await this.ensureConnection();
      const expiration = ttlSeconds || this.DEFAULT_TTL;
      await this.client.set(key, JSON.stringify(newValue), 'EX', expiration);
      return true;
    } catch (error) {
      this.logger.error(`Erro ao atualizar cache para ${key}:`, error);
      return false;
    }
  }

  private async ensureConnection() {
    if (!RedisService.isInitialized) {
      this.logger.log('Tentando inicializar conexão com Redis para cache...');

      try {
        if (!this.client) {
          await this.initializeClient();
        }

        RedisService.isInitialized = true;
        this.logger.log(
          'Conexão com Redis para cache estabelecida com sucesso',
        );
      } catch (error) {
        this.logger.error('Erro ao conectar com Redis para cache:', error);
        RedisService.isInitialized = false;
        throw error;
      }
    }
  }
}
