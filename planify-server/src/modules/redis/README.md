# Módulo de Cache Redis

Este módulo fornece um sistema de cache escalável e fácil de usar para toda a aplicação Planify.

## Características

- Cache com TTL configurável (padrão: 48 horas)
- Suporte a atualizações automáticas de cache após operações de escrita
- Implementação de padrão getOrSet para simplificar o gerenciamento de cache
- Suporte a remoção e atualização de cache
- Totalmente integrado com os módulos existentes

## Como Usar em Outros Módulos

### 1. Importe o RedisModule no seu módulo

```typescript
import { Module } from '@nestjs/common';
import { RedisModule } from '../redis/redis.module';

@Module({
  imports: [
    // Outros imports...
    RedisModule,
  ],
  // ...
})
export class SeuModule {}
```

### 2. Injete o RedisService no seu serviço

```typescript
import { Injectable } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class SeuService {
  constructor(private readonly redisService: RedisService) {}

  // ...
}
```

### 3. Use o método getOrSet para simplificar o uso do cache

```typescript
async obterDados(id: string, cacheTTL?: number): Promise<any> {
  const cacheKey = `seu-prefixo:${id}`;
  const ttl = cacheTTL || 48 * 60 * 60; // Use o padrão ou um TTL personalizado

  return this.redisService.getOrSet(
    cacheKey,
    () => this.buscarDadosNoBanco(id), // Função que será chamada apenas se não houver cache
    ttl
  );
}
```

### 4. Atualize ou invalide o cache após mudanças

```typescript
async criarOuAtualizar(data: any): Promise<any> {
  // Operação no banco de dados
  const resultado = await this.seuRepositorio.save(data);

  // Atualizar o cache com os novos dados
  const cacheKey = `seu-prefixo:${resultado.id}`;
  await this.redisService.update(cacheKey, resultado);

  // Ou invalidar totalmente o cache
  // await this.redisService.del(cacheKey);
  // await this.redisService.delByPattern(`seu-prefixo:*`);

  return resultado;
}
```

## Métodos Disponíveis

| Método                         | Descrição                                                 |
| ------------------------------ | --------------------------------------------------------- |
| `get(key)`                     | Obtém um valor do cache                                   |
| `set(key, value, ttl?)`        | Define um valor no cache com TTL opcional                 |
| `del(key)`                     | Remove uma chave do cache                                 |
| `delByPattern(pattern)`        | Remove chaves do cache que correspondam ao padrão         |
| `getOrSet(key, fetchFn, ttl?)` | Obtém do cache ou executa a função e armazena o resultado |
| `update(key, newValue, ttl?)`  | Atualiza um valor no cache                                |

## Boas Práticas

1. **Prefixos de Chave**: Use prefixos consistentes para as chaves, por exemplo: `user:123`, `post:456`

2. **TTL Adequado**: Escolha um TTL adequado para seus dados. Dados que mudam raramente podem ter um TTL maior.

3. **Padrão de Invalidação**: Invalide apenas o cache necessário, evitando limpar todo o cache quando uma parte específica muda.

4. **Cache por Contexto**: Organize o cache por contexto de usuário/tenant quando apropriado (ex: `user:123:settings`).

5. **Tratamento de Erros**: Sempre trate erros de cache de forma que a aplicação continue funcionando mesmo se o Redis estiver indisponível.

## Exemplo Completo

```typescript
async getDashboard(userId: string, cacheTTL?: number): Promise<DashboardDto> {
  const cacheKey = `dashboard:${userId}`;

  return this.redisService.getOrSet<DashboardDto>(
    cacheKey,
    async () => {
      // Esta função só será executada se o cache não existir
      const user = await this.userRepository.findOne(userId);
      const stats = await this.statsRepository.findByUser(userId);
      const notifications = await this.notificationRepository.findRecent(userId);

      return {
        user: { id: user.id, name: user.name },
        stats,
        notifications
      };
    },
    cacheTTL || 60 * 30 // 30 minutos ou o valor personalizado
  );
}
```
