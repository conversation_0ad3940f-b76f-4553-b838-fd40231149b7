/* eslint-disable prettier/prettier */
import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
import { TCreationStrategy } from 'src/data/@types/TCreationStrategy';
import { TPlanStatus } from 'src/data/@types/TPlanStatus';
import { PlanActivity } from 'src/modules/planActivities/entities/plan-activity.entity';
import { Prompt } from 'src/modules/prompts/entities/prompt.entity';
import { PlanRecommendations } from 'src/modules/recommendations/entities/planRecommendation.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { PlanWeek } from 'src/modules/weeks/entities/plan-weeks.entity';

@Table({
  tableName: 'UserPlans',
})
export class UserPlan extends Model<UserPlan> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @ForeignKey(() => Prompt)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  promptId: string;

  @BelongsTo(() => Prompt)
  prompt: Prompt;

  // @HasMany(() => PlanRecommendations)
  @HasOne(() => PlanRecommendations)
  recommendations: PlanRecommendations;

  @HasMany(() => PlanWeek)
  weeks: PlanWeek[];

  @HasMany(() => PlanActivity)
  activities: PlanActivity[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  title?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  target: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  currentParam: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  initialParam: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  finalTarget: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  finalTargetParam: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  startedAt: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isGeneratingPlan?: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isCompleted?: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isCompletedGeneration?: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  currentStepGeneration: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  endAt: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  duration: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  totalSteps: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  currentStep: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  totalWeeks: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  pendingSteps: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  hasHealthRisk: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  acceptedTerms: boolean;

  @Column({
    type: DataType.ENUM(
      'inProgress',
      'delayed',
      'completed',
      'inCreation',
      'waiting',
      'partiallyCompleted',
    ),
    allowNull: true,
  })
  planStatus?: TPlanStatus;

  // deletedAt
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deletedAt?: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  activitiesPerWeek: number;

  @Column({
    type: DataType.ENUM('auto', 'manual'),
    defaultValue: 'auto',
    allowNull: true,
  })
  creationStrategy?: TCreationStrategy;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  planColor?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  activityLevel?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  weeklyActivityFrequency?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  participantAge?: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  planSchemaValues?: Record<string, any>;
}
