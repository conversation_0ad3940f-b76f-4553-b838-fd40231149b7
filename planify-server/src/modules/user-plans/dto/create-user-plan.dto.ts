import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsArray,
  IsEnum,
} from 'class-validator';
import { TPlanStatus } from 'src/data/@types/TPlanStatus';
import { TCreationStrategy } from 'src/data/@types/TCreationStrategy';

export class CreateUserPlanDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  promptId: string;

  @ApiProperty()
  @IsOptional()
  title?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  target: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  currentParam?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  initialParam?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  finalTarget?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  finalTargetParam?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  startedAt: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  endAt: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  category?: string | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  duration: string;

  @ApiProperty({ enum: TCreationStrategy })
  @IsEnum(TCreationStrategy)
  @IsOptional()
  creationStrategy?: TCreationStrategy;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  totalSteps: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  currentStep: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  createEmptyWeeks?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isGeneratingPlan?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isCompletedGeneration?: boolean;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  currentStepGeneration?: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  totalWeeks: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  pendingSteps: number;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  hasHealthRisk: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  acceptedTerms: boolean;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  weeks?: [];

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  activitiesPerWeek?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  planColor?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  planStatus?: TPlanStatus;

  @ApiProperty()
  @IsString()
  @IsOptional()
  activityLevel?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  weeklyActivityFrequency?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  participantAge?: string;

  @ApiProperty({ type: 'object', additionalProperties: true })
  @IsOptional()
  planSchemaValues?: Record<string, any>;
}
