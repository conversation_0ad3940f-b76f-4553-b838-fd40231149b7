/* eslint-disable prettier/prettier */
import { Module, forwardRef } from '@nestjs/common';
import { UserPlansService } from './user-plans.service';
import { UserPlansController } from './user-plans.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { PlanWeek } from '../weeks/entities/plan-weeks.entity';
import { Prompt } from '../prompts/entities/prompt.entity';
import { User } from '../users/entities/user.entity';
import { Category } from '../categories/entities/category.entity';
import { SubCategory } from '../subCategory/entities/subCategory.entity';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { UserPlan } from './entities/user-plan.entity';
import { PlanActivity } from '../planActivities/entities/plan-activity.entity';
import { PlanRecommendations } from '../recommendations/entities/planRecommendation.entity';
import { RecommendationsModule } from '../recommendations/recomendations.module';
import { Notifications } from '../notifications/entities/notification.entity';
import { AchievementRecordsModule } from '../achievementRecords/achievementRecords.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { UserPermissionsModule } from '../userPermissions/userPermissions.module';
import { WeightHistoriesModule } from '../weight-histories/weight-histories.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      UserPlan,
      PlanWeek,
      PlanActivity,
      Prompt,
      User,
      Category,
      SubCategory,
      PlanRecommendations,
      Notifications,
    ]),

    JwtModule,
    AuthModule,
    UserPermissionsModule,

    forwardRef(() => RecommendationsModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => AchievementRecordsModule),
    forwardRef(() => WeightHistoriesModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [UserPlansController],
  providers: [UserPlansService],
  exports: [UserPlansService, SequelizeModule],
})
export class UserPlansModule {}
