/* eslint-disable prettier/prettier */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  Put,
  UseGuards,
  Req,
  Query,
  Sse,
} from '@nestjs/common';
import { UserPlansService } from './user-plans.service';
import { CreateUserPlanDto } from './dto/create-user-plan.dto';
import { ApiTags } from '@nestjs/swagger';
import { TActivityStatus } from 'src/data/@types/planAction';
import { AuthGuard } from '../auth/auth.guard';
import { TPlanStatus } from 'src/data/@types/TPlanStatus';
import { Observable, from, map, interval } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import {
  RequirePermission,
  TUserPermissionType,
  TUserScopes,
  UserPermissionsGuard,
} from '../userPermissions/userPermissions.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';
import { getUserIdFromToken } from 'src/utils';
import { UserPermissionsService } from '../userPermissions/userPermissions.service';

type SimplifiedMessageEvent = {
  data: string;
  type?: string;
  lastEventId?: string;
};

@ApiTags('Users Plans')
@Controller('user-plans')
export class UserPlansController {
  constructor(
    private readonly userPlansService: UserPlansService,
    private readonly userPermissionsService: UserPermissionsService,
  ) {}

  @Sse('/details/:id')
  async sseUpdates(
    @Param('id') id: string,
    @Query('token') token: string, // Pega o token da query string
  ): Promise<Observable<SimplifiedMessageEvent>> {
    const userId = getUserIdFromToken(token);
    await this.userPlansService.checkAndAdjustActivityLimits(id);
    return interval(1000).pipe(
      switchMap(() =>
        from(this.userPlansService.findPlanWithDetails(id, userId)),
      ),
      map((plan) => ({
        data: JSON.stringify(plan),
        type: 'message',
        lastEventId: '',
      })),
    );
  }

  @UseGuards(AuthGuard, PermissionsGuard, UserPermissionsGuard)
  @Permissions(TUserScopes.MANUAL_PLAN_WRITE)
  @RequirePermission(TUserPermissionType.CREATE_MANUAL_PLAN)
  @Post()
  create(@Body() createUserPlanDto: CreateUserPlanDto, @Req() token: string) {
    return this.userPlansService.create(createUserPlanDto, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard, UserPermissionsGuard)
  @Permissions(TUserScopes.MANUAL_PLAN_WRITE)
  @RequirePermission(TUserPermissionType.CREATE_MANUAL_PLAN)
  @Post('/week')
  createWeek(@Body() weekDto: any) {
    return this.userPlansService.createWeek(weekDto);
  }

  @UseGuards(AuthGuard, PermissionsGuard, UserPermissionsGuard)
  @Permissions(TUserScopes.MANUAL_PLAN_WRITE)
  @RequirePermission(TUserPermissionType.CREATE_OBJECTIVE_TASKS)
  @Post('/activity')
  createActivity(@Body() weekDto: any) {
    return this.userPlansService.createActivity(weekDto);
  }

  @UseGuards(AuthGuard, PermissionsGuard, UserPermissionsGuard)
  @Permissions(TUserScopes.AI_PLAN_WRITE)
  @RequirePermission(TUserPermissionType.CREATE_AI_PLAN)
  @Post('/generate')
  generatePlan(@Body() weekDto: any) {
    return this.userPlansService.generatePlan(weekDto);
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  customizePlan(@Param('id') id: string, @Body() plan: any) {
    return this.userPlansService.customizePlan(id, plan);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions(TUserScopes.MANUAL_PLAN_WRITE)
  @Patch('/infos/:id')
  updatePlan(@Param('id') id: string, @Body() plan: any) {
    return this.userPlansService.updatePlan(id, plan);
  }

  @Post('/close/:id')
  closeGeneration(@Param('id') id: string) {
    return this.userPlansService.closeGeneration(id);
  }

  @Get('/check')
  jobCheckPlans() {
    return this.userPlansService.jobCheckPlans();
  }

  @Permissions(TUserScopes.PLAN_READ)
  @UseGuards(AuthGuard)
  @Get()
  findAll(@Req() token: string) {
    return this.userPlansService.findAll(token);
  }

  @UseGuards(AuthGuard)
  @Get('/count-by-status/all')
  getPlansCountByStatus(@Req() token: string) {
    return this.userPlansService.getPlansCountByStatus(token);
  }

  @UseGuards(AuthGuard)
  @Get('/:id/all')
  findByUser(
    @Param('id') id: string,
    @Query('planStatus') planStatus: TPlanStatus,
  ) {
    return this.userPlansService.findByUser(id, planStatus);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions(TUserScopes.PLAN_READ)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.userPlansService.findOne(id);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  @HttpCode(204)
  async remove(@Param('id') id: string) {
    await this.userPlansService.removePlan(id);
  }

  @UseGuards(AuthGuard)
  @Put('/activity/:id')
  @HttpCode(200)
  updateActivityStatus(
    @Param('id') id: string,
    @Body('activityStatus') status: TActivityStatus,
    @Body('observation') observation: string,
    @Req() token: string,
  ) {
    return this.userPlansService.updateActivityStatus(
      id,
      status,
      observation,
      token,
    );
  }

  @UseGuards(AuthGuard)
  @Get('/activity/:id')
  getActivity(@Param('id') id: string) {
    return this.userPlansService.getActivity(id);
  }

  @UseGuards(AuthGuard)
  @Delete('/activity/:id')
  @HttpCode(200)
  async removeActivity(@Param('id') id: string, @Req() token: string) {
    return this.userPlansService.removeActivity(id, token);
  }

  @UseGuards(AuthGuard)
  @Patch('/activity/:id')
  @HttpCode(201)
  updateActivity(@Param('id') id: string, @Body() data: any) {
    return this.userPlansService.updateActivity(id, data);
  }

  // update Activity Description
  @UseGuards(AuthGuard)
  @Put('/activity/observation/:id')
  updateActivityObservation(
    @Param('id') id: string,
    @Body('observation') observation: string,
  ) {
    return this.userPlansService.updateActivityObservation(id, observation);
  }

  @UseGuards(AuthGuard)
  @Get('/activities/month/:month')
  getActivityByMonth(@Param('month') startMonth: Date, @Req() token: string) {
    return this.userPlansService.getAllActivitiesByMonth(startMonth, token);
  }

  @UseGuards(AuthGuard)
  @Get('/activities/day/:day')
  getAllActivitiesByDay(
    @Param('day') day: Date,
    @Req() token: string,
    @Query('status') status?: TActivityStatus,
  ) {
    return this.userPlansService.getAllActivitiesByDay(day, token, status);
  }

  @UseGuards(AuthGuard)
  @Get('/activities/:type')
  getAllActivitiesByType(@Param('type') type: string, @Req() token: string) {
    return this.userPlansService.getAllActivitiesByType(type, token);
  }

  @UseGuards(AuthGuard)
  @Patch('/update-plan-status/:status')
  updatePlanStatus(
    @Param('status') status: TPlanStatus,
    @Param('planId') planId: string,
  ) {
    return this.userPlansService.updatePlanStatus(status, planId);
  }

  @UseGuards(AuthGuard)
  @Get('/get-plan-by-activity/:activityId')
  getPlanByActivityId(@Param('activityId') activityId: string) {
    return this.userPlansService.getPlanByActivityId(activityId);
  }

  @UseGuards(AuthGuard)
  @Put('/activity/activityDetails/:id')
  @HttpCode(204)
  async updateActivityDetails(
    @Param('id') id: string,
    @Body() data: any,
    @Req() token: string,
  ) {
    await this.userPlansService.updateActivityDetails(id, data, token);
  }

  @UseGuards(AuthGuard)
  @Put('/activity/activityColor/:id')
  @HttpCode(204)
  async updateActivityColor(
    @Param('id') id: string,
    @Body() data: any,
    @Req() token: string,
  ) {
    await this.userPlansService.updateActivityColor(id, data, token);
  }

  @UseGuards(AuthGuard)
  @Get('/activities/timeline/:planId/:allPlanActivities')
  fetchActivitiesWithinPlanDates(
    @Req() token: string,
    @Param('planId') planId: string,
    @Param('allPlanActivities') allPlanActivities: string,
  ) {
    return this.userPlansService.searchActivitiesPlanDates(
      token,
      planId,
      allPlanActivities,
    );
  }
}
