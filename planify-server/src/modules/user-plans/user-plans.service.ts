/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
  Logger,
} from '@nestjs/common';
import { CreateUserPlanDto } from './dto/create-user-plan.dto';
import { UpdateUserPlanDto } from './dto/update-user-plan.dto';
import { InjectModel } from '@nestjs/sequelize';
import {
  startOfWeek,
  endOfWeek,
  addWeeks,
  format,
  startOfMonth,
  endOfMonth,
  startOfDay,
  endOfDay,
  isAfter,
  differenceInYears,
} from 'date-fns';
import { PlanWeek } from '../weeks/entities/plan-weeks.entity';
import { Prompt } from '../prompts/entities/prompt.entity';
import axios from 'axios';
import { User } from '../users/entities/user.entity';
import { TActivityStatus } from 'src/data/@types/planAction';
import { Category } from '../categories/entities/category.entity';
import { SubCategory } from '../subCategory/entities/subCategory.entity';
import { UserPlan } from './entities/user-plan.entity';
import { PlanActivity } from '../planActivities/entities/plan-activity.entity';
import { FindOptions, QueryTypes } from 'sequelize';
import { filterByPeriod, getUserId, getUserIdFromToken } from 'src/utils';
import { Op } from 'sequelize';
import { Cron, CronExpression } from '@nestjs/schedule';
import { TPlanStatus } from 'src/data/@types/TPlanStatus';
import { PlanRecommendations } from '../recommendations/entities/planRecommendation.entity';
import { RecomendationsService } from '../recommendations/recommendations.service';
import { Notifications } from '../notifications/entities/notification.entity';
import { TNotificationType } from 'src/data/@types/TNotificationType';
import { ClientProxy } from '@nestjs/microservices';
import { AchievementConditionValidationService } from '../achievementRecords/achievementConditionValidation.service';
import { NotificationsService } from '../notifications/notifications.service';
import { QueuesService } from '../queues/queues.service';
import { QUEUE_NAMES } from '../queues/constants';
import { globalQueuesService } from '../queues/queues.service';
import { QueueName } from '../queues/queues.service';
import { TCreationStrategy } from 'src/data/@types/TCreationStrategy';
import { UserPermissionsService } from '../userPermissions/userPermissions.service';
import { UserInfos } from '../users/entities/user-profile.entity';
import { calculateAge } from 'src/utils/calculateAge';
import { WeightHistoriesService } from '../weight-histories/weight-histories.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class UserPlansService {
  private readonly logger = new Logger(UserPlansService.name);

  constructor(
    @InjectModel(UserPlan)
    private userPlanModel: typeof UserPlan,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(PlanWeek)
    private planWeekModel: typeof PlanWeek,
    @InjectModel(PlanActivity)
    private planActivityModel: typeof PlanActivity,
    @InjectModel(Prompt)
    private promptModel: typeof Prompt,
    @InjectModel(Category)
    private categoryModel: typeof Category,
    @InjectModel(SubCategory)
    private subCategoryModel: typeof SubCategory,
    @Inject(forwardRef(() => AchievementConditionValidationService))
    private medalConditionService: AchievementConditionValidationService,
    @Inject(forwardRef(() => RecomendationsService))
    private recomendationsService: RecomendationsService,
    @Inject(forwardRef(() => NotificationsService))
    private notificationService: NotificationsService,
    @Inject(forwardRef(() => UserPermissionsService))
    private userPermissionsService: UserPermissionsService,
    private weightHistoriesService: WeightHistoriesService,
    private usersService: UsersService,
  ) { }

  private parseWeight(weight: string | number): number {
    this.logger.debug(
      `Parsing weight value: ${weight} (type: ${typeof weight})`,
    );

    if (typeof weight === 'string') {
      const cleanWeight = weight.replace(/,/g, '.').replace(/[^\d.]/g, '');
      this.logger.debug(`Cleaned weight string: ${cleanWeight}`);

      const parts = cleanWeight.split('.');
      if (parts.length > 2) {
        throw new BadRequestException(
          'Invalid weight format: multiple decimal points',
        );
      }

      const numericWeight = parseFloat(cleanWeight);
      this.logger.debug(`Parsed numeric weight: ${numericWeight}`);

      if (isNaN(numericWeight)) {
        throw new BadRequestException(
          'Invalid weight value: could not parse to number',
        );
      }

      if (numericWeight <= 0 || numericWeight >= 1000) {
        throw new BadRequestException('Weight must be between 0 and 1000 kg');
      }

      return Number(numericWeight.toFixed(2));
    }

    if (typeof weight === 'number') {
      if (weight <= 0 || weight >= 1000) {
        throw new BadRequestException('Weight must be between 0 and 1000 kg');
      }
      return Number(weight.toFixed(2));
    }

    throw new BadRequestException(`Unsupported weight type: ${typeof weight}`);
  }

  async createManualPlan(userPlan: CreateUserPlanDto): Promise<any> {
    try {
      const promptExists = await this.promptModel.findByPk(userPlan.promptId);
      if (!promptExists) {
        throw new BadRequestException({
          message: 'Prompt not found',
        });
      }

      const planData = {
        ...userPlan,
      };

      const plan = await this.userPlanModel.create(planData);
      const planWeeks = userPlan?.totalWeeks ?? 0;
      const startedAt = new Date(userPlan.startedAt);

      if (userPlan?.createEmptyWeeks) {
        for (let i = 0; i < planWeeks; i++) {
          const weekStart = addWeeks(startOfWeek(startedAt), i);
          const weekEnd = addWeeks(endOfWeek(startedAt), i);

          await this.createWeek({
            userId: plan.userId,
            planId: plan.id,
            weekNumber: i + 1,
            startDate: weekStart,
            endDate: weekEnd,
          });
        }
      }

      return plan;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException({
        message: 'Error creating manual plan',
        error: error.message,
      });
    }
  }

  async jobCheckPlans() {
    const penddingPlans = await this.userPlanModel.findAll({
      where: {
        isCompletedGeneration: false,
        isGeneratingPlan: true,
      },
    });

    penddingPlans.forEach((plan) => {
      this.generatePlan(plan);
    });

    return {
      message: 'Job already started successfully!',
    };
  }

  async create(userPlan: CreateUserPlanDto, token?: string): Promise<any> {
    const transaction = await this.userPlanModel.sequelize.transaction();

    try {
      const loggedUser = getUserId(token);

      const promptExists = await this.promptModel.findByPk(userPlan.promptId);
      if (!promptExists) {
        throw new BadRequestException({
          message: 'Prompt not found',
        });
      }

      const userExists = await this.userModel.findByPk(userPlan.userId, {
        include: [
          {
            model: UserInfos,
            as: 'userInfos',
          },
        ],
      });

      if (!userExists) {
        throw new BadRequestException({
          message: 'User not found',
        });
      }

      if (userPlan.currentParam) {
        try {
          this.logger.debug(
            `Processing weight update. Original value: ${userPlan.currentParam}`,
          );

          const weightValue = this.parseWeight(userPlan.currentParam);
          this.logger.debug(`Parsed weight value: ${weightValue}`);

          await this.weightHistoriesService.recordWeight(
            userPlan.userId,
            weightValue,
            transaction,
          );

          const [userInfo] = await UserInfos.findOrCreate({
            where: { userId: userPlan.userId },
            defaults: { userId: userPlan.userId },
            transaction,
          });

          await userInfo.update(
            { weight: String(weightValue) },
            { transaction },
          );
          this.logger.debug(
            `Successfully updated user weight to: ${weightValue}`,
          );
        } catch (error) {
          this.logger.error(`Error processing weight: ${error.message}`);
          throw new BadRequestException({
            message: 'Invalid weight value',
            error: error.message,
            originalValue: userPlan.currentParam,
          });
        }
      }

      const planData = {
        ...userPlan,
        creationStrategy: userPlan.creationStrategy || TCreationStrategy.AUTO,
        activityLevel: userExists?.userInfos?.activityLevel || '',
        weeklyActivityFrequency: `${userExists?.userInfos?.currentActivitiesWeek > 0 ? userExists?.userInfos?.currentActivitiesWeek : ''}`,
        currentParam:
          userPlan?.currentParam || userExists?.userInfos?.weight || '',
        participantAge: calculateAge(userExists?.userInfos?.birthDate),
      };

      const plan =
        planData?.createEmptyWeeks &&
          planData.creationStrategy === TCreationStrategy.MANUAL
          ? await this.createManualPlan(planData)
          : await this.userPlanModel.create(planData);

      if (loggedUser) {
        const milestones =
          await this.medalConditionService.checkObjectiveCreationAchievement(
            loggedUser,
          );

        // Se houver marcos, emite o evento de conquista
        if (milestones) {
          const payload = {
            userId: loggedUser,
            badgeCode: milestones,
          };
          await this.publishToQueue(QUEUE_NAMES.ACHIEVEMENT, payload);
        }
      }

      await transaction.commit();
      return plan;
    } catch (error) {
      await transaction.rollback();
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error creating plan: ${error.message}`);
      throw new InternalServerErrorException({
        message: 'Error creating plan',
        error: error.message,
      });
    }
  }

  async updateActivityStatusCron() {
    const currentDate = new Date();
    const previousDate = new Date(currentDate);
    previousDate.setDate(previousDate.getDate() - 1);

    await this.planActivityModel.update(
      { activityStatus: 'in_progress' },
      {
        where: {
          date: {
            [Op.between]: [startOfDay(currentDate), endOfDay(currentDate)],
          },
          isBlocked: {
            [Op.not]: true,
          },
        },
      },
    );

    // Atualiza atividades do dia anterior para 'pending', ignorando atividades com isBlocked = 1
    await this.planActivityModel.update(
      { activityStatus: 'pending' },
      {
        where: {
          date: {
            [Op.between]: [startOfDay(previousDate), endOfDay(previousDate)],
          },
          activityStatus: {
            [Op.not]: 'done',
          },
          isBlocked: {
            [Op.not]: true,
          },
        },
      },
    );

    const plans = await this.userPlanModel.findAll({
      where: {
        planStatus: 'inProgress',
      },
      include: [
        {
          model: PlanWeek,
          as: 'weeks',
          include: [
            {
              model: PlanActivity,
              as: 'activities',
              where: {
                activityStatus: 'pending',
                date: {
                  [Op.lte]: currentDate,
                },
                isBlocked: {
                  [Op.not]: true,
                },
              },
            },
          ],
        },
      ],
    });

    for (const plan of plans) {
      if (plan.weeks.length > 0) {
        const week = plan.weeks[0];
        if (week.activities.length > 0) {
          plan.planStatus = TPlanStatus.DELAYED;
          await plan.save();
        }
      }
    }

    return {
      message: 'Cron job finished successfully!',
    };
  }

  async checkDelayedActivitiesAndNotify() {
    const currentDate = new Date();

    const delayedActivities = await PlanActivity.findAll({
      where: {
        activityStatus: 'pending',
        date: {
          [Op.lt]: currentDate,
        },
        isBlocked: {
          [Op.not]: true,
        },
      },
      include: [
        { model: SubCategory, as: 'subCategory', attributes: ['id', 'name'] },
        {
          model: this.userPlanModel,
          as: 'userPlan',
          attributes: ['title'],
          where: { deletedAt: null },
        },
      ],
    });

    for (const activity of delayedActivities) {
      const notification =
        await this.notificationService.getNotificationBytargetId(activity);

      if (!notification) {
        await this.notificationService.create({
          userId: activity.userId,
          title: 'Atividade atrasada',
          description: `A atividade ${activity.activityDescription} do plano ${activity.userPlan.title} está em atraso!`,
          notificationStatus: TNotificationType.DELAYED,
          targetId: activity.id,
          targetType: 'activity',
          targetDate: activity.date,
        });
      } else {
        notification.updatedAt = new Date();
        notification.isRead = false;
        await notification.save();
      }
    }
  }

  async notifyDelayedActivities8AM() {
    await this.checkDelayedActivitiesAndNotify();
  }

  async notifyDelayedActivities1PM() {
    await this.checkDelayedActivitiesAndNotify();
  }

  async notifyDelayedActivities8PM() {
    await this.checkDelayedActivitiesAndNotify();
  }

  async updatePlansToInProgressIfExecutionStartsToday() {
    try {
      const currentDate = new Date();

      await this.userPlanModel.update(
        { planStatus: TPlanStatus.IN_PROGRESS },
        {
          where: {
            [Op.or]: [
              {
                planStatus: TPlanStatus.IN_CREATION,
                startedAt: {
                  [Op.between]: [
                    startOfDay(currentDate),
                    endOfDay(currentDate),
                  ],
                },
              },
              {
                planStatus: TPlanStatus.WAITING,
                startedAt: {
                  [Op.between]: [
                    startOfDay(currentDate),
                    endOfDay(currentDate),
                  ],
                },
              },
            ],
          },
        },
      );
    } catch (error) {
      console.error('Error updating plans:', error);
    }
  }

  async closeGeneration(id: string) {
    const userPlan = await this.userPlanModel.findByPk(id);

    if (!userPlan) {
      throw new NotFoundException({ error: 'Plan ID not found' });
    }

    userPlan.isCompletedGeneration = true;
    userPlan.isGeneratingPlan = false;
    await userPlan.save();
  }

  async customizePlan(id: string, data: any) {
    const userLimits = await this.userPermissionsService.verifyUserLimits(
      data.userId,
    );
    const userPlan = await this.userPlanModel.findByPk(id);

    if (!userPlan) {
      throw new NotFoundException({ error: 'Plan ID not found' });
    }

    const oldWeeks = await this.planWeekModel.findAll({
      where: { planId: id },
    });

    // Obter a contagem atual de atividades aiObjectiveTasks
    const currentAiObjectiveTasksCount =
      await this.getCurrentAiObjectiveTasksCount(id);
    const aiObjectiveTasksLimit = userLimits.aiObjectiveTasks.maxLimit;

    let newAiObjectiveTasksCount = currentAiObjectiveTasksCount; // Inicializa com a contagem atual

    for (const week of data.weeks) {
      const weekAlreadyExists = oldWeeks.find(
        (oldWeek) => oldWeek.weekNumber === week.weekNumber,
      );
      let weekId: string;

      if (!weekAlreadyExists) {
        const newWeek = await this.createWeek({ ...week, planId: userPlan.id });
        weekId = newWeek.id;
      } else {
        const currentWeek = await this.planWeekModel.findOne({
          where: { planId: id, weekNumber: week.weekNumber },
        });
        weekId = currentWeek?.id;
      }

      for (const activityDto of week.activities) {
        if (activityDto.activityDescription && activityDto.timeActivity) {
          const subCategory = await this.subCategoryModel.findOne({
            where: { name: activityDto.activityDescription || data?.category },
          });

          // Determinar se essa atividade deve ser bloqueada
          const willExceedLimit =
            aiObjectiveTasksLimit !== null &&
            aiObjectiveTasksLimit !== 0 &&
            newAiObjectiveTasksCount + 1 > aiObjectiveTasksLimit;
          const isBlocked = willExceedLimit ? 1 : null;

          // Criar atividade
          await this.createActivity({
            date: activityDto?.date,
            activityDescription: activityDto?.activityDescription,
            activityDetails: activityDto?.activityDetails,
            timeActivity: activityDto?.timeActivity,
            weekId,
            userPlanId: userPlan.id,
            category: subCategory?.id || data?.category || 'No',
            isBlocked,
          });

          newAiObjectiveTasksCount++; // Incrementar o contador após adicionar a atividade
        } else {
          console.error('activityDescription and timeActivity are required.');
        }
      }
    }

    await this.recomendationsService.generatePlanRecommendation(id);
  }

  // Método para obter a contagem atual de aiObjectiveTasks
  async getCurrentAiObjectiveTasksCount(userPlanId: string): Promise<number> {
    // Implemente a lógica para contar as atividades do tipo aiObjectiveTasks
    // Exemplo:
    const count = await this.planActivityModel.count({
      where: {
        userPlanId: userPlanId,
        // Adicione condições para identificar atividades do tipo aiObjectiveTasks
      },
    });
    return count;
  }

  async createWeek(weekData: any) {
    return await this.planWeekModel.create(weekData);
  }

  async generatePlan(data: any) {
    try {
      const currentUser = await this.userModel.findOne({
        where: { id: data?.userId },
      });

      if (!currentUser) {
        throw new BadRequestException({
          message: 'User not found',
        });
      }

      if (data.promptId) {
        const promptExists = await this.promptModel.findByPk(data.promptId);
        if (!promptExists) {
          throw new BadRequestException({
            message: 'Prompt not found',
          });
        }
      }

      let createdPlan = null;

      // Verifica se o plano é futuro
      if (this.isFuturePlan(data?.startAt, data?.startedAt)) {
        data.planStatus = TPlanStatus.WAITING;
      }

      const payloadData = {
        activitiesPerWeek: data.activitiesPerWeek ?? 3,
        title: data.title,
        userId: data.userId,
        promptId: data.promptId,
        startedAt: data?.startAt ?? data?.startedAt,
        endAt: data.endAt,
        target: data.target,
        creationStrategy: TCreationStrategy.AUTO,
        initialParam: data.initialParam,
        currentParam: data.currentParam,
        finalTargetParam: data.finalTargetParam,
        finalTarget: data.finalTarget,
        totalWeeks: data.totalWeeks,
        duration: data.duration,
        totalSteps: data.totalSteps,
        currentStep: 0,
        currentStepGeneration: 0,
        pendingSteps: data.totalSteps,
        hasHealthRisk: data.hasHealthRisk,
        acceptedTerms: data.acceptedTerms,
        isGeneratingPlan: true,
        category: data?.category ?? null,
        planColor: data?.planColor ?? '#0',
        planSchemaValues: data?.planSchemaValues ?? null,
        planStatus:
          (data?.planStatus as TPlanStatus) ?? TPlanStatus.IN_CREATION,
      };
      if (!data?.id) {
        createdPlan = await this.create(payloadData);
      }

      const API_CURRENT_URL = process.env.API_CURRENT_URL;
      const PLANAI_MODEL_CONNECTION = process.env.PLANAI_MODEL_CONNECTION;

      const planId = createdPlan?.id ?? data?.id;

      console.log('[PLAN DATA]: ', payloadData);

      try {
        await axios.post(`${PLANAI_MODEL_CONNECTION}/plan/${planId}`, {
          receiver: API_CURRENT_URL,
        });
      } catch (error) {
        console.error('Error generating plan: ', error);
        throw new InternalServerErrorException({
          message: 'Error generating plan in external service',
          error: error.message,
        });
      }

      return {
        message: 'Your plan is being generated. Please wait a moments.',
        plan: createdPlan,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof InternalServerErrorException
      ) {
        throw error;
      }
      throw new InternalServerErrorException({
        message: 'Error generating plan',
        error: error.message,
      });
    }
  }

  async createActivity(activityData: any) {
    let transaction;
    try {
      transaction = await this.userPlanModel.sequelize.transaction();

      const userPlan = await this.userPlanModel.findByPk(
        activityData.userPlanId,
        { transaction },
      );
      if (!userPlan) {
        throw new NotFoundException({ message: 'User Plan ID not found' });
      }

      const weekExists = await this.planWeekModel.findByPk(
        activityData.weekId,
        { transaction },
      );
      if (!weekExists) {
        throw new NotFoundException({ message: 'Week ID not found' });
      }

      const formattedData = {
        userId: userPlan.userId || null,
        userPlanId: activityData.userPlanId,
        weekId: activityData.weekId,
        date: activityData.date,
        activityDescription: activityData.activityDescription,
        activityDetails: JSON.stringify(activityData.activityDetails),
        timeActivity: activityData.timeActivity,
        category: activityData.category || 'No',
        observation: activityData.observation || null,
        isBlocked: activityData?.isBlocked || null,
      };

      const data = await this.planActivityModel.create(formattedData, {
        transaction,
      });

      if (userPlan.planStatus === TPlanStatus.IN_CREATION) {
        const activitiesCount = await this.planActivityModel.count({
          where: { userPlanId: userPlan.id },
        });

        if (activitiesCount === 9) {
          if (this.isFuturePlan(null, userPlan.startedAt)) {
            userPlan.planStatus = TPlanStatus.WAITING;
          } else {
            userPlan.planStatus = TPlanStatus.IN_PROGRESS;
          }
          await userPlan.save({ transaction });
        }
      }

      if (userPlan.planStatus === TPlanStatus.COMPLETED) {
        userPlan.planStatus = TPlanStatus.IN_PROGRESS;
        await userPlan.save({ transaction });
      }

      await transaction.commit();

      return {
        ...formattedData,
        id: data?.id || null,
        planStatus: userPlan?.planStatus || null,
      };
    } catch (error) {
      if (transaction) await transaction.rollback();
      throw new Error('Error creating activity: ' + error.message);
    }
  }

  async updateActivityObservation(activityId: string, observation: string) {
    const activity = await this.planActivityModel.findByPk(activityId);

    if (!activity) {
      throw new NotFoundException('Activity not found');
    }

    activity.observation = observation;
    await activity.save();

    return activity;
  }

  async findAll(token: string): Promise<UserPlan[]> {
    const loggedUser = getUserId(token);

    const userPlans = await this.userPlanModel.findAll({
      where: {
        userId: loggedUser,
        deletedAt: null,
      },
      include: [
        {
          model: Prompt,
          as: 'prompt',
          attributes: ['id', 'name'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name', 'description'],
            },
          ],
        },
        {
          model: PlanWeek,
          as: 'weeks',
          separate: true,
          include: [
            {
              model: PlanActivity,
              as: 'activities',
              attributes: {
                exclude: ['planWeekId', 'userPlanId', 'creationStrategy'],
              },
              include: [
                {
                  model: SubCategory,
                  as: 'subCategory',
                  attributes: ['id', 'name'],
                },
              ],
            },
          ],
        },
        {
          model: PlanRecommendations,
          as: 'recommendations',
          attributes: {
            exclude: ['userId', 'planId', 'createdAt', 'updatedAt'],
          },
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    // Ordenar as semanas e atividades após o carregamento
    for (const plan of userPlans) {
      if (plan.weeks) {
        plan.weeks.sort((a, b) => a.weekNumber - b.weekNumber);
        for (const week of plan.weeks) {
          if (week.activities) {
            week.activities.sort(
              (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
            );

            // Parse activityDetails para cada atividade
            for (const activity of week.activities) {
              if (activity?.activityDetails) {
                try {
                  activity.activityDetails = JSON.parse(
                    activity.activityDetails,
                  );
                } catch (e) {
                  console.error(
                    `Failed to parse activityDetails for activity ${activity.id}:`,
                    e,
                  );
                }
              }
            }
          }
        }
      }
    }

    return userPlans;
  }

  async findByUser(id: string, planStatus?: TPlanStatus) {
    const whereCondition: FindOptions['where'] = {
      userId: id,
      deletedAt: null,
    };

    if (planStatus) {
      whereCondition.planStatus = planStatus;
    }

    const userPlans = await this.userPlanModel.findAll({
      where: whereCondition,
      include: [
        {
          model: Prompt,
          as: 'prompt',
          attributes: ['id', 'name'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name', 'description'],
            },
          ],
        },
        {
          model: PlanWeek,
          as: 'weeks',
          separate: true,
          include: [
            {
              model: PlanActivity,
              as: 'activities',
              attributes: {
                exclude: ['planWeekId', 'userPlanId'],
              },
              include: [
                {
                  model: SubCategory,
                  as: 'subCategory',
                  attributes: ['id', 'name'],
                },
                {
                  model: this.userPlanModel,
                  as: 'userPlan',
                  attributes: ['title'],
                },
              ],
            },
          ],
        },
        {
          model: PlanRecommendations,
          as: 'recommendations',
          attributes: {
            exclude: ['userId', 'planId', 'createdAt', 'updatedAt'],
          },
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    // Ordenar as semanas e atividades após o carregamento
    for (const plan of userPlans) {
      if (plan.weeks) {
        plan.weeks.sort((a, b) => a.weekNumber - b.weekNumber);
        for (const week of plan.weeks) {
          if (week.activities) {
            week.activities.sort(
              (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
            );

            // Parse activityDetails para cada atividade
            for (const activity of week.activities) {
              if (activity?.activityDetails) {
                try {
                  activity.activityDetails = JSON.parse(
                    activity.activityDetails,
                  );
                } catch (e) {
                  console.error(
                    `Failed to parse activityDetails for activity ${activity.id}:`,
                    e,
                  );
                }
              }
            }
          }
        }
      }
    }

    return userPlans;
  }

  async findOne(id: string) {
    const planAlreadyExists = await this.userPlanModel.findOne({
      where: {
        id,
        deletedAt: null,
      },
      include: [
        {
          model: Prompt,
          as: 'prompt',
          attributes: ['id', 'name'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name', 'description'],
            },
          ],
        },
        {
          model: PlanWeek,
          as: 'weeks',
          separate: true,
          include: [
            {
              model: PlanActivity,
              as: 'activities',
              include: [
                {
                  model: SubCategory,
                  as: 'subCategory',
                  attributes: ['id', 'name'],
                },
                {
                  model: this.userPlanModel,
                  as: 'userPlan',
                  attributes: ['title'],
                },
              ],
              attributes: {
                exclude: ['planWeekId', 'userPlanId', 'creationStrategy'],
              },
            },
          ],
        },
        {
          model: PlanRecommendations,
          as: 'recommendations',
          attributes: {
            exclude: ['userId', 'planId', 'createdAt', 'updatedAt'],
          },
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    if (!planAlreadyExists) {
      throw new NotFoundException({
        message: 'Plan not found',
      });
    }

    // Ordenar as semanas
    if (planAlreadyExists.weeks) {
      planAlreadyExists.weeks.sort((a, b) => a.weekNumber - b.weekNumber);

      for (const week of planAlreadyExists.weeks) {
        if (week.activities) {
          for (const activity of week.activities) {
            activity.activityDetails = this.safelyParseJSON(
              activity.activityDetails,
            );

            if (activity.isBlocked) {
              activity.set({
                id: null,
                activityDetails: null,
                activityDescription: null,
                observation: null,
                timeActivity: null,
                subCategory: null,
                category: null,
              });
            }
          }
        }
      }
    }

    return planAlreadyExists;
  }

  async findPlanWithDetails(id: string, userId: string) {
    const planAlreadyExists = await this.userPlanModel.findOne({
      where: {
        id,
        deletedAt: null,
        userId,
      },
      include: [
        {
          model: Prompt,
          as: 'prompt',
          attributes: ['id', 'name'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name', 'description'],
            },
          ],
        },
        {
          model: PlanWeek,
          as: 'weeks',
          separate: true,
          include: [
            {
              model: PlanActivity,
              as: 'activities',
              include: [
                {
                  model: SubCategory,
                  as: 'subCategory',
                  attributes: ['id', 'name'],
                },
                {
                  model: this.userPlanModel,
                  as: 'userPlan',
                  attributes: ['title'],
                },
              ],
              attributes: {
                exclude: ['planWeekId', 'userPlanId', 'creationStrategy'],
              },
            },
          ],
        },
        {
          model: PlanRecommendations,
          as: 'recommendations',
          attributes: {
            exclude: ['userId', 'planId', 'createdAt', 'updatedAt'],
          },
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    if (!planAlreadyExists) {
      throw new NotFoundException({
        message: 'Plan not found',
      });
    }

    // Ordenar as semanas
    if (planAlreadyExists.weeks) {
      planAlreadyExists.weeks.sort((a, b) => a.weekNumber - b.weekNumber);

      for (const week of planAlreadyExists.weeks) {
        if (week.activities) {
          for (const activity of week.activities) {
            activity.activityDetails = this.safelyParseJSON(
              activity.activityDetails,
            );

            if (activity.isBlocked) {
              activity.set({
                id: null,
                activityDetails: null,
                activityDescription: null,
                observation: null,
                timeActivity: null,
                subCategory: null,
                category: null,
              });
            }
          }
        }
      }
    }

    return planAlreadyExists;
  }

  async checkAndAdjustActivityLimits(planId: string): Promise<void> {
    const plan = await this.userPlanModel.findByPk(planId, {
      include: [
        {
          model: PlanActivity,
          as: 'activities',
          where: {
            isBlocked: {
              [Op.or]: [true, false, null],
            },
          },
          required: false,
        },
      ],
    });

    if (!plan) throw new NotFoundException('Plan not found');

    if (
      plan.isCompleted ||
      plan.isGeneratingPlan ||
      plan.creationStrategy === 'manual'
    ) {
      return;
    }

    const userLimits = await this.userPermissionsService.getLimitsByUserId(
      plan.userId,
    );
    const aiObjectiveTasksLimit = userLimits.aiObjectiveTasks?.maxLimit;

    const allActivities = plan.activities || [];

    const completedActivities = allActivities.filter(
      (a) => a.activityStatus === 'done',
    );

    const nonCompletedActivities = allActivities.filter(
      (a) => a.activityStatus !== 'done',
    );

    const unblockedNonCompleted = nonCompletedActivities.filter(
      (a) => !a.isBlocked,
    );
    const blockedNonCompleted = nonCompletedActivities.filter(
      (a) => a.isBlocked,
    );

    if (aiObjectiveTasksLimit === 0 || aiObjectiveTasksLimit === null) {
      if (blockedNonCompleted.length === 0) {
        return;
      }

      const transaction = await this.userPlanModel.sequelize.transaction();
      try {
        await Promise.all(
          blockedNonCompleted.map((a) =>
            a.update({ isBlocked: false }, { transaction }),
          ),
        );
        await transaction.commit();
        return;
      } catch (error) {
        await transaction.rollback();
        throw new InternalServerErrorException(
          'Failed to update activity limits',
        );
      }
    }

    const maxUnblockedNonCompleted = Math.max(
      aiObjectiveTasksLimit - completedActivities.length,
      0,
    );

    if (unblockedNonCompleted.length > maxUnblockedNonCompleted) {
      const excess = unblockedNonCompleted.length - maxUnblockedNonCompleted;
      const toBlock = [...unblockedNonCompleted]
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, excess);

      const transaction = await this.userPlanModel.sequelize.transaction();
      try {
        await Promise.all(
          toBlock.map((a) => a.update({ isBlocked: true }, { transaction })),
        );
        await transaction.commit();
        return;
      } catch (error) {
        await transaction.rollback();
        throw new InternalServerErrorException(
          'Failed to block excess activities',
        );
      }
    }

    const remainingSlots =
      maxUnblockedNonCompleted - unblockedNonCompleted.length;
    if (remainingSlots > 0 && blockedNonCompleted.length > 0) {
      const toUnblock = [...blockedNonCompleted]
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(0, remainingSlots);

      const transaction = await this.userPlanModel.sequelize.transaction();
      try {
        await Promise.all(
          toUnblock.map((a) => a.update({ isBlocked: false }, { transaction })),
        );
        await transaction.commit();
        return;
      } catch (error) {
        await transaction.rollback();
        throw new InternalServerErrorException('Failed to unblock activities');
      }
    }

    return;
  }

  private safelyParseJSON(value: any) {
    try {
      return value ? JSON.parse(value) : null;
    } catch (e) {
      console.error('Erro ao fazer o parse do JSON:', e);
      return null;
    }
  }

  async getActivity(activityId: string) {
    const activity = await this.planActivityModel.findOne({
      where: { id: activityId },
      include: [
        { model: SubCategory, as: 'subCategory', attributes: ['id', 'name'] },
        { model: this.userPlanModel, as: 'userPlan', attributes: ['title'] },
      ],
      attributes: { exclude: ['planWeekId', 'userPlanId'] },
    });

    if (!activity) {
      throw new NotFoundException({
        message: 'Activity not found',
      });
    }

    return activity;
  }

  async updateActivityStatus(
    activityId: string,
    status: TActivityStatus,
    observation: string,
    token?: string,
  ) {
    const loggedUser = getUserId(token);
    const transaction = await this.planActivityModel.sequelize.transaction();

    try {
      const activity = await this.planActivityModel.findOne({
        where: { id: activityId },
        include: [
          {
            model: this.userPlanModel,
            as: 'userPlan',
            attributes: ['planStatus'],
          },
        ],
        attributes: { exclude: ['planWeekId', 'userPlanId'] },
        transaction,
      });

      if (!activity) {
        throw new NotFoundException(`Activity not found`);
      }

      if (typeof status !== 'string') {
        throw new NotFoundException(`Status must be a string`);
      }

      activity.activityStatus = status;
      activity.observation = observation || activity?.observation || null;
      await activity.save({ transaction });

      const week = await this.planWeekModel.findOne({
        where: { id: activity.weekId },
        attributes: ['planId'],
        transaction,
      });

      if (week) {
        const planActivities = await this.planActivityModel.findAll({
          where: { userPlanId: week.planId },
          attributes: ['activityStatus', 'isBlocked'],
          transaction,
        });
        const filteredActivities = planActivities.filter(
          (act) => act.dataValues.isBlocked !== true,
        );

        // Verifica se todas as atividades restantes estão concluídas
        const allActivitiesCompleted = filteredActivities.every(
          (act) => act.dataValues.activityStatus === 'done',
        );

        // Verifica se todas as atividades restantes não estão pendentes
        const allActivitiesNotPending = filteredActivities.every(
          (act) => act.dataValues.activityStatus !== 'pending',
        );

        const includingBlockedActivities = planActivities.some(
          (act) => act.isBlocked,
        );

        const userPlan = await this.userPlanModel.findOne({
          where: { id: week.planId },
          transaction,
        });

        if (userPlan) {
          if (allActivitiesCompleted) {
            userPlan.planStatus = includingBlockedActivities
              ? TPlanStatus.PARTIALLY_COMPLETED
              : TPlanStatus.COMPLETED;
            activity.userPlan.planStatus = includingBlockedActivities
              ? TPlanStatus.PARTIALLY_COMPLETED
              : TPlanStatus.COMPLETED;

            await userPlan.save({ transaction });

            await this.notificationService.create({
              userId: userPlan.userId,
              title: 'Objetivo do Plano Concluído',
              description: `Seu objetivo do plano ${userPlan.title} foi concluído com sucesso!`,
              notificationStatus: TNotificationType.COMPLETEDPLAN,
              targetType: 'plan',
            });
          } else if (allActivitiesNotPending) {
            userPlan.planStatus = TPlanStatus.IN_PROGRESS;
            activity.userPlan.planStatus = TPlanStatus.IN_PROGRESS;
            await userPlan.save({ transaction });
          }
        }
      }

      if (status === 'done') {
        await this.notificationService.markAsReadByTargetIdAndType(
          activityId,
          TNotificationType.COMPLETEDACTIVITY,
        );
      }

      await transaction.commit();

      if (status === 'done') {
        const milestones =
          await this.medalConditionService.checkMilestoneAchievement(
            loggedUser,
          );

        if (milestones) {
          const payload = {
            userId: loggedUser,
            badgeCode: milestones,
          };

          await this.publishToQueue(QUEUE_NAMES.ACHIEVEMENT, payload);
        }
      }
      if (status === 'done') {
        const completedPlans =
          await this.medalConditionService.checkCompletedPlanAchievement(
            loggedUser,
          );

        if (completedPlans) {
          const payload = {
            userId: loggedUser,
            badgeCode: completedPlans,
          };

          await this.publishToQueue(QUEUE_NAMES.ACHIEVEMENT, payload);
        }
      }
    } catch (error) {
      if (transaction) await transaction.rollback();
      throw new Error('Error updating activity status: ' + error.message);
    }
  }

  private isFuturePlan(startAt?: string, startedAt?: string): boolean {
    const dateToCheck = startAt || startedAt;
    return dateToCheck ? isAfter(new Date(dateToCheck), new Date()) : false;
  }

  async updatePlan(id: string, data: any) {
    const userPlan = await this.userPlanModel.findByPk(id);

    if (!userPlan) {
      throw new NotFoundException({ error: 'Plan ID not found' });
    }

    const response = await userPlan.update(data);
    return response;
  }

  async getPlansCountByStatus(token: string) {
    const loggedUser = getUserId(token);

    return await this.userPlanModel.findAll({
      where: { userId: loggedUser, deletedAt: null },
      attributes: [
        'planStatus',
        [this.userPlanModel.sequelize.fn('COUNT', 'planStatus'), 'count'],
      ],
      group: ['planStatus'],
    });
  }

  async removePlan(id: string) {
    const transaction = await this.userPlanModel.sequelize.transaction();

    try {
      const plan = await this.userPlanModel.findByPk(id, {
        transaction,
      });

      if (!plan) {
        throw new NotFoundException(`Plan not found`);
      }

      if (plan.deletedAt) {
        throw new BadRequestException(`Plan is already deleted`);
      }

      plan.deletedAt = new Date();
      plan.isGeneratingPlan = false;
      plan.isCompletedGeneration = true;

      await plan.save({ transaction });

      // Notificação de plano excluído
      const notification = {
        userId: plan.userId,
        title: 'Plano excluído com sucesso',
        description: `Seu plano para ${plan.title} foi excluído com sucesso!!`,
        notificationStatus: TNotificationType.DELETED,
        targetType: 'plan',
      };

      await this.notificationService.create(notification);

      // Emit notification event to update the client
      await this.notificationService.emitNotificationEvent(plan.userId);

      // Obter IDs das atividades do plano
      const activities = await PlanActivity.findAll({
        where: { userPlanId: id },
        attributes: ['id'],
      });

      const activitiesIds = activities.map((activity) => activity.id);

      // Delegar atualização das notificações de plano e atividades
      await this.notificationService.markPlanAndActivitiesNotificationsAsRead(
        id,
        activitiesIds,
      );

      await transaction.commit();

      return plan;
    } catch (error) {
      await transaction.rollback();
      console.error(`Error deleting plan with ID ${id}:`, error);

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      throw new InternalServerErrorException(
        `Error deleting plan: ${error.message}`,
      );
    }
  }

  async removeActivity(activityId: string, token: string) {
    const transaction = await this.planActivityModel.sequelize.transaction();

    try {
      const loggedUser = getUserId(token);
      if (!loggedUser) {
        throw new UnauthorizedException('Unauthorized');
      }

      const activity = await this.planActivityModel.findOne({
        where: { id: activityId },
        attributes: { exclude: ['planWeekId'] },
        include: [
          {
            model: this.userPlanModel,
            as: 'userPlan',
            attributes: ['id', 'planStatus'],
            where: { deletedAt: null },
          },
        ],
        transaction,
      });

      if (!activity) {
        throw new NotFoundException('Activity not found');
      }

      await this.planActivityModel.destroy({
        where: { id: activityId },
        transaction,
      });

      if (activity?.activityStatus === 'pending') {
        const planActivities = await this.planActivityModel.findAll({
          where: { userPlanId: activity.userPlanId },
          transaction,
        });

        const allActivitiesNotPending = planActivities.every(
          (act) => act.activityStatus !== 'pending',
        );

        if (
          allActivitiesNotPending &&
          activity.userPlan.planStatus === 'delayed'
        ) {
          const userPlan = await this.userPlanModel.findByPk(
            activity.userPlanId,
            {
              transaction,
            },
          );

          if (!userPlan) {
            throw new NotFoundException('User plan not found');
          }

          userPlan.planStatus = 'inProgress' as TPlanStatus;
          await userPlan.save({ transaction });

          activity.userPlan.planStatus = 'inProgress' as TPlanStatus;
        }
      }

      const updatedCount =
        await this.notificationService.markAsReadByTargetId(activityId);

      await transaction.commit();

      return {
        message: 'Activity deleted successfully',
        notificationsUpdated: updatedCount,
        planStatus: activity?.userPlan?.planStatus || null,
        id: activityId,
        weekId: activity?.weekId || null,
      };
    } catch (error) {
      await transaction.rollback();
      console.error(`Error deleting activity with ID ${activityId}:`, error);

      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }

      throw new InternalServerErrorException(
        `Error deleting activity: ${error.message}`,
      );
    }
  }

  async updateActivity(activityId: string, data: any) {
    const activity = await this.planActivityModel.findOne({
      where: { id: activityId },
    });

    if (!activity) {
      throw new NotFoundException(`Activity not found`);
    }

    await activity.update(data);
    return activity;
  }

  async getAllActivitiesByMonth(
    startMonth: Date,
    token: string,
  ): Promise<any[]> {
    const loggedUser = getUserId(token);

    const startOfMonthDate = startOfMonth(startMonth);
    const endOfMonthDate = endOfMonth(startMonth);

    const activities = await PlanActivity.findAll({
      where: {
        userId: loggedUser,
        date: {
          [Op.between]: [startOfMonthDate, endOfMonthDate],
        },
        isBlocked: {
          [Op.not]: true,
        },
      },
      attributes: {
        exclude: ['creationStrategy'],
      },
      include: [
        { model: SubCategory, as: 'subCategory', attributes: ['id', 'name'] },
        {
          model: this.userPlanModel,
          as: 'userPlan',
          attributes: ['title'],
          where: { deletedAt: null },
        },
      ],
    });

    return activities;
  }

  async getAllActivitiesByDay(
    date: Date,
    request: any,
    status?: string,
  ): Promise<any[]> {
    const loggedUser = request?.user?.userId;

    if (!loggedUser) {
      throw new UnauthorizedException('Usuário não autorizado');
    }

    const startOfDayDate = startOfDay(date);
    const endOfDayDate = endOfDay(date);

    const startOfDayUtc = new Date(
      Date.UTC(
        startOfDayDate.getFullYear(),
        startOfDayDate.getMonth(),
        startOfDayDate.getDate(),
        0,
        0,
        0,
      ),
    );
    const endOfDayUtc = new Date(
      Date.UTC(
        endOfDayDate.getFullYear(),
        endOfDayDate.getMonth(),
        endOfDayDate.getDate(),
        23,
        59,
        59,
      ),
    );

    const whereClause: any = {
      userId: loggedUser,
      date: {
        [Op.between]: [startOfDayUtc, endOfDayUtc],
      },
      isBlocked: {
        [Op.not]: true,
      },
    };

    if (status) {
      whereClause.activityStatus = { [Op.not]: status };
    }

    const activities = await PlanActivity.findAll({
      where: whereClause,
      attributes: {
        exclude: ['planWeekId', 'creationStrategy'],
      },
      include: [
        { model: SubCategory, as: 'subCategory', attributes: ['id', 'name'] },
        {
          model: this.userPlanModel,
          as: 'userPlan',
          attributes: ['title'],
          where: { deletedAt: null },
        },
      ],
    });

    return activities;
  }

  async getAllActivitiesByType(type: string, token: string): Promise<any[]> {
    const loggedUser = getUserId(token);

    const activities = await PlanActivity.findAll({
      where: {
        userId: loggedUser,
        activityStatus: type,
        isBlocked: {
          [Op.not]: true,
        },
      },
      attributes: {
        exclude: ['creationStrategy'],
      },
      include: [
        { model: SubCategory, as: 'subCategory', attributes: ['id', 'name'] },
        {
          model: this.userPlanModel,
          as: 'userPlan',
          attributes: ['title'],
          where: { deletedAt: null },
        },
      ],
    });

    return activities;
  }

  async updatePlanStatus(status: TPlanStatus, planId: string) {
    const plan = await this.userPlanModel.findOne({
      where: { id: planId },
    });

    if (!plan) {
      throw new NotFoundException(`Plan not found`);
    }

    plan.planStatus = status;
    await plan.save();

    return plan;
  }

  async getPlanByActivityId(activityId: string) {
    const activity = await this.planActivityModel.findOne({
      where: { id: activityId },
    });

    if (!activity) {
      throw new NotFoundException(`Activity not found`);
    }

    const plan = await this.userPlanModel.findOne({
      where: { id: activity.userPlanId },
      attributes: ['id', 'title'],
    });

    if (!plan) {
      throw new NotFoundException(`Plan not found`);
    }

    return plan;
  }

  async updateActivityDetails(activityId: string, data: any, token: string) {
    const loggedUser = getUserId(token);

    try {
      const activity = await this.planActivityModel.findOne({
        where: {
          id: activityId,
          userId: loggedUser,
        },
      });

      if (!activity) {
        throw new NotFoundException(`Activity with ID ${activityId} not found`);
      }

      if (activity.userId !== loggedUser) {
        throw new NotFoundException(
          `Unauthorized access to activity with ID ${activityId}`,
        );
      }
      // Verificar se a atividade está bloqueada
      if (activity.isBlocked) {
        throw new ForbiddenException(
          `Activity with ID ${activityId} is blocked and cannot be updated`,
        );
      }

      activity.activityDetails = JSON.stringify(data);
      await activity.save();

      return activity;
    } catch (error) {
      throw new NotFoundException('Activity not found');
    }
  }

  async updateActivityColor(activityId: string, data: any, token: string) {
    const loggedUser = getUserId(token);

    const activity = await this.planActivityModel.findOne({
      where: {
        id: activityId,
        userId: loggedUser,
      },
    });

    if (!activity) {
      throw new NotFoundException('Activity not found or unauthorized access!');
    }

    activity.activityColor = data.color;
    await activity.save();

    return activity;
  }

  async searchActivitiesPlanDates(
    token: string,
    planId: string,
    allPlanActivities: string,
  ) {
    const loggedUser = getUserId(token);

    const plan = await this.userPlanModel.findOne({
      where: {
        id: planId,
        userId: loggedUser,
      },
    });

    if (!plan) {
      throw new NotFoundException('Plan not found!');
    }

    if (allPlanActivities === 'true') {
      const activities = await PlanActivity.findAll({
        where: {
          userId: loggedUser,
          date: {
            [Op.gte]: plan.startedAt,
            [Op.lte]: plan.endAt,
          },
        },
        include: [
          {
            model: this.userPlanModel,
            as: 'userPlan',
            attributes: ['title', 'id', 'planColor'],
            include: [
              {
                model: Prompt,
                as: 'prompt',
                attributes: ['name'],
                include: [
                  {
                    model: Category,
                    as: 'category',
                    attributes: ['name'],
                  },
                ],
              },
            ],
            where: { deletedAt: null },
          },
        ],
      });

      return activities;
    }

    return await this.planActivityModel.findAll({
      where: {
        userId: loggedUser,
        userPlanId: planId,
      },
      include: [
        {
          model: this.userPlanModel,
          as: 'userPlan',
          attributes: ['title', 'id', 'planColor'],
          where: { deletedAt: null },
        },
      ],
      order: [['date', 'ASC']],
    });
  }

  async publishToQueue(queueName: QueueName, data: any) {
    return globalQueuesService.publish(queueName, data);
  }

  async getPlanTasksSize(planId: string) {
    const planDetails = await this.findOne(planId);

    const activitiesSize = await planDetails?.weeks?.reduce((acc, week) => {
      return acc + week.activities.length;
    }, 0);

    return {
      activitiesSize,
      planDetails,
    };
  }

  async findManualObjectivesByPeriod(
    period: string,
    userId: string,
  ): Promise<number> {
    const { startDate, currentDate } = filterByPeriod(period);

    const objectives = await this.userPlanModel.findAll({
      where: {
        userId: userId,
        creationStrategy: TCreationStrategy.MANUAL,
        createdAt: {
          [Op.between]: [startDate, currentDate],
        },
        deletedAt: null,
      },
    });
    return objectives.length;
  }

  async findAiObjectivesByPeriod(
    period: string,
    userId: string,
  ): Promise<number> {
    const { startDate, currentDate } = filterByPeriod(period);

    const objectives = await this.userPlanModel.findAll({
      where: {
        userId: userId,
        creationStrategy: TCreationStrategy.AUTO,
        createdAt: {
          [Op.between]: [startDate, currentDate],
        },
        deletedAt: null,
      },
    });

    return objectives.length;
  }

  async findPlansByExpirationDate(
    startDate: Date,
    endDate: Date,
  ): Promise<UserPlan[]> {
    return this.userPlanModel.findAll({
      where: {
        endAt: {
          [Op.between]: [startDate, endDate],
        },
        deletedAt: null,
      },
    });
  }
}
