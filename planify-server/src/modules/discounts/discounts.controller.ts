import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
} from '@nestjs/common';
import { DiscountsService } from './discounts.service';
import { CreateDiscountDto } from './dto/create-discount.dto';
import { UpdateDiscountDto } from './dto/update-discount.dto';
import { AuthGuard } from '../auth/auth.guard';
import {
  ValidateDiscountDto,
  ValidateDiscountPriceDto,
} from './dto/validate-discount.dto';
import { ValidateDiscountApplicationDto } from './dto/validate-discount-application.dto';
import { GetPublicPriceDto } from './dto/get-public-price.dto';


@Controller('discounts')
export class DiscountsController {
  constructor(private readonly discountsService: DiscountsService) { }

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() discountDto: CreateDiscountDto, @Req() token: string) {
    return this.discountsService.create(discountDto, token);
  }

  @UseGuards(AuthGuard)
  @Post('/validate')
  validateDiscount(
    @Body() discountDto: ValidateDiscountDto,
    @Req() token: string,
  ) {
    return this.discountsService.validateDiscount(discountDto, token);
  }

  @UseGuards(AuthGuard)
  @Post('/validate/price')
  getCurrentPrice(
    @Body() discountDto: ValidateDiscountPriceDto,
    @Req() token: string,
  ) {
    return this.discountsService.getCurrentPrice(discountDto, token);
  }

  @UseGuards(AuthGuard)
  @Post('/apply')
  applyDiscount(
    @Body() discountDto: ValidateDiscountApplicationDto,
    @Req() token: string,
  ) {
    return this.discountsService.applyDiscount(discountDto, token);
  }


  @Post('/calculate-price')
  calculateDiscountedPriceByUserId(@Body() discountDto: GetPublicPriceDto) {
    return this.discountsService.calculateDiscountedPriceByUserId(discountDto);
  }

  @UseGuards(AuthGuard)
  @Get('/:id/history')
  findDiscountHistory(@Param('id') id: string) {
    return this.discountsService.findDiscountHistory(id);
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.discountsService.findOne(id);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.discountsService.findAll();
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateDiscountDto: UpdateDiscountDto,
    @Req() token: string,
  ) {
    return this.discountsService.update(id, updateDiscountDto, token);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Req() token: string) {
    return this.discountsService.remove(id, token);
  }
}
