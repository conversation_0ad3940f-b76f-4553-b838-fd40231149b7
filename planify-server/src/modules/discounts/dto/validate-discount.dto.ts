import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class ValidateDiscountDto {
  @ApiProperty({
    description: 'Código do desconto a ser validado',
    required: true,
    example: 'PLANIFY_10',
  })
  @IsString()
  @IsNotEmpty()
  discountCode: string;
}

export class ValidateDiscountPriceDto extends ValidateDiscountDto {
  @ApiProperty({
    description: 'ID do plano de pagamento',
    required: true,
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  paymentPlanId: string;
}
