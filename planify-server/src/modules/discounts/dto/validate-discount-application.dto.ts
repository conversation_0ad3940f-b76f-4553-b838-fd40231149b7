import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class ValidateDiscountApplicationDto {
  @ApiProperty({
    description: 'Código do desconto a ser validado',
    required: true,
    example: 'PLANIFY_10',
  })
  @IsString()
  @IsNotEmpty()
  discountCode: string;

  @ApiProperty({
    description: 'ID do usuário que está aplicando o desconto',
    required: true,
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
}
