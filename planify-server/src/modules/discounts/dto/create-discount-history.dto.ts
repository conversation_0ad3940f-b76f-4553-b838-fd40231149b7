import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsInt, IsUUID } from 'class-validator';

export class CreateDiscountHistoryDto {
  @ApiProperty({
    description: 'ID do desconto',
    required: true,
  })
  @IsUUID(4)
  @IsNotEmpty()
  discountId: string;

  @ApiProperty({
    description: 'ID do proprietário do desconto',
    required: true,
  })
  @IsUUID(4)
  @IsNotEmpty()
  ownerId: string;

  @ApiProperty({
    description: 'ID do usuário que utilizou o desconto',
    required: true,
  })
  @IsUUID(4)
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Origem do desconto',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  origin: string;

  @ApiProperty({
    description: 'Tipo do desconto',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Valor do desconto',
    required: true,
    type: Number,
  })
  @IsInt()
  @IsNotEmpty()
  value: number;

  @ApiProperty({
    description: 'Código do desconto',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  code: string;
}
