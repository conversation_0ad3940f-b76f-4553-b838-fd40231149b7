import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  IsNumber,
  IsOptional,
  IsBoolean,
  IsUUID,
  IsDateString,
  IsInt,
} from 'class-validator';

export class CreateDiscountDto {
  @ApiProperty({
    description: 'Origem do desconto',
    required: true,
    enum: ['internal', 'external', 'mentor', 'user', 'affiliates', 'custom'],
  })
  @IsEnum(['internal', 'external', 'mentor', 'user', 'affiliates', 'custom'])
  @IsNotEmpty()
  origin: string;

  @ApiProperty({
    description: 'Tipo do desconto',
    required: false,
    enum: ['percentage', 'fixed'],
    default: 'percentage',
  })
  @IsEnum(['percentage', 'fixed'])
  @IsOptional()
  type?: string;

  @ApiProperty({
    description: 'Valor do desconto',
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  value: number;

  @ApiProperty({
    description: 'Status do desconto',
    required: false,
    enum: ['active', 'inactive'],
    default: 'active',
  })
  @IsEnum(['active', 'inactive'])
  @IsOptional()
  status?: string;

  @ApiProperty({
    description: 'Código do desconto',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  discountCode: string;

  @ApiProperty({
    description: 'Permitir na primeira compra',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  allowInFirstPurchase?: boolean;

  @ApiProperty({
    description: 'Máximo de uso por usuário',
    required: false,
    default: 1,
  })
  @IsInt()
  @IsOptional()
  maxUsageByUser?: number;

  @ApiProperty({
    description: 'Limite máximo de usuários',
    required: false,
    default: 1,
  })
  @IsInt()
  @IsOptional()
  maxUsersLimit?: number;

  @ApiProperty({
    description:
      'Tempo de expiração em dias (ex: 30 para 30 dias, 3650 para 10 anos)',
    required: true,
    example: 30,
    type: Number,
  })
  @IsInt()
  @IsNotEmpty()
  expiresIn: number;

  @ApiProperty({
    description: 'Data de expiração',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  expirationAt?: Date;
}
