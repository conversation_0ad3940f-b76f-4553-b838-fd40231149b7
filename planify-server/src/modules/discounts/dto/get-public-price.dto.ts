import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class GetPublicPriceDto {
  @ApiProperty({
    description: 'Código do desconto a ser validado',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  discountCode: string;

  @ApiProperty({
    description: 'ID do plano de pagamento',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  paymentPlanId: string;

  @ApiProperty({
    description: 'ID do usuário',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
} 