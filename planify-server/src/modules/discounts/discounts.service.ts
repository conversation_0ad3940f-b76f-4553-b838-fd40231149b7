import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateDiscountDto } from './dto/create-discount.dto';
import { UpdateDiscountDto } from './dto/update-discount.dto';
import { InjectModel } from '@nestjs/sequelize';
import { Discount } from './entities/discount.entity';
import { DiscountsHistory } from './entities/discountsHistory.entity';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { calculateExpirationDate, getUserId } from 'src/utils';
import { UsersService } from '../users/users.service';
import {
  ValidateDiscountDto,
  ValidateDiscountPriceDto,
} from './dto/validate-discount.dto';
import { ValidateDiscountApplicationDto } from './dto/validate-discount-application.dto';
import { User } from '../users/entities/user.entity';
import { PaymentPlansService } from '../payment-plans/payment-plans.service';
import { GetPublicPriceDto } from './dto/get-public-price.dto';

@Injectable()
export class DiscountsService {
  private logger = createModuleLogger(DiscountsService.name);

  constructor(
    @InjectModel(Discount)
    private discountModel: typeof Discount,
    @InjectModel(DiscountsHistory)
    private discountHistoryModel: typeof DiscountsHistory,
    private readonly usersService: UsersService,
    private readonly paymentPlansService: PaymentPlansService,
  ) {}

  async create(discountDto: CreateDiscountDto, token: string) {
    const loggedUser = getUserId(token);

    const user = await this.usersService.findOne(loggedUser);

    const permittedOrigins = ['mentor', 'affiliates', 'custom', 'admin'];

    if (!permittedOrigins.includes(user.userType)) {
      this.logger.warn(
        `User with id ${loggedUser} not allowed to create discounts`,
      );
      throw new UnauthorizedException(`User not allowed to create discounts`);
    }

    const expirationDate = calculateExpirationDate(discountDto.expiresIn);

    const payload = {
      ...discountDto,
      ownerId: loggedUser,
      expirationAt: expirationDate,
    };

    const discount = await this.discountModel.create(payload);

    this.logger.info(
      `Discount ${discount.discountCode} created with expiresIn: ${discountDto.expiresIn}, expirationAt: ${expirationDate || 'null'}`,
    );

    return discount;
  }

  async findAll() {
    const discounts = await this.discountModel.findAll({
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['name'],
        },
      ],
    });
    return discounts;
  }

  async findOne(id: string) {
    const discount = await this.discountModel.findOne({
      where: {
        id,
      },
    });

    if (!discount) {
      this.logger.warn(`Discount with id ${id} not found`);
      throw new NotFoundException(`Discount with id ${id} not found`);
    }
    return discount;
  }

  async findDiscountHistory(id: string) {
    const discount = await this.discountModel.findOne({
      where: {
        id,
      },
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    if (!discount) {
      this.logger.warn(`Discount with id ${id} not found`);
      throw new NotFoundException(`Discount with id ${id} not found`);
    }

    const history = await this.discountHistoryModel.findAll({
      where: {
        discountId: id,
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email'],
        },
        // {
        //   model: User,
        //   as: 'owner',
        //   attributes: ['id', 'name', 'email'],
        // },
      ],
    });

    const discountData = discount.toJSON();
    const historyData = history.map((record) => record.toJSON());

    return {
      ...discountData,
      history: historyData,
    };
  }

  async findDiscount(id: string) {
    const discount = await this.discountModel.findOne({
      where: {
        id,
      },
      attributes: {
        exclude: ['ownerId'],
      },
    });

    if (!discount) {
      this.logger.warn(`Discount with id ${id} not found`);
      throw new NotFoundException(`Discount with id ${id} not found`);
    }
    return discount;
  }

  async findByCode(code: string) {
    const discount = await this.discountModel.findOne({
      where: {
        discountCode: code,
      },
      // attributes: {
      //   exclude: ['ownerId'],
      // },
    });

    if (!discount) {
      this.logger.warn(`Discount with code ${code} not found`);
      throw new NotFoundException(`Discount with code ${code} not found`);
    }
    return discount;
  }

  async validateDiscount(discountDto: ValidateDiscountDto, token: string) {
    const { discountCode } = discountDto;
    const userId = getUserId(token);
    const discount = await this.findByCode(discountCode);

    const alreadyUsed = await this.discountHistoryModel.findOne({
      where: {
        userId,
        discountId: discount.id,
      },
    });

    if (alreadyUsed) {
      this.logger.warn(`User ${userId} already used discount ${discountCode}`);
      throw new UnauthorizedException('Discount already used by user');
    }

    return {
      allowDiscount: true,
      discountId: discount.id,
      discountCode: discount.discountCode,
    };
  }

  async getCurrentPrice(discountDto: ValidateDiscountPriceDto, token: string) {
    const userId = getUserId(token);
    const discount = await this.findByCode(discountDto.discountCode);

    if (!discount) {
      this.logger.warn(
        `Discount with code ${discountDto.discountCode} not found`,
      );
      throw new NotFoundException(
        `Discount with code ${discountDto.discountCode} not found`,
      );
    }

    const userUsageCount = await this.discountHistoryModel.count({
      where: {
        userId,
        discountId: discount.id,
      },
    });

    if (userUsageCount >= discount.maxUsageByUser) {
      this.logger.warn(
        `User ${userId} reached maximum usage limit for discount ${discountDto.discountCode}`,
      );
      throw new UnauthorizedException(
        'Maximum usage limit reached for this discount by user',
      );
    }

    const totalUsersUsed = await this.discountHistoryModel.count({
      where: { discountId: discount.id },
      distinct: true,
      col: 'userId',
    });

    if (
      discount.maxUsersLimit &&
      totalUsersUsed >= discount.maxUsersLimit &&
      userUsageCount === 0
    ) {
      this.logger.warn(
        `Maximum user limit reached for discount ${discountDto.discountCode}`,
      );
      throw new UnauthorizedException(
        'Maximum user limit reached for this discount',
      );
    }

    const paymentPlan = await this.paymentPlansService.findOne(
      discountDto.paymentPlanId,
    );

    if (!paymentPlan) {
      this.logger.warn(
        `Payment plan with id ${discountDto.paymentPlanId} not found`,
      );
      throw new NotFoundException(
        `Payment plan with id ${discountDto.paymentPlanId} not found`,
      );
    }

    const basePrice = Number(paymentPlan.promotionalPrice || paymentPlan.price);

    let discountAmount: number;
    if (discount.type === 'percentage') {
      discountAmount = (Number(discount.value) / 100) * basePrice;
    } else {
      discountAmount = Number(discount.value);
    }

    const currentPrice = basePrice - discountAmount;

    return {
      allowDiscount: true,
      discountId: discount.id,
      discountCode: discount.discountCode,
      originalPrice: basePrice,
      discountAmount,
      discountType: discount.type,
      discountValue: Number(discount.value),
      currentPrice: currentPrice.toFixed(2),
    };
  }

  async applyDiscount(
    discountDto: ValidateDiscountApplicationDto,
    token: string,
  ) {
    await this.validateDiscount(discountDto, token);
    const discountData = await this.findByCode(discountDto.discountCode);

    return await this.discountHistoryModel.create({
      userId: discountDto.userId,
      discountId: discountData.id,
      ownerId: discountData.ownerId,
      origin: discountData.origin,
      type: discountData.type,
      value: discountData.value,
      code: discountData.discountCode,
    });
  }

  async update(id: string, discountDto: UpdateDiscountDto, token: string) {
    const loggedUser = getUserId(token);

    await this.usersService.findOne(loggedUser);

    const discount = await this.findOne(id);

    if (discount.ownerId !== loggedUser) {
      this.logger.warn(
        `User with id ${loggedUser} not allowed to update discount with id ${id}`,
      );
      throw new UnauthorizedException(
        `User not allowed to update discount with id ${id}`,
      );
    }

    if (discount) {
      await discount.update(discountDto);
    }
  }

  async remove(id: string, token: string) {
    const loggedUser = getUserId(token);
    const discount = await this.findOne(id);

    if (discount.ownerId !== loggedUser) {
      this.logger.warn(
        `User with id ${loggedUser} not allowed to delete discount with id ${id}`,
      );
      throw new UnauthorizedException(
        `User not allowed to delete discount with id ${id}`,
      );
    }

    if (discount) {
      this.logger.info(`Deleting discount with id ${id}`);
      discount.destroy();
    }
  }

  async calculateDiscountedPriceByUserId(discountDto: GetPublicPriceDto) {
    const discount = await this.findByCode(discountDto.discountCode);

    if (!discount) {
      this.logger.warn(
        `Discount with code ${discountDto.discountCode} not found`,
      );
      throw new NotFoundException(
        `Discount with code ${discountDto.discountCode} not found`,
      );
    }

    const userUsageCount = await this.discountHistoryModel.count({
      where: {
        userId: discountDto.userId,
        discountId: discount.id,
      },
    });

    if (userUsageCount >= discount.maxUsageByUser) {
      this.logger.warn(
        `User ${discountDto.userId} reached maximum usage limit for discount ${discountDto.discountCode}`,
      );
      throw new UnauthorizedException(
        'Maximum usage limit reached for this discount by user',
      );
    }

    const totalUsersUsed = await this.discountHistoryModel.count({
      where: { discountId: discount.id },
      distinct: true,
      col: 'userId',
    });

    if (
      discount.maxUsersLimit &&
      totalUsersUsed >= discount.maxUsersLimit &&
      userUsageCount === 0
    ) {
      this.logger.warn(
        `Maximum user limit reached for discount ${discountDto.discountCode}`,
      );
      throw new UnauthorizedException(
        'Maximum user limit reached for this discount',
      );
    }

    const paymentPlan = await this.paymentPlansService.findOne(
      discountDto.paymentPlanId,
    );

    if (!paymentPlan) {
      this.logger.warn(
        `Payment plan with id ${discountDto.paymentPlanId} not found`,
      );
      throw new NotFoundException(
        `Payment plan with id ${discountDto.paymentPlanId} not found`,
      );
    }

    const basePrice = Number(paymentPlan.promotionalPrice || paymentPlan.price);

    let discountAmount: number;
    if (discount.type === 'percentage') {
      discountAmount = (Number(discount.value) / 100) * basePrice;
    } else {
      discountAmount = Number(discount.value);
    }

    const currentPrice = basePrice - discountAmount;

    return {
      allowDiscount: true,
      discountId: discount.id,
      discountCode: discount.discountCode,
      originalPrice: basePrice,
      discountAmount,
      discountType: discount.type,
      discountValue: Number(discount.value),
      currentPrice: currentPrice.toFixed(2),
    };
  }
}
