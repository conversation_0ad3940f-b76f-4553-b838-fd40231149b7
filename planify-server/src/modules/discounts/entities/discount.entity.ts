import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'Discounts',
})
export class Discount extends Model<Discount> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  ownerId: string;

  @BelongsTo(() => User)
  owner: User;

  @Column({
    type: DataType.ENUM(
      'internal',
      'external',
      'mentor',
      'user',
      'affiliates',
      'custom',
    ),
    allowNull: false,
  })
  origin: string;

  @Column({
    type: DataType.ENUM('percentage', 'fixed'),
    allowNull: true,
    defaultValue: 'percentage',
  })
  type: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  value: number;

  @Column({
    type: DataType.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active',
  })
  status: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  discountCode: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  allowInFirstPurchase: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  maxUsageByUser: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  maxUsersLimit: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  expiresIn: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expirationAt: Date;
}
