import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';
import { Discount } from './discount.entity';

@Table({
  tableName: 'DiscountsHistory',
})
export class DiscountsHistory extends Model<DiscountsHistory> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Discount)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  discountId: string;

  @BelongsTo(() => Discount)
  discount: Discount;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  ownerId: string;

  @BelongsTo(() => User, { as: 'owner' })
  owner: User;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => User, { as: 'user' })
  user: User;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  origin: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  value: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  code: string;
}
