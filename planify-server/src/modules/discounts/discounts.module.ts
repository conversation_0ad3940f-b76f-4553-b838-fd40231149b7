import { forwardRef, Module } from '@nestjs/common';
import { DiscountsService } from './discounts.service';
import { DiscountsController } from './discounts.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { JwtModule } from '@nestjs/jwt';
import { AuthModule } from '../auth/auth.module';
import { Discount } from './entities/discount.entity';
import { DiscountsHistory } from './entities/discountsHistory.entity';
import { UsersModule } from '../users/users.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Discount, DiscountsHistory]),
    forwardRef(() => AuthModule),
    JwtModule,
    UsersModule,
    PaymentPlansModule,
  ],
  controllers: [DiscountsController],
  providers: [DiscountsService],
  exports: [DiscountsService, SequelizeModule],
})
export class DiscountsModule {}
