import { forwardRef, Module } from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { Templates<PERSON>ontroller } from './templates.controller';
import { Template } from './entities/template.entity';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    SequelizeModule.forFeature([Template]),
    forwardRef(() => AuthModule),
    JwtModule,
  ],
  controllers: [TemplatesController],
  providers: [TemplatesService],
  exports: [TemplatesService, SequelizeModule],
})
export class TemplatesModule {}
