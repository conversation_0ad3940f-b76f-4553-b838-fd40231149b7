import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({
  tableName: 'Templates',
})
export class Template extends Model<Template> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  identificationTag: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string;

  @Column({
    type: DataType.ENUM('email', 'whatsapp', 'sms', 'push', 'custom'),
    allowNull: true,
    defaultValue: 'custom',
  })
  type: string;

  @Column({
    type: DataType.ENUM('active', 'inactive', 'draft'),
    allowNull: true,
    defaultValue: 'active',
  })
  status: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  content: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  parameterizations: JSON;
}
