import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { InjectModel } from '@nestjs/sequelize';
import { Template } from './entities/template.entity';
import { createModuleLogger } from 'src/utils/moduleLogger';

@Injectable()
export class TemplatesService {
  private logger = createModuleLogger(TemplatesService.name);
  constructor(
    @InjectModel(Template)
    private templateModel: typeof Template,
  ) {}

  async create(createTemplateDto: CreateTemplateDto) {
    const template = await this.templateModel.create(createTemplateDto);
    this.logger.info(`Template created: ${template.id}`);
    return template;
  }

  async findAll() {
    try {
      if (!this.templateModel) {
        this.logger.error('Template model not initialized');
        throw new Error('Template model not initialized');
      }

      return await this.templateModel.findAll();
    } catch (error) {
      this.logger.error(`Error finding all templates: ${error.message}`);
      throw error;
    }
  }

  async findOne(id: string) {
    try {
      const template = await this.templateModel.findByPk(id);
      if (!template) {
        this.logger.error(`Template not found: ${id}`);
        throw new NotFoundException('Template not found');
      }
      return template;
    } catch (error) {
      this.logger.error(`Error finding template ${id}: ${error.message}`);
      throw error;
    }
  }

  async findByTag(identificationTag: string): Promise<Template> {
    const template = await this.templateModel.findOne({
      where: { identificationTag },
    });
    if (!template) {
      this.logger.error(
        `Template with identificationTag not found: ${identificationTag}`,
      );
      throw new NotFoundException(
        `Template with identificationTag ${identificationTag} not found`,
      );
    }
    return template;
  }

  async update(id: string, updateTemplateDto: UpdateTemplateDto) {
    const template = await this.templateModel.findByPk(id);
    if (!template) {
      this.logger.error(`Template not found: ${id}`);
      throw new NotFoundException('Template not found');
    }
    await template.update(updateTemplateDto);
    return template;
  }

  async remove(id: string) {
    const template = await this.templateModel.findByPk(id);
    if (!template) {
      this.logger.error(`Template not found: ${id}`);
      throw new NotFoundException('Template not found');
    }
    await template.destroy();
    return template;
  }
}
