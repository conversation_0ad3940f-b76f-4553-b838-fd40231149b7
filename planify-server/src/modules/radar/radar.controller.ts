import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
} from '@nestjs/common';
import { RadarService } from './radar.service';
import { CreateRadarDto } from './dto/create-radar.dto';
import { UpdateRadarDto } from './dto/update-radar.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import {
  TUserPermissionType,
  RequirePermission,
  TUserScopes,
  UserPermissionsGuard,
} from '../userPermissions/userPermissions.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';

@ApiTags('Radar PlanAI')
@Controller('radar')
export class RadarController {
  constructor(private readonly radarService: RadarService) {}

  @UseGuards(AuthGuard, PermissionsGuard, UserPermissionsGuard)
  @Permissions(TUserScopes.RADAR_WRITE)
  @RequirePermission(TUserPermissionType.CREATE_RADAR)
  @Post()
  create(@Body() createRadarDto: CreateRadarDto) {
    return this.radarService.create(createRadarDto);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions(TUserScopes.RADAR_READ)
  @Get(':userId')
  findOne(@Param('userId') userId: string) {
    return this.radarService.findByUser(userId);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions(TUserScopes.RADAR_READ)
  @Get('/details/:radarId')
  findById(@Param('radarId') radarId: string) {
    return this.radarService.findById(radarId);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions(TUserScopes.RADAR_READ) 
  @Patch(':userId')
  update(
    @Param('userId') userId: string,
    @Body() updateRadarDto: UpdateRadarDto,
  ) {
    return this.radarService.update(userId, updateRadarDto);
  }
}
