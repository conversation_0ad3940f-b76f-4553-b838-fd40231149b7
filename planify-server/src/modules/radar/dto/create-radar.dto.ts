import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateRadarDto {
  @ApiProperty()
  @IsString()
  userId: string;

  @ApiProperty()
  @IsNumber()
  businessAndCareer: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  businessAndCareerComment: string | null;

  @ApiProperty()
  @IsNumber()
  moneyAndFinances: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  moneyAndFinancesObservation: string | null;

  @ApiProperty()
  @IsNumber()
  loveAndFamily: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  loveAndFamilyObservation: string | null;

  @ApiProperty()
  @IsNumber()
  spirituality: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  spiritualityObservation: string | null;

  @ApiProperty()
  @IsNumber()
  personalDeveloment: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  personalDevelomentObservation: string | null;

  @ApiProperty()
  @IsNumber()
  helth: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  helthObservation: string | null;

  @ApiProperty()
  @IsNumber()
  friendsAndSocial: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  friendsAndSocialObservation: string | null;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isCompletedRecommendation?: boolean;
}
