/* eslint-disable prettier/prettier */
import {
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { CreateRadarDto } from './dto/create-radar.dto';
import { UpdateRadarDto } from './dto/update-radar.dto';
import { InjectModel } from '@nestjs/sequelize';
import { User } from '../users/entities/user.entity';
import { Radar } from './entities/radar.entity';
import { RecomendationsService } from '../recommendations/recommendations.service';
import { Op } from 'sequelize';
import { filterByPeriod } from 'src/utils';

@Injectable()
export class RadarService {
  constructor(
    @Inject(forwardRef(() => RecomendationsService))
    private recomendationsService: RecomendationsService,
    @InjectModel(Radar)
    private radarModel: typeof Radar,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async create(radarDto: CreateRadarDto) {
    const user = await this.userModel.findByPk(radarDto?.userId);

    if (!user) {
      throw new Error('User not found');
    }

    const radarInfo = await this.radarModel.create(radarDto);

    return radarInfo;
  }

  async findAllPendings() {
    const radarList = await this.radarModel.findAll({
      where: { isCompletedRecommendation: false, isCompleted: true },
    });

    return radarList;
  }

  async findByUser(id: string) {
    const user = await this.userModel.findByPk(id);

    if (!user) {
      throw new Error('User not found');
    }
    const radar = await this.radarModel.findOne({
      where: { userId: id },
    });

    if (!radar) {
      throw new NotFoundException('User Radar not found');
    }
    return radar;
  }

  async findById(id: string) {
    const radar = await this.radarModel.findOne({
      where: { id: id },
      attributes: { exclude: ['createdAt', 'updatedAt'] },
    });

    const user = await this.userModel.findByPk(radar.userId);

    if (!user) {
      throw new NotFoundException({ message: 'User not found' });
    }

    if (!radar) {
      throw new NotFoundException({ message: 'User Radar not found' });
    }
    return {
      username: user?.name,
      ...radar?.dataValues,
    };
  }

  async update(id: string, radarDto: UpdateRadarDto) {
    const user = await this.userModel.findByPk(id);

    if (!user) {
      throw new Error('User not found');
    }

    const radar = await this.radarModel.findOne({
      where: { userId: id },
    });

    if (radar) {
      radar.update(radarDto);
      const result = await radar.save();

      if (result.isCompleted) {
        await this.recomendationsService.generateRecommendation(result?.id);
      }
      return result;
    } else {
      throw new NotFoundException('UserId or radar not found');
    }
  }

  async updateStatus(radarId: string, radarDto: UpdateRadarDto) {
    const radar = await this.radarModel.findOne({
      where: { id: radarId },
    });

    if (radar) {
      radar.update(radarDto);
      const result = await radar.save();
      return result;
    } else {
      throw new NotFoundException('UserId or radar not found');
    }
  }

  async findRadarByPeriod(period: string, userId: string): Promise<number> {
    const { startDate, currentDate } = filterByPeriod(period);

    const radar = await this.radarModel.findAll({
      where: {
        userId: userId,
        createdAt: {
          [Op.between]: [startDate, currentDate],
        },
      },
    });

    return radar.length;
  }
}
