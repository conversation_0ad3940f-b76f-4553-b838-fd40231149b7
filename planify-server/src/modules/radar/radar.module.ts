import { Module, forwardRef } from '@nestjs/common';
import { RadarService } from './radar.service';
import { RadarController } from './radar.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { Radar } from './entities/radar.entity';
import { User } from '../users/entities/user.entity';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { RecommendationsModule } from '../recommendations/recomendations.module';
import { UserPermissionsModule } from '../userPermissions/userPermissions.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Radar, User]),
    forwardRef(() => AuthModule),
    JwtModule,
    forwardRef(() => RecommendationsModule),
    UserPermissionsModule,
  ],
  controllers: [RadarController],
  providers: [RadarService],
  exports: [RadarService],
})
export class RadarModule {}
