import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'Radar',
})
export class Radar extends Model<Radar> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  businessAndCareer: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  businessAndCareerObservation: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  moneyAndFinances: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  moneyAndFinancesObservation: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  loveAndFamily: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  loveAndFamilyObservation: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  spirituality: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  spiritualityObservation: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  personalDeveloment: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  personalDevelomentObservation: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  helth: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  helthObservation: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  relationship: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  relationshipObservation: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  friendsAndSocial: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  friendsAndSocialObservation: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isCompleted?: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isCompletedRecommendation?: boolean;
}
