import { Module } from '@nestjs/common';
import { LlmService } from './llm.service';
import { LlmController } from './llm.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { LLMPrompt } from './entities/llm-prompt.entity';

@Module({
  imports: [SequelizeModule.forFeature([LLMPrompt])],
  controllers: [LlmController],
  providers: [LlmService],
  exports: [LlmService],
})
export class LlmModule {}
