import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateLlmDto } from './dto/create-llm.dto';
import { LLMPrompt } from './entities/llm-prompt.entity';
import { createModuleLogger } from 'src/utils/moduleLogger';

@Injectable()
export class LlmService {
  private logger = createModuleLogger(LlmService.name);

  constructor(
    @InjectModel(LLMPrompt)
    private llmPromptModel: typeof LLMPrompt,
  ) {}

  create(llmDto: CreateLlmDto) {
    return this.llmPromptModel.create({ ...llmDto });
  }

  findAll() {
    return this.llmPromptModel.findAll();
  }

  findOne(id: string) {
    return this.llmPromptModel.findByPk(id);
  }

  async findOneByName(name: string) {
    const llmPrompt = await this.llmPromptModel.findOne({
      where: { name: name },
    });
    if (!llmPrompt) {
      this.logger.error(`LLM Prompt not found: ${name}`);
      throw new NotFoundException('LLM Prompt not found');
    }
    return llmPrompt;
  }

  async update(id: string, llmDto: Partial<CreateLlmDto>) {
    const llmPrompt = await this.llmPromptModel.findByPk(id);
    if (llmPrompt) {
      this.logger.info(`Updating LLM Prompt: ${id}`);
      return llmPrompt.update(llmDto);
    }
    return null;
  }

  async remove(id: string) {
    const llmPrompt = await this.llmPromptModel.findByPk(id);
    if (!llmPrompt) {
      this.logger.error(`LLM Prompt not found: ${id}`);
      throw new NotFoundException('LLM Prompt not found');
    }
    return await llmPrompt.destroy();
  }
}
