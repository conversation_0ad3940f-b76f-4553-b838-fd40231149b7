import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { LlmService } from './llm.service';
import { CreateLlmDto } from './dto/create-llm.dto';

import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@ApiTags('llm')
@ApiBearerAuth()
@Controller('llm-prompts')
export class LlmController {
  constructor(private readonly llmService: LlmService) {}

  @Post()
  create(@Body() llmDto: CreateLlmDto) {
    return this.llmService.create(llmDto);
  }

  @Get()
  findAll() {
    return this.llmService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.llmService.findOne(id);
  }

  @Get('/name/:name')
  findOneByName(@Param('name') name: string) {
    return this.llmService.findOneByName(name);
  }
  @Patch(':id')
  update(@Param('id') id: string, @Body() llmDto: Partial<CreateLlmDto>) {
    return this.llmService.update(id, llmDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.llmService.remove(id);
  }
}
