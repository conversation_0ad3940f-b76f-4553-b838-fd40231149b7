import axios from 'axios';
export async function sendEmailService(options, key) {
  const url = 'https://api.brevo.com/v3/smtp/email';
  const headers = {
    accept: 'application/json',
    'api-key': key,
    'content-type': 'application/json',
  };

  try {
    const response = await axios.post(url, options, { headers });

    if (response && response.status === 201) {
      console.info('Email sent successfully.');
    } else {
      throw new Error('Email sending failed.');
    }
  } catch (error) {
    console.error('Error during sending email:', error);
    return new Error(`Failed to send email due to ${error}`);
  }
}
