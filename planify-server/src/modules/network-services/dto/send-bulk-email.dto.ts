import { EmailTypes } from '../types';

export type BulkEmailRecipientType = 'all' | 'group' | 'specific';

export interface SendBulkEmailDto {
  templateType: EmailTypes;
  recipientsType: BulkEmailRecipientType;
  groupType?: string; // para grupo de usuários (ID do plano de pagamento)
  emails?: string[]; // para usuários específicos
  data?: Record<string, any>; // dados adicionais para o template
}
