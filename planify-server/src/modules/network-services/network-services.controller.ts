import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { NetworkService } from './network-services.service';
import { AuthGuard } from '../auth/auth.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';
import { SendEmailDto } from './dto/send-email.dto';
import { SendBulkEmailDto } from './dto/send-bulk-email.dto';
import { UsersService } from '../users/users.service';

@UseGuards(AuthGuard, PermissionsGuard)
@Permissions('admin')
@Controller('network-services')
export class NetworkServicesController {
  constructor(
    private readonly networkServicesService: NetworkService,
    private readonly usersService: UsersService,
  ) {}

  @Post('/send-email')
  async sendEmail(@Body() data: SendEmailDto) {
    const { type, email, name, data: additionalData = {} } = data;
    return this.networkServicesService.sendEmail(type, {
      email,
      name,
      ...additionalData,
    });
  }

  @Post('/batch')
  async sendEmailBatch(@Body() data: SendBulkEmailDto) {
    return this.networkServicesService.sendEmailBatch(data);
  }

  // @Post('/email/custom')
  // genericEmail(@Body() data: any) {
  //   return this.networkServicesService.sendGenericEmail(data);
  // }
}
