<html>
  <head>
    <meta charset='utf-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Planify</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
      }
      .wrapper {
        width: 100%;
        background-color: #f2f2f2;
        padding: 20px;
        box-sizing: border-box;
      }
      .container {
        max-width: 600px;
        width: 100%;
        margin: 0 auto;
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
      }
      .header-wrapper {
        padding: 10px 10px 30px 10px;
      }
      .header {
        width: 100%;
        margin-bottom: 0;
        display: block;
        border-radius: 8px;
      }
      .content {
        padding: 0 30px;
        margin-bottom: 30px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      .footer {
        width: 100%;
        margin-top: 30px;
        display: block;
      }
      .signature {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
        margin-bottom: 30px;
        padding: 0 30px;
      }
      .signature img {
        width: 40px;
        height: 60px;
      }
      .signature-text {
        font-size: 14px;
        color: #666;
      }
      h1 {
        font-size: 24px;
        margin-bottom: 20px;
        color: #333;
      }
      p {
        margin: 10px 0;
        font-size: 16px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      img {
        max-width: 100%;
        height: auto;
        display: block;
      }
      ul {
        padding-left: 20px;
        margin: 10px 0;
      }
      li {
        margin-bottom: 8px;
      }
      a {
        color: #e01e36;
        text-decoration: underline;
        font-weight: 500;
      }
      .link-button {
        display: inline-block;
        color: #e01e36;
        font-weight: 500;
        text-decoration: underline;
      }
      @media only screen and (max-width: 600px) {
        .wrapper {
          padding: 10px;
        }
        .header-wrapper {
          padding: 10px 10px 20px 10px;
        }
        .content {
          padding: 0 15px;
        }
        .signature {
          padding: 0 15px;
        }
        h1 {
          font-size: 20px;
        }
        p {
          font-size: 14px;
        }
      }
    </style>
  </head>
  <body style='margin: 0; padding: 0; background-color: #F2F2F2;'>
    <div
      class='wrapper'
      style='width: 100%; background-color: #F2F2F2; padding: 20px; box-sizing: border-box;'
    >
      <div class='container'>
        <div class='header-wrapper'>
          <img
            src='https://storage.planify.app.br/api/v1/buckets/planify-prod/objects/download?preview=true&prefix=header.png'
            alt='Header'
            class='header'
          />
        </div>

        <div class='content'>
          {{#if name}}
            <h1>Olá, {{name}}</h1>
          {{/if}}

          {{{dynamicContent}}}
        </div>

        <div class='signature'>
          <div class='signature-text'>
            <p>Obrigado,<br />Equipe Planify</p>
          </div>
          <img
            src='https://storage.planify.app.br/api/v1/buckets/planify-prod/objects/download?preview=true&prefix=bot-icon.png'
            alt='Bot Icon'
          />
        </div>

        <img
          src='https://storage.planify.app.br/api/v1/buckets/planify-prod/objects/download?preview=true&prefix=footer.png'
          alt='Footer'
          class='footer'
        />
      </div>
    </div>
  </body>
</html>