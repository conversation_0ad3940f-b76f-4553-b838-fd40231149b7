/* eslint-disable prettier/prettier */
import {
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import * as fs from 'fs';
import * as handlebars from 'handlebars';
import { sendEmailService } from './services/sendEmail';
import { EmailTypes } from './types';
import { TemplatesService } from '../templates/templates.service';
import { Template as EmailTemplate } from '../templates/entities/template.entity';
import { createModuleLogger } from '../../utils/moduleLogger';
import { QueuesService } from '../queues/queues.service';
import { QUEUE_NAMES } from '../queues/constants';
import { UsersService } from '../users/users.service';

@Injectable()
export class NetworkService {
  private baseTemplateCompiled: handlebars.TemplateDelegate;
  private readonly logger = createModuleLogger('NetworkService');

  constructor(
    private readonly templatesService: TemplatesService,
    private readonly queuesService: QueuesService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
  ) {
    handlebars.registerHelper('urlOnly', (url) => {
      const escapedUrl = handlebars.escapeExpression(url);
      return new handlebars.SafeString(
        `<a href="${escapedUrl}" style="color: #E01E36; text-decoration: underline; font-weight: 500;">${escapedUrl}</a>`,
      );
    });

    const baseTemplateContent = fs.readFileSync(
      './src/modules/network-services/templates/base-template.hbs',
      'utf8',
    );
    this.baseTemplateCompiled = handlebars.compile(baseTemplateContent);
    this.logger.info('Base email template loaded and compiled.');
  }

  private getCommonTemplateData() {
    return {
      currentYear: new Date().getFullYear(),
    };
  }

  async sendEmailRegister(data: {
    email: string;
    name: string;
    password: string;
  }): Promise<void | Error> {
    try {
      const baseUrl =
        process.env.FRONTEND_URL?.replace(/\/$/, '') ||
        'https://app.planify.com.br';
      const shortDisplayLink = baseUrl;

      const emailData = {
        email: data.email,
        name: data.name,
        password: data.password,
        link: baseUrl,
        shortDisplayLink: shortDisplayLink,
      };

      this.logger.info('Sending registration email', { email: data.email });
      return this.sendEmail('welcome-by-email', emailData, true);
    } catch (err) {
      this.logger.error('Error preparing registration email:', err);
      return err instanceof Error
        ? err
        : new Error(`Failed to send email due to ${err}`);
    }
  }

  async sendPasswordResetEmail(data: {
    email: string;
    name: string;
    resetToken: string;
  }): Promise<void | Error> {
    try {
      const baseUrl = process.env.FRONTEND_URL?.replace(/\/$/, '');
      const resetLink = `${baseUrl}/reset-password?token=${data.resetToken}`;

      const tokenPreview = data.resetToken.substring(0, 20) + '...';
      const shortDisplayLink = `${baseUrl}/reset-password?token=${tokenPreview}`;

      this.logger.info('Sending password reset email', { email: data.email });
      return this.sendEmail(
        'reset-password',
        {
          email: data.email,
          name: data.name,
          link: resetLink,
          shortDisplayLink: shortDisplayLink,
        },
        true,
      );
    } catch (err) {
      this.logger.error('Error preparing password reset email:', err);
      throw err;
    }
  }

  async sendEmail(
    type: EmailTypes,
    data: { email: string; name: string; [key: string]: any },
    useQueue = true,
  ): Promise<void | Error> {
    if (useQueue) {
      await this.queuesService.publish(QUEUE_NAMES.EMAIL, { type, data });
      this.logger.info(`Email enviado para fila (${QUEUE_NAMES.EMAIL})`, {
        type,
        email: data.email,
      });
      return;
    }
    try {
      this.logger.info(`Attempting to send email type: ${type}`, {
        email: data.email,
      });

      const emailTemplate: EmailTemplate =
        await this.templatesService.findByTag(type);

      if (!emailTemplate || emailTemplate.type !== 'email') {
        this.logger.error(
          `Email template not found or invalid type for tag: ${type}`,
        );
        throw new NotFoundException(
          `Email template with tag ${type} not found or not of type email.`,
        );
      }

      this.logger.info(`Template found for type ${type}`, {
        templateId: emailTemplate.id,
      });

      let dynamicTemplateContent = emailTemplate.content;

      if (type === 'welcome-by-email' || type === 'reset-password') {
        dynamicTemplateContent = dynamicTemplateContent.replace(
          /{{link}}/g,
          `<a href="{{link}}" style="color: #E01E36; text-decoration: underline; font-weight: 500;">{{link}}</a>`,
        );
        this.logger.info(
          `Modified template content to wrap {{link}} tag for type ${type}`,
        );
      }

      const placeholderRegex = /{{\s*([\w.]+)\s*}}/g;
      const placeholders = new Set<string>();
      let match;
      while ((match = placeholderRegex.exec(dynamicTemplateContent)) !== null) {
        placeholders.add(match[1]);
      }
      const missingFields = Array.from(placeholders).filter(
        (ph) => !(ph in data),
      );
      if (missingFields.length > 0) {
        this.logger.warn(
          `Existem campos não preenchidos no template de e-mail (${type}): ${missingFields.join(', ')}`,
          { dataFields: Object.keys(data) },
        );
      }

      const dynamicTemplateCompiled = handlebars.compile(
        dynamicTemplateContent,
      );

      const specificTemplateData = {
        ...this.getCommonTemplateData(),
        ...data,
      };

      const processedDynamicContent =
        dynamicTemplateCompiled(specificTemplateData);

      const baseTemplateData = {
        ...this.getCommonTemplateData(),
        name: data.name,
        dynamicContent: processedDynamicContent,
      };

      const finalHtml = this.baseTemplateCompiled(baseTemplateData);

      const serviceKey = process.env.BREVO_API_KEY;
      if (!serviceKey) {
        this.logger.error('BREVO_API_KEY not found in environment variables.');
        throw new Error('Email service configuration error: Missing API Key.');
      }

      const serviceOptions = {
        sender: {
          name: 'Planify',
          email: String(process.env.FROM_EMAIL),
        },
        to: [{ email: data.email, name: data.name }],
        subject: emailTemplate.name,
        htmlContent: finalHtml,
      };

      this.logger.info('Sending email via external service', {
        to: data.email,
        subject: serviceOptions.subject,
      });

      await sendEmailService(serviceOptions, serviceKey);
      this.logger.info(`Email type ${type} sent successfully to ${data.email}`);

      return;
    } catch (err) {
      this.logger.error(
        `Error sending email type ${type} to ${data.email}:`,
        err,
      );
      return err instanceof Error
        ? err
        : new Error(`Failed to send email due to ${err}`);
    }
  }

  async sendEmailBatch(data: {
    recipientsType: 'all' | 'group' | 'specific';
    groupType?: string;
    emails?: string[];
    templateType: EmailTypes;
    data?: { [key: string]: any };
  }): Promise<{ success: boolean; message: string }> {
    let recipients: { email: string; name: string }[] = [];

    if (data.recipientsType === 'all') {
      const users = await this.usersService.findAll();
      recipients = users.map((u) => ({ email: u.email, name: u.name }));
    } else if (data.recipientsType === 'group' && data.groupType) {
      const users = await this.usersService.findAll({
        where: { paymentPlanId: data.groupType },
      });
      recipients = users.map((u) => ({ email: u.email, name: u.name }));
    } else if (data.recipientsType === 'specific' && data.emails) {
      const users = await Promise.all(
        data.emails.map(async (email) => {
          const user = await this.usersService.findByEmail(email);
          return user
            ? { email: user.email, name: user.name }
            : { email, name: email };
        }),
      );
      recipients = users;
    } else {
      throw new Error('Tipo de destinatário inválido ou parâmetros ausentes.');
    }

    const emailsPayload = recipients.map((recipient) => ({
      type: data.templateType,
      data: {
        email: recipient.email,
        name: recipient.name,
        ...(data.data || {}),
      },
    }));

    await this.queuesService.publish(QUEUE_NAMES.EMAIL, emailsPayload);

    return {
      success: true,
      message: `Solicitação de envio em massa recebida para ${recipients.length} destinatários.`,
    };
  }
}
