import { forwardRef, Module } from '@nestjs/common';
import { NetworkService } from './network-services.service';
import { NetworkServicesController } from './network-services.controller';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { TemplatesModule } from '../templates/templates.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    TemplatesModule,
  ],
  controllers: [NetworkServicesController],
  providers: [NetworkService],
  exports: [NetworkService, TemplatesModule],
})
export class NetworkServicesModule {}
