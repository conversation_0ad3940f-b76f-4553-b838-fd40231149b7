/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { NotificationsService } from '../notifications/notifications.service';
import { AchievementRecordsService } from '../achievementRecords/achievementRecords.service';
import { UsersService } from '../users/users.service';
import { PaymentPlansService } from '../payment-plans/payment-plans.service';
import { PaymentsService } from '../payments/payments.service';
import { NetworkService } from '../network-services/network-services.service';
import { CreateNotificationDto } from '../notifications/dto/create-notification.dto';
import { QueuesService } from './queues.service';
import { QUEUE_NAMES } from './constants';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { AffiliatesService } from '../affiliates/affiliates.service';

@Injectable()
export class QueuesConsumer {
  private static isInitialized = false;
  private logger = createModuleLogger(QueuesConsumer.name);

  constructor(
    private readonly queuesService: QueuesService,
    private readonly notificationsService: NotificationsService,
    private readonly achievementRecordsService: AchievementRecordsService,
    private readonly usersService: UsersService,
    private readonly paymentPlansService: PaymentPlansService,
    private readonly paymentsService: PaymentsService,
    private readonly networkService: NetworkService,
    private readonly affiliatesService: AffiliatesService,
  ) {}

  async initializeConsumers() {
    if (QueuesConsumer.isInitialized) {
      return;
    }

    try {
      this.logger.info(
        '\n[QueuesConsumer] ====== INICIANDO CONFIGURAÇÃO DAS FILAS ======',
      );
      this.logger.info('[QueuesConsumer] Filas configuradas:');
      Object.entries(QUEUE_NAMES).forEach(([name, queue]) => {
        this.logger.info(`- ${name}: ${queue}`);
      });

      await this.queuesService.onModuleInit();
      this.logger.info(
        '\n[QueuesConsumer] Serviço de filas inicializado com sucesso',
      );

      this.logger.info('\n[QueuesConsumer] Iniciando consumidores...');

      // Achievement Queue Consumer
      await this.queuesService.consumer(
        QUEUE_NAMES.ACHIEVEMENT,
        async (data) => {
          try {
            this.logger.info(
              '[QueuesConsumer] Evento de achievement recebido:',
              data,
            );

            // Verifica se é um evento de achievement válido
            const achievementData = {
              userId: data.content?.userId || data.userId,
              badgeCode: data.content?.badgeCode || data.badgeCode,
            };

            if (achievementData.userId && achievementData.badgeCode) {
              const result = await this.achievementRecordsService.create({
                userId: achievementData.userId,
                badgeCode: achievementData.badgeCode,
              });

              if (result) {
                this.logger.info(
                  '[QueuesConsumer] Achievement processado com sucesso',
                );

                // Emitir evento para atualizar a UI depois de processar o achievement
                setTimeout(async () => {
                  try {
                    await this.queuesService.publish(
                      QUEUE_NAMES.NOTIFICATION_EVENTS,
                      {
                        userId: achievementData.userId,
                        action: 'achievement',
                      },
                    );
                    this.logger.info(
                      '[QueuesConsumer] Evento de atualização de UI enviado após achievement',
                    );
                  } catch (error) {
                    this.logger.error(
                      '[QueuesConsumer] Erro ao emitir evento após achievement:',
                      error,
                    );
                  }
                }, 300);
              } else {
                this.logger.info('[QueuesConsumer] Achievement já existe');
              }
            } else {
              this.logger.error(
                '[QueuesConsumer] Dados de achievement inválidos:',
                JSON.stringify(data),
              );
            }
          } catch (error) {
            this.logger.error(
              '[QueuesConsumer] Erro ao processar achievement:',
              {
                error: error.message,
                data,
              },
            );
            throw error;
          }
        },
      );

      // Notification Queue Consumer
      await this.queuesService.consumer(
        QUEUE_NAMES.NOTIFICATION,
        async (data) => {
          try {
            this.logger.info(
              '[QueuesConsumer] Evento de notificação recebido:',
              data,
            );

            const notificationData = data.content || data;

            if (notificationData.userId) {
              const createNotificationDto: CreateNotificationDto = {
                userId: notificationData.userId,
                type: notificationData.type || 'default',
                title: notificationData.title,
                description: notificationData.description,
                notificationStatus: notificationData.notificationStatus,
                targetType: notificationData.targetType,
                targetDate: notificationData.targetDate,
                targetId: notificationData.targetId || null,
              };

              await this.notificationsService.create(createNotificationDto);
              this.logger.info(
                '[QueuesConsumer] Notificação criada com sucesso para userId:',
                notificationData.userId,
              );

              // Emitir evento para atualizar a UI depois de criar a notificação
              setTimeout(async () => {
                try {
                  await this.queuesService.publish(
                    QUEUE_NAMES.NOTIFICATION_EVENTS,
                    {
                      userId: notificationData.userId,
                      action: 'create',
                    },
                  );
                  this.logger.info(
                    '[QueuesConsumer] Evento de atualização de UI enviado após criar notificação',
                  );
                } catch (error) {
                  this.logger.error(
                    '[QueuesConsumer] Erro ao emitir evento após criar notificação:',
                    error,
                  );
                }
              }, 300);
            } else {
              this.logger.error(
                '[QueuesConsumer] Dados de notificação inválidos:',
                data,
              );
            }
          } catch (error) {
            this.logger.error(
              '[QueuesConsumer] Erro ao processar notificação:',
              error,
            );
            throw error;
          }
        },
      );

      // Notification Events Queue Consumer (SSE)
      await this.queuesService.consumer(
        QUEUE_NAMES.NOTIFICATION_EVENTS,
        async (data) => {
          try {
            this.logger.info(
              '[QueuesConsumer] Evento de notificação SSE recebido:',
              data,
            );

            const notificationData = data.content || data;

            if (notificationData.userId) {
              // Se já tiver as notificações no payload, use-as
              if (notificationData.notifications) {
                this.notificationsService.handleNotificationEvent({
                  userId: notificationData.userId,
                  count: notificationData.notifications.length,
                  notifications: notificationData.notifications,
                });
                this.logger.info(
                  '[QueuesConsumer] Evento SSE processado com notificações existentes para userId:',
                  notificationData.userId,
                );
              } else {
                // Adicionar logs detalhados para debug
                this.logger.info(
                  '[QueuesConsumer] Buscando notificações atualizadas para emitir evento SSE:',
                  notificationData.userId,
                );

                // Caso contrário, buscar notificações e emitir o evento
                await this.notificationsService.emitNotificationEvent(
                  notificationData.userId,
                );
                this.logger.info(
                  '[QueuesConsumer] Evento SSE processado via emitNotificationEvent para userId:',
                  notificationData.userId,
                );
              }
            } else {
              this.logger.warn(
                '[QueuesConsumer] Evento SSE sem userId, ignorando:',
                data,
              );
            }
          } catch (error) {
            this.logger.error(
              '[QueuesConsumer] Erro ao processar evento SSE:',
              error,
            );
            throw error;
          }
        },
      );

      // Email Queue Consumer
      await this.queuesService.consumer(QUEUE_NAMES.EMAIL, async (data) => {
        try {
          this.logger.info('[QueuesConsumer] Evento de email recebido:', data);

          const emailData = data.content || data;

          if (Array.isArray(emailData)) {
            let batch = [];
            for (let i = 0; i < emailData.length; i++) {
              batch.push(emailData[i]);
              if (batch.length === 80 || i === emailData.length - 1) {
                await Promise.all(
                  batch.map((item) =>
                    this.networkService.sendEmail(item.type, item.data, false),
                  ),
                );
                this.logger.info(
                  `[QueuesConsumer] Lote de ${batch.length} e-mails enviado.`,
                );
                batch = [];
                if (i < emailData.length - 1) {
                  this.logger.info(
                    '[QueuesConsumer] Aguardando 1 minuto para próximo lote.',
                  );
                  await new Promise((resolve) => setTimeout(resolve, 60000));
                }
              }
            }
          } else {
            await this.networkService.sendEmail(
              emailData.type,
              emailData.data,
              false,
            );
            this.logger.info('[QueuesConsumer] Email enviado com sucesso');
          }
        } catch (error) {
          this.logger.error('[QueuesConsumer] Erro ao processar email:', {
            error: error.message,
            data,
          });
          throw error;
        }
      });

      // Analytics Metric Queue Consumer
      await this.queuesService.consumer(
        QUEUE_NAMES.ANALYTICS_METRIC,
        async (data) => {
          try {
            this.logger.info(
              '[QueuesConsumer] Evento de métrica de analytics recebido:',
              data,
            );

            const metricData = data.content || data;

            if (metricData.referralCode) {
              this.logger.info(
                `[QueuesConsumer] Processando métrica de analytics para referralCode: ${metricData.referralCode}`,
              );

              const result = await this.affiliatesService.createAnalytic({
                referralCode: metricData.referralCode,
                userId: metricData.userId,
                metricType: metricData.metricType,
                metricValue: metricData.metricValue,
                paymentId: metricData.paymentId,
              });

              if (result) {
                this.logger.info(
                  `[QueuesConsumer] Métrica de analytics processada com sucesso: ${metricData.metricType}`,
                );
              }
            } else {
              this.logger.error(
                '[QueuesConsumer] Dados de métrica de analytics inválidos:',
                JSON.stringify(metricData),
              );
            }
          } catch (error) {
            this.logger.error(
              '[QueuesConsumer] Erro ao processar métrica de analytics:',
              {
                error: error.message,
                data,
              },
            );
            throw error;
          }
        },
      );

      this.logger.info('\n[QueuesConsumer] Status das filas:');
      this.logger.info(
        `✓ ${QUEUE_NAMES.ACHIEVEMENT}: Consumidor iniciado e aguardando eventos`,
      );
      this.logger.info(
        `✓ ${QUEUE_NAMES.NOTIFICATION}: Consumidor iniciado e aguardando eventos`,
      );
      this.logger.info(
        `✓ ${QUEUE_NAMES.NOTIFICATION_EVENTS}: Consumidor iniciado e aguardando eventos (SSE)`,
      );
      this.logger.info(
        `✓ ${QUEUE_NAMES.EMAIL}: Consumidor iniciado e aguardando eventos`,
      );
      this.logger.info(
        `✓ ${QUEUE_NAMES.ANALYTICS_METRIC}: Consumidor iniciado e aguardando eventos`,
      );
      this.logger.info(
        '\n[QueuesConsumer] ====== CONFIGURAÇÃO DAS FILAS CONCLUÍDA ======\n',
      );

      QueuesConsumer.isInitialized = true;
      this.logger.info(
        '[QueuesConsumer] Todos os consumidores inicializados com sucesso',
      );
    } catch (error) {
      this.logger.error(
        '[QueuesConsumer] Erro ao inicializar consumidores:',
        error,
      );
      QueuesConsumer.isInitialized = false;
      throw error;
    }
  }

  async handleUnreadNotifications(data: { userId: string }) {
    try {
      const notifications =
        await this.notificationsService.findUnreadNotifications(data.userId);
      await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION_EVENTS, {
        userId: data.userId,
        notifications,
      });
    } catch (error) {
      this.logger.error('Error processing unread notifications:', error);
      throw error;
    }
  }
}
