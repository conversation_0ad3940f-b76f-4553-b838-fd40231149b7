/* eslint-disable prettier/prettier */
import { forwardRef, Global, Module, OnModuleInit } from '@nestjs/common';
import { QueuesService } from './queues.service';
import { QueuesConsumer } from './queuesConsumer';
import { NotificationsModule } from '../notifications/notifications.module';
import { AchievementRecordsModule } from '../achievementRecords/achievementRecords.module';
import { UsersModule } from '../users/users.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';
import { PaymentsModule } from '../payments/payments.module';
import { NetworkServicesModule } from '../network-services/network-services.module';
import { NotificationsService } from '../notifications/notifications.service';
import { ModuleRef } from '@nestjs/core';
import { AffiliatesModule } from '../affiliates/affiliates.module';

@Global()
@Module({
  imports: [
    forwardRef(() => NotificationsModule),
    forwardRef(() => AchievementRecordsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => PaymentPlansModule),
    forwardRef(() => PaymentsModule),
    forwardRef(() => NetworkServicesModule),
    forwardRef(() => AffiliatesModule),
  ],
  providers: [QueuesService, QueuesConsumer],
  exports: [QueuesService, QueuesConsumer],
})
export class QueuesModule implements OnModuleInit {
  private static isInitialized = false;

  constructor(
    private readonly queuesService: QueuesService,
    private readonly queuesConsumer: QueuesConsumer,
    private readonly moduleRef: ModuleRef,
  ) {}

  async onModuleInit() {
    if (QueuesModule.isInitialized) {
      return;
    }

    try {
      // Garante que o serviço de filas está inicializado antes dos consumidores
      await this.queuesService.onModuleInit();

      // Inicializa os consumidores
      await this.queuesConsumer.initializeConsumers();

      // Tentativa de resolver o serviço de notificações após inicialização
      try {
        const notificationsService = this.moduleRef.get(NotificationsService, {
          strict: false,
        });
        if (notificationsService) {
          this.queuesService.setNotificationsService(notificationsService);
          console.log(
            '[QueuesModule] NotificationsService configurado com sucesso',
          );
        }
      } catch (error) {
        console.warn(
          '[QueuesModule] Não foi possível resolver NotificationsService agora:',
          error.message,
        );
      }

      QueuesModule.isInitialized = true;
    } catch (error) {
      console.error('[QueuesModule] Erro ao inicializar módulo:', error);
      QueuesModule.isInitialized = false;
      throw error;
    }
  }
}
