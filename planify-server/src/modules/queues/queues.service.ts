import { Injectable, OnModuleInit, Inject, forwardRef } from '@nestjs/common';
import { QUEUE_NAMES } from './constants';
import { QueueManager } from '@planify/queues';
import { NotificationsService } from '../notifications/notifications.service';

export type QueueName = (typeof QUEUE_NAMES)[keyof typeof QUEUE_NAMES];

@Injectable()
export class QueuesService implements OnModuleInit {
  private static instance: QueuesService | null = null;
  private queueManager!: QueueManager;
  private static isInitialized = false;
  private notificationsServiceRef?: NotificationsService;

  constructor(
    @Inject(forwardRef(() => NotificationsService))
    private readonly notificationsService?: NotificationsService,
  ) {
    if (!QueuesService.instance) {
      QueuesService.instance = this;
    }
    return QueuesService.instance;
  }

  setNotificationsService(service: NotificationsService) {
    this.notificationsServiceRef = service;
  }

  private getNotificationsService() {
    return this.notificationsServiceRef || this.notificationsService;
  }

  async onModuleInit() {
    if (QueuesService.isInitialized) {
      return;
    }

    try {
      console.log('\n[QueuesService] Iniciando conexão com Redis...');
      await this.initialize();
      console.log('[QueuesService] Conexão com Redis estabelecida com sucesso');
      console.log('[QueuesService] Configurações:');
      console.log(`- Host: ${process.env.REDIS_HOST || 'localhost'}`);
      console.log(`- Port: ${process.env.REDIS_PORT || '6379'}`);
      console.log(`- Username: ${process.env.REDIS_USERNAME || 'default'}`);
      console.log('[QueuesService] Serviço de filas pronto para uso\n');
      QueuesService.isInitialized = true;
    } catch (error) {
      console.error('\n[QueuesService] ❌ ERRO AO CONECTAR COM REDIS:', error);
      QueuesService.isInitialized = false;
      throw error;
    }
  }

  private async initialize() {
    if (!this.queueManager) {
      try {
        this.queueManager = new QueueManager({
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          username: process.env.REDIS_USERNAME || 'default',
          password: process.env.REDIS_PASSWORD || 'redis123',
        });
      } catch (error) {
        console.error(
          '[QueuesService] Erro ao inicializar QueueManager:',
          error,
        );
        throw error;
      }
    }
  }

  static getInstance(): QueuesService {
    if (!QueuesService.instance) {
      QueuesService.instance = new QueuesService();
    }
    return QueuesService.instance;
  }

  private validateQueueName(queueName: QueueName): void {
    const validQueues = Object.values(QUEUE_NAMES);
    if (!validQueues.includes(queueName)) {
      throw new Error(
        `Invalid queue name: ${queueName}. Valid queues are: ${validQueues.join(', ')}`,
      );
    }
  }

  private async ensureConnection() {
    if (!QueuesService.isInitialized) {
      console.log('[QueuesService] Tentando inicializar conexão com Redis...');

      try {
        if (!this.queueManager) {
          this.queueManager = new QueueManager({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            username: process.env.REDIS_USERNAME || 'default',
            password: process.env.REDIS_PASSWORD || 'redis123',
          });
        }

        QueuesService.isInitialized = true;
        console.log(
          '[QueuesService] Conexão com Redis estabelecida com sucesso',
        );
      } catch (error) {
        console.error('[QueuesService] Erro ao conectar com Redis:', error);
        QueuesService.isInitialized = false;
      }
    }
  }

  async publish(queueName: QueueName, data: any) {
    try {
      await this.ensureConnection();
      this.validateQueueName(queueName);

      if (!this.queueManager) {
        console.error('[QueuesService] QueueManager não está inicializado');
        return false;
      }

      try {
        const result = await this.queueManager.publish(queueName, data);

        if (queueName === QUEUE_NAMES.NOTIFICATION && data.userId) {
          setTimeout(async () => {
            try {
              const notificationsService = this.getNotificationsService();
              if (notificationsService) {
                await notificationsService.emitNotificationEvent(data.userId);
              }
            } catch (error) {
              console.error('[QueuesService] Erro ao emitir evento:', error);
            }
          }, 100);
        }

        return result;
      } catch (error) {
        console.error(
          `[QueuesService] Erro ao publicar na fila ${queueName}:`,
          error,
        );
        return false;
      }
    } catch (error) {
      console.error('[QueuesService] Erro na conexão Redis:', error);
      return false;
    }
  }

  async consumer(queueName: QueueName, callback: (data: any) => Promise<void>) {
    await this.ensureConnection();
    this.validateQueueName(queueName);
    try {
      return await this.queueManager.consumer(queueName, callback);
    } catch (error) {
      console.error(
        `[QueuesService] Erro ao configurar consumidor para ${queueName}:`,
        error,
      );
      throw error;
    }
  }

  async disconnect() {
    if (this.queueManager) {
      await this.queueManager.disconnect();
      QueuesService.isInitialized = false;
    }
  }
}
export const globalQueuesService = QueuesService.getInstance();
