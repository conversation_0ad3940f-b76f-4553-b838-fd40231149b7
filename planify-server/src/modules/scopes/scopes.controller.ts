import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ScopesService } from './scopes.service';
import { CreateScopeDto } from './dto/create-scope.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';

@ApiTags('scopes')
@ApiBearerAuth()
@UseGuards(AuthGuard, PermissionsGuard)
@Permissions('admin')
@Controller('scopes')
export class ScopesController {
  constructor(private readonly scopesService: ScopesService) {}

  @Post()
  create(@Body() createScopeDto: CreateScopeDto) {
    return this.scopesService.create(createScopeDto);
  }

  @Get()
  findAll() {
    return this.scopesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.scopesService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateScopeDto: CreateScopeDto) {
    return this.scopesService.update(id, updateScopeDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.scopesService.remove(id);
  }
}
