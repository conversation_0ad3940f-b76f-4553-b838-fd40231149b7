import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateScopeDto } from './dto/create-scope.dto';
import { InjectModel } from '@nestjs/sequelize';
import { Scope } from './entities/scope.entity';

@Injectable()
export class ScopesService {
  constructor(
    @InjectModel(Scope)
    private scopeModel: typeof Scope,
  ) {}

  async create(createScopeDto: CreateScopeDto) {
    const scope = await this.scopeModel.create({
      ...createScopeDto,
    } as Scope);
    return scope;
  }

  async findAll() {
    return await this.scopeModel.findAll({
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: string) {
    const scope = await this.scopeModel.findByPk(id);

    if (!scope) {
      throw new NotFoundException('Escopo não encontrado');
    }

    return scope;
  }

  async update(id: string, updateScopeDto: CreateScopeDto) {
    const scope = await this.findOne(id);
    await scope.update({
      ...updateScopeDto,
    });
    return scope;
  }

  async remove(id: string) {
    const scope = await this.findOne(id);
    await scope.destroy();
    return { message: 'Escopo removido com sucesso' };
  }
}
