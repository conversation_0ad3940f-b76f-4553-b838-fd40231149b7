import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateScopeDto {
  @ApiProperty({
    description: 'Nome do escopo',
    example: '<PERSON>it<PERSON>',
  })
  @IsNotEmpty({ message: 'O nome é obrigatório' })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Tag do escopo',
    example: 'READ',
  })
  @IsNotEmpty({ message: 'A tag é obrigatória' })
  @IsString()
  tag: string;
}
