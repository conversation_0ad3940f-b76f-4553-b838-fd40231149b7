import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({
  tableName: 'Scopes',
})
export class Scope extends Model<Scope> {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  tag: string;
}
