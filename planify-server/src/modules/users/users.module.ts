/* eslint-disable prettier/prettier */
import { forwardRef, Module } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { SequelizeModule } from '@nestjs/sequelize';
import { JwtModule } from '@nestjs/jwt';
import { UserInfos } from './entities/user-profile.entity';
import { AuthModule } from '../auth/auth.module';
import { PaymentsModule } from '../payments/payments.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';
import { Scope } from '../scopes/entities/scope.entity';
import { Payment } from '../payments/entities/payment.entity';
import { PaymentPlans } from '../payment-plans/entities/payment-plan.entity';
import { NetworkServicesModule } from '../network-services/network-services.module';
import { WeightHistoriesModule } from '../weight-histories/weight-histories.module';
import { FilesModule } from '../files/files.module';
import { File } from '../files/entities/file.entity';
import { AffiliatesModule } from 'src/modules/affiliates/affiliates.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      User,
      UserInfos,
      Payment,
      PaymentPlans,
      Scope,
      File,
    ]),
    JwtModule,
    forwardRef(() => AuthModule),
    forwardRef(() => PaymentsModule),
    forwardRef(() => PaymentPlansModule),
    forwardRef(() => NetworkServicesModule),
    forwardRef(() => WeightHistoriesModule),
    forwardRef(() => FilesModule),
    forwardRef(() => AffiliatesModule),
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService, SequelizeModule],
})
export class UsersModule {}
