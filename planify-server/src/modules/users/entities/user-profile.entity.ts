/* eslint-disable prettier/prettier */
import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './user.entity';

@Table({
  tableName: 'UserInfos',
})
export class UserInfos extends Model<UserInfos> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => User)
  user: User;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  birthDate: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  gender: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  cellphone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  cep: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  state: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  city: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  location: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  locationNumber: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  locationComplement: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  weight: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  currentActivitiesWeek: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  lastCheckup: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  menstualCycle: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  canOutExercises: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  receiveMessages: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  activityLevel: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  authorizeAllChannels: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  authorizePartners: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  receiveNewsletter: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  receiveNotifications: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  profileImage: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  financialEducationLevel: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  investorProfile: string;
}
