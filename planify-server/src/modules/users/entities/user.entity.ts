/* eslint-disable prettier/prettier */
import {
  Column,
  Model,
  Table,
  DataType,
  BeforeCreate,
  HasOne,
  BelongsTo,
  ForeignKey,
  HasMany,
} from 'sequelize-typescript';
import * as bcrypt from 'bcrypt';
import { UserInfos } from './user-profile.entity';
import { PaymentPlans } from 'src/modules/payment-plans/entities/payment-plan.entity';
import { Payment } from 'src/modules/payments/entities/payment.entity';

type TUser = 'user' | 'admin' | 'partner' | 'custom' | 'mentor' | 'affiliate';
@Table({
  tableName: 'Users',
})
export class User extends Model<User> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  cpf: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isPasswordSubmited?: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isResetPassword?: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  userType: TUser;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  activationCode?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  sharedCode?: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  lastAccess: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isValidatedAccount?: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  password: string;

  @Column({
    type: DataType.ENUM('active', 'pending', 'inactive'),
    allowNull: false,
    defaultValue: 'pending',
  })
  userAccountStatus: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  pendingStatusReason?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: 'false',
  })
  termsAccepted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  termsAcceptedAt: string;

  @ForeignKey(() => PaymentPlans)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  paymentPlanId: string;

  @BelongsTo(() => PaymentPlans, 'paymentPlanId')
  paymentPlan: PaymentPlans;

  permissions?: string[];

  @HasOne(() => UserInfos)
  userInfos: UserInfos;

  @HasMany(() => Payment)
  payments: Payment[];

  @BeforeCreate
  static async hashPassword(instance: User) {
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(instance.password, saltRounds);
    instance.password = hashedPassword;
  }
}
