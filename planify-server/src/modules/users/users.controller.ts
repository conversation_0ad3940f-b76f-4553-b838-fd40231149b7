/* eslint-disable prettier/prettier */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Req,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { AuthGuard } from '../auth/auth.guard';
import { ApiTags, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { CheckUserDto } from './dto/check-email-or-cpf.dto';
import { ValidatePasswordDto } from './dto/validate-password.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request } from 'express';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  create(@Body() userDto: CreateUserDto) {
    return this.usersService.create(userDto);
  }

  @Get('/find-by-id/:id')
  findById(@Param('id') id: string) {
    return this.usersService.findById(id);
  }

  // atualiza dados do usuario
  @Patch('/update-user/:id')
  updateUserData(@Param('id') id: string, @Body() data: any) {
    return this.usersService.updateUserData(id, data);
  }

  @Get('/check-user')
  async checkUser(@Query() query: CheckUserDto) {
    const { value, type } = query;
    return this.usersService.checkUser(value, type);
  }

  @UseGuards(AuthGuard)
  @Get('/user-information')
  getUser(@Req() token: string) {
    return this.usersService.getUser(token);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.usersService.findAll();
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }

  @UseGuards(AuthGuard)
  @Patch('/profile')
  updateProfile(@Req() token: string, @Body() userDto: any) {
    return this.usersService.updateProfile(token, userDto);
  }

  @Get('/validate-activation-code/:sharedCode')
  validateActivationCode(@Param('sharedCode') sharedCode: string) {
    return this.usersService.validateActivationCode(sharedCode);
  }

  @Patch('/activate')
  activateUser(@Body() data: any) {
    return this.usersService.activateUser(data);
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() userDto: UpdateUserDto) {
    return this.usersService.update(+id, userDto);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.usersService.remove(+id);
  }

  @UseGuards(AuthGuard)
  @Post('validate-password')
  validatePassword(
    @Body() { password }: ValidatePasswordDto,
    @Req() token: string,
  ) {
    return this.usersService.verifyUserPasswordOrThrow(password, token);
  }

  @UseGuards(AuthGuard)
  @Patch('/profile/image')
  @UseInterceptors(FileInterceptor('image'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
          description: 'The profile image file',
        },
      },
    },
  })
  async updateProfileImage(
    @Req() token: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('No image file provided');
    }
    return this.usersService.updateProfileImage(token, file);
  }
}
