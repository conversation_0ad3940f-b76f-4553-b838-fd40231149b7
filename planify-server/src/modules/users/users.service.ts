/* eslint-disable prettier/prettier */
import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
  forwardRef,
  Inject,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserInfos } from './entities/user-profile.entity';
import { Op, Sequelize } from 'sequelize';
import { Transaction } from 'sequelize';
import { Payment } from '../payments/entities/payment.entity';
import { PaymentPlans } from '../payment-plans/entities/payment-plan.entity';
import { Permission } from '../permissions/entities/permission.entity';
import { Scope } from '../scopes/entities/scope.entity';
import { UserIdentifierType } from 'src/data/@types/UserIdentifierType';
import { UpdateUserPasswordDto } from './dto/update-user-password.dto';
import { getUserId } from 'src/utils';
import { compare } from 'bcrypt';
import { NetworkService } from '../network-services/network-services.service';
import { WeightHistoriesService } from '../weight-histories/weight-histories.service';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { FilesService } from '../files/files.service';
import { FileType } from '../files/enums/file-type.enum';
import { File } from '../files/entities/file.entity';
import { AffiliatesService } from '../affiliates/affiliates.service';
import { QueuesService } from '../queues/queues.service';
import { QUEUE_NAMES } from '../queues/constants';

@Injectable()
export class UsersService {
  private logger = createModuleLogger(UsersService.name);

  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(UserInfos)
    private userInfosModel: typeof UserInfos,
    @InjectModel(Payment)
    private paymentModel: typeof Payment,
    @InjectModel(PaymentPlans)
    private paymentPlanModel: typeof PaymentPlans,
    @InjectModel(Scope)
    private scopeModel: typeof Scope,
    @InjectModel(File)
    private fileModel: typeof File,
    @Inject(forwardRef(() => NetworkService))
    private networkService: NetworkService,
    @Inject(forwardRef(() => WeightHistoriesService))
    private weightHistoriesService: WeightHistoriesService,
    @Inject(forwardRef(() => FilesService))
    private filesService: FilesService,
    @Inject(forwardRef(() => AffiliatesService))
    private affiliatesService: AffiliatesService,
    @Inject(forwardRef(() => QueuesService))
    private queuesService: QueuesService,
  ) {}

  async findOneWithSharedCodeDateCode(dateCode: string) {
    return await this.userModel.findOne({
      where: { sharedCode: { [Op.like]: `PY${dateCode}-%` } },
      order: [['createdAt', 'DESC']],
    });
  }

  async create(user: CreateUserDto): Promise<User> {
    let transaction;

    try {
      transaction = await this.userModel.sequelize.transaction();

      const accountExists = await this.userModel.findOne({
        where: { email: user.email },
      });

      if (accountExists) {
        this.logger.warn(`User with email ${user.email} already exists`);
        throw new BadRequestException(
          'Invalid email format or this account already exists',
        );
      }

      const now = new Date();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const year = String(now.getFullYear()).slice(-2);
      const dateCode = `${month}${year}`;

      const lastUser = await this.findOneWithSharedCodeDateCode(dateCode);

      let nextSequence = 1;
      if (lastUser?.sharedCode) {
        const lastSequence = parseInt(lastUser.sharedCode.split('-')[1], 10);
        nextSequence = lastSequence + 1;
      }

      // Formata o código sequencial para 4 dígitos (ex: 0001)
      const sequenceCode = String(nextSequence).padStart(4, '0');

      // Gera o sharedCode no formato PYMMYY-XXXX
      const sharedCode = `PY${dateCode}-${sequenceCode}`;
      user.sharedCode = sharedCode;

      let isAffiliateCode = false;
      let referralCode = null;

      if (user?.activationCode) {
        const { isValid, type, code } = await this.validateActivationCode(
          user.activationCode,
        );

        if (!isValid) {
          throw new BadRequestException({ message: 'Invalid activation code' });
        }

        if (type === 'affiliate') {
          isAffiliateCode = true;
          referralCode = code;
        }

        user.isValidatedAccount = true;
      }

      if (user?.termsAccepted) {
        user.termsAcceptedAt = new Date().toISOString();
      }

      const { cellphone, ...userData } = user;

      const createdUser = await this.userModel.create(userData, {
        transaction,
      });

      await this.userInfosModel.create(
        {
          userId: createdUser.id,
          cellphone: cellphone,
        },
        { transaction },
      );

      await transaction.commit();

      await this.networkService.sendEmail('welcome', {
        email: user.email,
        name: user.name,
      });

      if (isAffiliateCode && referralCode) {
        try {
          await this.queuesService.publish(QUEUE_NAMES.ANALYTICS_METRIC, {
            referralCode,
            metricType: 'registration',
            userId: createdUser.id,
          });

          this.logger.debug(
            `Publicado evento na fila para registrar métrica de registro de afiliado para o usuário ${createdUser.id} com referralCode ${referralCode}`,
          );
        } catch (error) {
          this.logger.error(
            `Erro ao publicar métrica de afiliado na fila: ${error.message}`,
          );

          try {
            await this.affiliatesService.createAnalytic({
              referralCode,
              metricType: 'registration',
              userId: createdUser.id,
            });
            this.logger.debug(
              `Registered affiliate registration metric for user ${createdUser.id} with referral code ${referralCode}`,
            );
          } catch (fallbackError) {
            this.logger.error(
              `Error registering affiliate metric (fallback): ${fallbackError.message}`,
            );
          }
        }
      }

      return createdUser;
    } catch (error) {
      if (transaction) await transaction.rollback();
      throw error;
    }
  }

  async findOrCreate(user: CreateUserDto) {
    const userAccount = await this.userModel.findOne({
      where: { email: user.email },
    });

    if (userAccount) {
      return userAccount;
    } else {
      return await this.userModel.create(user);
    }
  }

  async getUser(token: string) {
    const loggedUser = getUserId(token);
    if (!loggedUser) {
      throw new Error('Usuário não encontrado');
    }

    return this.userModel.findByPk(loggedUser, {
      attributes: { exclude: ['password'] },
      include: [
        UserInfos,
        {
          model: PaymentPlans,
          attributes: [
            'name',
            'typePlan',
            'status',
            'id',
            'recurrencyType',
            'assignDescription',
            'price',
          ],
        },
        {
          model: Payment,
          attributes: [
            'id',
            'subscriptionId',
            'canceledAt',
            'reason',
            'expirationAt',
            'status',
            'createdAt',
          ],
          required: false,
          where: {
            paymentPlanId: {
              [Op.or]: [
                { [Op.eq]: Sequelize.col('User.paymentPlanId') },
                { [Op.is]: null },
              ],
            },
            userId: loggedUser,
          },
          order: [['createdAt', 'DESC']],
        },
      ],
    });
  }

  async findAll(filter: any = {}) {
    try {
      const users = await this.userModel.findAll({
        ...filter,
        attributes: { exclude: ['password'] },
        include: [UserInfos],
      });

      return users;
    } catch (error) {
      throw new BadRequestException('Failed to retrieve users');
    }
  }

  async findByEmail(email: string) {
    return await this.userModel.findOne({ where: { email } });
  }

  async findOneByEmail(email: string) {
    return await this.userModel.findOne({ where: { email } });
  }

  async findOne(id: string) {
    const user = await this.userModel.findByPk(id, {
      attributes: { exclude: ['password'] },
      include: [UserInfos],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findOneWithPassword(id: string) {
    return await this.userModel.findByPk(id);
  }

  async update(id: string | number, user: UpdateUserDto) {
    return await this.userModel.update(user, { where: { id } });
  }

  async activateUser(data: any) {
    const { activationCode, userId } = data;

    if (!userId) {
      throw new UnauthorizedException({ message: 'User not found' });
    }

    const { isValid, type, code } =
      await this.validateActivationCode(activationCode);

    if (!isValid) {
      throw new UnauthorizedException({
        message: 'Invalid activation code',
      });
    }

    const currentUser = await this.userModel.findByPk(userId);

    currentUser.activationCode = activationCode;
    currentUser.isValidatedAccount = true;

    await currentUser.save();

    // Registrar métrica se for código de afiliado
    if (type === 'affiliate' && code) {
      try {
        await this.affiliatesService.createAnalytic({
          referralCode: code,
          metricType: 'registration',
          userId,
        });
        this.logger.debug(
          `Registered affiliate registration metric for user ${userId} with referral code ${code}`,
        );
      } catch (error) {
        this.logger.error(
          `Error registering affiliate metric: ${error.message}`,
        );
      }
    }
  }

  async validateActivationCode(sharedCode: string) {
    try {
      const user = await this.userModel.findOne({
        where: { sharedCode },
      });

      if (user) {
        return { isValid: true, type: 'user', code: sharedCode };
      }

      try {
        const referralExists =
          await this.affiliatesService.findReferralByCode(sharedCode);
        if (referralExists) {
          return { isValid: true, type: 'affiliate', code: sharedCode };
        }

        const affiliateExists =
          await this.affiliatesService.findByCode(sharedCode);
        if (affiliateExists) {
          return { isValid: true, type: 'affiliate', code: sharedCode };
        }
      } catch (error) {
        this.logger.error(`Error validating affiliate code: ${error.message}`);
      }

      return { isValid: false, type: null, code: null };
    } catch (error) {
      this.logger.error(`Error validating activation code: ${error.message}`);
      return { isValid: false, type: null, code: null };
    }
  }

  async updateProfile(token: string, userProfile: any) {
    const loggedUser = getUserId(token);
    const transaction = await this.userModel.sequelize.transaction();

    try {
      if (!userProfile || Object.keys(userProfile).length === 0) {
        throw new BadRequestException('Invalid profile data');
      }

      const user = await this.userModel.findByPk(loggedUser, {
        include: [UserInfos],
        transaction,
      });

      if (!user) {
        throw new BadRequestException('User not found');
      }

      const { cpf, name, ...otherProfileData } = userProfile;

      if (cpf) {
        const existingCpfUser = await this.userModel.findOne({
          where: { cpf, id: { [Op.ne]: loggedUser } },
          transaction,
        });

        if (existingCpfUser) {
          throw new BadRequestException('CPF already in use by another user');
        }
      }

      const [userInfo, created] = await this.userInfosModel.findOrCreate({
        where: { userId: loggedUser },
        defaults: { ...userProfile, userId: loggedUser },
        transaction,
      });

      if (!created) {
        await userInfo.update({ cpf, ...otherProfileData }, { transaction });
      }

      const userUpdateData = {} as any;
      if (!user.cpf && cpf) {
        userUpdateData.cpf = cpf;
      }
      if (name) {
        userUpdateData.name = name;
      }

      if (Object.keys(userUpdateData).length > 0) {
        await user.update(userUpdateData, { transaction });
      }

      if (userProfile.weight) {
        await this.weightHistoriesService.recordWeight(
          loggedUser,
          parseFloat(userProfile.weight),
          transaction,
        );
      }

      await transaction.commit();
      return userInfo;
    } catch (error) {
      await transaction.rollback();
      console.error('Error updating user profile:', error);
      throw new BadRequestException('Failed to update user profile');
    }
  }

  async updateUserData(id: string, userProfile: any) {
    const transaction = await this.userModel.sequelize.transaction();

    try {
      if (!userProfile || Object.keys(userProfile).length === 0) {
        throw new BadRequestException('Invalid profile data');
      }

      const user = await this.userModel.findByPk(id, {
        include: [UserInfos],
        transaction,
      });

      if (!user) {
        throw new BadRequestException('User not found');
      }

      const { cpf, name, activationCode, email, ...otherProfileData } =
        userProfile;

      if (activationCode && activationCode !== user.activationCode) {
        const { isValid, type, code } =
          await this.validateActivationCode(activationCode);

        if (!isValid) {
          throw new BadRequestException('Invalid activation code');
        }

        if (type === 'affiliate' && code) {
          try {
            await this.affiliatesService.createAnalytic({
              referralCode: code,
              metricType: 'registration',
              userId: id,
            });
            this.logger.debug(
              `Registered affiliate registration metric for user ${id} with referral code ${code}`,
            );
          } catch (error) {
            this.logger.error(
              `Error registering affiliate metric: ${error.message}`,
            );
          }
        }
      }

      const [userInfo, created] = await this.userInfosModel.findOrCreate({
        where: { userId: id },
        defaults: { ...userProfile, userId: id },
        transaction,
      });

      if (!created) {
        await userInfo.update({ cpf, ...otherProfileData }, { transaction });
      }

      const userUpdateData = {} as any;

      userUpdateData.name = name;
      userUpdateData.cpf = cpf;
      userUpdateData.activationCode = activationCode;
      userUpdateData.email = email;

      if (Object.keys(userUpdateData).length > 0) {
        await user.update(userUpdateData, { transaction });
      }

      await transaction.commit();
      return userInfo;
    } catch (error) {
      await transaction.rollback();
      console.error('Error updating user profile:', error);
      throw new BadRequestException('Failed to update user profile');
    }
  }

  async remove(id: number) {
    const user = await this.userModel.findByPk(id);
    if (user) {
      await user.destroy();
    }
  }

  async updateLastAccess(userId: string, data: string) {
    try {
      const user = await this.userModel.findByPk(userId);

      if (!user) {
        throw new UnauthorizedException({ message: 'User not found' });
      }

      user.lastAccess = data;
      await user.save();

      return user;
    } catch (error) {
      console.error('Error updating last access:', error);
      throw error;
    }
  }

  async updateUserPaymentPlan(
    userId: string,
    paymentPlanId: string,
    transaction?: Transaction,
  ): Promise<void> {
    const localTransaction =
      transaction || (await this.userModel.sequelize.transaction());

    try {
      console.log('[UsersService] Iniciando atualização do plano do usuário:', {
        userId,
        paymentPlanId,
      });

      // Find user first
      const user = await this.userModel.findByPk(userId, {
        transaction: localTransaction,
        lock: true,
      });

      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      console.log('[UsersService] Usuário encontrado:', {
        userId: user.id,
        currentPlan: user.paymentPlanId,
        newPlan: paymentPlanId,
      });

      // Force update directly in the database
      await this.userModel.update(
        { paymentPlanId },
        {
          where: { id: userId },
          transaction: localTransaction,
        },
      );

      // Update also the local instance
      user.paymentPlanId = paymentPlanId;
      await user.save({ transaction: localTransaction });

      // Check if the update was made
      const updatedUser = await this.userModel.findByPk(userId, {
        transaction: localTransaction,
      });

      console.log('[UsersService] Usuário após atualização:', {
        userId: updatedUser.id,
        paymentPlanId: updatedUser.paymentPlanId,
      });

      if (!transaction) {
        await localTransaction.commit();
      }

      console.log(
        `[UsersService] PaymentPlanId atualizado para usuário ${userId}: ${paymentPlanId}`,
      );
    } catch (error) {
      if (!transaction) {
        await localTransaction.rollback();
      }
      console.error('[UsersService] Erro ao atualizar paymentPlanId:', error);
      throw error;
    }
  }

  async findUserPlan(userId: string) {
    const user = await this.userModel.findByPk(userId, {
      include: [
        {
          model: PaymentPlans,
          as: 'paymentPlan',
          include: [
            {
              model: Permission,
              where: { status: 'active' },
              attributes: ['scopes'],
              required: false,
            },
          ],
        },
      ],
    });

    let paymentPlan;

    if (!user?.paymentPlan) {
      // If no plan, return the free plan with its permissions
      paymentPlan = await this.paymentPlanModel.findOne({
        where: {
          typePlan: 'free',
        },
        include: [
          {
            model: Permission,
            where: { status: 'active' },
            attributes: ['scopes'],
            required: false,
          },
        ],
      });
    } else {
      paymentPlan = user.paymentPlan;
    }

    // Find detailed scopes
    if (paymentPlan?.permissions?.[0]?.scopes) {
      const scopesList = await this.scopeModel.findAll({
        where: {
          tag: {
            [Op.in]: paymentPlan.permissions[0].scopes,
          },
        },
        attributes: ['id', 'tag', 'name'],
      });

      paymentPlan.setDataValue('scopesList', scopesList);
    }

    return paymentPlan;
  }

  async checkUser(value: string, type: UserIdentifierType) {
    try {
      // Remove a máscara do CPF, se o tipo for 'cpf'
      const formattedValue =
        type === UserIdentifierType.CPF ? value.replace(/\D/g, '') : value;

      const user = await this.userModel.findOne({
        where: { [type]: formattedValue },
        attributes: ['id', 'userAccountStatus', 'pendingStatusReason'],
      });

      return {
        exists: !!user,
        id: user?.id || null,
        userAccountStatus: user?.userAccountStatus || null,
        pendingStatusReason: user?.pendingStatusReason || null,
      };
    } catch (error) {
      throw new Error(`Failed to check user: ${error.message}`);
    }
  }

  async findById(id: string) {
    try {
      const user = await this.userModel.findByPk(id, {
        attributes: { exclude: ['password'] },
        include: [{ model: UserInfos }],
      });

      if (!user || ['active', 'inactive'].includes(user.userAccountStatus)) {
        throw new BadRequestException(`User with ID ${id} not found`);
      }

      const { cellphone } = user.userInfos || {};
      const {
        activationCode,
        email,
        name,
        cpf,
        termsAccepted,
        termsAcceptedAt,
      } = user;

      return {
        cellphone,
        activationCode,
        email,
        name,
        cpf,
        termsAccepted,
        termsAcceptedAt,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to find user by ID: ${error.message}`,
      );
    }
  }

  async updatePassword(
    id: string | number,
    updatePasswordDto: UpdateUserPasswordDto,
  ) {
    const user = await this.userModel.findByPk(String(id));
    if (!user) {
      throw new UnauthorizedException({ message: 'User not found' });
    }

    await user.update({ password: updatePasswordDto.password });
    return true;
  }

  async verifyUserPasswordOrThrow(
    password: string,
    token: string,
  ): Promise<boolean> {
    const userId = getUserId(token);
    const user = await this.userModel.findByPk(String(userId));

    if (!user) throw new BadRequestException('User not found');

    const isPasswordValid = await compare(password, user.password);
    if (!isPasswordValid) throw new BadRequestException('Invalid password');

    return true;
  }

  async updateProfileImage(token: string, file: Express.Multer.File) {
    const loggedUser = getUserId(token);
    const transaction = await this.userModel.sequelize.transaction();

    try {
      // Verificar se o usuário existe
      const user = await this.userModel.findByPk(loggedUser, {
        include: [UserInfos],
        transaction,
      });

      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Fazer upload do novo avatar
      const uploadedFile = await this.filesService.create(
        file,
        token,
        FileType.AVATAR,
      );

      // Atualizar ou criar UserInfos com a nova URL da imagem
      let userInfo = await this.userInfosModel.findOne({
        where: { userId: loggedUser },
        transaction,
      });

      if (!userInfo) {
        userInfo = await this.userInfosModel.create(
          {
            userId: loggedUser,
            profileImage: uploadedFile.fileUrl,
          },
          { transaction },
        );
      } else {
        await userInfo.update(
          {
            profileImage: uploadedFile.fileUrl,
          },
          { transaction },
        );
      }

      await transaction.commit();

      return {
        message: 'Profile image updated successfully',
        profileImage: uploadedFile.fileUrl,
      };
    } catch (error) {
      await transaction.rollback();
      throw new BadRequestException(
        error.message || 'Failed to update profile image',
      );
    }
  }
}
