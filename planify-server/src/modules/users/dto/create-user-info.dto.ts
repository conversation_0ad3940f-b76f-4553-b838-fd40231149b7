import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsDateString,
} from 'class-validator';

export class CreateUserInfoDto {
  @ApiProperty()
  @IsOptional()
  @IsDateString()
  birthDate?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  cellphone?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  cep?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  locationNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  locationComplement?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  weight?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  currentActivitiesWeek?: number;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  lastCheckup?: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  menstualCycle?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  canOutExercises?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  receiveMessages?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  authorizeAllChannels?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  authorizePartners?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  receiveNewsletter?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  receiveNotifications?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  profileImage?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  financialEducationLevel?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  investorProfile?: string;
}
