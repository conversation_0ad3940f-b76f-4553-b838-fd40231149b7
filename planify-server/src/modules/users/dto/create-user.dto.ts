import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  MinL<PERSON>th,
  IsOptional,
  IsBoolean,
} from 'class-validator';
export class CreateUserDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  @ApiProperty()
  @IsOptional()
  @IsString()
  userType?: 'user' | 'admin' | 'partner' | 'mentor' | 'affiliate' | 'custom';

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isPasswordSubmited?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isValidatedAccount?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  activationCode?: string;

  email: string;
  @ApiProperty()
  @IsString()
  @IsOptional()
  sharedCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  paymentPlanId?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isResetPassword?: boolean;

  @ApiProperty()
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  cellphone?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  pendingStatusReason?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  userAccountStatus?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  termsAccepted?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  termsAcceptedAt?: string;
}
