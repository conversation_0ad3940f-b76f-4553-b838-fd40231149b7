import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import { CreateNegotiationDto } from './dto/create-negotiation.dto';
import { UpdateNegotiationDto } from './dto/update-negotiation.dto';
import { Negotiation } from './entities/negotiation.entity';
import { NegotiationAffiliate } from './entities/negotiation-affiliate.entity';
import { NegotiationPaymentPlan } from './entities/negotiation-payment-plan.entity';
import { Affiliate } from '../affiliates/entities/affiliate.entity';
import { PaymentPlans } from '../payment-plans/entities/payment-plan.entity';
import { paginateResults } from 'src/utils/pagination';

@Injectable()
export class NegotiationsService {
  constructor(
    @InjectModel(Negotiation)
    private negotiationModel: typeof Negotiation,
    @InjectModel(NegotiationAffiliate)
    private negotiationAffiliateModel: typeof NegotiationAffiliate,
    @InjectModel(NegotiationPaymentPlan)
    private negotiationPaymentPlanModel: typeof NegotiationPaymentPlan,
    private sequelize: Sequelize,
  ) {}

  async create(createNegotiationDto: CreateNegotiationDto) {
    const t = await this.sequelize.transaction();
    try {
      const {
        affiliateIds,
        paymentPlanIds,
        planConfigurations,
        ...negotiationData
      } = createNegotiationDto;

      const negotiation = await this.negotiationModel.create(negotiationData, {
        transaction: t,
      });

      await this.associateAffiliates(negotiation.id, affiliateIds, t);

      if (negotiationData.allowDefault && paymentPlanIds?.length) {
        await this.associatePaymentPlans(negotiation.id, paymentPlanIds, t);
      } else if (!negotiationData.allowDefault && planConfigurations?.length) {
        await this.associatePlanConfigurations(
          negotiation.id,
          planConfigurations,
          t,
        );
      } else {
        await t.rollback();
        throw new BadRequestException(
          'É necessário fornecer planos de pagamento',
        );
      }

      await t.commit();
      return this.findOne(negotiation.id);
    } catch (error) {
      await t.rollback();
      throw error;
    }
  }

  async findAll(status?: string, page: number = 1, limit: number = 10) {
    const whereConditions = status ? { status } : {};

    return await paginateResults(
      { page, limit },
      {
        model: this.negotiationModel,
        whereConditions,
        includeOptions: [
          {
            model: Affiliate,
            through: { attributes: [] },
          },
          {
            model: PaymentPlans,
            through: { attributes: [] },
          },
        ],
        orderOptions: [['createdAt', 'DESC']],
      },
    );
  }

  async findOne(id: string) {
    const negotiation = await this.negotiationModel.findByPk(id, {
      include: [
        {
          model: Affiliate,
          through: { attributes: [] },
        },
        {
          model: PaymentPlans,
          through: {
            attributes: [
              'type',
              'value',
              'recurrencyType',
              'recurrencyPeriod',
              'recurrencyLimit',
            ],
          },
        },
      ],
    });

    if (!negotiation) {
      throw new NotFoundException(`Negociação com ID ${id} não encontrada`);
    }

    return negotiation;
  }

  async update(id: string, updateNegotiationDto: UpdateNegotiationDto) {
    const t = await this.sequelize.transaction();
    try {
      const negotiation = await this.negotiationModel.findByPk(id);

      if (!negotiation) {
        await t.rollback();
        throw new NotFoundException(`Negociação com ID ${id} não encontrada`);
      }

      const {
        affiliateIds,
        paymentPlanIds,
        planConfigurations,
        ...negotiationData
      } = updateNegotiationDto;

      // Atualizar dados da negociação
      await negotiation.update(negotiationData, { transaction: t });

      // Se forneceu novos afiliados, atualiza as associações
      if (affiliateIds) {
        // Remover associações existentes
        await this.negotiationAffiliateModel.destroy({
          where: { negotiationId: id },
          transaction: t,
        });

        // Criar novas associações
        await this.associateAffiliates(id, affiliateIds, t);
      }

      // Se está alterando o tipo de configuração (padrão ou individual)
      if (negotiationData.allowDefault !== undefined) {
        // Remover todas as associações de planos existentes
        await this.negotiationPaymentPlanModel.destroy({
          where: { negotiationId: id },
          transaction: t,
        });

        // Associar novos planos conforme o modo selecionado
        if (negotiationData.allowDefault && paymentPlanIds?.length) {
          await this.associatePaymentPlans(id, paymentPlanIds, t);
        } else if (
          !negotiationData.allowDefault &&
          planConfigurations?.length
        ) {
          await this.associatePlanConfigurations(id, planConfigurations, t);
        }
      } else if (negotiation.allowDefault && paymentPlanIds) {
        // Se continua usando o valor padrão, mas alterou os planos
        await this.negotiationPaymentPlanModel.destroy({
          where: { negotiationId: id },
          transaction: t,
        });
        await this.associatePaymentPlans(id, paymentPlanIds, t);
      } else if (!negotiation.allowDefault && planConfigurations) {
        // Se continua usando valores individuais, mas alterou as configurações
        await this.negotiationPaymentPlanModel.destroy({
          where: { negotiationId: id },
          transaction: t,
        });
        await this.associatePlanConfigurations(id, planConfigurations, t);
      }

      await t.commit();
      return this.findOne(id);
    } catch (error) {
      await t.rollback();
      throw error;
    }
  }

  async updateStatus(id: string, status: 'active' | 'inactive') {
    const negotiation = await this.negotiationModel.findByPk(id);

    if (!negotiation) {
      throw new NotFoundException(`Negociação com ID ${id} não encontrada`);
    }

    await negotiation.update({ status });
    return this.findOne(id);
  }

  async remove(id: string) {
    const negotiation = await this.negotiationModel.findByPk(id);

    if (!negotiation) {
      throw new NotFoundException(`Negociação com ID ${id} não encontrada`);
    }

    await negotiation.destroy();
    return { id };
  }

  // Métodos auxiliares
  private async associateAffiliates(
    negotiationId: string,
    affiliateIds: string[],
    transaction: Transaction,
  ) {
    const affiliateAssociations = affiliateIds.map((affiliateId) => ({
      negotiationId,
      affiliateId,
    }));

    await this.negotiationAffiliateModel.bulkCreate(affiliateAssociations, {
      transaction,
    });
  }

  private async associatePaymentPlans(
    negotiationId: string,
    paymentPlanIds: string[],
    transaction: Transaction,
  ) {
    const planAssociations = paymentPlanIds.map((paymentPlanId) => ({
      negotiationId,
      paymentPlanId,
    }));

    await this.negotiationPaymentPlanModel.bulkCreate(planAssociations, {
      transaction,
    });
  }

  private async associatePlanConfigurations(
    negotiationId: string,
    planConfigurations: any[],
    transaction: Transaction,
  ) {
    const planAssociations = planConfigurations.map((config) => ({
      negotiationId,
      paymentPlanId: config.paymentPlanId,
      type: config.type,
      value: config.value,
      recurrencyType: config.recurrencyType,
      recurrencyPeriod: config.recurrencyPeriod,
      recurrencyLimit: config.recurrencyLimit,
    }));

    await this.negotiationPaymentPlanModel.bulkCreate(planAssociations, {
      transaction,
    });
  }

  // Métodos para gerenciar afiliados e planos específicos de uma negociação
  async findNegotiationAffiliates(negotiationId: string) {
    const negotiation = await this.negotiationModel.findByPk(negotiationId, {
      include: [
        {
          model: Affiliate,
          through: { attributes: [] },
        },
      ],
    });

    if (!negotiation) {
      throw new NotFoundException(
        `Negociação com ID ${negotiationId} não encontrada`,
      );
    }

    return negotiation.affiliates;
  }

  async findNegotiationPaymentPlans(negotiationId: string) {
    const negotiation = await this.negotiationModel.findByPk(negotiationId, {
      include: [
        {
          model: PaymentPlans,
          through: {
            attributes: [
              'type',
              'value',
              'recurrencyType',
              'recurrencyPeriod',
              'recurrencyLimit',
            ],
          },
        },
      ],
    });

    if (!negotiation) {
      throw new NotFoundException(
        `Negociação com ID ${negotiationId} não encontrada`,
      );
    }

    return negotiation.paymentPlans;
  }

  async updateNegotiationAffiliates(
    negotiationId: string,
    affiliateIds: string[],
  ) {
    const t = await this.sequelize.transaction();
    try {
      const negotiation = await this.negotiationModel.findByPk(negotiationId);

      if (!negotiation) {
        await t.rollback();
        throw new NotFoundException(
          `Negociação com ID ${negotiationId} não encontrada`,
        );
      }

      // Remover associações existentes
      await this.negotiationAffiliateModel.destroy({
        where: { negotiationId },
        transaction: t,
      });

      // Criar novas associações
      await this.associateAffiliates(negotiationId, affiliateIds, t);

      await t.commit();
      return this.findNegotiationAffiliates(negotiationId);
    } catch (error) {
      await t.rollback();
      throw error;
    }
  }

  async updateNegotiationPaymentPlans(
    negotiationId: string,
    planConfigurations: any[],
  ) {
    const t = await this.sequelize.transaction();
    try {
      const negotiation = await this.negotiationModel.findByPk(negotiationId);

      if (!negotiation) {
        await t.rollback();
        throw new NotFoundException(
          `Negociação com ID ${negotiationId} não encontrada`,
        );
      }

      // Remover associações existentes
      await this.negotiationPaymentPlanModel.destroy({
        where: { negotiationId },
        transaction: t,
      });

      // Criar novas associações com configurações específicas
      await this.associatePlanConfigurations(
        negotiationId,
        planConfigurations,
        t,
      );

      await t.commit();
      return this.findNegotiationPaymentPlans(negotiationId);
    } catch (error) {
      await t.rollback();
      throw error;
    }
  }
}
