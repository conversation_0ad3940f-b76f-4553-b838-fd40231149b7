import { PartialType } from '@nestjs/swagger';
import { CreateNegotiationDto } from './create-negotiation.dto';
import { IsEnum, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateNegotiationDto extends PartialType(CreateNegotiationDto) {
  @ApiPropertyOptional({
    description: 'Status da negociação',
    enum: ['active', 'inactive'],
  })
  @IsEnum(['active', 'inactive'])
  @IsOptional()
  status?: 'active' | 'inactive';
}
