import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  IsNumber,
  IsUUID,
  IsOptional,
  IsArray,
  Min,
  IsBoolean,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { NegotiationPlanDto } from './negotiation-plan.dto';

export class CreateNegotiationDto {
  @ApiProperty({ description: 'Nome da negociação' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Aplicar a todos os planos ou definir valores individuais',
    default: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  allowDefault: boolean;

  @ApiPropertyOptional({
    description: 'Tipo da negociação',
    enum: ['percentage', 'fixed'],
  })
  @IsEnum(['percentage', 'fixed'])
  @IsOptional()
  @ValidateIf((o) => o.allowDefault === true)
  type?: 'percentage' | 'fixed';

  @ApiPropertyOptional({ description: 'Valor da negociação' })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @ValidateIf((o) => o.allowDefault === true)
  value?: number;

  @ApiPropertyOptional({
    description: 'Tipo de recorrência',
    enum: ['limited', 'unlimited'],
    default: 'limited',
  })
  @IsEnum(['limited', 'unlimited'])
  @IsOptional()
  @ValidateIf((o) => o.allowDefault === true)
  recurrencyType?: 'limited' | 'unlimited';

  @ApiPropertyOptional({
    description: 'Período de recorrência',
    enum: ['monthly', 'yearly', 'first_payment'],
    default: 'monthly',
  })
  @IsEnum(['monthly', 'yearly', 'first_payment'])
  @IsOptional()
  @ValidateIf((o) => o.allowDefault === true)
  recurrencyPeriod?: 'monthly' | 'yearly' | 'first_payment';

  @ApiPropertyOptional({ description: 'Limite de recorrência', default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  @ValidateIf((o) => o.allowDefault === true)
  recurrencyLimit?: number;

  @ApiPropertyOptional({
    description: 'Status da negociação',
    enum: ['active', 'inactive'],
    default: 'active',
  })
  @IsEnum(['active', 'inactive'])
  @IsOptional()
  status?: 'active' | 'inactive';

  @ApiProperty({ description: 'Lista de IDs dos afiliados', type: [String] })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  affiliateIds: string[];

  @ApiProperty({
    description:
      'Lista de IDs dos planos de pagamento ou configurações específicas por plano',
    type: [String],
  })
  @IsArray()
  @ValidateIf((o) => o.allowDefault === true)
  @IsUUID('4', { each: true })
  paymentPlanIds?: string[];

  @ApiPropertyOptional({
    description: 'Configurações específicas por plano',
    type: [NegotiationPlanDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NegotiationPlanDto)
  @ValidateIf((o) => o.allowDefault === false)
  @IsOptional()
  planConfigurations?: NegotiationPlanDto[];
}
