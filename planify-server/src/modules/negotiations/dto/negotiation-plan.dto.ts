import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  IsNumber,
  IsUUID,
  IsOptional,
  Min,
} from 'class-validator';

export class NegotiationPlanDto {
  @ApiProperty({ description: 'ID do plano de pagamento' })
  @IsUUID('4')
  @IsNotEmpty()
  paymentPlanId: string;

  @ApiProperty({
    description: 'Tipo da negociação',
    enum: ['percentage', 'fixed'],
  })
  @IsEnum(['percentage', 'fixed'])
  @IsNotEmpty()
  type: 'percentage' | 'fixed';

  @ApiProperty({ description: 'Valor da negociação' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  value: number;

  @ApiPropertyOptional({
    description: 'Tipo de recorrência',
    enum: ['limited', 'unlimited'],
    default: 'limited',
  })
  @IsEnum(['limited', 'unlimited'])
  @IsOptional()
  recurrencyType?: 'limited' | 'unlimited';

  @ApiPropertyOptional({
    description: 'Período de recorrência',
    enum: ['monthly', 'yearly', 'first_payment'],
    default: 'monthly',
  })
  @IsEnum(['monthly', 'yearly', 'first_payment'])
  @IsOptional()
  recurrencyPeriod?: 'monthly' | 'yearly' | 'first_payment';

  @ApiPropertyOptional({ description: 'Limite de recorrência', default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  recurrencyLimit?: number;
}
