import { <PERSON>du<PERSON> } from '@nestjs/common';
import { NegotiationsService } from './negotiations.service';
import { NegotiationsController } from './negotiations.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { Negotiation } from './entities/negotiation.entity';
import { NegotiationAffiliate } from './entities/negotiation-affiliate.entity';
import { NegotiationPaymentPlan } from './entities/negotiation-payment-plan.entity';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Negotiation,
      NegotiationAffiliate,
      NegotiationPaymentPlan,
    ]),
    UsersModule,
    AuthModule,
  ],
  controllers: [NegotiationsController],
  providers: [NegotiationsService],
  exports: [NegotiationsService],
})
export class NegotiationsModule {}
