import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { NegotiationsService } from './negotiations.service';
import { CreateNegotiationDto } from './dto/create-negotiation.dto';
import { UpdateNegotiationDto } from './dto/update-negotiation.dto';
import { NegotiationPlanDto } from './dto/negotiation-plan.dto';
import { AuthGuard } from '../auth/auth.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';

@ApiTags('Negociações')
@Controller('negotiations')
export class NegotiationsController {
  constructor(private readonly negotiationsService: NegotiationsService) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Criar uma nova negociação' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Negociação criada com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dados inválidos',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  create(@Body() createNegotiationDto: CreateNegotiationDto) {
    return this.negotiationsService.create(createNegotiationDto);
  }

  @Get()
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Listar todas as negociações' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lista de negociações',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status da negociação',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página',
    type: Number,
  })
  findAll(
    @Query('status') status?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.negotiationsService.findAll(status, page, limit);
  }

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obter uma negociação específica' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Detalhes da negociação',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Negociação não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  findOne(@Param('id') id: string) {
    return this.negotiationsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Atualizar uma negociação' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Negociação atualizada com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Negociação não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dados inválidos',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  update(
    @Param('id') id: string,
    @Body() updateNegotiationDto: UpdateNegotiationDto,
  ) {
    return this.negotiationsService.update(id, updateNegotiationDto);
  }

  @Delete(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover uma negociação' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Negociação removida com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Negociação não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  remove(@Param('id') id: string) {
    return this.negotiationsService.remove(id);
  }

  // Endpoints para gerenciar afiliados e planos específicos
  @Get(':id/affiliates')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Listar afiliados de uma negociação' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lista de afiliados da negociação',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Negociação não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  findNegotiationAffiliates(@Param('id') id: string) {
    return this.negotiationsService.findNegotiationAffiliates(id);
  }

  @Get(':id/payment-plans')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Listar planos de pagamento de uma negociação' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lista de planos da negociação',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Negociação não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  findNegotiationPaymentPlans(@Param('id') id: string) {
    return this.negotiationsService.findNegotiationPaymentPlans(id);
  }

  @Patch(':id/affiliates')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Atualizar afiliados de uma negociação' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Afiliados atualizados com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Negociação não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  updateNegotiationAffiliates(
    @Param('id') id: string,
    @Body() affiliateIds: string[],
  ) {
    return this.negotiationsService.updateNegotiationAffiliates(
      id,
      affiliateIds,
    );
  }

  @Patch(':id/payment-plans')
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Atualizar planos de pagamento de uma negociação' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Planos atualizados com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Negociação não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Sem permissões suficientes',
  })
  updateNegotiationPaymentPlans(
    @Param('id') id: string,
    @Body() planConfigurations: NegotiationPlanDto[],
  ) {
    return this.negotiationsService.updateNegotiationPaymentPlans(
      id,
      planConfigurations,
    );
  }
}
