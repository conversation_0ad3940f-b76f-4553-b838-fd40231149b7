import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import {
  Negotiation,
  NegotiationType,
  RecurrencyPeriod,
  RecurrencyType,
} from './negotiation.entity';
import { PaymentPlans } from 'src/modules/payment-plans/entities/payment-plan.entity';

@Table({
  tableName: 'NegotiationPaymentPlans',
})
export class NegotiationPaymentPlan extends Model<NegotiationPaymentPlan> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Negotiation)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  negotiationId: string;

  @ForeignKey(() => PaymentPlans)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  paymentPlanId: string;

  // Campos para valores individuais por plano
  @Column({
    type: DataType.ENUM('percentage', 'fixed'),
    allowNull: true,
  })
  type: NegotiationType;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  value: number;

  @Column({
    type: DataType.ENUM('limited', 'unlimited'),
    allowNull: true,
  })
  recurrencyType: RecurrencyType;

  @Column({
    type: DataType.ENUM('monthly', 'yearly', 'first_payment'),
    allowNull: true,
  })
  recurrencyPeriod: RecurrencyPeriod;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  recurrencyLimit: number;

  @BelongsTo(() => Negotiation)
  negotiation: Negotiation;

  @BelongsTo(() => PaymentPlans)
  paymentPlan: PaymentPlans;
}
