import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Negotiation } from './negotiation.entity';
import { Affiliate } from 'src/modules/affiliates/entities/affiliate.entity';

@Table({
  tableName: 'NegotiationAffiliates',
})
export class NegotiationAffiliate extends Model<NegotiationAffiliate> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Negotiation)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  negotiationId: string;

  @ForeignKey(() => Affiliate)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  affiliateId: string;

  @BelongsTo(() => Negotiation)
  negotiation: Negotiation;

  @BelongsTo(() => Affiliate)
  affiliate: Affiliate;
}
