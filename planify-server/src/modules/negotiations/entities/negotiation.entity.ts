import {
  Column,
  Model,
  Table,
  DataType,
  BelongsToMany,
} from 'sequelize-typescript';
import { Affiliate } from 'src/modules/affiliates/entities/affiliate.entity';
import { PaymentPlans } from 'src/modules/payment-plans/entities/payment-plan.entity';
import { NegotiationAffiliate } from './negotiation-affiliate.entity';
import { NegotiationPaymentPlan } from './negotiation-payment-plan.entity';

export type NegotiationType = 'percentage' | 'fixed';
export type RecurrencyType = 'limited' | 'unlimited';
export type RecurrencyPeriod = 'monthly' | 'yearly' | 'first_payment';
export type NegotiationStatus = 'active' | 'inactive';

@Table({
  tableName: 'Negotiations',
})
export class Negotiation extends Model<Negotiation> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.ENUM('percentage', 'fixed'),
    allowNull: true,
  })
  type: NegotiationType;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  value: number;

  @Column({
    type: DataType.ENUM('limited', 'unlimited'),
    allowNull: true,
    defaultValue: 'limited',
  })
  recurrencyType: RecurrencyType;

  @Column({
    type: DataType.ENUM('monthly', 'yearly', 'first_payment'),
    allowNull: true,
    defaultValue: 'monthly',
  })
  recurrencyPeriod: RecurrencyPeriod;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  recurrencyLimit: number;

  @Column({
    type: DataType.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active',
  })
  status: NegotiationStatus;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  allowDefault: boolean;

  @BelongsToMany(() => Affiliate, () => NegotiationAffiliate)
  affiliates: Affiliate[];

  @BelongsToMany(() => PaymentPlans, () => NegotiationPaymentPlan)
  paymentPlans: PaymentPlans[];
}
