import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { WeightHistoriesService } from './weight-histories.service';
import { CreateWeightHistoryDto } from './dto/create-weight-history.dto';
import { UpdateWeightHistoryDto } from './dto/update-weight-history.dto';
import { AuthGuard } from '../auth/auth.guard';

@ApiTags('weight-histories')
@Controller('weight-histories')
export class WeightHistoriesController {
  constructor(private readonly weightHistoriesService: WeightHistoriesService) { }

  @Post()
  @ApiOperation({ summary: 'Create weight history record' })
  @ApiResponse({ status: 201, description: 'Record created successfully.' })
  @UseGuards(AuthGuard)
  create(@Body() createWeightHistoryDto: CreateWeightHistoryDto) {
    return this.weightHistoriesService.create(createWeightHistoryDto);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get all weight history records for a user' })
  @ApiResponse({ status: 200, description: 'Returns all weight records.' })
  @UseGuards(AuthGuard)
  findAll(@Param('userId') userId: string) {
    return this.weightHistoriesService.findAll(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get specific weight history record' })
  @ApiResponse({ status: 200, description: 'Returns the weight record.' })
  @UseGuards(AuthGuard)
  findOne(@Param('id') id: string) {
    return this.weightHistoriesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update weight history record' })
  @ApiResponse({ status: 200, description: 'Record updated successfully.' })
  @UseGuards(AuthGuard)
  update(
    @Param('id') id: string,
    @Body() updateWeightHistoryDto: UpdateWeightHistoryDto,
  ) {
    return this.weightHistoriesService.update(id, updateWeightHistoryDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete weight history record' })
  @ApiResponse({ status: 200, description: 'Record deleted successfully.' })
  @UseGuards(AuthGuard)
  remove(@Param('id') id: string) {
    return this.weightHistoriesService.remove(id);
  }
} 