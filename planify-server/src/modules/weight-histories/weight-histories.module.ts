import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { WeightHistory } from './entities/weight-history.entity';
import { WeightHistoriesService } from './weight-histories.service';
import { WeightHistoriesController } from './weight-histories.controller';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    SequelizeModule.forFeature([WeightHistory]),
    forwardRef(() => UsersModule),
    forwardRef(() => AuthModule)
  ],
  providers: [WeightHistoriesService],
  controllers: [WeightHistoriesController],
  exports: [WeightHistoriesService, SequelizeModule]
})
export class WeightHistoriesModule { } 