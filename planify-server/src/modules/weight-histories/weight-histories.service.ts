import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Transaction } from 'sequelize';
import { WeightHistory } from './entities/weight-history.entity';
import { CreateWeightHistoryDto } from './dto/create-weight-history.dto';
import { UpdateWeightHistoryDto } from './dto/update-weight-history.dto';

@Injectable()
export class WeightHistoriesService {
  private readonly logger = new Logger(WeightHistoriesService.name);

  constructor(
    @InjectModel(WeightHistory)
    private weightHistoryModel: typeof WeightHistory,
  ) { }

  private validateAndParseWeight(weight: string | number): number {
    let numericWeight: number;

    if (typeof weight === 'string') {
      const cleanWeight = weight.replace(/[^\d.]/g, '');
      numericWeight = parseFloat(cleanWeight);
    } else {
      numericWeight = weight;
    }

    return Math.round(numericWeight * 100) / 100;
  }

  async create(createWeightHistoryDto: CreateWeightHistoryDto, transaction?: Transaction) {
    const validatedWeight = this.validateAndParseWeight(createWeightHistoryDto.recordedWeight);

    return await this.weightHistoryModel.create(
      {
        ...createWeightHistoryDto,
        recordedWeight: validatedWeight,
      },
      { transaction },
    );
  }

  async findAll(userId: string) {
    return await this.weightHistoryModel.findAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: string) {
    const weightHistory = await this.weightHistoryModel.findByPk(id);
    if (!weightHistory) {
      throw new NotFoundException('Weight history record not found');
    }
    return weightHistory;
  }

  async update(id: string, updateWeightHistoryDto: UpdateWeightHistoryDto, transaction?: Transaction) {
    const weightHistory = await this.findOne(id);

    const updateData: Partial<WeightHistory> = {};

    if (updateWeightHistoryDto.recordedWeight !== undefined) {
      updateData.recordedWeight = this.validateAndParseWeight(updateWeightHistoryDto.recordedWeight);
    }

    if (updateWeightHistoryDto.userId !== undefined) {
      updateData.userId = updateWeightHistoryDto.userId;
    }

    return await weightHistory.update(updateData, { transaction });
  }

  async remove(id: string, transaction?: Transaction) {
    const weightHistory = await this.findOne(id);
    await weightHistory.destroy({ transaction });
    return { id };
  }

  async recordWeight(userId: string, weight: number, transaction?: Transaction) {
    this.logger.debug(`Recording weight for user ${userId}: ${weight}`);

    if (typeof weight !== 'number') {
      throw new BadRequestException(`Invalid weight type: ${typeof weight}`);
    }

    if (weight <= 0 || weight >= 1000) {
      throw new BadRequestException('Weight must be between 0 and 1000 kg');
    }

    const lastRecord = await this.weightHistoryModel.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']],
    });

    if (!lastRecord || lastRecord.recordedWeight !== weight) {
      try {
        return await this.weightHistoryModel.create(
          {
            userId,
            recordedWeight: weight,
          },
          { transaction }
        );
      } catch (error) {
        this.logger.error(`Error saving weight: ${error.message}`);
        throw new BadRequestException({
          message: 'Error saving weight record',
          error: error.message,
          weightValue: weight
        });
      }
    }
    return lastRecord;
  }
} 