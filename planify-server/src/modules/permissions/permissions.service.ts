import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { InjectModel } from '@nestjs/sequelize';
import { Permission } from './entities/permission.entity';
import { PaymentPlans } from '../payment-plans/entities/payment-plan.entity';
import { Scope } from '../scopes/entities/scope.entity';
import { UsersService } from '../users/users.service';

@Injectable()
export class PermissionsService {
  constructor(
    @InjectModel(Permission)
    private permissionModel: typeof Permission,
    @InjectModel(PaymentPlans)
    private paymentPlansModel: typeof PaymentPlans,
    @InjectModel(Scope)
    private scopeModel: typeof Scope,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
  ) {}

  async create(createPermissionDto: CreatePermissionDto) {
    const paymentPlan = await this.paymentPlansModel.findByPk(
      createPermissionDto.paymentPlanId,
    );

    if (!paymentPlan) {
      throw new NotFoundException({
        message: 'Payment plan not found',
      });
    }

    if (createPermissionDto.scopes?.length) {
      const scopes = await this.scopeModel.findAll({
        where: {
          id: createPermissionDto.scopes,
        },
      });

      if (scopes.length !== createPermissionDto.scopes.length) {
        throw new UnprocessableEntityException({
          message: 'One or more scopes were not found',
        });
      }
    }

    const permission = await this.permissionModel.create({
      ...createPermissionDto,
    } as Permission);

    return permission;
  }

  async findScopes(permissions: Permission[]) {
    const permissionsWithScopeTags = await Promise.all(
      permissions.map(async (permission) => {
        if (permission.scopes?.length) {
          const scopes = await this.scopeModel.findAll({
            where: {
              id: permission.scopes,
            },
            attributes: ['tag'],
          });
          return {
            ...permission.toJSON(),
            scopes: scopes.map((scope) => scope.tag),
          };
        }
        return permission.toJSON();
      }),
    );

    return permissionsWithScopeTags;
  }

  async findAll() {
    const permissions = await this.permissionModel.findAll({
      include: [
        {
          model: PaymentPlans,
          attributes: ['name', 'price'],
        },
      ],
    });

    const permissionsWithScopeTags = await this.findScopes(permissions);

    return permissionsWithScopeTags;
  }

  async findOne(id: string) {
    const permission = await this.permissionModel.findByPk(id, {
      include: [
        {
          model: PaymentPlans,
          attributes: ['name', 'price'],
        },
      ],
    });

    if (!permission) {
      throw new NotFoundException({
        message: 'Permission not found',
      });
    }

    return permission;
  }

  async findOneWithScopeTags(id: string) {
    const permission = await this.findOne(id);

    if (permission.scopes?.length) {
      const scopes = await this.scopeModel.findAll({
        where: {
          id: permission.scopes,
        },
        attributes: ['tag'],
      });
      return {
        ...permission.toJSON(),
        scopes: scopes.map((scope) => scope.tag),
      };
    }

    return permission.toJSON();
  }

  async update(id: string, updatePermissionDto: CreatePermissionDto) {
    const permission = await this.findOne(id);

    if (updatePermissionDto.paymentPlanId) {
      const paymentPlan = await this.paymentPlansModel.findByPk(
        updatePermissionDto.paymentPlanId,
      );

      if (!paymentPlan) {
        throw new NotFoundException({
          message: 'Payment plan not found',
        });
      }
    }

    if (updatePermissionDto.scopes !== undefined) {
      if (
        Array.isArray(updatePermissionDto.scopes) &&
        updatePermissionDto.scopes.length === 0
      ) {
        permission.scopes = [];
      } else if (
        Array.isArray(updatePermissionDto.scopes) &&
        updatePermissionDto.scopes.length > 0
      ) {
        const scopes = await this.scopeModel.findAll({
          where: {
            id: updatePermissionDto.scopes,
          },
        });

        if (scopes.length !== updatePermissionDto.scopes.length) {
          throw new UnprocessableEntityException(
            'One or more scopes were not found',
          );
        }

        permission.scopes = [...updatePermissionDto.scopes];
      }
    }

    if (updatePermissionDto.status) {
      permission.status = updatePermissionDto.status;
    }

    if (updatePermissionDto.description !== undefined) {
      permission.description = updatePermissionDto.description;
    }

    if (updatePermissionDto.paymentPlanId) {
      permission.paymentPlanId = updatePermissionDto.paymentPlanId;
    }

    await permission.save();

    return this.findOneWithScopeTags(id);
  }

  async findByPaymentPlanId(paymentPlanId: string) {
    const permission = await this.permissionModel.findOne({
      where: { paymentPlanId },
      include: [
        {
          model: PaymentPlans,
          attributes: ['name', 'price'],
        },
      ],
    });

    if (permission.scopes?.length) {
      const scopes = await this.scopeModel.findAll({
        where: {
          id: permission.scopes,
        },
        attributes: ['tag'],
      });
      return {
        ...permission.toJSON(),
        scopes: scopes.map((scope) => scope.tag),
      };
    }

    return permission.toJSON();
  }

  async remove(id: string) {
    const permission = await this.findOne(id);
    await permission.destroy();
    return { message: 'Permission removed successfully' };
  }
}
