import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { TPermissionStatus } from 'src/data/@types/TPermissionStatus';
import { PaymentPlans } from 'src/modules/payment-plans/entities/payment-plan.entity';

@Table({
  tableName: 'Permissions',
})
export class Permission extends Model<Permission> {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
  })
  id: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
    get() {
      const scopes = this.getDataValue('scopes');
      // Garantir que sempre retorne um array, mesmo que o valor seja null
      return scopes || [];
    },
    set(value: string[]) {
      // Garantir que o valor sendo definido é um array
      this.setDataValue('scopes', Array.isArray(value) ? value : []);
    },
  })
  scopes: string[];

  @Column({
    type: DataType.ENUM('active', 'inactive', 'pending'),
    allowNull: false,
    defaultValue: 'active',
  })
  status: TPermissionStatus;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string;

  @ForeignKey(() => PaymentPlans)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  paymentPlanId: string;

  @BelongsTo(() => PaymentPlans)
  paymentPlan: PaymentPlans;
}
