import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { TPermissionStatus } from 'src/data/@types/TPermissionStatus';

export class CreatePermissionDto {
  @ApiProperty({
    description: 'Array of scope IDs',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  scopes?: string[];

  @ApiProperty({
    description: 'Permission status',
    required: false,
    enum: ['active', 'inactive', 'pending'],
    default: 'active',
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'pending'])
  status?: TPermissionStatus;

  @ApiProperty({
    description: 'Permission description',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Payment plan ID',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID(4)
  paymentPlanId: string;
}
