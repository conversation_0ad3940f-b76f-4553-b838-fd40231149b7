import { Column, DataType, Model, Table, HasMany } from 'sequelize-typescript';
import {
  TPaymentPlans,
  TPaymentPlansRecurrency,
  TObjectivesPeriod,
  TRadarPeriod,
} from 'src/data/@types/paymentPlan';
import { Permission } from 'src/modules/permissions/entities/permission.entity';

@Table({
  tableName: 'PaymentPlans',
})
export class PaymentPlans extends Model<PaymentPlans> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  gatewayPlanId: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string | null;

  @Column({
    type: DataType.ENUM('active', 'inactive', 'draft'),
    allowNull: true,
    defaultValue: 'draft',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  displayOrder: number;

  // AI Objectives
  @Column({
    type: DataType.ENUM('6 months', 'year', 'unlimited', 'custom'),
    allowNull: true,
    defaultValue: 'year',
  })
  aiObjectivesPeriod: TObjectivesPeriod;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 50,
  })
  aiObjectivesLimit: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 50,
  })
  aiPlanTasksLimits: number;

  // Manual Objectives
  @Column({
    type: DataType.ENUM('6 months', 'year', 'unlimited', 'custom'),
    allowNull: true,
    defaultValue: 'year',
  })
  manualObjectivesPeriod: TObjectivesPeriod;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 100,
  })
  manualObjectivesLimit: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 50,
  })
  manualPlanTasksLimits: number;

  // Radar
  @Column({
    type: DataType.ENUM('3 months', '6 months', 'year', 'custom'),
    allowNull: true,
    defaultValue: 'year',
  })
  radarLimitPeriod: TRadarPeriod;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  radarLimit: number;

  // Tasks
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  allowTasksControl: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 15,
  })
  pendingTasksLimit: number;

  // Notes
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  allowNotes: boolean;

  // Emotional Analysis
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  allowEmotionalAnalysis: boolean;

  @Column({
    type: DataType.ENUM('6 months', 'year', 'unlimited', 'custom'),
    allowNull: true,
    defaultValue: 'year',
  })
  emotionalAnalysisPeriod: TObjectivesPeriod;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  emotionalAnalysisLimit: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: 'free',
  })
  price: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  promotionalPrice: string;

  @Column({
    type: DataType.ENUM(
      'free',
      'start',
      'premium',
      'ultimate',
      'ultimate_mentor',
    ),
    allowNull: false,
    defaultValue: 'free',
  })
  typePlan: TPaymentPlans;

  @Column({
    type: DataType.ENUM('month', 'year'),
    allowNull: false,
    defaultValue: 'month',
  })
  recurrencyType: TPaymentPlansRecurrency;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
  })
  recurrencyPeriod: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: '',
  })
  assignDescription: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
  })
  recurrencyInstallments: number;

  // Networking
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  allowNetworking: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  allowMentoredUserAnalytics: boolean;

  @HasMany(() => Permission)
  permissions: Permission[];
}
