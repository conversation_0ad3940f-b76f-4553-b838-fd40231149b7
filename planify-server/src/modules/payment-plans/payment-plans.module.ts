import { forwardRef, Module } from '@nestjs/common';
import { PaymentPlansService } from './payment-plans.service';
import { PaymentPlansController } from './payment-plans.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { PaymentPlans } from './entities/payment-plan.entity';
import { PermissionsModule } from '../permissions/permissions.module';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from '../users/users.module';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    SequelizeModule.forFeature([PaymentPlans, User]),
    forwardRef(() => UsersModule),
    PermissionsModule,
    ConfigModule,
  ],
  controllers: [PaymentPlansController],
  providers: [PaymentPlansService],
  exports: [PaymentPlansService, SequelizeModule],
})
export class PaymentPlansModule {}
