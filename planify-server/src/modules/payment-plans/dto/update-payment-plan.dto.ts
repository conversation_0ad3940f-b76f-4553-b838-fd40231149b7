import { PartialType } from '@nestjs/swagger';
import { CreatePaymentPlanDto } from './create-payment-plan.dto';
import { IsArray, IsOptional, IsString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdatePaymentPlanDto extends PartialType(CreatePaymentPlanDto) {
  @ApiProperty({
    description: 'Array of scope IDs',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  scopes?: string[];

  @ApiProperty({
    description: 'Permission description',
    required: false,
  })
  @IsOptional()
  @IsString()
  permissionDescription?: string;
}
