import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  TPaymentPlans,
  TPaymentPlansRecurrency,
  TObjectivesPeriod,
  TRadarPeriod,
} from 'src/data/@types/paymentPlan';

export class CreatePaymentPlanDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  gatewayPlanId?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description?: string | null;

  @ApiProperty()
  @IsEnum(['active', 'inactive', 'draft'])
  @IsOptional()
  status?: string;

  // AI Objectives
  @ApiProperty()
  @IsEnum(['6 months', 'year', 'unlimited', 'custom'])
  @IsOptional()
  aiObjectivesPeriod?: TObjectivesPeriod;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  aiObjectivesLimit?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  aiPlanTasksLimits?: number;

  // Manual Objectives
  @ApiProperty()
  @IsEnum(['6 months', 'year', 'unlimited', 'custom'])
  @IsOptional()
  manualObjectivesPeriod?: TObjectivesPeriod;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  manualObjectivesLimit?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  manualPlanTasksLimits?: number;

  // Radar
  @ApiProperty()
  @IsEnum(['3 months', '6 months', 'year', 'custom'])
  @IsOptional()
  radarLimitPeriod?: TRadarPeriod;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  radarLimit?: number;

  // Tasks
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  allowTasksControl?: boolean;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  pendingTasksLimit?: number;

  // Notes
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  allowNotes?: boolean;

  // Emotional Analysis
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  allowEmotionalAnalysis?: boolean;

  @ApiProperty()
  @IsEnum(['6 months', 'year', 'unlimited', 'custom'])
  @IsOptional()
  emotionalAnalysisPeriod?: TObjectivesPeriod;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  emotionalAnalysisLimit?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  price?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  promotionalPrice?: string;

  @ApiProperty()
  @IsEnum([
    'free',
    'start',
    'standard',
    'premium',
    'ultimate',
    'ultimate_mentor',
  ])
  @IsNotEmpty()
  typePlan: TPaymentPlans;

  @ApiProperty()
  @IsEnum(['month', 'year'])
  @IsNotEmpty()
  recurrencyType: TPaymentPlansRecurrency;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  recurrencyPeriod: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  recurrencyInstallments: number;

  // Networking
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  allowNetworking?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  allowMentoredUserAnalytics?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  assignDescription?: string;
}
