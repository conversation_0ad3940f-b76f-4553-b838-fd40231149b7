import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsArray,
  IsEnum,
  IsOptional,
  Min,
  IsBoolean,
} from 'class-validator';
import {
  TPaymentPlans,
  TPaymentPlansRecurrency,
  TObjectivesPeriod,
  TRadarPeriod,
} from 'src/data/@types/paymentPlan';

export class CreatePaymentPlanWithPermissionsDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  displayOrder?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsEnum(['active', 'inactive', 'draft'])
  @IsOptional()
  status?: string;

  @ApiProperty()
  @IsString()
  price: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  promotionalPrice?: string;

  @ApiProperty()
  @IsEnum([
    'free',
    'start',
    'standard',
    'premium',
    'ultimate',
    'ultimate_mentor',
  ])
  typePlan: TPaymentPlans;

  @ApiProperty()
  @IsEnum(['month', 'year'])
  recurrencyType: TPaymentPlansRecurrency;

  @ApiProperty()
  @IsNumber()
  @Min(1)
  recurrencyPeriod: number;

  @ApiProperty()
  @IsNumber()
  @Min(1)
  recurrencyInstallments: number;

  @ApiProperty()
  @IsOptional()
  @IsEnum(['6 months', 'year', 'unlimited', 'custom'])
  aiObjectivesPeriod?: TObjectivesPeriod;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  aiObjectivesLimit?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  aiPlanTasksLimits?: number;

  @ApiProperty()
  @IsOptional()
  @IsEnum(['6 months', 'year', 'unlimited', 'custom'])
  manualObjectivesPeriod?: TObjectivesPeriod;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  manualObjectivesLimit?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  manualPlanTasksLimits?: number;

  @ApiProperty()
  @IsOptional()
  @IsEnum(['3 months', '6 months', 'year', 'custom'])
  radarLimitPeriod?: TRadarPeriod;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  radarLimit?: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  allowTasksControl?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  pendingTasksLimit?: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  allowNotes?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  allowEmotionalAnalysis?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsEnum(['6 months', 'year', 'unlimited', 'custom'])
  emotionalAnalysisPeriod?: TObjectivesPeriod;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  emotionalAnalysisLimit?: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  allowNetworking?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  allowMentoredUserAnalytics?: boolean;

  @ApiProperty()
  @IsString()
  permissionDescription: string;

  @ApiProperty()
  @IsArray()
  scopes: string[];

  @ApiProperty()
  @IsOptional()
  @IsArray()
  installments?: number[];

  @ApiProperty()
  @IsOptional()
  @IsArray()
  paymentMethods?: string[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  statementDescriptor?: string;
}
