import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { PaymentPlansService } from './payment-plans.service';
import { CreatePaymentPlanDto } from './dto/create-payment-plan.dto';
import { UpdatePaymentPlanDto } from './dto/update-payment-plan.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';
import { CreatePaymentPlanWithPermissionsDto } from './dto/create-payment-plan-with-permissions.dto';
import { PaymentPlans } from './entities/payment-plan.entity';

@ApiTags('Payment Plans')
@Controller('payment-plans')
export class PaymentPlansController {
  constructor(private readonly paymentPlansService: PaymentPlansService) {}

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Post()
  create(@Body() paymentPlanDto: CreatePaymentPlanDto, @Req() token: string) {
    return this.paymentPlansService.create(paymentPlanDto, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Post('setup')
  createWithPermissions(
    @Body() createDto: CreatePaymentPlanWithPermissionsDto,
    @Req() token: string,
  ) {
    return this.paymentPlansService.createWithPermissions(createDto, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Patch('setup/:id')
  updateWithPermissions(
    @Param('id') id: string,
    @Body() updateDto: CreatePaymentPlanWithPermissionsDto,
    @Req() token: string,
  ) {
    return this.paymentPlansService.updateWithPermissions(id, updateDto, token);
  }

  @Get()
  findAll(@Query('period') period?: 'month' | 'semiannual' | 'year') {
    return this.paymentPlansService.findAll(period);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.paymentPlansService.findOne(id);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() paymentPlanDto: UpdatePaymentPlanDto,
    @Req() token: string,
  ) {
    return this.paymentPlansService.update(id, paymentPlanDto, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Patch(':id/with-permissions')
  updatePlanWithPermissions(
    @Param('id') id: string,
    @Body() paymentPlanDto: UpdatePaymentPlanDto,
    @Req() token: string,
  ) {
    return this.paymentPlansService.update(id, paymentPlanDto, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Delete(':id')
  remove(@Param('id') id: string, @Req() token: string) {
    return this.paymentPlansService.remove(id, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Patch('order/:id')
  updateDisplayOrder(
    @Param('id') id: string,
    @Body('order') order: number,
    @Req() token: string,
  ) {
    return this.paymentPlansService.updateDisplayOrder(id, order, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Post('sync-gateway-plans')
  recreateGatewayPlans(@Body() plans: PaymentPlans[], @Req() token: string) {
    return this.paymentPlansService.recreateGatewayPlans(plans, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Post('/reply/:planId')
  replicatePlan(@Param('planId') planId: string, @Req() token: string) {
    return this.paymentPlansService.replicatePlan(planId, token);
  }
}
