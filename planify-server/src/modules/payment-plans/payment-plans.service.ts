import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreatePaymentPlanDto } from './dto/create-payment-plan.dto';
import { UpdatePaymentPlanDto } from './dto/update-payment-plan.dto';
import { InjectModel } from '@nestjs/sequelize';
import { getUserId } from 'src/utils';
import { User } from '../users/entities/user.entity';
import { PaymentPlans } from './entities/payment-plan.entity';
import { CreatePaymentPlanWithPermissionsDto } from './dto/create-payment-plan-with-permissions.dto';
import { PermissionsService } from '../permissions/permissions.service';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { Op } from 'sequelize';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { Scope } from '../scopes/entities/scope.entity';

@Injectable()
export class PaymentPlansService {
  private logger = createModuleLogger(PaymentPlansService.name);
  constructor(
    @InjectModel(PaymentPlans)
    private paymentPlansModel: typeof PaymentPlans,

    @InjectModel(User)
    private userModel: typeof User,
    private permissionsService: PermissionsService,
    private configService: ConfigService,
    @InjectModel(Scope)
    private scopeModel: typeof Scope,
  ) {}

  async create(paymentPlanDto: CreatePaymentPlanDto, token: string) {
    await this.validateUser(token);

    const paymentPlan = await this.paymentPlansModel.create({
      name: paymentPlanDto.name,
      description: paymentPlanDto.description || null,
      status: paymentPlanDto.status || 'active',
      price: paymentPlanDto.price,
      promotionalPrice: paymentPlanDto.promotionalPrice || null,
      typePlan: paymentPlanDto.typePlan,
      recurrencyType: paymentPlanDto.recurrencyType,
      recurrencyPeriod: paymentPlanDto.recurrencyPeriod,
      recurrencyInstallments: paymentPlanDto.recurrencyInstallments,
      aiObjectivesPeriod: paymentPlanDto.aiObjectivesPeriod || 'year',
      aiObjectivesLimit: paymentPlanDto.aiObjectivesLimit ?? 50,
      manualObjectivesPeriod: paymentPlanDto.manualObjectivesPeriod || 'year',
      manualObjectivesLimit: paymentPlanDto.manualObjectivesLimit ?? 100,
      radarLimitPeriod: paymentPlanDto.radarLimitPeriod || 'year',
      radarLimit: paymentPlanDto.radarLimit ?? 1,
      allowTasksControl: paymentPlanDto.allowTasksControl ?? true,
      pendingTasksLimit: paymentPlanDto.pendingTasksLimit ?? 15,
      allowNotes: paymentPlanDto.allowNotes ?? true,
      allowEmotionalAnalysis: paymentPlanDto.allowEmotionalAnalysis ?? false,
      emotionalAnalysisPeriod: paymentPlanDto.emotionalAnalysisPeriod || 'year',
      emotionalAnalysisLimit: paymentPlanDto.emotionalAnalysisLimit ?? 1,
      allowNetworking: paymentPlanDto.allowNetworking ?? false,
      allowMentoredUserAnalytics:
        paymentPlanDto.allowMentoredUserAnalytics ?? false,
      assignDescription: paymentPlanDto?.assignDescription || '',
    });

    return paymentPlan;
  }

  async findAll(period?: 'month' | 'semiannual' | 'year') {
    const baseWhere = {
      status: 'active',
    };

    if (!period) {
      return this.paymentPlansModel.findAll({
        order: [['displayOrder', 'ASC']],
      });
    }

    const periodConfig = {
      month: { period: 1, type: 'month' },
      semiannual: { period: 6, type: 'month' },
      year: { period: 1, type: 'year' },
    };

    const { period: recurrencyPeriod, type: recurrencyType } =
      periodConfig[period];

    const startPlan = await this.paymentPlansModel.findOne({
      where: {
        ...baseWhere,
        name: 'START',
      },
    });

    const freePlan = await this.paymentPlansModel.findOne({
      where: {
        ...baseWhere,
        [Op.or]: [{ price: '0' }, { typePlan: 'free' }],
        recurrencyType: 'year',
        recurrencyPeriod: 1,
        name: {
          [Op.notIn]: ['VIEW', 'START'],
        },
      },
      order: [['displayOrder', 'ASC']],
    });

    const paidPlans = await this.paymentPlansModel.findAll({
      where: {
        ...baseWhere,
        recurrencyType,
        recurrencyPeriod,
        name: {
          [Op.notIn]: ['VIEW', 'START'],
        },
        [Op.and]: [
          { price: { [Op.ne]: '0' } },
          { typePlan: { [Op.ne]: 'free' } },
        ],
      },
      order: [['displayOrder', 'ASC']],
    });

    const allPlans = [];

    if (startPlan) allPlans.push(startPlan);
    if (freePlan) allPlans.push(freePlan);
    allPlans.push(...paidPlans);

    return allPlans;
  }

  async findOne(planId: string) {
    const plan = await this.paymentPlansModel.findOne({
      where: { id: planId },
    });

    return plan;
  }

  async update(
    id: string,
    paymentPlanDto: UpdatePaymentPlanDto,
    token: string,
  ) {
    await this.validateUser(token);
    const paymentPlan = await this.paymentPlansModel.findByPk(id);

    if (!paymentPlan) {
      throw new NotFoundException('PaymentPlan not found');
    }

    const isDraftToActive =
      paymentPlan.status === 'draft' &&
      paymentPlanDto.status === 'active' &&
      !paymentPlan.gatewayPlanId;

    await paymentPlan.update(paymentPlanDto);

    if (
      isDraftToActive &&
      paymentPlanDto.price !== 'free' &&
      paymentPlanDto.typePlan !== 'free' &&
      paymentPlanDto.price !== '0'
    ) {
      try {
        const priceInCents = Math.round(Number(paymentPlanDto.price) * 100);
        const interval = 'month';
        const intervalCount =
          paymentPlanDto.recurrencyType === 'year'
            ? paymentPlanDto.recurrencyPeriod * 12
            : paymentPlanDto.recurrencyPeriod;

        const gatewayPayload = {
          interval,
          interval_count: intervalCount,
          pricing_scheme: {
            scheme_type: 'Unit',
            price: priceInCents,
          },
          quantity: 1,
          name: paymentPlanDto.name || paymentPlan.name,
          currency: 'BRL',
          billing_type: 'prepaid',
          minimum_price: priceInCents,
          installments: paymentPlanDto.recurrencyInstallments
            ? [paymentPlanDto.recurrencyInstallments]
            : [1],
          payment_methods: ['credit_card'],
          items: [],
          statement_descriptor: (paymentPlanDto.name || paymentPlan.name)
            .substring(0, 13)
            .toUpperCase()
            .replace(/[^A-Z0-9]/g, ''),
        };

        this.logger.info(
          'Plano alterado de draft para active. Enviando para o gateway:',
          JSON.stringify(gatewayPayload, null, 2),
        );

        const gatewayResponse = await axios.post(
          `${this.configService.get('PAYMENT_GATEWAY_URL')}/api/recurrence/plans`,
          gatewayPayload,
          {
            headers: {
              'x-secret': this.configService.get('PAYMENT_GATEWAY_SECRET'),
            },
          },
        );

        if (!gatewayResponse.data?.data?.id) {
          throw new Error(
            'ID do plano não retornado pelo gateway de pagamento',
          );
        }

        await paymentPlan.update({
          gatewayPlanId: gatewayResponse.data.data.id,
        });

        this.logger.info(
          'Plano registrado no gateway com sucesso:',
          gatewayResponse.data.data.id,
        );
      } catch (error) {
        this.logger.error(
          'Erro ao registrar plano no gateway:',
          error.response?.data || error.message,
        );

        await paymentPlan.update({ status: 'draft' });

        throw new Error(
          `Falha ao criar plano no gateway de pagamento: ${error.response?.data?.message || error.message}`,
        );
      }
    }

    if (paymentPlanDto.scopes !== undefined) {
      this.logger.info('Atualizando permissões para o plano:', id);
      this.logger.info('Escopos recebidos:', paymentPlanDto.scopes);

      const permissions = await this.permissionsService.findAll();
      const permission = permissions.find((p) => p.paymentPlanId === id);

      if (permission) {
        const updateResult = await this.permissionsService.update(
          permission.id,
          {
            paymentPlanId: id,
            scopes: paymentPlanDto.scopes,
            description:
              paymentPlanDto.permissionDescription || permission.description,
          },
        );

        this.logger.info('Permissões atualizadas:', updateResult);
      } else if (paymentPlanDto.permissionDescription) {
        const newPermission = await this.permissionsService.create({
          description: paymentPlanDto.permissionDescription,
          paymentPlanId: id,
          scopes: paymentPlanDto.scopes || [],
        });

        this.logger.info('Novas permissões criadas:', newPermission);
      }
    }

    return await paymentPlan.save();
  }

  async remove(id: string, token: string) {
    await this.validateUser(token);

    const paymentPlan = await this.paymentPlansModel.findOne({
      where: { id: id },
    });

    if (!paymentPlan) {
      throw new NotFoundException('PaymentPlan not found');
    }

    if (paymentPlan.gatewayPlanId) {
      try {
        await axios.delete(
          `${this.configService.get('PAYMENT_GATEWAY_URL')}/api/recurrence/plans/${paymentPlan.gatewayPlanId}`,
          {
            headers: {
              'x-secret': this.configService.get('PAYMENT_GATEWAY_SECRET'),
            },
          },
        );
      } catch (error) {
        this.logger.error(
          'Erro ao remover plano no gateway:',
          error.response?.data || error.message,
        );
      }
    }

    try {
      const permissions = await this.permissionsService.findAll();
      const planPermission = permissions.find((p) => p.paymentPlanId === id);

      if (planPermission) {
        await this.permissionsService.remove(planPermission.id);
      }

      await paymentPlan.destroy();

      return { message: 'Plano removido com sucesso' };
    } catch (error) {
      this.logger.error('Erro ao remover permissões:', error);
      throw error;
    }
  }

  async validateUser(token) {
    const loggedUser = getUserId(token);
    const user = await this.userModel.findOne({
      where: { id: loggedUser },
    });

    if (user?.userType !== 'admin') {
      throw new UnauthorizedException({
        message: 'You are not authorized to perform this action',
      });
    }
  }

  private async getNextDisplayOrder(): Promise<number> {
    const maxOrder = (await this.paymentPlansModel.max(
      'displayOrder',
    )) as number;
    return (maxOrder || 0) + 1;
  }

  private async updateDisplayOrders(
    currentOrder: number,
    newOrder: number,
    excludeId?: string,
  ) {
    if (currentOrder === newOrder) return;

    if (currentOrder < newOrder) {
      await this.paymentPlansModel.decrement('displayOrder', {
        where: {
          displayOrder: { [Op.gt]: currentOrder, [Op.lte]: newOrder },
          ...(excludeId && { id: { [Op.ne]: excludeId } }),
        },
      });
    } else {
      await this.paymentPlansModel.increment('displayOrder', {
        where: {
          displayOrder: { [Op.gte]: newOrder, [Op.lt]: currentOrder },
          ...(excludeId && { id: { [Op.ne]: excludeId } }),
        },
      });
    }
  }

  async updateDisplayOrder(id: string, newOrder: number, token: string) {
    await this.validateUser(token);

    const plan = await this.paymentPlansModel.findByPk(id);
    if (!plan) {
      throw new NotFoundException('Plano não encontrado');
    }

    const currentOrder = plan.displayOrder;
    await this.updateDisplayOrders(currentOrder, newOrder);

    await plan.update({ displayOrder: newOrder });
    return plan;
  }

  async createWithPermissions(
    createDto: CreatePaymentPlanWithPermissionsDto,
    token: string,
  ) {
    await this.validateUser(token);

    const displayOrder = await this.getNextDisplayOrder();

    const paymentPlan = await this.paymentPlansModel.create({
      name: createDto.name,
      description: createDto.description || null,
      status: 'active',
      displayOrder,
      price: createDto.price,
      promotionalPrice: createDto.promotionalPrice || null,
      typePlan: createDto.typePlan,
      recurrencyType: createDto.recurrencyType,
      recurrencyPeriod: createDto.recurrencyPeriod,
      recurrencyInstallments: createDto.recurrencyInstallments,
      aiObjectivesPeriod: createDto.aiObjectivesPeriod || 'year',
      aiObjectivesLimit: createDto.aiObjectivesLimit ?? 50,
      manualObjectivesPeriod: createDto.manualObjectivesPeriod || 'year',
      manualObjectivesLimit: createDto.manualObjectivesLimit ?? 100,
      radarLimitPeriod: createDto.radarLimitPeriod || 'year',
      radarLimit: createDto.radarLimit ?? 1,
      allowTasksControl: createDto.allowTasksControl ?? true,
      pendingTasksLimit: createDto.pendingTasksLimit ?? 15,
      allowNotes: createDto.allowNotes ?? true,
      allowEmotionalAnalysis: createDto.allowEmotionalAnalysis ?? false,
      emotionalAnalysisPeriod: createDto.emotionalAnalysisPeriod || 'year',
      emotionalAnalysisLimit: createDto.emotionalAnalysisLimit ?? 1,
      allowNetworking: createDto.allowNetworking ?? false,
      allowMentoredUserAnalytics: createDto.allowMentoredUserAnalytics ?? false,
    });

    await this.permissionsService.create({
      description: createDto.permissionDescription,
      paymentPlanId: paymentPlan.id,
      scopes: createDto.scopes,
    });

    if (
      createDto.price === 'free' ||
      createDto.typePlan === 'free' ||
      createDto.price === '0'
    ) {
      return paymentPlan;
    }

    const priceInCents = Math.round(Number(createDto.price) * 100);

    const interval = 'month';
    const intervalCount =
      createDto.recurrencyType === 'year'
        ? createDto.recurrencyPeriod * 12
        : createDto.recurrencyPeriod;

    const gatewayPayload = {
      interval,
      interval_count: intervalCount,
      pricing_scheme: {
        scheme_type: 'Unit',
        price: priceInCents,
      },
      quantity: 1,
      name: createDto.name,
      currency: 'BRL',
      billing_type: 'prepaid',
      minimum_price: priceInCents,
      installments: createDto.installments || [1],
      payment_methods: createDto.paymentMethods || ['credit_card'],
      items: [],
      statement_descriptor: (createDto.statementDescriptor || createDto.name)
        .substring(0, 13)
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, ''),
    };

    try {
      this.logger.info(
        'Payload enviado para o gateway:',
        JSON.stringify(gatewayPayload, null, 2),
      );

      const gatewayResponse = await axios.post(
        `${this.configService.get('PAYMENT_GATEWAY_URL')}/api/recurrence/plans`,
        gatewayPayload,
        {
          headers: {
            'x-secret': this.configService.get('PAYMENT_GATEWAY_SECRET'),
          },
        },
      );

      if (!gatewayResponse.data?.data?.id) {
        throw new Error('ID do plano não retornado pelo gateway de pagamento');
      }

      await paymentPlan.update({
        gatewayPlanId: gatewayResponse.data.data.id,
      });

      return {
        ...paymentPlan.toJSON(),
        gatewayPlanId: gatewayResponse.data.data.id,
      };
    } catch (error) {
      this.logger.error(
        'Erro ao registrar plano no gateway:',
        error.response?.data || error.message,
      );
      if (paymentPlan?.id) {
        await this.remove(paymentPlan.id, token);
      }
      throw new Error(
        `Falha ao criar plano no gateway de pagamento: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async updateWithPermissions(
    id: string,
    updateDto: CreatePaymentPlanWithPermissionsDto,
    token: string,
  ) {
    await this.validateUser(token);

    const paymentPlan = await this.paymentPlansModel.findByPk(id);
    if (!paymentPlan) {
      throw new NotFoundException('Plano de pagamento não encontrado');
    }

    if (
      updateDto.displayOrder &&
      updateDto.displayOrder !== paymentPlan.displayOrder
    ) {
      await this.updateDisplayOrders(
        paymentPlan.displayOrder,
        updateDto.displayOrder,
        paymentPlan.id,
      );
    }

    await paymentPlan.update({
      name: updateDto.name,
      description: updateDto.description || null,
      displayOrder: updateDto.displayOrder || paymentPlan.displayOrder,
      price: updateDto.price,
      promotionalPrice: updateDto.promotionalPrice || null,
      typePlan: updateDto.typePlan,
      recurrencyType: updateDto.recurrencyType,
      recurrencyPeriod: updateDto.recurrencyPeriod,
      recurrencyInstallments: updateDto.recurrencyInstallments,
      aiObjectivesPeriod: updateDto.aiObjectivesPeriod || 'year',
      aiObjectivesLimit: updateDto.aiObjectivesLimit ?? 50,
      manualObjectivesPeriod: updateDto.manualObjectivesPeriod || 'year',
      manualObjectivesLimit: updateDto.manualObjectivesLimit ?? 100,
      radarLimitPeriod: updateDto.radarLimitPeriod || 'year',
      radarLimit: updateDto.radarLimit ?? 1,
      allowTasksControl: updateDto.allowTasksControl ?? true,
      pendingTasksLimit: updateDto.pendingTasksLimit ?? 15,
      allowNotes: updateDto.allowNotes ?? true,
      allowEmotionalAnalysis: updateDto.allowEmotionalAnalysis ?? false,
      emotionalAnalysisPeriod: updateDto.emotionalAnalysisPeriod || 'year',
      emotionalAnalysisLimit: updateDto.emotionalAnalysisLimit ?? 1,
      allowNetworking: updateDto.allowNetworking ?? false,
      allowMentoredUserAnalytics: updateDto.allowMentoredUserAnalytics ?? false,
    });

    const permissions = await this.permissionsService.findAll();
    const permission = permissions.find(
      (p) => p.paymentPlanId === paymentPlan.id,
    );

    if (permission) {
      this.logger.info('Atualizando permissões para o plano:', paymentPlan.id);
      this.logger.info('ID da permissão:', permission.id);
      this.logger.info('Escopos recebidos:', updateDto.scopes);

      const updateResult = await this.permissionsService.update(permission.id, {
        description: updateDto.permissionDescription,
        paymentPlanId: paymentPlan.id,
        scopes: updateDto.scopes,
      });

      this.logger.info('Permissões atualizadas:', updateResult);
    } else {
      this.logger.info(
        'Criando novas permissões para o plano:',
        paymentPlan.id,
      );
      this.logger.info('Escopos recebidos:', updateDto.scopes);

      const newPermission = await this.permissionsService.create({
        description: updateDto.permissionDescription,
        paymentPlanId: paymentPlan.id,
        scopes: updateDto.scopes,
      });

      this.logger.info('Novas permissões criadas:', newPermission);
    }

    if (updateDto.price === 'free' || updateDto.typePlan === 'free') {
      return paymentPlan;
    }

    if (!paymentPlan.gatewayPlanId) {
      return this.createWithPermissions(updateDto, token);
    }

    const previousPlan = {
      price: paymentPlan.previous('price'),
      name: paymentPlan.previous('name'),
      recurrencyType: paymentPlan.previous('recurrencyType'),
      recurrencyPeriod: paymentPlan.previous('recurrencyPeriod'),
      installments: paymentPlan.previous('recurrencyInstallments'),
    };

    const currentPlan = {
      price: updateDto.price,
      name: updateDto.name,
      recurrencyType: updateDto.recurrencyType,
      recurrencyPeriod: updateDto.recurrencyPeriod,
      installments: updateDto.installments,
    };

    if (
      !currentPlan.price ||
      !currentPlan.name ||
      !currentPlan.recurrencyType ||
      !currentPlan.recurrencyPeriod
    ) {
      this.logger.warn(
        'Dados incompletos para atualização no gateway:',
        currentPlan,
      );
      return paymentPlan;
    }

    const hasPaymentGatewayChanges =
      previousPlan.price !== currentPlan.price ||
      previousPlan.name !== currentPlan.name ||
      previousPlan.recurrencyType !== currentPlan.recurrencyType ||
      previousPlan.recurrencyPeriod !== currentPlan.recurrencyPeriod ||
      JSON.stringify(previousPlan.installments) !==
        JSON.stringify(currentPlan.installments);

    if (!hasPaymentGatewayChanges) {
      this.logger.info(
        'Nenhuma alteração relevante para o gateway de pagamento',
      );
      return paymentPlan;
    }

    const priceInCents = Math.round(Number(updateDto.price) * 100);
    const interval = 'month';
    const intervalCount =
      updateDto.recurrencyType === 'year'
        ? updateDto.recurrencyPeriod * 12
        : updateDto.recurrencyPeriod;

    const gatewayPayload = {
      interval,
      interval_count: intervalCount,
      pricing_scheme: {
        scheme_type: 'Unit',
        price: priceInCents,
      },
      quantity: 1,
      name: updateDto.name,
      currency: 'BRL',
      billing_type: 'prepaid',
      minimum_price: priceInCents,
      installments: updateDto.installments || [1],
      payment_methods: updateDto.paymentMethods || ['credit_card'],
      items: [],
      statement_descriptor: (updateDto.statementDescriptor || updateDto.name)
        .substring(0, 13)
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, ''),
    };

    try {
      this.logger.info(
        'Payload enviado para o gateway:',
        JSON.stringify(gatewayPayload, null, 2),
      );

      const gatewayResponse = await axios.patch(
        `${this.configService.get('PAYMENT_GATEWAY_URL')}/api/recurrence/plans/${paymentPlan.gatewayPlanId}`,
        gatewayPayload,
        {
          headers: {
            'x-secret': this.configService.get('PAYMENT_GATEWAY_SECRET'),
          },
          timeout: 10000, // 10 segundos de timeout
          validateStatus: (status) => status < 500, // Considera erro apenas status >= 500
        },
      );

      if (gatewayResponse.status >= 400) {
        this.logger.error(
          'Erro na resposta do gateway:',
          JSON.stringify(gatewayResponse.data, null, 2),
        );
        throw new Error(
          `Gateway retornou erro ${gatewayResponse.status}: ${gatewayResponse.data?.message || 'Erro desconhecido'}`,
        );
      }

      if (!gatewayResponse.data?.data?.id) {
        this.logger.error(
          'ID do plano não retornado pelo gateway de pagamento',
        );
        throw new Error('ID do plano não retornado pelo gateway de pagamento');
      }

      this.logger.info(
        'Resposta do gateway:',
        JSON.stringify(gatewayResponse.data, null, 2),
      );

      return {
        ...paymentPlan.toJSON(),
        gatewayPlanId: gatewayResponse.data.data.id,
      };
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        this.logger.error('Timeout na conexão com o gateway');
        throw new Error(
          'Timeout na conexão com o gateway de pagamento. Tente novamente mais tarde.',
        );
      }

      if (error.response?.status >= 500) {
        this.logger.error(
          'Erro interno do gateway:',
          error.response?.data || error.message,
        );
        throw new Error(
          'Gateway de pagamento indisponível. Tente novamente mais tarde.',
        );
      }

      this.logger.error(
        'Erro ao atualizar plano no gateway:',
        error.response?.data || error.message,
      );

      if (error.response?.status === 502) {
        this.logger.error(
          'Gateway de pagamento temporariamente indisponível. Tente novamente em alguns minutos.',
        );
        throw new Error(
          'Gateway de pagamento temporariamente indisponível. Tente novamente em alguns minutos.',
        );
      }

      if (error.response?.status === 400) {
        this.logger.error(
          'Erro ao atualizar plano no gateway:',
          error.response?.data || error.message,
        );
      }

      throw new Error(
        `Falha ao atualizar plano no gateway de pagamento: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async recreateGatewayPlans(plans: PaymentPlans[], token: string) {
    await this.validateUser(token);

    const results = [];
    const errors = [];

    for (const plan of plans) {
      try {
        if (
          plan.price === 'free' ||
          plan.typePlan === 'free' ||
          plan.price === '0'
        ) {
          results.push({
            id: plan.id,
            status: 'skipped',
            message: 'Free plan, no gateway integration needed',
          });
          continue;
        }

        const priceInCents = Math.round(
          Number(plan.promotionalPrice || plan.price) * 100,
        );
        const interval = 'month';
        const intervalCount =
          plan.recurrencyType === 'year'
            ? plan.recurrencyPeriod * 12
            : plan.recurrencyPeriod;

        const gatewayPayload = {
          interval,
          interval_count: intervalCount,
          pricing_scheme: {
            scheme_type: 'Unit',
            price: priceInCents,
          },
          quantity: 1,
          name: plan.name,
          currency: 'BRL',
          billing_type: 'prepaid',
          minimum_price: priceInCents,
          installments: [plan.recurrencyInstallments],
          payment_methods: ['credit_card'],
          items: [],
          statement_descriptor: plan.name
            .substring(0, 13)
            .toUpperCase()
            .replace(/[^A-Z0-9]/g, ''),
        };

        const gatewayResponse = await axios.post(
          `${this.configService.get('PAYMENT_GATEWAY_URL')}/api/recurrence/plans`,
          gatewayPayload,
          {
            headers: {
              'x-secret': this.configService.get('PAYMENT_GATEWAY_SECRET'),
            },
          },
        );

        if (!gatewayResponse.data?.data?.id) {
          throw new Error(
            'ID do plano não retornado pelo gateway de pagamento',
          );
        }

        await this.paymentPlansModel.update(
          { gatewayPlanId: gatewayResponse.data.data.id },
          { where: { id: plan.id } },
        );

        results.push({
          id: plan.id,
          status: 'success',
          oldGatewayPlanId: plan.gatewayPlanId,
          newGatewayPlanId: gatewayResponse.data.data.id,
        });
      } catch (error) {
        this.logger.error(
          `Erro ao recriar plano ${plan.id} no gateway:`,
          error.response?.data || error.message,
        );

        errors.push({
          id: plan.id,
          status: 'error',
          error: error.response?.data?.message || error.message,
        });
      }
    }

    return {
      success: results,
      errors: errors,
    };
  }

  async checkGatewayPlans(userId: string) {
    try {
      const plans = await this.paymentPlansModel.findAll();
      const freePlan = plans.find((plan) => plan.typePlan === 'start');

      if (!freePlan) {
        throw new BadRequestException('Plano gratuito não encontrado.');
      }

      const paymentGatewayUrl = this.configService.get('PAYMENT_GATEWAY_URL');
      const paymentGatewaySecret = this.configService.get(
        'PAYMENT_GATEWAY_SECRET',
      );

      const { data: paymentResponse } = await axios.post(
        `${paymentGatewayUrl}/checkout`,
        {
          userId,
          paymentPlanId: freePlan.id,
        },
        {
          headers: {
            'x-secret': paymentGatewaySecret,
          },
        },
      );

      return paymentResponse;
    } catch (error) {
      this.logger.error(
        'Erro ao verificar planos no gateway de pagamento:',
        error,
      );
      throw new BadRequestException('Falha ao processar o pagamento.');
    }
  }

  async replicatePlan(id: string, token: string) {
    await this.validateUser(token);
    const paymentPlan = await this.findOne(id);

    if (!paymentPlan) {
      throw new NotFoundException('Plano não encontrado.');
    }

    const nextOrder = await this.getNextDisplayOrder();

    const planData = paymentPlan.toJSON();

    delete planData.id;
    delete planData.createdAt;
    delete planData.updatedAt;
    delete planData.gatewayPlanId;

    const payload = {
      ...planData,
      name: `${paymentPlan.name} - copy`,
      status: 'draft',
      displayOrder: nextOrder,
    };

    const newPlan = await this.paymentPlansModel.create(payload);

    let permissionsSuccess = false;

    try {
      const permissions = await this.permissionsService.findAll();
      this.logger.info(
        `Encontradas ${permissions.length} permissões no sistema`,
      );

      const planPermission = permissions.find((p) => p.paymentPlanId === id);

      if (planPermission) {
        this.logger.info(
          `Permissão encontrada para o plano ${id}: ${JSON.stringify(planPermission)}`,
        );

        if (planPermission.scopes && planPermission.scopes.length > 0) {
          this.logger.info(
            `Tentando copiar ${planPermission.scopes.length} scopes para o novo plano: ${JSON.stringify(planPermission.scopes)}`,
          );

          try {
            const newPermission = await this.permissionsService.create({
              description: planPermission.description,
              paymentPlanId: newPlan.id,
              scopes: planPermission.scopes,
              status: 'active',
            });

            this.logger.info(
              `Permissões copiadas com sucesso: ${JSON.stringify(newPermission)}`,
            );
            permissionsSuccess = true;
          } catch (scopeError) {
            this.logger.warn(
              `Erro ao copiar scopes com IDs originais: ${scopeError.message}`,
            );

            try {
              const scopeTags = planPermission.scopes;
              if (
                Array.isArray(scopeTags) &&
                scopeTags.some(
                  (tag) => typeof tag === 'string' && tag.includes('.'),
                )
              ) {
                this.logger.info(
                  `Tentando buscar scopes pelas tags: ${JSON.stringify(scopeTags)}`,
                );

                const scopesResult = await this.scopeModel.findAll({
                  where: {
                    tag: scopeTags,
                  },
                });

                if (scopesResult && scopesResult.length > 0) {
                  const scopeIds = scopesResult.map((s) => s.id);
                  this.logger.info(
                    `Encontrados ${scopeIds.length} scopes válidos por tag`,
                  );

                  await this.permissionsService.create({
                    description: planPermission.description,
                    paymentPlanId: newPlan.id,
                    scopes: scopeIds,
                    status: 'active',
                  });

                  this.logger.info('Permissões criadas com base nas tags');
                  permissionsSuccess = true;
                } else {
                  throw new Error('Nenhum scope encontrado pelas tags');
                }
              } else {
                throw new Error('Formato de scopes não reconhecível como tags');
              }
            } catch (tagError) {
              this.logger.warn(
                `Erro ao buscar scopes por tag: ${tagError.message}`,
              );

              // Se tudo falhar, criamos permissão sem scopes
              try {
                await this.permissionsService.create({
                  description: planPermission.description,
                  paymentPlanId: newPlan.id,
                  scopes: [],
                  status: 'active',
                });
                this.logger.info('Permissão básica criada sem scopes');
                permissionsSuccess = true;
              } catch (basicError) {
                this.logger.error(
                  `Erro ao criar permissão básica: ${basicError.message}`,
                );
              }
            }
          }
        } else {
          this.logger.info(
            'Plano original não possui scopes, criando permissão básica',
          );

          try {
            await this.permissionsService.create({
              description: planPermission.description || 'Permissões básicas',
              paymentPlanId: newPlan.id,
              scopes: [],
              status: 'active',
            });
            this.logger.info('Permissão básica criada');
            permissionsSuccess = true;
          } catch (noScopeError) {
            this.logger.error(
              `Erro ao criar permissão sem scopes: ${noScopeError.message}`,
            );
          }
        }
      } else {
        this.logger.warn(
          `Nenhuma permissão encontrada para o plano original ${id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Erro geral ao copiar permissões do plano: ${error.message}`,
      );
    }

    return {
      ...newPlan.toJSON(),
      permissionsStatus: permissionsSuccess ? 'success' : 'failed',
    };
  }
}
