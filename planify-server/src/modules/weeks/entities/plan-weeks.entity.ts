import {
  <PERSON><PERSON>sTo,
  Column,
  <PERSON>Type,
  Foreign<PERSON>ey,
  HasMany,
  Model,
  Table,
} from 'sequelize-typescript';
import { PlanActivity } from 'src/modules/planActivities/entities/plan-activity.entity';
import { UserPlan } from 'src/modules/user-plans/entities/user-plan.entity';

@Table({
  tableName: 'PlanActionWeeks',
})
export class PlanWeek extends Model<PlanWeek> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => UserPlan)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  planId: string;

  @BelongsTo(() => UserPlan)
  plan: UserPlan;

  @HasMany(() => PlanActivity, {
    onDelete: 'CASCADE',
    hooks: true,
  })
  activities: PlanActivity[];

  @Column({
    type: DataType.INTEGER,
    defaultValue: null,
  })
  weekNumber: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  startDate: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  endDate: string;
}
