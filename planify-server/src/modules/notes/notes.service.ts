/* eslint-disable prettier/prettier */
import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateNoteDto } from './dto/create-note.dto';
import { Note } from './entities/note.entity';
import { User } from '../users/entities/user.entity';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { getUserId } from 'src/utils';

@Injectable()
export class NotesService {
  constructor(
    @InjectModel(Note)
    private notesModel: typeof Note,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async create(noteDto: CreateNoteDto) {
    const user = await this.userModel.findByPk(noteDto?.userId);

    if (!user) {
      throw new Error('User not found');
    }

    await this.notesModel.increment('position', {
      by: 1,
      where: { userId: noteDto.userId },
    });

    // Criar a nova anotação com a posição 1
    const note = await this.notesModel.create({
      ...noteDto,
      category: noteDto.category.toString(),
      position: 1,
    });

    return note;
  }

  async findByUser(userId: string) {
    const user = await this.userModel.findByPk(userId);

    if (!user) {
      throw new Error('User not found');
    }

    const notesList = await this.notesModel.findAll({
      where: { userId: userId, filed: false },
      order: [['position', 'ASC']],
    });

    return notesList;
  }

  async remove(id: string) {
    const note = await this.notesModel.findOne({
      where: { id: id },
    });

    if (!note) {
      throw new Error('Note not found');
    }

    await note.destroy();
  }

  async update(id: string, noteDto: CreateNoteDto) {
    const note = await this.notesModel.findOne({
      where: { id: id },
    });

    if (!note) {
      throw new Error('Note not found');
    }

    const noteUpdate = await note.update(noteDto);

    return noteUpdate;
  }

  async filed(id: string) {
    const note = await this.notesModel.findOne({
      where: { id: id },
    });

    if (!note) {
      throw new Error('Note not found');
    }

    const noteUpdate = await note.update({ filed: true });

    return noteUpdate;
  }

  async moveNote(noteId: string, newPosition: number, token: string) {
    const loggedUserId = getUserId(token);

    if (!loggedUserId) {
      throw new NotFoundException('Invalid user');
    }

    const user = await this.userModel.findByPk(loggedUserId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const note = await this.notesModel.findOne({
      where: { id: noteId, userId: user.id },
    });

    if (!note) {
      throw new Error('Note not found');
    }

    const currentPosition = note.position;

    // Atualizar as posições das outras notas
    if (newPosition < currentPosition) {
      // Movendo para cima
      await this.notesModel.increment('position', {
        by: 1,
        where: {
          userId: user.id,
          position: {
            [Op.gte]: newPosition,
            [Op.lt]: currentPosition,
          },
        },
      });
    } else if (newPosition > currentPosition) {
      // Movendo para baixo
      await this.notesModel.decrement('position', {
        by: 1,
        where: {
          userId: user.id,
          position: {
            [Op.lte]: newPosition,
            [Op.gt]: currentPosition,
          },
        },
      });
    }

    // Atualizar a posição da nota movida
    note.position = newPosition;
    await note.save();

    return note;
  }
}
