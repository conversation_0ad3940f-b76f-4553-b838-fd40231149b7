/* eslint-disable prettier/prettier */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
} from '@nestjs/common';
import { NotesService } from './notes.service';
import { CreateNoteDto } from './dto/create-note.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';

@ApiTags('User notes')
@Controller('notes')
export class NotesController {
  constructor(private readonly notesService: NotesService) {}

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createNoteDto: CreateNoteDto) {
    return this.notesService.create(createNoteDto);
  }

  @UseGuards(AuthGuard)
  @Get(':userId')
  findOne(@Param('userId') userId: string) {
    return this.notesService.findByUser(userId);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.notesService.remove(id);
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() createNoteDto: CreateNoteDto) {
    return this.notesService.update(id, createNoteDto);
  }

  @UseGuards(AuthGuard)
  @Patch('/filed/:id')
  filed(@Param('id') id: string) {
    return this.notesService.filed(id);
  }

  @UseGuards(AuthGuard)
  @Patch('/move/:noteId')
  async moveNote(
    @Req() token: string,
    @Param('noteId') noteId: string,
    @Body('newPosition') newPosition: number,
  ) {
    return this.notesService.moveNote(noteId, newPosition, token);
  }
}
