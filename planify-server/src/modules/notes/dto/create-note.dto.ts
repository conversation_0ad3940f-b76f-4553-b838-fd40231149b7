import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';

export enum ETCategory {
  REMEMBER = 'remember',
  WARNING = 'warning',
  IMPORTANT = 'important',
  SKETCH = 'sketch',
}

export class CreateNoteDto {
  @ApiProperty()
  @IsString()
  userId: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty({ enum: ETCategory, enumName: 'ETCategory' })
  @IsEnum(ETCategory)
  category: ETCategory;

  @ApiProperty()
  @IsString()
  descriptionNote: string;

  @ApiProperty()
  @IsString()
  cardColor: string;
}
