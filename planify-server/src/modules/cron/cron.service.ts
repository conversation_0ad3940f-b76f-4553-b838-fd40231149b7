import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PaymentsService } from '../payments/payments.service';
import { UserPlansService } from '../user-plans/user-plans.service';
import { AchievementRecordsService } from '../achievementRecords/achievementRecords.service';
import { RecomendationsService } from '../recommendations/recommendations.service';
import { createModuleLogger } from 'src/utils/moduleLogger';

@Injectable()
export class CronService {
  private logger = createModuleLogger(CronService.name);
  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly userPlansService: UserPlansService,
    private readonly achievementRecordsService: AchievementRecordsService,
    private readonly recommendationsService: RecomendationsService,
  ) {}

  // Payments
  @Cron(CronExpression.EVERY_HOUR)
  async handleExpiredSubscriptions() {
    this.logger.info(
      '[CronService] Iniciando verificação de assinaturas expiradas',
    );
    await this.paymentsService.checkExpiredSubscriptions();
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async handleExpiringSubscriptions() {
    this.logger.info(
      '[CronService] Verificando assinaturas próximas do vencimento',
    );
    await this.paymentsService.checkExpiringSubscriptions();
  }

  // User Plans
  @Cron(CronExpression.EVERY_30_MINUTES)
  async handlePendingPlans() {
    this.logger.info('[CronService] Verificando planos pendentes');
    await this.userPlansService.jobCheckPlans();
  }

  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async handleActivityStatus() {
    this.logger.info('[CronService] Atualizando status das atividades');
    await this.userPlansService.updateActivityStatusCron();
  }

  @Cron(CronExpression.EVERY_DAY_AT_8AM)
  async handleDelayedActivities8AM() {
    this.logger.info('[CronService] Verificando atividades atrasadas - 8AM');
    await this.userPlansService.notifyDelayedActivities8AM();
  }

  @Cron(CronExpression.EVERY_DAY_AT_1PM)
  async handleDelayedActivities1PM() {
    this.logger.info('[CronService] Verificando atividades atrasadas - 1PM');
    await this.userPlansService.notifyDelayedActivities1PM();
  }

  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async handleDelayedActivities8PM() {
    this.logger.info('[CronService] Verificando atividades atrasadas - 8PM');
    await this.userPlansService.notifyDelayedActivities8PM();
  }

  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async handlePlansToInProgress() {
    this.logger.info('[CronService] Atualizando planos para em progresso');
    await this.userPlansService.updatePlansToInProgressIfExecutionStartsToday();
  }

  // Achievement Records
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleExpiredAchievements() {
    this.logger.info('[CronService] Verificando conquistas expiradas');
    await this.achievementRecordsService.jobCheckExpiredAchievements();
  }

  // Recommendations
  @Cron(CronExpression.EVERY_10_MINUTES)
  async handlePendingRecommendations() {
    this.logger.info('[CronService] Verificando recomendações pendentes');
    await this.recommendationsService.jobCheckRecommendations();
  }
}
