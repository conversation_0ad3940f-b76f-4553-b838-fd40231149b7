import { Module } from '@nestjs/common';
import { CronService } from './cron.service';
import { PaymentsModule } from '../payments/payments.module';
import { UserPlansModule } from '../user-plans/user-plans.module';
import { AchievementRecordsModule } from '../achievementRecords/achievementRecords.module';
import { RecommendationsModule } from '../recommendations/recomendations.module';

@Module({
  imports: [
    PaymentsModule,
    UserPlansModule,
    AchievementRecordsModule,
    RecommendationsModule,
  ],
  providers: [CronService],
})
export class CronModule {}
