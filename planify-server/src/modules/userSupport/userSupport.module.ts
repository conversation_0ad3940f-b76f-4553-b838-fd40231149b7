/* eslint-disable prettier/prettier */
import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PermissionsModule } from '../permissions/permissions.module';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from '../users/users.module';
import { User } from '../users/entities/user.entity';
import { UserSupportController } from './userSupport.controller';
import { UserSupportService } from './userSupport.service';
import { UserSupport } from './entities/userSupport.entity';
import { QueuesModule } from '../queues/queues.module';

@Module({
  imports: [
    SequelizeModule.forFeature([UserSupport, User]),
    forwardRef(() => UsersModule),
    PermissionsModule,
    ConfigModule,
    forwardRef(() => QueuesModule),
  ],
  controllers: [UserSupportController],
  providers: [UserSupportService],
  exports: [UserSupportService, SequelizeModule],
})
export class UserSupportModule {}
