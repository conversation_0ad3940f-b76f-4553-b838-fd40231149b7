/* eslint-disable prettier/prettier */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, IsArray } from 'class-validator';
import { SupportMessage } from '../entities/userSupport.entity';

export class CreateUserSupportDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  cellphone: string;

  @ApiProperty()
  @IsString()
  message: string;

  @ApiProperty({ type: [String], default: [] })
  @IsOptional()
  @IsArray()
  imageUrls: string[] = [];

  @ApiProperty({ type: [Object], default: [] })
  @IsOptional()
  @IsArray()
  conversationHistory: SupportMessage[] = [];

  @ApiProperty({ default: 'open' })
  @IsOptional()
  @IsString()
  status: string = 'open';
}
