/* eslint-disable prettier/prettier */
import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';

export interface SupportMessage {
  userId: string;
  userName: string;
  userEmail: string;
  userMessage: string;
  userType: string | null;
  createdAt: Date;
}

export interface ConversationHistory {
  messages: SupportMessage[];
}

@Table({
  tableName: 'UserSupport',
})
export class UserSupport extends Model<UserSupport> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  cellphone: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  message: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
    get() {
      const rawValue = this.getDataValue('imageUrls');
      return rawValue || [];
    },
  })
  imageUrls: string[];

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
    get() {
      const rawValue = this.getDataValue('conversationHistory');
      return rawValue || [];
    },
  })
  conversationHistory: SupportMessage[];

  @Column({
    type: DataType.ENUM('open', 'in_progress', 'closed', 'pending', 'canceled'),
    allowNull: true,
    defaultValue: 'open',
  })
  status: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  updatedAt: Date;
}
