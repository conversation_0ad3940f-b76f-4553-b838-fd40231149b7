/* eslint-disable prettier/prettier */
import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserSupport, SupportMessage } from './entities/userSupport.entity';
import { CreateUserSupportDto } from './dto/create.userSupport.dto';
import { getUserId } from 'src/utils';
import { paginateResults } from 'src/utils/pagination';
import { CreateMessageDto } from './dto/createMessage.dto';
import { UsersService } from '../users/users.service';
import { sendDiscordNotificationAlert } from 'src/utils/sendDiscordNotification';
import { envs } from 'src/utils/envsProxy';
import { QueuesService } from '../queues/queues.service';
import { QUEUE_NAMES } from '../queues/constants';

@Injectable()
export class UserSupportService {
  constructor(
    @InjectModel(UserSupport)
    private readonly userSupportModel: typeof UserSupport,

    private readonly userService: UsersService,
    private readonly queuesService: QueuesService,
  ) {}

  async findAll(status?: string, page: number = 1, limit: number = 10) {
    const whereConditions = status ? { status } : {};

    return await paginateResults(
      { page, limit },
      {
        model: this.userSupportModel,
        whereConditions,
        orderOptions: [['createdAt', 'DESC']],
      },
    );
  }

  async findOne(id: string) {
    const response = await this.userSupportModel.findOne({
      where: {
        id: id,
      },
    });

    if (!response) {
      throw new NotFoundException({ message: 'Ticket not found' });
    }

    return response;
  }

  async findByUser(token: string) {
    const loggedUser = getUserId(token);

    return await this.userSupportModel.findAll({
      where: { userId: loggedUser },
    });
  }

  async create(userSupportDto: CreateUserSupportDto): Promise<UserSupport> {
    try {
      const response = await this.userSupportModel.create(userSupportDto);

      if (!response) {
        throw new InternalServerErrorException('Internal server error');
      }

      await sendDiscordNotificationAlert({
        baseUrl: envs.SUPPORT_WEBHOOK_URL,
        title: `Novo chamado de suporte recebido: ${response.title}`,
        message: response.message,
        aditionalInfos: {
          user: {
            name: response.name,
            email: response.email,
          },
        },
        serviceName: 'User Support',
      });

      return response;
    } catch (error) {
      console.error('Error creating support ticket:', error);
      throw new InternalServerErrorException(
        'Erro ao criar chamado de suporte',
        error,
      );
    }
  }

  async createMessage(id: string, data: CreateMessageDto, token: string) {
    try {
      const loggedUser = getUserId(token);

      const ticket = await this.findOne(id);
      const user = await this.userService.findOne(data?.userId ?? loggedUser);

      const messageData: SupportMessage = {
        userId: loggedUser,
        userName: user?.name,
        userEmail: user?.email,
        userMessage: data?.message,
        userType: user?.userType ?? null,
        createdAt: new Date(),
      };

      const currentHistory = ticket.conversationHistory || [];
      const updatedHistory = [...currentHistory, messageData];

      await ticket.update({
        conversationHistory: updatedHistory,
      });

      if (user?.userType === 'admin' && ticket.userId !== loggedUser) {
        await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION, {
          userId: ticket.userId,
          title: 'Você tem uma nova mensagem',
          description:
            'Acesse agora a área de suporte para acompanhar o status do seu chamado!',
          targetId: ticket.id,
          notificationStatus: 'success',
          targetType: 'support',
        });
      } else if (user?.userType !== 'admin') {
        try {
          await sendDiscordNotificationAlert({
            baseUrl: envs.SUPPORT_WEBHOOK_URL,
            title: `Nova resposta em chamado: ${ticket.title}`,
            message: data?.message,
            aditionalInfos: {
              user: {
                name: user?.name,
                email: user?.email,
              },
              ticketId: ticket.id,
            },
            serviceName: 'User Support',
          });
        } catch (error) {
          console.error('Error sending support alert:', error);
        }
      }

      return await ticket.save();
    } catch (error) {
      console.error('Error creating message:', error);
      throw new InternalServerErrorException('Erro ao criar mensagem', error);
    }
  }

  async update(id: string, userSupportDto: Partial<CreateUserSupportDto>) {
    try {
      const ticket = await this.findOne(id);

      return ticket.update(userSupportDto);
    } catch (error) {
      console.error('Error updating support ticket:', error);
      throw new InternalServerErrorException(
        'Error to update support ticket',
        error,
      );
    }
  }

  async closeTicket(ticketId: string, token: string) {
    const loggedUser = getUserId(token);

    if (!loggedUser) {
      throw new UnauthorizedException('User not found');
    }

    const ticket = await this.findOne(ticketId);

    ticket.update({
      status: 'closed',
    });

    return await ticket.save();
  }

  async remove(id: string) {
    const ticket = await this.findOne(id);

    if (!ticket) {
      throw new NotFoundException('Ticket not found');
    }

    await ticket.destroy();
  }

  async findByUserSupport(
    token: string,
    status?: string,
    page: number = 1,
    limit: number = 10,
  ) {
    const loggedUser = getUserId(token);

    const user = await this.userService.findOne(loggedUser);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const whereConditions: any = { userId: loggedUser };
    if (status) {
      whereConditions.status = status;
    }

    const offset = (page - 1) * limit;

    const { rows, count } = await this.userSupportModel.findAndCountAll({
      where: whereConditions,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
    });

    const data = rows.map((ticket) => ({
      ...ticket.toJSON(),
      conversationHistory: ticket.conversationHistory || [],
    }));

    return {
      data,
      total: count,
      page,
      pageSize: limit,
      totalPages: Math.ceil(count / limit),
    };
  }
}
