/* eslint-disable prettier/prettier */
import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Patch,
  Req,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { UserSupportService } from './userSupport.service';
import { CreateUserSupportDto } from './dto/create.userSupport.dto';
import { AuthGuard } from '../auth/auth.guard';
import { PermissionsGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions';
import { CreateMessageDto } from './dto/createMessage.dto';

@ApiTags('User Support')
@Controller('user-support')
export class UserSupportController {
  constructor(private readonly userSupportService: UserSupportService) { }

  @Post()
  create(@Body() userSupportDto: CreateUserSupportDto) {
    return this.userSupportService.create(userSupportDto);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Get()
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'search', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  findAll(
    @Query('status') status?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.userSupportService.findAll(status, page, limit);
  }

  @UseGuards(AuthGuard)
  @Get()
  findByUser(@Req() token: string) {
    return this.userSupportService.findByUser(token);
  }

  @UseGuards(AuthGuard)
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'search', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @Get('/tickets')
  findByUserSupport(
    @Req() token: string,
    @Query('status') status?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.userSupportService.findByUserSupport(token, status, page, limit);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Patch('/close/:id')
  closeTicket(@Param('id') id: string, @Req() token: string) {
    return this.userSupportService.closeTicket(id, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Patch('/:id')
  @ApiOperation({ summary: 'Atualiza um ticket de suporte' })
  @ApiParam({ name: 'id', description: 'ID do ticket' })
  @ApiBody({
    description: 'Dados para atualização. Todos os campos são opcionais.',
    type: CreateUserSupportDto,
  })
  updateStatus(
    @Param('id') id: string,
    @Body() userSupportDto: Partial<CreateUserSupportDto>,
  ) {
    return this.userSupportService.update(id, userSupportDto);
  }

  @UseGuards(AuthGuard)
  @Post('/message/:id')
  createMessage(
    @Param('id') id: string,
    @Body() data: CreateMessageDto,
    @Req() token: string,
  ) {
    return this.userSupportService.createMessage(id, data, token);
  }

  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Delete('/:id')
  remove(@Param('id') id: string) {
    return this.userSupportService.remove(id);
  }
}
