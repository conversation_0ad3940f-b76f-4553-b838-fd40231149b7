import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Category } from 'src/modules/categories/entities/category.entity';

@Table({
  tableName: 'Prompts',
})
export class Prompt extends Model<Prompt> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  description: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  persona: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  customAction: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  returnType: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  position: number;

  @ForeignKey(() => Category)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  categoryId: string;

  @BelongsTo(() => Category)
  category: Category;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  isActive: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  iconUrl: string;
}
