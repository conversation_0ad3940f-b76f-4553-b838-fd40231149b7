/* eslint-disable  */
import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreatePromptDto } from './dto/create-prompt.dto';
import { UpdatePromptDto } from './dto/update-prompt.dto';
import { Prompt } from './entities/prompt.entity';
import { InjectModel } from '@nestjs/sequelize';
import { Category } from '../categories/entities/category.entity';

@Injectable()
export class PromptsService {
  constructor(
    @InjectModel(Prompt)
    private promptModel: typeof Prompt,
    @InjectModel(Category)
    private categorytModel: typeof Category,
  ) {}

  async create(promptDto: CreatePromptDto) {
    if (!promptDto.categoryId) {
      throw new UnauthorizedException('Category is required');
    }

    const currentCategory = await this.categorytModel.findByPk(
      promptDto.categoryId,
    );

    if (!currentCategory) {
      throw new NotFoundException('Category not found');
    }

    return await this.promptModel.create({
      ...promptDto,
      categoryId: currentCategory.id,
    });
  }

  async findAll() {
    return await this.promptModel.findAll({
      include: [
        {
          model: Category,
          as: 'category',
          attributes: { exclude: ['createdAt', 'updatedAt'] },
        },
      ],
    });
  }

  async findOne(id: string) {
    return await this.promptModel.findOne({
      where: { id },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: { exclude: ['createdAt', 'updatedAt'] },
        },
      ],
    });
  }

  async update(id: string, updatePromptDto: UpdatePromptDto) {
    const prompt = await this.promptModel.findByPk(id);

    if (!prompt) {
      throw new NotFoundException('Prompt not found');
    }

    await prompt.update({
      ...updatePromptDto,
    });
    return prompt;
  }

  async remove(id: string) {
    const prompt = await this.promptModel.findByPk(id);
    if (!prompt) {
      throw new NotFoundException('Prompt not found');
    }
    await prompt.destroy();
  }

  async findByCategoryId(id: string) {
    const prompt = await this.promptModel.findOne({
      where: { categoryId: id },
    });

    if (!prompt) {
      throw new NotFoundException('No prompt found for this category');
    }

    return prompt;
  }

  async findAllByCategoryId(id: string) {
    const prompts = await this.promptModel.findAll({
      where: { categoryId: id },
    });

    if (prompts.length === 0) {
      return [];
    }

    return prompts;
  }

  async findAllCategoriesAndPrompts() {
    const categories = await this.promptModel.findAll({
      include: [
        {
          model: Category,
          as: 'category',
          attributes: { exclude: ['createdAt', 'updatedAt'] },
          order: [['position', 'ASC']],
        },
      ],
      order: [['category', 'position', 'ASC']],
    });

    if (categories.length === 0) {
      return [];
    }

    const categoriesWithPrompts = categories.reduce((acc, item) => {
      const existingCategoryIndex = acc.findIndex(
        (category) => category.id === item.category.id,
      );

      if (existingCategoryIndex !== -1) {
        acc[existingCategoryIndex].prompts.push({
          id: item.id,
          prompt: item.name,
        });
      } else {
        acc.push({
          id: item.category.id,
          category: item.category.name,
          description: item.category.description,
          prompts: [{ id: item.id, prompt: item.name }],
        });
      }

      return acc;
    }, []);

    return categoriesWithPrompts;
  }

  async findAllCategoriesWithPrompts() {
    return this.categorytModel.findAll({
      include: [
        {
          model: Prompt,
          as: 'prompts',
          attributes: { exclude: ['createdAt', 'updatedAt'] },
          order: [['position', 'ASC']],
        },
      ],
      order: [['position', 'ASC']],
    });
  }
}
