import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class CreatePromptDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  categoryId?: string;

  @ApiProperty()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsOptional()
  customAction?: string;

  @ApiProperty()
  @IsOptional()
  persona?: string;

  @ApiProperty()
  @IsOptional()
  returnType?: string;

  @ApiProperty()
  @IsOptional()
  position?: number;

  @ApiProperty()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty()
  @IsOptional()
  iconUrl?: string;
}
