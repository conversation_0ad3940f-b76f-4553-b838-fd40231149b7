/* eslint-disable prettier/prettier */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { PromptsService } from './prompts.service';
import { CreatePromptDto } from './dto/create-prompt.dto';
import { UpdatePromptDto } from './dto/update-prompt.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';

@ApiTags('Prompts')
@Controller('prompts')
export class PromptsController {
  constructor(private readonly promptsService: PromptsService) {}

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createPromptDto: CreatePromptDto) {
    return this.promptsService.create(createPromptDto);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.promptsService.findAll();
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.promptsService.findOne(id);
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() updatePromptDto: UpdatePromptDto) {
    return this.promptsService.update(id, updatePromptDto);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.promptsService.remove(id);
  }

  @UseGuards(AuthGuard)
  @Get('/categoryId/:id')
  findByCategoryId(@Param('id') id: string) {
    return this.promptsService.findByCategoryId(id);
  }

  @UseGuards(AuthGuard)
  @Get('/categoryId/all/:id')
  findAllByCategoryId(@Param('id') id: string) {
    return this.promptsService.findAllByCategoryId(id);
  }

  @UseGuards(AuthGuard)
  @Get('/all/prompts')
  findAllCategoriesAndPrompts() {
    return this.promptsService.findAllCategoriesWithPrompts();
  }
}
