import { Module, forwardRef } from '@nestjs/common';
import { PromptsService } from './prompts.service';
import { PromptsController } from './prompts.controller';
import { Prompt } from './entities/prompt.entity';
import { SequelizeModule } from '@nestjs/sequelize';
import { Category } from '../categories/entities/category.entity';
import { CategoriesModule } from '../categories/categories.module';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    SequelizeModule.forFeature([Prompt, Category]),
    CategoriesModule,
    forwardRef(() => AuthModule),
    JwtModule,
  ],
  controllers: [PromptsController],
  providers: [PromptsService],
})
export class PromptsModule {}
