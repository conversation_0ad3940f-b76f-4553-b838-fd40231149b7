import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { SubCategoryService } from './subCategory.service';
import { CreateSubCategoryDto } from './dto/create-subCategory.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';

@ApiTags('Subcategories')
@Controller('sub-category')
export class SubCategoryController {
  constructor(private readonly subCategoryService: SubCategoryService) {}

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() categoryDto: CreateSubCategoryDto) {
    return this.subCategoryService.create(categoryDto);
  }

  @UseGuards(AuthGuard)
  @Get('/all/sub-categories/by-category/:id')
  findAllByCategory(@Param('id') id: string) {
    return this.subCategoryService.findAllByCategory(id);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.subCategoryService.findAll();
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.subCategoryService.findOne(+id);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.subCategoryService.remove(+id);
  }
}
