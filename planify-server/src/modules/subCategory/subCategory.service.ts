import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { SubCategory } from './entities/subCategory.entity';
import { CreateSubCategoryDto } from './dto/create-subCategory.dto';

@Injectable()
export class SubCategoryService {
  constructor(
    @InjectModel(SubCategory)
    private categoryModel: typeof SubCategory,
  ) {}

  async create(categoryDto: CreateSubCategoryDto) {
    return await this.categoryModel.create(categoryDto);
  }

  async findAll() {
    const categories = await this.categoryModel.findAll();

    return categories;
  }

  findOne(id: number) {
    return `This action returns a #${id} category`;
  }

  update(id: number) {
    return `This action updates a #${id} category`;
  }

  remove(id: number) {
    return `This action removes a #${id} category`;
  }

  async findAllByCategory(id: string) {
    const subCategories = await this.categoryModel.findAll({
      where: {
        categoryId: id,
      },
    });

    return subCategories;
  }
}
