/* eslint-disable prettier/prettier */
import 'dotenv/config';
import {
  Injectable,
  NotFoundException,
  HttpException,
  UnauthorizedException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { AuthDto } from './dto/auth.dto';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { compare, hash } from 'bcrypt';
import { jwtDecode } from 'jwt-decode';
import { TDecodedCredential, TUserCredentials } from 'src/data/@types/user';
import { NetworkService } from '../network-services/network-services.service';
import { faker } from '@faker-js/faker';
import { FilesService } from '../files/files.service';
import { ScopesService } from '../scopes/scopes.service';
import { PaymentsService } from '../payments/payments.service';
import { RequestPasswordResetDto } from './dto/request-password-reset.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { QueuesService } from '../queues/queues.service';
import { AchievementRecordsService } from '../achievementRecords/achievementRecords.service';
import { PaymentPlansService } from '../payment-plans/payment-plans.service';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { AffiliatesService } from '../affiliates/affiliates.service';

@Injectable()
export class AuthService {
  private logger = createModuleLogger(AuthService.name);

  constructor(
    private userService: UsersService,
    private jwtService: JwtService,
    private networkService: NetworkService,
    private filesService: FilesService,
    private scopesSevice: ScopesService,
    private paymentsService: PaymentsService,
    private paymentPlansService: PaymentPlansService,
    private queuesService: QueuesService,
    @Inject(forwardRef(() => AchievementRecordsService))
    private achievementRecordsService: AchievementRecordsService,
    @Inject(forwardRef(() => AffiliatesService))
    private affiliatesService: AffiliatesService,
  ) {}

  public decodeToken(token: string) {
    try {
      return this.jwtService.decode(token);
    } catch (error) {
      return null;
    }
  }

  async findPermissions(user) {
    const permissions: string[] = [user?.userType];

    if (user?.userType === 'admin') {
      const scopesList = await this.scopesSevice.findAll();
      scopesList.map((item) => {
        permissions.push(item?.tag);
      });
    }

    if (user?.paymentPlanId) {
      const scopeTags = await this.paymentsService.getUserPlanPermissions(
        user.id,
      );
      permissions.push(...scopeTags);
    }

    return [...new Set(permissions)];
  }

  async auth(authDto: AuthDto) {
    const user = await this.userService.findByEmail(authDto.email);

    if (!user) {
      this.logger.warn(`User not found: ${authDto.email}`);
      throw new NotFoundException({
        message: 'User not found',
      });
    }

    const isPasswordValid = await compare(authDto.password, user.password);

    if (!isPasswordValid) {
      this.logger.warn(`Invalid password for user: ${authDto.email}`);
      throw new UnauthorizedException({
        message: 'E-mail or password incorrect, please try again.',
      });
    }

    try {
      await this.userService.updateLastAccess(user.id, `${new Date()}`);

      if (!user.lastAccess) {
        this.logger.info('Emitting welcome achievement');
        try {
          await this.achievementRecordsService.create({
            userId: user.id,
            badgeCode: 'BBV001',
          });
        } catch (achievementError) {
          this.logger.error(
            'Error creating welcome achievement:',
            achievementError,
          );
        }
      }
    } catch (error) {
      this.logger.error('Error updating last access:', error);
    }

    const permissions = await this.findPermissions(user);

    const tokenPayload = {
      userId: user.id,
      username: user.name,
      isValidatedAccount: user?.isValidatedAccount ?? false,
      lastAccess: user?.lastAccess || null,
      permissions: permissions,
    };

    const token = await this.jwtService.signAsync(tokenPayload);
    const avatarUrl = await this.filesService.findUserAvatar(user.id);

    return {
      token,
      avatarUrl,
    };
  }

  async authAdmin(authDto: AuthDto) {
    const user = await this.userService.findByEmail(authDto.email);

    if (!user) {
      this.logger.warn(`User not found: ${authDto.email}`);
      throw new NotFoundException({
        message: 'User not found',
      });
    }

    const isPasswordValid = await compare(authDto.password, user.password);

    if (!isPasswordValid) {
      this.logger.warn(`Invalid password for user: ${authDto.email}`);
      throw new UnauthorizedException({
        message: 'E-mail or password incorrect, please try again.',
      });
    }

    if (user?.userType !== 'admin') {
      this.logger.warn(`User is not an admin: ${authDto.email}`);
      throw new UnauthorizedException({
        message: 'You are not authorized to perform this action',
      });
    }

    const permissions = await this.findPermissions(user);

    const tokenPayload = {
      userId: user.id,
      username: user.name,
      isValidatedAccount: user?.isValidatedAccount ?? false,
      permissions: permissions,
    };

    const token = await this.jwtService.signAsync(tokenPayload);
    return {
      token,
    };
  }

  async authGoogle(credentials: TUserCredentials) {
    const tokenInfos: TDecodedCredential = jwtDecode(credentials?.credential);

    const { name, email } = tokenInfos;

    const defaultPass = faker.internet.password({
      length: 10,
      pattern: /[a-zA-Z0-9]/,
    });
    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = String(now.getFullYear()).slice(-2);
    const dateCode = `${month}${year}`;

    const lastUser =
      await this.userService.findOneWithSharedCodeDateCode(dateCode);

    let nextSequence = 1;
    if (lastUser?.sharedCode) {
      const lastSequence = parseInt(lastUser.sharedCode.split('-')[1], 10);
      nextSequence = lastSequence + 1;
    }

    // Formata o código sequencial para 4 dígitos (ex: 0001)
    const sequenceCode = String(nextSequence).padStart(4, '0');

    // Gera o sharedCode no formato PYMMYY-XXXX
    const sharedCode = `PY${dateCode}-${sequenceCode}`;

    const userInfos = {
      name: name,
      email,
      password: defaultPass,
      sharedCode,
    };

    const userAccountByEmail = await this.userService.findByEmail(email);
    const userDataPermission = userAccountByEmail?.permissions;
    const userData = {
      id: null,
      name: null,
      sharedCode: null,
      isValidatedAccount: userAccountByEmail?.isValidatedAccount ?? false,
      permissions: [userDataPermission],
    };

    if (!userAccountByEmail) {
      const user: any = await this.userService.findOrCreate(userInfos);

      userData.id = user.id;
      userData.name = user.email;
      userData.isValidatedAccount = user?.isValidatedAccount ?? false;
      userData.permissions = [user.permissions];

      if (!user?.isPasswordSubmited) {
        const data = {
          name: user?.name,
          email: user?.email,
          password: defaultPass,
        };
        await this.networkService.sendEmailRegister(data);

        user.update({ isPasswordSubmited: true });
        user.save();
      }
      await this.paymentPlansService.checkGatewayPlans(user.id);
    }

    const permissions = await this.findPermissions(
      userAccountByEmail || userData,
    );

    const tokenPayload = {
      userId: userData?.id ?? userAccountByEmail.id,
      username: userData?.name ?? userAccountByEmail.name,
      isValidatedAccount:
        userData?.isValidatedAccount ??
        userAccountByEmail?.isValidatedAccount ??
        false,
      lastAccess: userAccountByEmail?.lastAccess || null,
      permissions: permissions,
    };

    const token = await this.jwtService.signAsync(tokenPayload);
    const userId = userData?.id ?? userAccountByEmail.id;
    const avatarUrl = await this.filesService.findUserAvatar(userId);

    if (!token) {
      this.logger.warn(`Token not generated for user: ${userId}`);
      throw new UnauthorizedException({
        message: 'E-mail or password incorrect, please try again.',
      });
    }

    return {
      token,
      avatarUrl,
    };
  }

  async refreshToken(data) {
    const tokenData: any = jwtDecode(data.token);
    const { userId } = tokenData;

    const user = await this.userService.findOne(userId);

    if (!user) {
      this.logger.warn(`User not found: ${userId}`);
      throw new HttpException(
        {
          message: 'User not found',
        },
        417,
      );
    }

    const permissions = await this.findPermissions(user);

    const tokenPayload = {
      userId: user.id,
      username: user.name,
      isValidatedAccount: user?.isValidatedAccount ?? false,
      lastAccess: user?.lastAccess || null,
      permissions: permissions,
    };

    const token = await this.jwtService.signAsync(tokenPayload);
    const avatarUrl = await this.filesService.findUserAvatar(user.id);

    return {
      token,
      avatarUrl,
    };
  }

  async requestPasswordReset(requestDto: RequestPasswordResetDto) {
    const user = await this.userService.findByEmail(requestDto.email);
    if (!user) {
      this.logger.info(
        `Password reset request for non-existent user: ${requestDto.email}`,
      );
      return {
        message:
          'If the email exists, you will receive the instructions to reset the password',
      };
    }

    const resetToken = await this.jwtService.signAsync(
      {
        userId: user.id,
        passwordHash: user.password,
        type: 'password-reset',
      },
      { expiresIn: '60m' },
    );

    await this.networkService.sendPasswordResetEmail({
      email: user.email,
      name: user.name,
      resetToken,
    });

    this.logger.info('Password reset request sent successfully');

    return {
      message: 'Password reset email sent successfully',
    };
  }

  async resetPassword(resetDto: ResetPasswordDto) {
    try {
      const payload = await this.jwtService.verifyAsync(resetDto.token);

      if (payload.type !== 'password-reset') {
        this.logger.warn('Invalid  type on reset password');
        throw new UnauthorizedException({
          message: 'Invalid or expired token',
        });
      }

      const user = await this.userService.findOneWithPassword(payload.userId);

      if (!user) {
        this.logger.warn('User not found on reset password');
        throw new UnauthorizedException({
          message: 'Invalid or expired token',
        });
      }

      if (payload.passwordHash !== user.password) {
        this.logger.warn('Invalid or expired token hash on reset password');
        throw new UnauthorizedException({
          message: 'Invalid or expired token',
        });
      }

      const hashedPassword = await hash(resetDto.password, 10);
      await this.userService.updatePassword(user.id, {
        password: hashedPassword,
      });

      return {
        message: 'Password changed successfully',
      };
    } catch (error) {
      this.logger.error('Reset password error:', error);

      if (error.name === 'JsonWebTokenError') {
        this.logger.warn('Invalid or expired token on reset password');
        throw new UnauthorizedException({
          message: 'Invalid or expired token',
        });
      }
      if (error.name === 'TokenExpiredError') {
        this.logger.warn('Token expired on reset password');
        throw new UnauthorizedException({
          message: 'Token expired',
        });
      }
      this.logger.error('Unexpected error on reset password:', error);
      throw new UnauthorizedException({
        message: 'Invalid or expired token',
      });
    }
  }

  async authAffiliate(email: string, password: string) {
    this.logger.info(`Tentativa de autenticação para afiliado: ${email}`);

    const affiliate = await this.affiliatesService.findByEmail(email, true);

    if (!affiliate) {
      this.logger.warn(`Affiliate not found: ${email}`);
      throw new NotFoundException({
        message: 'Affiliate not found',
      });
    }

    this.logger.info(
      `Afiliado encontrado: ${affiliate.id}, status: ${affiliate.status}`,
    );

    const isPasswordValid = await compare(password, affiliate.password);

    if (!isPasswordValid) {
      this.logger.warn(`Invalid password for affiliate: ${email}`);
      throw new UnauthorizedException({
        message: 'E-mail or password incorrect, please try again.',
      });
    }

    const tokenPayload = {
      affiliateId: affiliate.id,
      affiliateName: affiliate.name,
      status: affiliate.status,
      permissions: ['affiliate'],
    };

    this.logger.info(
      `Gerando token para afiliado com payload: ${JSON.stringify(tokenPayload)}`,
    );
    const token = await this.jwtService.signAsync(tokenPayload);
    this.logger.info(
      `Token gerado para afiliado: ${token.substring(0, 15)}...`,
    );

    const affiliateResponse = affiliate.get({ plain: true });
    delete affiliateResponse.password;

    return {
      token,
    };
  }
}
