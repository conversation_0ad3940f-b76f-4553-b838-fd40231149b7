import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AFFILIATE_PERMISSIONS_KEY } from './affiliate-permissions';
import { createModuleLogger } from 'src/utils/moduleLogger';

@Injectable()
export class AffiliatePermissionsGuard implements CanActivate {
  private logger = createModuleLogger(AffiliatePermissionsGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      AFFILIATE_PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const affiliate = request.affiliate;

    if (!affiliate) {
      this.logger.warn('Affiliate data not found in request');
      throw new NotFoundException({ message: 'Affiliate not found' });
    }

    if (!affiliate.permissions || !Array.isArray(affiliate.permissions)) {
      this.logger.warn(
        `Invalid permissions format for affiliate: ${affiliate.id}`,
      );
      throw new UnauthorizedException({
        message: 'Invalid permissions format',
      });
    }

    const hasPermission = requiredPermissions.every((permission) =>
      affiliate.permissions.includes(permission),
    );

    if (!hasPermission) {
      this.logger.warn(
        `Affiliate ${affiliate.id} lacks required permissions: ${requiredPermissions.join(', ')}. Has: ${affiliate.permissions.join(', ')}`,
      );
      throw new UnauthorizedException({
        message: 'Unauthorized, you do not have sufficient permissions',
      });
    }

    return true;
  }
}
