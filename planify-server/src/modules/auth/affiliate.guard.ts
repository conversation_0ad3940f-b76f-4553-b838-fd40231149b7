import 'dotenv/config';
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { AffiliatesService } from '../affiliates/affiliates.service';
import { createModuleLogger } from 'src/utils/moduleLogger';

@Injectable()
export class AffiliateGuard implements CanActivate {
  private logger = createModuleLogger(AffiliateGuard.name);

  constructor(
    private jwtService: JwtService,
    private affiliatesService: AffiliatesService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      this.logger.warn('Token not found');
      throw new UnauthorizedException('Token not found');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      if (!payload || !payload.affiliateId) {
        this.logger.warn('Invalid affiliate token - missing affiliateId');
        throw new UnauthorizedException('Invalid token format for affiliate');
      }

      const affiliate = await this.affiliatesService.findOne(
        payload.affiliateId,
      );

      if (!affiliate) {
        this.logger.warn(`Affiliate not found with ID: ${payload.affiliateId}`);
        throw new NotFoundException('Affiliate not found');
      }

      if (affiliate.status !== 'active') {
        this.logger.warn(`Affiliate status is not active: ${affiliate.status}`);
        throw new UnauthorizedException('Affiliate account is not active');
      }

      request['affiliate'] = {
        ...payload,
        id: affiliate.id,
        name: affiliate.name,
        email: affiliate.email,
        status: affiliate.status,
      };

      return true;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }

      this.logger.error(`Token validation error: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
