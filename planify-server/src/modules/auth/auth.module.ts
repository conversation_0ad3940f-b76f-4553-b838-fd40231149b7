import 'dotenv/config';
import { forwardRef, Module } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtModule } from '@nestjs/jwt';
import { AuthInterceptor } from './auth.interceptor';
import { AuthGuard } from './auth.guard';
import { PermissionsGuard } from './permission.guard';
import { UsersService } from '../users/users.service';
import { User } from '../users/entities/user.entity';
import { SequelizeModule } from '@nestjs/sequelize';
import { UserInfos } from '../users/entities/user-profile.entity';
import { NetworkService } from '../network-services/network-services.service';
import { FilesModule } from '../files/files.module';
import { ScopesService } from '../scopes/scopes.service';
import { Scope } from '../scopes/entities/scope.entity';
import { UsersModule } from '../users/users.module';
import { PaymentsModule } from '../payments/payments.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';
import { NetworkServicesModule } from '../network-services/network-services.module';
import { QueuesModule } from '../queues/queues.module';
import { ScopesModule } from '../scopes/scopes.module';
import { AchievementRecordsModule } from '../achievementRecords/achievementRecords.module';
import { WeightHistoriesModule } from '../weight-histories/weight-histories.module';
import { AffiliatesModule } from '../affiliates/affiliates.module';

@Module({
  imports: [
    SequelizeModule.forFeature([User, UserInfos, Scope]),
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '30d' },
    }),
    forwardRef(() => UsersModule),
    forwardRef(() => PaymentsModule),
    forwardRef(() => PaymentPlansModule),
    forwardRef(() => FilesModule),
    forwardRef(() => NetworkServicesModule),
    forwardRef(() => QueuesModule),
    forwardRef(() => ScopesModule),
    forwardRef(() => AchievementRecordsModule),
    forwardRef(() => WeightHistoriesModule),
    forwardRef(() => AffiliatesModule),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    AuthInterceptor,
    AuthGuard,
    PermissionsGuard,
    UsersService,
    NetworkService,
    ScopesService,
  ],
  exports: [JwtModule, AuthGuard, PermissionsGuard, AuthService, UsersService],
})
export class AuthModule {}
