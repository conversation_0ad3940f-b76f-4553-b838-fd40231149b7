import { Body, Controller, Patch, Post } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthDto } from './dto/auth.dto';
import { TUserCredentials } from 'src/data/@types/user';
import { ApiTags } from '@nestjs/swagger';
import { RequestPasswordResetDto } from './dto/request-password-reset.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';

@ApiTags('Auth service')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post()
  create(@Body() createAuthDto: AuthDto) {
    return this.authService.auth(createAuthDto);
  }

  @Post('/google')
  authGoogle(@Body() credentials: TUserCredentials) {
    return this.authService.authGoogle(credentials);
  }

  @Post('/admin')
  authAdmin(@Body() createAuthDto: AuthDto) {
    return this.authService.authAdmin(createAuthDto);
  }
  @Patch('/refresh')
  refreshToken(@Body() token: string) {
    return this.authService.refreshToken(token);
  }

  @Post('request-password-reset')
  async requestPasswordReset(@Body() requestDto: RequestPasswordResetDto) {
    return this.authService.requestPasswordReset(requestDto);
  }

  @Post('reset-password')
  async resetPassword(@Body() resetDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetDto);
  }
}
