import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from './permissions';
import { createModuleLogger } from 'src/utils/moduleLogger';

@Injectable()
export class PermissionsGuard implements CanActivate {
  private logger = createModuleLogger(PermissionsGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      this.logger.warn('User not found in request');
      throw new NotFoundException({ message: 'User not found' });
    }

    this.logger.info(
      `Verificando permissões necessárias: ${requiredPermissions.join(', ')}`,
    );
    this.logger.info(
      `Permissões do usuário: ${JSON.stringify(user.permissions || [])}`,
    );

    // Verificação especial para afiliados
    if (request.affiliate && requiredPermissions.includes('affiliate')) {
      this.logger.info('Concedendo acesso para afiliado');
      return true;
    }

    if (!user.permissions) {
      this.logger.warn('User has no permissions');
      throw new UnauthorizedException({
        message: 'Unauthorized, you do not have sufficient permissions',
      });
    }

    const hasPermission = requiredPermissions.every((permission) =>
      user.permissions.includes(permission),
    );

    if (!hasPermission) {
      this.logger.warn(
        `User ${user.userId || user.id} lacks required permissions: ${requiredPermissions.join(', ')}`,
      );
      throw new UnauthorizedException({
        message: 'Unauthorized, you do not have sufficient permissions',
      });
    }

    return true;
  }
}
