import 'dotenv/config';
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  NotFoundException,
  Inject,
  Optional,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { UsersService } from '../users/users.service';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { AffiliatesService } from '../affiliates/affiliates.service';

@Injectable()
export class AuthGuard implements CanActivate {
  private logger = createModuleLogger(AuthGuard.name);

  constructor(
    private jwtService: JwtService,
    private usersService: UsersService,
    @Optional()
    @Inject(AffiliatesService)
    private affiliatesService?: AffiliatesService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      this.logger.warn('Token not found');
      throw new ForbiddenException('Token not found');
    }
    try {
      this.logger.info(`Verificando token: ${token.substring(0, 15)}...`);

      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
        ignoreExpiration: false,
        ignoreNotBefore: true,
      });

      this.logger.info(`Token payload: ${JSON.stringify(payload)}`);

      if (payload.userId) {
        const user = await this.usersService.findOne(payload.userId);

        if (!user) {
          this.logger.warn(`User not found ${payload.userId}`);
          throw new NotFoundException('User not found');
        }

        if (!payload.isValidatedAccount) {
          throw new ForbiddenException('Account not validated');
        }

        request['user'] = {
          ...payload,
          id: payload.userId,
          permissions: payload.permissions || [],
        };
        return true;
      } else if (payload.affiliateId) {
        if (!this.affiliatesService) {
          this.logger.warn(
            'AffiliatesService not available, but token contains affiliateId',
          );
          throw new ForbiddenException('Invalid token format for this context');
        }

        this.logger.info(
          `Processando token de afiliado: ${payload.affiliateId}`,
        );

        try {
          const affiliate = await this.affiliatesService.findOne(
            payload.affiliateId,
          );

          this.logger.info(
            `Afiliado encontrado: ${affiliate.id}, status: ${affiliate.status}`,
          );

          // Permitir afiliados com status pending
          // Se no futuro precisar verificar o status, descomente a verificação abaixo
          // if (affiliate.status !== 'active') {
          //   this.logger.warn(`Affiliate status is not active: ${affiliate.status}`);
          //   throw new ForbiddenException('Affiliate account is not active');
          // }

          request['user'] = {
            id: affiliate.id,
            name: affiliate.name,
            email: affiliate.email,
            permissions: payload.permissions || ['affiliate'],
          };

          request['affiliate'] = {
            id: affiliate.id,
            affiliateId: payload.affiliateId,
            name: affiliate.name,
            email: affiliate.email,
            status: affiliate.status,
            permissions: payload.permissions || ['affiliate'],
          };

          this.logger.info(`Afiliado autenticado com sucesso: ${affiliate.id}`);
          return true;
        } catch (error) {
          this.logger.error(
            `Erro processando token de afiliado: ${error.message}`,
          );
          if (error instanceof NotFoundException) {
            throw error;
          }
          throw new ForbiddenException('Error verifying affiliate credentials');
        }
      } else {
        this.logger.warn(
          'Invalid token format - missing userId or affiliateId',
        );
        throw new ForbiddenException('Invalid token format');
      }
    } catch (error) {
      this.logger.error(`Token validation error: ${error.message}`);
      throw new ForbiddenException('Invalid or expired token');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];

    if (type === 'Bearer') {
      if (token && token.startsWith('{') && token.endsWith('}')) {
        try {
          const tokenObj = JSON.parse(token);
          return tokenObj.token || token;
        } catch (e) {
          this.logger.warn(`Erro ao parsear token como JSON: ${e.message}`);
          return token;
        }
      }
      return token;
    }

    return undefined;
  }
}
