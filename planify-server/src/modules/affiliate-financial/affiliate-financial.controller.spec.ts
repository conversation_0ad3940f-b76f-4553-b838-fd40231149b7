import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateFinancialController } from './affiliate-financial.controller';
import { AffiliateFinancialService } from './affiliate-financial.service';

describe('AffiliateFinancialController', () => {
  let controller: AffiliateFinancialController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AffiliateFinancialController],
      providers: [AffiliateFinancialService],
    }).compile();

    controller = module.get<AffiliateFinancialController>(AffiliateFinancialController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
