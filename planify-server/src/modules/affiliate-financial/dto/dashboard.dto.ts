import { ApiProperty } from '@nestjs/swagger';

export class TransferDto {
  @ApiProperty({
    description: 'ID da transferência',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nome do usuário',
    example: '<PERSON> Silva',
  })
  userName: string;

  @ApiProperty({
    description: 'Nome do produto',
    example: 'Planify Pro Anual',
  })
  productName: string;

  @ApiProperty({
    description: 'Valor da comissão',
    example: 350.0,
  })
  amount: number;

  @ApiProperty({
    description: 'Data da comissão',
    example: '2025-03-15',
  })
  date: string;

  @ApiProperty({
    description: 'Status da comissão',
    example: 'paid',
    enum: [
      'pending',
      'approved',
      'paid',
      'rejected',
      'failed',
      'canceled',
      'refunded',
    ],
  })
  status: string;

  @ApiProperty({
    description: 'Link do comprovante de pagamento',
    example: 'https://cdn.planify.com/comprovantes/123456.pdf',
    required: false,
  })
  paymentReceiptUrl?: string;
}

export class DashboardResponseDto {
  @ApiProperty({
    description: 'Total de comissões acumuladas',
    example: 12580.5,
  })
  total: number;

  @ApiProperty({
    description: 'Comissões pendentes',
    example: 1850.75,
  })
  pending: number;

  @ApiProperty({
    description: 'Total de vendas realizadas',
    example: 47,
  })
  sales: number;

  @ApiProperty({
    description: 'Número de indicações ativas',
    example: 28,
  })
  referrals: number;

  @ApiProperty({
    description: 'Taxa de conversão',
    example: 18.5,
  })
  conversion: number;

  @ApiProperty({
    description: 'Lista de transferências recentes',
    type: [TransferDto],
  })
  latestTransfers: TransferDto[];
}
