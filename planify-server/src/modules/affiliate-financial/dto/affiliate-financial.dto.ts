import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsUUID,
  IsOptional,
  IsNumber,
  Min,
} from 'class-validator';

export class CreateAffiliateFinancialDto {
  @ApiProperty({
    description: 'Nome do banco',
  })
  @IsString()
  @IsOptional()
  bankName?: string;

  @ApiProperty({
    description: 'Número da agência bancária',
  })
  @IsString()
  @IsOptional()
  bankAgency?: string;

  @ApiProperty({
    description: 'Número da conta bancária',
  })
  @IsString()
  @IsOptional()
  bankAccount?: string;

  @ApiProperty({
    description: 'Chave PIX',
  })
  @IsString()
  @IsOptional()
  pixKey?: string;
}

export class UpdateAffiliateFinancialDto extends PartialType(
  CreateAffiliateFinancialDto,
) {
  @ApiProperty({
    description: 'ID do afiliado',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  affiliateId?: string;
}

export class CreateTransferDto {
  @ApiProperty({
    description: 'ID do afiliado',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  affiliateId: string;

  @ApiProperty({
    description: 'Valor da transferência',
    required: true,
    example: 100.5,
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(0.01)
  amount: number;

  @ApiProperty({
    description: 'ID do usuário relacionado à transferência',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'ID do pagamento associado',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  paymentId?: string;

  @ApiProperty({
    description: 'Código de referral que gerou a comissão',
    required: false,
  })
  @IsString()
  @IsOptional()
  referralCode?: string;
}
