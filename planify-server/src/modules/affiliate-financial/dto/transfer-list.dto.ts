import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export class TransferFilterDto {
  @ApiProperty({
    description: 'Status da transferência para filtrar',
    enum: [
      'pending',
      'approved',
      'paid',
      'rejected',
      'failed',
      'canceled',
      'refunded',
      'all',
    ],
    required: false,
    default: 'all',
  })
  @IsEnum([
    'pending',
    'approved',
    'paid',
    'rejected',
    'failed',
    'canceled',
    'refunded',
    'all',
  ])
  @IsOptional()
  status?: string = 'all';
}

export class TransferItemDto {
  @ApiProperty({
    description: 'ID da transferência',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nome do usuário',
    example: 'João Silva',
  })
  userName: string;

  @ApiProperty({
    description: 'Nome do produto',
    example: 'Planify Pro Anual',
  })
  productName: string;

  @ApiProperty({
    description: 'Valor da comissão',
    example: 350.0,
  })
  amount: number;

  @ApiProperty({
    description: 'Data da comissão',
    example: '2025-03-15',
  })
  date: string;

  @ApiProperty({
    description: 'Status da comissão',
    example: 'paid',
    enum: [
      'pending',
      'approved',
      'paid',
      'rejected',
      'failed',
      'canceled',
      'refunded',
    ],
  })
  status: string;

  @ApiProperty({
    description: 'Link do comprovante de pagamento',
    example: 'https://cdn.planify.com/comprovantes/123456.pdf',
    required: false,
  })
  paymentReceiptUrl?: string;
}

export type TransferListResponseDto = TransferItemDto[];
