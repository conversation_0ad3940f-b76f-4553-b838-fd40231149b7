import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { AffiliateFinancial } from './entities/affiliate-financial.entity';
import {
  CreateAffiliateFinancialDto,
  UpdateAffiliateFinancialDto,
} from './dto/affiliate-financial.dto';
import { getAffiliateId } from 'src/utils';
import { AffiliatesService } from '../affiliates/affiliates.service';
import { Transfer } from './entities/transfer.entity';
import { DashboardResponseDto, TransferDto } from './dto/dashboard.dto';
import { TransferListResponseDto } from './dto/transfer-list.dto';
import { UsersService } from '../users/users.service';
import { Op, Sequelize } from 'sequelize';
import { RedisService } from '../redis/redis.service';
import { PaymentsService } from '../payments/payments.service';
import { PaymentPlansService } from '../payment-plans/payment-plans.service';

@Injectable()
export class AffiliateFinancialService {
  private readonly logger = new Logger(AffiliateFinancialService.name);
  private readonly CACHE_TTL = 48 * 60 * 60;

  constructor(
    @InjectModel(AffiliateFinancial)
    private readonly affiliateFinancialModel: typeof AffiliateFinancial,
    @InjectModel(Transfer)
    private readonly transferModel: typeof Transfer,
    @Inject(forwardRef(() => AffiliatesService))
    private readonly affiliatesService: AffiliatesService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => PaymentsService))
    private readonly paymentsService: PaymentsService,
    @Inject(forwardRef(() => PaymentPlansService))
    private readonly paymentPlansService: PaymentPlansService,
    private readonly redisService: RedisService,
  ) {}

  private async getProductName(transfer: Transfer): Promise<string> {
    try {
      if (transfer.paymentId) {
        const payment = await this.paymentsService.getUserPaymentPlan(
          transfer.paymentId,
        );
        if (!payment) {
          return 'Planify';
        }

        const paymentPlan = await this.paymentPlansService.findOne(
          payment.paymentPlanId,
        );
        if (!paymentPlan) {
          return 'Planify';
        }

        return paymentPlan.name;
      } else if (transfer.userId) {
        try {
          const user = await this.usersService.findOne(transfer.userId);
          if (user && user.paymentPlanId) {
            const paymentPlan = await this.paymentPlansService.findOne(
              user.paymentPlanId,
            );
            if (paymentPlan) {
              return paymentPlan.name;
            }
          }
        } catch (error) {
          this.logger.error(
            `Erro ao buscar plano do usuário: ${error.message}`,
          );
        }
      }

      return 'Planify';
    } catch (error) {
      this.logger.error(`Erro ao obter nome do plano: ${error.message}`);
      return 'Planify';
    }
  }

  private getDashboardCacheKey(affiliateId: string): string {
    return `affiliate:dashboard:${affiliateId}`;
  }

  private getTransfersCacheKey(affiliateId: string, status: string): string {
    return `affiliate:transfers:${affiliateId}:${status || 'all'}`;
  }

  private getAffiliateFinancialCacheKey(affiliateId: string): string {
    return `affiliate:financial:${affiliateId}`;
  }

  private async updateCacheAfterChanges(affiliateId: string): Promise<void> {
    setTimeout(async () => {
      try {
        const dashboardCacheKey = this.getDashboardCacheKey(affiliateId);
        const dashboardData = await this.fetchDashboardData(affiliateId);
        await this.redisService.update(
          dashboardCacheKey,
          dashboardData,
          this.CACHE_TTL,
        );

        // Atualizar cache de todas as transferências
        const transfersAllCacheKey = this.getTransfersCacheKey(
          affiliateId,
          'all',
        );
        const allTransfers = await this.fetchTransfersList(affiliateId, {});
        await this.redisService.update(
          transfersAllCacheKey,
          allTransfers,
          this.CACHE_TTL,
        );

        // Atualizar cache para cada status de transferência
        const statusList = [
          'pending',
          'approved',
          'paid',
          'rejected',
          'canceled',
          'refunded',
        ];
        await Promise.all(
          statusList.map(async (status) => {
            const statusCacheKey = this.getTransfersCacheKey(
              affiliateId,
              status,
            );
            const statusTransfers = await this.fetchTransfersList(affiliateId, {
              status,
            });
            await this.redisService.update(
              statusCacheKey,
              statusTransfers,
              this.CACHE_TTL,
            );
          }),
        );

        const financialCacheKey =
          this.getAffiliateFinancialCacheKey(affiliateId);
        const financialData = await this.fetchAffiliateFinancialData(
          affiliateId,
        ).catch(() => null);
        if (financialData) {
          await this.redisService.update(
            financialCacheKey,
            financialData,
            this.CACHE_TTL,
          );
        }

        this.logger.log(
          `Cache atualizado em background para o afiliado ${affiliateId}`,
        );
      } catch (error) {
        this.logger.error(
          `Erro ao atualizar cache em background: ${error.message}`,
        );
      }
    }, 0);
  }

  private async clearAffiliateCaches(affiliateId: string): Promise<void> {
    setTimeout(async () => {
      try {
        await this.redisService.delByPattern(`affiliate:*:${affiliateId}*`);

        const statusList = [
          'all',
          'pending',
          'approved',
          'paid',
          'rejected',
          'canceled',
          'refunded',
        ];
        await Promise.all(
          statusList.map(async (status) => {
            const cacheKey = this.getTransfersCacheKey(affiliateId, status);
            await this.redisService.del(cacheKey);
          }),
        );

        this.logger.log(
          `Cache limpo em background para o afiliado ${affiliateId}`,
        );
      } catch (error) {
        this.logger.error(
          `Erro ao limpar cache em background para o afiliado ${affiliateId}:`,
          error,
        );
      }
    }, 0);
  }

  private async fetchDashboardData(
    affiliateId: string,
  ): Promise<DashboardResponseDto> {
    try {
      const affiliate = await this.affiliatesService.findOne(affiliateId);
      if (!affiliate) {
        throw new NotFoundException({
          message: 'Afiliado não encontrado',
        });
      }

      const [
        totalCommissionsResult,
        pendingCommissionsResult,
        totalSalesResult,
        affiliateMetrics,
        recentTransfers,
      ] = await Promise.all([
        this.transferModel.findOne({
          attributes: [[Sequelize.fn('SUM', Sequelize.col('amount')), 'total']],
          where: {
            affiliateId,
            status: {
              [Op.in]: ['paid', 'approved'],
            },
          },
          raw: true,
        }) as Promise<any>,
        this.transferModel.findOne({
          attributes: [[Sequelize.fn('SUM', Sequelize.col('amount')), 'total']],
          where: {
            affiliateId,
            status: 'pending',
          },
          raw: true,
        }) as Promise<any>,
        this.transferModel.count({
          where: {
            affiliateId,
            status: {
              [Op.notIn]: ['rejected', 'failed', 'canceled', 'refunded'],
            },
          },
        }),
        this.affiliatesService.findAllReferrals({ affiliateId }),
        this.transferModel.findAll({
          where: { affiliateId },
          order: [['createdAt', 'DESC']],
          limit: 5,
        }),
      ]);

      const totalCommissions = totalCommissionsResult?.total || 0;
      const pendingCommissions = pendingCommissionsResult?.total || 0;
      const totalSales = totalSalesResult || 0;
      const activeReferrals = affiliateMetrics.registrations || 0;

      const conversionRate =
        activeReferrals > 0
          ? ((affiliateMetrics.payments / activeReferrals) * 100).toFixed(1)
          : 0;

      if (!recentTransfers || recentTransfers.length === 0) {
        return {
          total: Number(totalCommissions),
          pending: Number(pendingCommissions),
          sales: totalSales,
          referrals: activeReferrals,
          conversion: parseFloat(conversionRate.toString()),
          latestTransfers: [],
        };
      }

      const latestTransfersPromises = recentTransfers.map(async (transfer) => {
        let userName = 'Cliente';

        if (transfer.userId) {
          try {
            const user = await this.usersService.findOne(transfer.userId);
            if (user) {
              userName = user.name;
            }
          } catch (error) {
            this.logger.error(
              `Erro ao buscar informações do usuário: ${error.message}`,
            );
          }
        }

        const productName = await this.getProductName(transfer);

        return {
          id: transfer.id,
          userName,
          productName,
          amount: transfer.amount,
          date: transfer.createdAt.toISOString().split('T')[0],
          status: transfer.status,
          paymentReceiptUrl: transfer.paymentReceiptUrl,
        };
      });

      const latestTransfers = await Promise.all(latestTransfersPromises);

      return {
        total: Number(totalCommissions),
        pending: Number(pendingCommissions),
        sales: totalSales,
        referrals: activeReferrals,
        conversion: parseFloat(conversionRate.toString()),
        latestTransfers,
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar dados do dashboard: ${error.message}`);
      throw error;
    }
  }

  async getDashboard(token, cacheTTL?: number): Promise<DashboardResponseDto> {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        this.logger.warn(
          'Tentativa de acesso ao dashboard sem ID de afiliado válido',
        );
        throw new UnauthorizedException({
          message: 'Acesso não autorizado',
        });
      }

      const cacheKey = this.getDashboardCacheKey(affiliateId);
      const ttl = cacheTTL || this.CACHE_TTL;

      return this.redisService.getOrSet<DashboardResponseDto>(
        cacheKey,
        () => this.fetchDashboardData(affiliateId),
        ttl,
      );
    } catch (error) {
      this.logger.error(`Erro ao obter dados do dashboard: ${error.message}`);
      throw error;
    }
  }

  async create(affiliateDto: CreateAffiliateFinancialDto, token) {
    const affiliateId = getAffiliateId(token);

    if (!affiliateId) {
      this.logger.warn('Tentativa de acesso sem ID de afiliado válido');
      throw new UnauthorizedException({
        message: 'Acesso não autorizado para afiliados',
      });
    }

    this.logger.debug(
      `Criando/Atualizando informações financeiras para afiliado: ${affiliateId}`,
    );

    const affiliate = await this.affiliatesService.findOne(affiliateId);
    if (!affiliate) {
      throw new NotFoundException('Afiliado não encontrado');
    }

    const existingFinancial = await this.affiliateFinancialModel.findOne({
      where: { affiliateId: affiliate.id },
    });

    let result;
    if (existingFinancial) {
      await existingFinancial.update(affiliateDto);
      result = existingFinancial;
    } else {
      result = await this.affiliateFinancialModel.create({
        ...affiliateDto,
        affiliateId: affiliate.id,
      });
    }

    const financialCacheKey = this.getAffiliateFinancialCacheKey(affiliateId);
    await this.redisService.del(financialCacheKey);

    await this.updateCacheAfterChanges(affiliateId);

    return result;
  }

  async findOne(id: string) {
    const financial = await this.affiliateFinancialModel.findByPk(id, {
      include: [
        {
          model: AffiliateFinancial.sequelize.models.Affiliate,
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    if (!financial) {
      throw new NotFoundException({
        message: 'Informação financeira não encontrada',
      });
    }

    return financial;
  }

  private async fetchAffiliateFinancialData(affiliateId: string) {
    const affiliate = await this.affiliatesService.findOne(affiliateId);
    if (!affiliate) {
      throw new NotFoundException({
        message: 'Afiliado não encontrado',
      });
    }

    const financial = await this.affiliateFinancialModel.findOne({
      where: { affiliateId },
      include: [
        {
          model: AffiliateFinancial.sequelize.models.Affiliate,
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    if (!financial) {
      throw new NotFoundException({
        message: 'Informação financeira não encontrada para este afiliado',
      });
    }

    return financial;
  }

  async findOneByToken(token, cacheTTL?: number) {
    const affiliateId = getAffiliateId(token);

    if (!affiliateId) {
      this.logger.warn('Tentativa de acesso sem ID de afiliado válido');
      throw new UnauthorizedException({
        message: 'Acesso não autorizado',
      });
    }

    const cacheKey = this.getAffiliateFinancialCacheKey(affiliateId);
    const ttl = cacheTTL || this.CACHE_TTL;

    return this.redisService.getOrSet(
      cacheKey,
      () => this.fetchAffiliateFinancialData(affiliateId),
      ttl,
    );
  }

  async findById(id: string) {
    return this.fetchAffiliateFinancialData(id);
  }

  async update(affiliateDto: UpdateAffiliateFinancialDto, token) {
    const affiliateId = getAffiliateId(token);

    if (!affiliateId) {
      this.logger.warn(
        `Tentativa de acesso sem ID de afiliado válido ${affiliateId}`,
      );
      throw new UnauthorizedException({
        message: 'Acesso não autorizado',
      });
    }

    const financial = await this.affiliateFinancialModel.findOne({
      where: { affiliateId },
    });

    if (!financial) {
      throw new NotFoundException({
        message: `Informação financeira não encontrada para este afiliado ${affiliateId}`,
      });
    }

    if (affiliateDto.affiliateId && affiliateDto.affiliateId !== affiliateId) {
      throw new UnauthorizedException({
        message: `Não é permitido alterar o ID do afiliado ${affiliateId}`,
      });
    }

    if (affiliateDto.affiliateId) {
      delete affiliateDto.affiliateId;
    }

    await financial.update(affiliateDto);

    const financialCacheKey = this.getAffiliateFinancialCacheKey(affiliateId);
    await this.redisService.del(financialCacheKey);

    await this.updateCacheAfterChanges(affiliateId);

    return financial;
  }

  async updateById(id: string, affiliateDto: UpdateAffiliateFinancialDto) {
    const financial = await this.affiliateFinancialModel.findByPk(id);

    if (!financial) {
      throw new NotFoundException({
        message: `Informação financeira não encontrada ${id}`,
      });
    }

    if (
      affiliateDto.affiliateId &&
      affiliateDto.affiliateId !== financial.affiliateId
    ) {
      const affiliate = await this.affiliatesService.findOne(
        affiliateDto.affiliateId,
      );
      if (!affiliate) {
        throw new NotFoundException({
          message: `Afiliado não encontrado ${affiliateDto.affiliateId}`,
        });
      }
    }

    await financial.update(affiliateDto);

    const financialCacheKey = this.getAffiliateFinancialCacheKey(
      financial.affiliateId,
    );
    await this.redisService.del(financialCacheKey);

    await this.updateCacheAfterChanges(financial.affiliateId);

    return financial;
  }

  async remove(token) {
    const affiliateId = getAffiliateId(token);

    if (!affiliateId) {
      this.logger.warn(
        `Tentativa de acesso sem ID de afiliado válido ${affiliateId}`,
      );
      throw new UnauthorizedException({
        message: 'Acesso não autorizado',
      });
    }

    const financial = await this.affiliateFinancialModel.findOne({
      where: { affiliateId },
    });

    if (!financial) {
      throw new NotFoundException({
        message: 'Informação financeira não encontrada para este afiliado',
      });
    }

    await financial.destroy();

    const financialCacheKey = this.getAffiliateFinancialCacheKey(affiliateId);
    await this.redisService.del(financialCacheKey);

    await this.clearAffiliateCaches(affiliateId);
  }

  async createTransfer(data: {
    affiliateId: string;
    amount: number;
    userId?: string;
    paymentId?: string;
    referralCode?: string;
  }): Promise<Transfer> {
    if (data.userId && data.referralCode && data.paymentId) {
      const existingTransfer = await this.transferModel.findOne({
        where: {
          affiliateId: data.affiliateId,
          userId: data.userId,
          paymentId: data.paymentId,
          referralCode: data.referralCode,
          amount: data.amount,
        },
      });

      if (existingTransfer) {
        this.logger.log(
          `Transferência idempotente encontrada para afiliado=${data.affiliateId}, userId=${data.userId}, paymentId=${data.paymentId}`,
        );
        return existingTransfer;
      }
    }

    const transfer = await this.transferModel.create({
      ...data,
      status: 'pending',
    });

    await this.updateCacheAfterChanges(data.affiliateId);

    return transfer;
  }

  private async fetchTransfersList(
    affiliateId: string,
    filter: any,
  ): Promise<TransferListResponseDto> {
    try {
      const affiliate = await this.affiliatesService.findOne(affiliateId);
      if (!affiliate) {
        throw new NotFoundException({
          message: 'Afiliado não encontrado',
        });
      }

      const whereClause: any = { affiliateId };

      if (filter.status && filter.status !== 'all') {
        whereClause.status = filter.status;
      }

      const transfers = await this.transferModel.findAll({
        where: whereClause,
        order: [['createdAt', 'DESC']],
      });

      if (!transfers || transfers.length === 0) {
        return [];
      }

      const transfersPromises = transfers.map(async (transfer) => {
        let userName = 'Cliente';

        if (transfer.userId) {
          try {
            const user = await this.usersService.findOne(transfer.userId);
            if (user) {
              userName = user.name;
            }
          } catch (error) {
            this.logger.error(
              `Erro ao buscar informações do usuário: ${error.message}`,
            );
          }
        }

        const productName = await this.getProductName(transfer);

        return {
          id: transfer.id,
          userName,
          productName,
          amount: transfer.amount,
          date: transfer.createdAt.toISOString().split('T')[0],
          status: transfer.status,
          paymentReceiptUrl: transfer.paymentReceiptUrl,
        };
      });

      return Promise.all(transfersPromises);
    } catch (error) {
      this.logger.error(`Erro ao listar transferências: ${error.message}`);
      throw error;
    }
  }

  async listTransfers(
    token,
    filter,
    cacheTTL?: number,
  ): Promise<TransferListResponseDto> {
    try {
      const affiliateId = getAffiliateId(token);

      if (!affiliateId) {
        this.logger.warn(
          'Tentativa de acesso à listagem de transferências sem ID de afiliado válido',
        );
        throw new UnauthorizedException({
          message: 'Acesso não autorizado',
        });
      }

      const status = filter.status || 'all';
      const cacheKey = this.getTransfersCacheKey(affiliateId, status);
      const ttl = cacheTTL || this.CACHE_TTL;

      return this.redisService.getOrSet<TransferListResponseDto>(
        cacheKey,
        () => this.fetchTransfersList(affiliateId, filter),
        ttl,
      );
    } catch (error) {
      this.logger.error(`Erro ao listar transferências: ${error.message}`);
      throw error;
    }
  }

  async updateTransferStatus(
    transferId: string,
    newStatus: string,
    paymentReceiptUrl?: string,
  ) {
    try {
      const transfer = await this.transferModel.findByPk(transferId);

      if (!transfer) {
        throw new NotFoundException(
          `Transferência não encontrada com ID: ${transferId}`,
        );
      }

      const previousStatus = transfer.status;
      transfer.status = newStatus;
      if (paymentReceiptUrl !== undefined) {
        transfer.paymentReceiptUrl = paymentReceiptUrl;
      }
      await transfer.save();

      this.logger.log(
        `Status da transferência ${transferId} atualizado de ${previousStatus} para ${newStatus}`,
      );

      await this.updateCacheAfterChanges(transfer.affiliateId);

      return transfer;
    } catch (error) {
      this.logger.error(
        `Erro ao atualizar status da transferência: ${error.message}`,
      );
      throw error;
    }
  }

  async listTransfersByReferralId(
    referralId: string,
    month?: string,
    year?: string,
  ) {
    try {
      // Definir mês/ano padrão se não informado
      const now = new Date();
      const filterMonth = month ? parseInt(month, 10) : now.getMonth() + 1;
      const filterYear = year ? parseInt(year, 10) : now.getFullYear();

      // Calcular datas de início e fim do mês
      const startDate = new Date(filterYear, filterMonth - 1, 1, 0, 0, 0, 0);
      const endDate = new Date(filterYear, filterMonth, 0, 23, 59, 59, 999);

      const transfers = await this.transferModel.findAll({
        where: {
          referralCode: referralId,
          createdAt: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        },
        order: [['createdAt', 'DESC']],
      });

      if (!transfers || transfers.length === 0) {
        return [];
      }

      const transfersPromises = transfers.map(async (transfer) => {
        let userName = 'Cliente';
        if (transfer.userId) {
          try {
            const user = await this.usersService.findOne(transfer.userId);
            if (user) {
              userName = user.name;
            }
          } catch (error) {
            this.logger.error(
              `Erro ao buscar informações do usuário: ${error.message}`,
            );
          }
        }
        const productName = await this.getProductName(transfer);
        return {
          id: transfer.id,
          userName,
          productName,
          amount: transfer.amount,
          date: transfer.createdAt.toISOString().split('T')[0],
          status: transfer.status,
          paymentReceiptUrl: transfer.paymentReceiptUrl,
        };
      });
      return Promise.all(transfersPromises);
    } catch (error) {
      this.logger.error(
        `Erro ao listar transferências por referralId: ${error.message}`,
      );
      throw error;
    }
  }
}
