import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  HttpStatus,
  HttpCode,
  Query,
} from '@nestjs/common';
import { AffiliateFinancialService } from './affiliate-financial.service';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import {
  CreateAffiliateFinancialDto,
  UpdateAffiliateFinancialDto,
} from './dto/affiliate-financial.dto';
import { Permissions } from '../auth/permissions';
import { PermissionsGuard } from '../auth/permission.guard';
import { DashboardResponseDto } from './dto/dashboard.dto';
import { TransferFilterDto, TransferItemDto } from './dto/transfer-list.dto';

@ApiTags('Affiliate Financial')
@ApiBearerAuth()
@Controller('affiliate-financial')
export class AffiliateFinancialController {
  constructor(
    private readonly affiliateFinancialService: AffiliateFinancialService,
  ) {}

  @ApiOperation({
    summary: 'Obter dados do dashboard do afiliado',
    description: 'Obtém métricas gerais e comissões recentes para o dashboard',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dados do dashboard obtidos com sucesso',
    type: DashboardResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @Get('dashboard')
  getDashboard(@Req() token) {
    return this.affiliateFinancialService.getDashboard(token);
  }

  @ApiOperation({
    summary: 'Listar transferências do afiliado',
    description:
      'Obtém a lista de transferências/comissões do afiliado com filtro por status',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lista de transferências obtida com sucesso',
    type: [TransferItemDto],
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @Get('transfers')
  listTransfers(@Req() token, @Query() filter: TransferFilterDto) {
    return this.affiliateFinancialService.listTransfers(token, filter);
  }

  @ApiOperation({
    summary:
      'Obter informações financeiras de um afiliado específico (somente admin)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Informações financeiras recuperadas com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Informações financeiras não encontradas',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Get(':id')
  findById(@Param('id') id: string) {
    return this.affiliateFinancialService.findById(id);
  }

  @ApiOperation({
    summary: 'Criar ou atualizar informações financeiras do afiliado logado',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Informações financeiras criadas com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dados inválidos',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @Post()
  create(
    @Body() createAffiliateFinancialDto: CreateAffiliateFinancialDto,
    @Req() token,
  ) {
    return this.affiliateFinancialService.create(
      createAffiliateFinancialDto,
      token,
    );
  }

  @ApiOperation({ summary: 'Obter informações financeiras do afiliado logado' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Informações financeiras recuperadas com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Informações financeiras não encontradas',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @Get()
  findOne(@Req() token) {
    return this.affiliateFinancialService.findOneByToken(token);
  }

  @ApiOperation({
    summary: 'Atualizar informações financeiras do afiliado logado',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Informações financeiras atualizadas com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dados inválidos',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @Patch()
  update(
    @Body() updateAffiliateFinancialDto: UpdateAffiliateFinancialDto,
    @Req() token,
  ) {
    return this.affiliateFinancialService.update(
      updateAffiliateFinancialDto,
      token,
    );
  }

  @ApiOperation({
    summary: 'Remover informações financeiras do afiliado logado',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Informações financeiras removidas com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Informações financeiras não encontradas',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('affiliate')
  @Delete()
  @HttpCode(HttpStatus.OK)
  remove(@Req() token) {
    return this.affiliateFinancialService.remove(token);
  }

  @ApiOperation({
    summary: 'Atualizar status de uma transferência (somente admin)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Status da transferência atualizado com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Transferência não encontrada',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Patch('transfers/:id/status')
  updateTransferStatus(
    @Param('id') id: string,
    @Body('status') status: string,
    @Body('paymentReceiptUrl') paymentReceiptUrl?: string,
  ) {
    return this.affiliateFinancialService.updateTransferStatus(
      id,
      status,
      paymentReceiptUrl,
    );
  }

  @ApiOperation({
    summary: 'Listar transferências por referralId',
    description:
      'Obtém a lista de transferências associadas ao referralId informado. Permite filtrar por mês e ano (admin apenas).',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lista de transferências obtida com sucesso',
    type: [TransferItemDto],
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Não autorizado',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @Permissions('admin')
  @Get('transfers/by-referral/:referralId')
  async listTransfersByReferralId(
    @Param('referralId') referralId: string,
    @Query('month') month?: string,
    @Query('year') year?: string,
  ) {
    return this.affiliateFinancialService.listTransfersByReferralId(
      referralId,
      month,
      year,
    );
  }
}
