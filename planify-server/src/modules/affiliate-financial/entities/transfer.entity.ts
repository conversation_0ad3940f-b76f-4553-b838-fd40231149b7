import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Affiliate } from '../../affiliates/entities/affiliate.entity';

@Table({
  tableName: 'Transfers',
})
export class Transfer extends Model<Transfer> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Affiliate)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  affiliateId: string;

  @BelongsTo(() => Affiliate)
  affiliate: Affiliate;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  amount: number;

  @Column({
    type: DataType.ENUM(
      'pending',
      'approved',
      'paid',
      'rejected',
      'failed',
      'canceled',
      'refunded',
    ),
    allowNull: false,
    defaultValue: 'pending',
  })
  status: string;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  paymentId: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  referralCode: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  paymentReceiptUrl: string;
}
