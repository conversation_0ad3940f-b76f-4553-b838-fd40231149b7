import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { Affiliate } from 'src/modules/affiliates/entities/affiliate.entity';

@Table({
  tableName: 'AffiliatesFinancial',
})
export class AffiliateFinancial extends Model<AffiliateFinancial> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Affiliate)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  affiliateId: string;

  @BelongsTo(() => Affiliate)
  affiliate: Affiliate;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: null,
  })
  bankAgency: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: null,
  })
  bankName: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: null,
  })
  bankAccount: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: null,
  })
  pixKey: string;
}
