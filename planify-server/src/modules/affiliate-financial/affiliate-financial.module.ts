import { Module, forwardRef } from '@nestjs/common';
import { AffiliateFinancialService } from './affiliate-financial.service';
import { AffiliateFinancialController } from './affiliate-financial.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { AffiliateFinancial } from './entities/affiliate-financial.entity';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { AffiliatesModule } from '../affiliates/affiliates.module';
import { Transfer } from './entities/transfer.entity';
import { UsersModule } from '../users/users.module';
import { RedisModule } from '../redis/redis.module';
import { PaymentsModule } from '../payments/payments.module';
import { PaymentPlansModule } from '../payment-plans/payment-plans.module';

@Module({
  imports: [
    SequelizeModule.forFeature([AffiliateFinancial, Transfer]),
    forwardRef(() => AuthModule),
    forwardRef(() => AffiliatesModule),
    forwardRef(() => UsersModule),
    forwardRef(() => PaymentsModule),
    forwardRef(() => PaymentPlansModule),
    RedisModule,
    JwtModule,
  ],
  controllers: [AffiliateFinancialController],
  providers: [AffiliateFinancialService],
  exports: [AffiliateFinancialService, SequelizeModule],
})
export class AffiliateFinancialModule {}
