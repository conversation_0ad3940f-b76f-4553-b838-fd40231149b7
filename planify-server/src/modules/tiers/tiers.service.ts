/* eslint-disable prettier/prettier */
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Tier } from './entities/Tier.entity';
import { TierGroup } from './entities/tierGroup.entity';
import { CreateTierDto } from './dto/create-Tier.dto';
import { UpdateTierDto } from './dto/update-Tier.dto';
import { CreateTierGroupDto } from './dto/create-TierGroup.dto';
import { User } from '../users/entities/user.entity';
import { AchievementRecordsService } from '../achievementRecords/achievementRecords.service';

@Injectable()
export class TiersService {
  constructor(
    @InjectModel(TierGroup)
    private readonly tierGroupModel: typeof TierGroup,
    @InjectModel(Tier)
    private readonly tierModel: typeof Tier,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @Inject(forwardRef(() => AchievementRecordsService))
    private achievementRecordsService: AchievementRecordsService,
  ) {}

  createTier(createTierDto: CreateTierDto) {
    return this.tierModel.create(createTierDto);
  }

  findAllTiers() {
    return this.tierModel.findAll();
  }

  findOneTier(id: string) {
    return this.tierModel.findByPk(id);
  }

  updateTier(id: string, updateTierDto: UpdateTierDto) {
    return this.tierModel.update(updateTierDto, { where: { id } });
  }

  removeTier(id: string) {
    return this.tierModel.destroy({ where: { id } });
  }

  // async createTierGroup(createTierGroupDto: CreateTierGroupDto) {
  //   let transaction = null;
  //   try {
  //     const { tiers, userId, description, name } = createTierGroupDto;

  //     transaction = await this.tierGroupModel.sequelize.transaction();

  //     const tierGroup = await this.tierGroupModel.create(
  //       { description, name, userId },
  //       { transaction },
  //     );

  //     const tierData = tiers.map((tier) => ({
  //       ...tier,
  //       TierGroupId: tierGroup.id,
  //     }));

  //     await this.tierModel.bulkCreate(tierData, { transaction });

  //     await transaction.commit();

  //     return tierGroup;
  //   } catch (error) {
  //     if (transaction) await transaction.rollback();
  //     throw error;
  //   }
  // }

  async createTierGroup(createTierGroupDto: CreateTierGroupDto) {
    let transaction = null;

    try {
      const { tiers, userId, description, name } = createTierGroupDto;

      // Inicia uma transação
      transaction = await this.tierGroupModel.sequelize.transaction();

      // Verifica se há um grupo de níveis ativo
      const activeTierGroupExists = await this.tierGroupModel.findOne({
        where: { active: true },
        transaction,
      });

      // Cria o novo grupo de níveis
      const tierGroup = await this.tierGroupModel.create(
        {
          description,
          name,
          userId,
          active: !activeTierGroupExists, // Ativa o novo grupo caso nenhum esteja ativo
        },
        { transaction },
      );

      // Prepara os dados dos níveis
      const tierData = tiers.map((tier) => ({
        ...tier,
        TierGroupId: tierGroup.id,
      }));

      // Insere os níveis associados
      await this.tierModel.bulkCreate(tierData, { transaction });

      // Confirma a transação
      await transaction.commit();

      return tierGroup;
    } catch (error) {
      // Reverte a transação em caso de erro
      if (transaction) await transaction.rollback();
      throw error;
    }
  }

  async findTierGroupsByUser(userId: string): Promise<{
    userName: string;
    currentLevel: string;
    currentScore: number;
    pointsToNextLevel: number;
    nextLevel: string;
    badgeImage?: string;
    thumbnailImage?: string;
  }> {
    // Verificar se o usuário existe
    const user = await this.userModel.findByPk(userId);
    if (!user) {
      throw new BadRequestException('Usuário não encontrado.');
    }

    // Buscar grupos de níveis com seus tiers
    const tierGroups = await this.tierGroupModel.findAll({
      include: [Tier],
    });
    if (tierGroups.length === 0) {
      throw new BadRequestException('Nenhum grupo de níveis encontrado.');
    }

    // Calcular pontuação atual do usuário
    const currentScore =
      await this.achievementRecordsService.getAchievementPointsByUserIdRaw(
        userId,
      );

    // Selecionar o grupo de níveis atual (primeiro encontrado)
    const currentTierGroup = tierGroups[0];
    const tiers = [...currentTierGroup.tiers];

    // Ordenar os níveis por pontuação inicial
    tiers.sort(
      (a, b) => parseInt(a.scoreStart, 10) - parseInt(b.scoreStart, 10),
    );

    // Determinar o nível atual e próximo
    const currentTier = tiers.find(
      (tier) =>
        currentScore >= parseInt(tier.scoreStart, 10) &&
        currentScore <= parseInt(tier.scoreEnd, 10),
    );
    const nextTier = tiers.find(
      (tier) => parseInt(tier.scoreStart, 10) > currentScore,
    );

    // Extrair informações relevantes
    const currentLevel = currentTier ? currentTier.tierName : 'Desconhecido';
    const pointsToNextLevel = nextTier
      ? parseInt(nextTier.scoreStart, 10) - currentScore
      : 0;
    const nextLevel = nextTier ? nextTier.tierName : 'Nenhum próximo nível';

    // Retornar resultado formatado
    return {
      userName: user.name,
      currentLevel,
      currentScore,
      pointsToNextLevel,
      nextLevel,
      badgeImage: currentTier?.badgeImage,
      thumbnailImage: currentTier?.thumbnailImage,
    };
  }

  findAllTierGroups() {
    return this.tierGroupModel.findAll();
  }

  findOneTierGroup(id: string) {
    return this.tierGroupModel.findByPk(id);
  }

  updateTierGroup(id: string, updateTierGroupDto: any) {
    return this.tierGroupModel.update(updateTierGroupDto, { where: { id } });
  }

  removeTierGroup(id: string) {
    return this.tierGroupModel.destroy({ where: { id } });
  }

  async findActiveTierGroup() {
    const tierGroup = await this.tierGroupModel.findOne({
      where: { active: true },
      include: [Tier],
    });

    if (tierGroup) return tierGroup;

    throw new BadRequestException(
      'Nenhum grupo de níveis ativo foi encontrado. Verifique se há um grupo configurado como ativo.',
    );
  }
}
