/* eslint-disable prettier/prettier */
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateTierDto {
  @ApiProperty()
  @IsString()
  TierGroupId: string;

  @ApiProperty()
  @IsString()
  tierName: string;

  @ApiProperty()
  @IsNumber()
  tierOrder: number;

  @ApiProperty()
  @IsString()
  isFinalTier: boolean;

  @ApiProperty()
  @IsString()
  scoreStart: string;

  @ApiProperty()
  @IsString()
  scoreEnd: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsOptional()
  createdAt: Date;

  @ApiProperty()
  @IsOptional()
  badgeImage: string;

  @ApiProperty()
  @IsOptional()
  thumbnailImage: string;
}
