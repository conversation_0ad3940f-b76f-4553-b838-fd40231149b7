/* eslint-disable prettier/prettier */
import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { TierGroup } from './entities/tierGroup.entity';
import { Tier } from './entities/Tier.entity';
import { TiersController } from './tiers.controller';
import { TiersService } from './tiers.service';
import { User } from '../users/entities/user.entity';
import { AchievementRecords } from '../achievementRecords/entities/achievementRecords.entity';
import { AchievementRecordsModule } from '../achievementRecords/achievementRecords.module';

@Module({
  imports: [
    SequelizeModule.forFeature([TierGroup, Tier, AchievementRecords, User]),
    forwardRef(() => AuthModule),
    forwardRef(() => AchievementRecordsModule),
    JwtModule,
  ],
  controllers: [TiersController],
  providers: [TiersService],
})
export class TiersModule {}
