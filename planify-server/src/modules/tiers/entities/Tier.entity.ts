/* eslint-disable prettier/prettier */
// Tier.entity.ts
import {
  Column,
  Model,
  Table,
  ForeignKey,
  DataType,
  BelongsTo,
} from 'sequelize-typescript';
import { TierGroup } from './tierGroup.entity';

@Table({
  tableName: 'Tier',
})
export class Tier extends Model<Tier> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => TierGroup)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  TierGroupId: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  tierName: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  tierOrder: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  isFinalTier: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  scoreStart: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  scoreEnd: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  description: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  createdAt: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  badgeImage: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  thumbnailImage: string;

  @BelongsTo(() => TierGroup)
  tierGroup: TierGroup;
}
