/* eslint-disable prettier/prettier */
import {
  Column,
  Model,
  Table,
  HasMany,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';
import { Tier } from './Tier.entity'; // Certifique-se de que o caminho está correto

@Table({
  tableName: 'TierGroup',
})
export class TierGroup extends Model<TierGroup> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  active: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  description: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  createdAt: Date;

  @HasMany(() => Tier)
  tiers: Tier[];
}
