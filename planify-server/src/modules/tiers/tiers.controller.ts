/* eslint-disable prettier/prettier */
import { ApiTags } from '@nestjs/swagger';
import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { TiersService } from './tiers.service';
import { CreateTierGroupDto } from './dto/create-TierGroup.dto';

@ApiTags('Tiers')
@Controller('tiers')
export class TiersController {
  constructor(private readonly tiersService: TiersService) {}

  @UseGuards(AuthGuard)
  @Get('tiers')
  findAllTiers() {
    return this.tiersService.findAllTiers();
  }

  @UseGuards(AuthGuard)
  @Get('tierGroups')
  findAllTierGroups() {
    return this.tiersService.findAllTierGroups();
  }

  @UseGuards(AuthGuard)
  @Get('tierGroups/:id')
  findOneTierGroup(id: string) {
    return this.tiersService.findOneTierGroup(id);
  }

  @UseGuards(AuthGuard)
  @Get('tiers/:id')
  findOneTier(id: string) {
    return this.tiersService.findOneTier(id);
  }

  @UseGuards(AuthGuard)
  @Get('tiers/:id')
  removeTier(id: string) {
    return this.tiersService.removeTier(id);
  }

  @UseGuards(AuthGuard)
  @Get('tierGroups/:id')
  removeTierGroup(id: string) {
    return this.tiersService.removeTierGroup(id);
  }

  @UseGuards(AuthGuard)
  @Get('tiers')
  createTier(createTierDto: any) {
    return this.tiersService.createTier(createTierDto);
  }

  @UseGuards(AuthGuard)
  @Post('tierGroups')
  createTierGroup(@Body() createTierGroupDto: CreateTierGroupDto) {
    return this.tiersService.createTierGroup(createTierGroupDto);
  }

  @UseGuards(AuthGuard)
  @Get('tierGroupsByUser/:userId')
  findTierGroupsByUser(@Param('userId') userId: string) {
    return this.tiersService.findTierGroupsByUser(userId);
  }

  @UseGuards(AuthGuard)
  @Get('activeTierGroup')
  findActiveTierGroup() {
    return this.tiersService.findActiveTierGroup();
  }
}
