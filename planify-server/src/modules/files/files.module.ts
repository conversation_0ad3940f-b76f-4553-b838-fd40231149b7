import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { FilesController } from './files.controller';
import { FilesService } from './files.service';
import { File } from './entities/file.entity';
import { MinioConfig } from '../../config/minio.config';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    SequelizeModule.forFeature([File]),
    forwardRef(() => AuthModule),
    JwtModule,
  ],
  controllers: [FilesController],
  providers: [FilesService, MinioConfig],
  exports: [FilesService],
})
export class FilesModule {}
