import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from '../../users/entities/user.entity';
import { FileType } from '../enums/file-type.enum';

@Table({
  tableName: 'files',
})
export class File extends Model {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    allowNull: false,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => User)
  user: User;

  @Column({
    type: DataType.ENUM(...Object.values(FileType)),
    allowNull: false,
    defaultValue: FileType.CUSTOM,
  })
  type: FileType;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  originalName: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  filename: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  mimeType: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  bucket: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  path: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  size: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  fileUrl: string;
}
