import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { File } from './entities/file.entity';
import { FileType } from './enums/file-type.enum';
import { MinioConfig } from '../../config/minio.config';
import { getUserId } from 'src/utils';
import { envs } from 'src/utils/envsProxy';

@Injectable()
export class FilesService {
  constructor(
    @InjectModel(File)
    private fileModel: typeof File,
  ) { }

  async create(
    file: Express.Multer.File,
    token: string,
    type: FileType = FileType.CUSTOM,
  ) {
    const loggedUserId = getUserId(token);
    const bucket = process.env.MINIO_BUCKET || 'files';
    const filename = file.originalname;

    const existingFile = await this.fileModel.findOne({
      where: {
        filename,
        userId: loggedUserId,
      },
    });

    const bucketExists = await MinioConfig.client.bucketExists(bucket);
    if (!bucketExists) {
      await MinioConfig.client.makeBucket(bucket);
    }

    await MinioConfig.client.putObject(
      bucket,
      filename,
      file.buffer,
      file.size,
      { 'Content-Type': file.mimetype },
    );

    if (existingFile) {
      await existingFile.update({
        mimeType: file.mimetype,
        size: file.size,
        type,
        updatedAt: new Date(),
      });

      return existingFile;
    }

    const fileData = await this.fileModel.create({
      userId: loggedUserId,
      originalName: file.originalname,
      filename,
      mimeType: file.mimetype,
      bucket,
      path: `${bucket}/${filename}`,
      size: file.size,
      type,
      fileUrl: `${envs.BUCKET_URL}/${bucket}/objects/download?preview=true&prefix=${filename}`,
    });

    return fileData;
  }

  async findAll(type?: FileType) {
    const where = type ? { type } : {};
    const files = await this.fileModel.findAll({ where });

    const filesWithUrls = await Promise.all(
      files.map(async (file) => {
        const url = await MinioConfig.client.presignedGetObject(
          file.bucket,
          file.filename,
          24 * 60 * 60,
        );

        return {
          ...file.toJSON(),
          url,
        };
      }),
    );

    return filesWithUrls;
  }

  async findOne(id: string) {
    const file = await this.fileModel.findByPk(id);
    if (!file) {
      throw new NotFoundException(`File with ID ${id} not found`);
    }
    return file;
  }

  async remove(id: string, token: string) {
    const loggedUserId = getUserId(token);

    const file = await this.fileModel.findOne({
      where: {
        id: id,
        userId: loggedUserId,
      },
    });

    if (!file) {
      throw new NotFoundException(
        `File with ID ${id} not found or you don't have permission to delete it`,
      );
    }

    await MinioConfig.client.removeObject(file.bucket, file.filename);
    await file.destroy();

    return { message: 'File deleted successfully' };
  }

  async getFileUrl(id: string) {
    const file = await this.findOne(id);

    const url = await MinioConfig.client.presignedGetObject(
      file.bucket,
      file.filename,
      24 * 60 * 60,
    );

    return { url };
  }

  async findUserAvatar(userId: string): Promise<string | null> {
    const avatar = await this.fileModel.findOne({
      where: {
        userId,
        type: FileType.AVATAR,
      },
    });

    if (!avatar) {
      return null;
    }

    const url = await MinioConfig.client.presignedGetObject(
      avatar.bucket,
      avatar.filename,
      24 * 60 * 60,
    );

    return url;
  }
}
