import {
  <PERSON>,
  Get,
  Post,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  UseGuards,
  Req,
  Query,
  Body,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FilesService } from './files.service';
import { ApiTags, ApiConsumes, ApiBody, ApiQuery } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { FileType } from './enums/file-type.enum';

@ApiTags('files')
@UseGuards(AuthGuard)
@Controller('files')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        type: {
          type: 'string',
          enum: Object.values(FileType),
          default: FileType.CUSTOM,
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Req() token: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('type') type: FileType = FileType.CUSTOM,
  ) {
    return this.filesService.create(file, token, type);
  }

  @Get()
  @ApiQuery({
    name: 'type',
    required: false,
    enum: FileType,
    description: 'Filtrar por tipo de arquivo',
  })
  findAll(@Query('type') type?: FileType) {
    return this.filesService.findAll(type);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.filesService.findOne(id);
  }

  @Get(':id/download')
  getFileUrl(@Param('id') id: string) {
    return this.filesService.getFileUrl(id);
  }

  @Delete(':id')
  remove(@Req() token: string, @Param('id') id: string) {
    return this.filesService.remove(id, token);
  }
}
