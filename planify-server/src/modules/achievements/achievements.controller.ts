/* eslint-disable prettier/prettier */
import { ApiTags } from '@nestjs/swagger';
import { AchievementsService } from './achievements.service';
import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { CreateAchievementsDto } from './dto/create-achievements.dto';

@ApiTags('Achievements')
@Controller('achievements')
export class AchievementsController {
  constructor(private readonly achievementsService: AchievementsService) {}

  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.achievementsService.findAll();
  }

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createAchievementDto: CreateAchievementsDto) {
    return this.achievementsService.create(createAchievementDto);
  }

  @UseGuards(AuthGuard)
  @Get('/:badgeCode')
  getAchievementByBadgeCode(@Param('badgeCode') badgeCode: string) {
    return this.achievementsService.getAchievementByBadgeCode(badgeCode);
  }
}
