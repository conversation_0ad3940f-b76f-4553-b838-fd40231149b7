/* eslint-disable prettier/prettier */
import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { Achievement } from './entities/achievement.entity';
import { AchievementsController } from './achievements.controller';
import { AchievementsService } from './achievements.service';
import { AchievementRecordsModule } from '../achievementRecords/achievementRecords.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Achievement]),
    forwardRef(() => AuthModule),
    JwtModule,
    forwardRef(() => AchievementRecordsModule),
  ],
  controllers: [AchievementsController],
  providers: [AchievementsService],
  exports: [AchievementsService, SequelizeModule],
})
export class AchievementsModule {}
