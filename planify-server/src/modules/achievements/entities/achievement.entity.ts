/* eslint-disable prettier/prettier */
import { Column, Model, Table, DataType, HasMany } from 'sequelize-typescript';
import { AchievementRecords } from 'src/modules/achievementRecords/entities/achievementRecords.entity';

@Table({
  tableName: 'Achievements',
})
export class Achievement extends Model<Achievement> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  points: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  description: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  image?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  badgeCode: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  position: number;

  @Column({
    type: DataType.STRING(2),
    allowNull: true,
  })
  expirationPeriod: string;

  @HasMany(() => AchievementRecords)
  achievementRecords: AchievementRecords[];
}
