/* eslint-disable prettier/prettier */
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Achievement } from './entities/achievement.entity';
import { InjectModel } from '@nestjs/sequelize';
import { CreateAchievementsDto } from './dto/create-achievements.dto';
import { AchievementRecordsService } from '../achievementRecords/achievementRecords.service';

@Injectable()
export class AchievementsService {
  constructor(
    @InjectModel(Achievement)
    private achievementModel: typeof Achievement,
    @Inject(forwardRef(() => AchievementRecordsService))
    private achievementRecordsService: AchievementRecordsService,
  ) {}

  async create(achievementDto: CreateAchievementsDto) {
    return await this.achievementModel.create(achievementDto);
  }

  async findAll() {
    const achievements = await this.achievementModel.findAll();

    return achievements;
  }

  findOne(id: string) {
    return `This action returns a #${id} achievement`;
  }

  async getAchievementByBadgeCode(badgeCode: string) {
    return await this.achievementModel.findOne({
      where: { badgeCode },
    });
  }
}
