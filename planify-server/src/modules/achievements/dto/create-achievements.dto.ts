/* eslint-disable prettier/prettier */
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateAchievementsDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsNumber()
  points: number;

  @ApiProperty()
  @IsString()
  image?: string;

  @ApiProperty()
  @IsString()
  badgeCode: string;

  @ApiProperty()
  @IsOptional()
  position?: number;

  @ApiProperty()
  @IsOptional()
  expirationPeriod?: string;
}
