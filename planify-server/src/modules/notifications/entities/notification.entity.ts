/* eslint-disable prettier/prettier */
import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { TNotificationType } from 'src/data/@types/TNotificationType';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'Notifications',
})
export class Notifications extends Model<Notifications> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  description: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  isRead: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  readAt?: string;

  @Column({
    type: DataType.ENUM(
      'deleted',
      'updated',
      'created',
      'delayed',
      'default',
      'error',
      'success',
      'delActivity',
      'delPlan',
      'completedActivity',
      'completedPlan',
      'planDelayed',
    ),
    allowNull: true,
    defaultValue: 'default',
  })
  notificationStatus: TNotificationType;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  targetId: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  readCount: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  targetType: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  targetDate?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  hasBeenViewed: boolean;
}
