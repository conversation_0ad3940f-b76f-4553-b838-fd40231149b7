/* eslint-disable prettier/prettier */
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  OnModuleDestroy,
} from '@nestjs/common';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { InjectModel } from '@nestjs/sequelize';
import { User } from '../users/entities/user.entity';
import { Notifications } from './entities/notification.entity';
import { getUserId } from 'src/utils';
import { BehaviorSubject } from 'rxjs';
import { TNotificationType } from 'src/data/@types/TNotificationType';
import { AchievementConditionValidationService } from '../achievementRecords/achievementConditionValidation.service';
import { QueuesService } from '../queues/queues.service';
import { QUEUE_NAMES } from '../queues/constants';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { Response } from 'express';
import { Observable } from 'rxjs';
import { interval, of, merge, from } from 'rxjs';
import {
  map,
  retry,
  debounceTime,
  catchError,
  distinctUntilChanged,
} from 'rxjs/operators';
import { SSEMessageEvent } from './dto/sse-message-event.dto';

@Injectable()
export class NotificationsService implements OnModuleDestroy {
  private readonly maxRetries = 3;
  private readonly retryDelay = 5 * 60 * 1000;
  private notificationEvents = new Map<
    string,
    BehaviorSubject<{ count: number; notifications: Notifications[] }>
  >();
  private notificationBuffer = new Map<string, number>();
  private bufferTimeout = new Map<string, NodeJS.Timeout>();
  private subjectLastActivity = new Map<string, number>();
  private cleanupInterval: NodeJS.Timeout;

  private logger = createModuleLogger(NotificationsService.name);

  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(Notifications)
    private notificationsModel: typeof Notifications,
    @Inject(forwardRef(() => QueuesService))
    private readonly queuesService: QueuesService,
    @Inject(forwardRef(() => AchievementConditionValidationService))
    private medalConditionService: AchievementConditionValidationService,
  ) {
    this.cleanupInterval = setInterval(
      () => this.cleanupInactiveSubjects(),
      30 * 60 * 1000,
    );
  }

  private cleanupInactiveSubjects() {
    const now = Date.now();
    const inactivityThreshold = 2 * 60 * 60 * 1000; // 2 horas

    this.notificationEvents.forEach((subject, userId) => {
      const lastActivity = this.subjectLastActivity.get(userId) || 0;
      if (now - lastActivity > inactivityThreshold) {
        this.logger.info(`Removendo subject inativo para userId: ${userId}`);

        if (!subject.closed) {
          subject.complete();
        }

        this.notificationEvents.delete(userId);
        this.subjectLastActivity.delete(userId);
      }
    });

    this.logger.debug(
      `Cleanup concluído. Subjects ativos: ${this.notificationEvents.size}`,
    );
  }

  updateSubjectActivity(userId: string, isActive: boolean = true): void {
    if (!userId) return;

    if (isActive) {
      this.subjectLastActivity.set(userId, Date.now());
      this.logger.debug(`Subject activity updated for userId: ${userId}`);
    } else {
      this.subjectLastActivity.set(userId, 0); // Timestamp zero indica inativo
      this.logger.info(`Subject marked as inactive for userId: ${userId}`);
    }
  }

  async findByUser(userId: string) {
    const user = await this.userModel.findByPk(userId);

    if (!user) {
      throw new UnauthorizedException({
        message: 'User not found',
      });
    }
  }

  async findAllPaginated(
    page: number,
    limit: number,
    token: string,
    isRead?: string,
    targetType?: string,
  ) {
    const validatedPage = Math.max(1, page);
    const validatedLimit = Math.max(1, limit);
    const parsedIsRead = isRead !== undefined ? isRead === 'true' : undefined;

    const loggedUser = getUserId(token);
    const user = await this.userModel.findByPk(loggedUser);

    if (!user) {
      throw new UnauthorizedException({ message: 'User not found.' });
    }

    const baseFilter = { userId: loggedUser, ...(targetType && { targetType }) };
    const paginationFilter = {
      ...baseFilter,
      ...(parsedIsRead !== undefined && { isRead: parsedIsRead }),
    };

    try {
      const [notifications, allCount, readCount] = await Promise.all([
        this.notificationsModel.findAndCountAll({
          where: paginationFilter,
          order: [['createdAt', 'DESC']],
          limit: validatedLimit,
          offset: (validatedPage - 1) * validatedLimit,
        }),
        this.notificationsModel.count({ where: baseFilter }),
        this.notificationsModel.count({ where: { ...baseFilter, isRead: true } }),
      ]);

      const unreadCount = allCount - readCount;
      return {
        ...notifications,
        allCount,
        stats: {
          total: allCount,
          read: readCount,
          unread: unreadCount,
        },
      };
    } catch (error) {
      this.logger.error('Failed to retrieve notifications', error);
      throw new NotFoundException({
        message: 'Failed to retrieve notifications.',
        error: error.message,
      });
    }
  }

  async markAllAsRead(token: string) {
    const loggedUser = getUserId(token);

    const user = await this.userModel.findByPk(loggedUser);
    if (!user) {
      this.logger.warn(`User not found ${loggedUser}.`);
      throw new UnauthorizedException({ message: 'User not found.' });
    }

    try {
      const notifications = await this.notificationsModel.findAll({
        where: { userId: loggedUser, isRead: false },
      });

      for (const notification of notifications) {
        notification.isRead = true;
        notification.readAt = new Date().toISOString();
        notification.readCount += 1;
        await notification.save();
      }

      let publishSuccess = false;
      try {
        publishSuccess = Boolean(
          await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION_EVENTS, {
            userId: loggedUser,
            action: 'markAllAsRead',
          }),
        );

        if (publishSuccess) {
          this.logger.info(
            `Evento de marcação de todas notificações como lidas publicado com sucesso`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Erro ao publicar evento após marcar todas notificações como lidas: ${error.message}`,
        );
      }

      if (!publishSuccess) {
        this.logger.info(
          `Tentando fallback: emitir evento diretamente para userId: ${loggedUser}`,
        );
        try {
          await this.emitNotificationEvent(loggedUser);
          this.logger.info(
            `Fallback bem-sucedido: evento emitido diretamente para userId: ${loggedUser}`,
          );
        } catch (err) {
          this.logger.error(
            `Erro no fallback ao emitir evento diretamente: ${err.message}`,
          );
        }
      }

      return { success: true, count: notifications.length };
    } catch (error) {
      this.logger.error(error.message);
      throw new NotFoundException({
        message: 'Failed to mark all notifications as read.',
        error: error.message,
      });
    }
  }

  async countUnreadNotifications(userId: string) {
    const user = await this.userModel.findByPk(userId);

    if (!user) {
      this.logger.warn(`User not found ${userId}.`);
      throw new UnauthorizedException({ message: 'User not found.' });
    }

    try {
      const notifications = await this.notificationsModel.findAndCountAll({
        where: {
          userId: userId,
          isRead: false,
        },
      });

      return notifications.count;
    } catch (error) {
      this.logger.error(error.message);
      throw new NotFoundException({
        message: 'Failed to retrieve notifications.',
        error: error.message,
      });
    }
  }

  getNotificationEventSubject(
    userId: string,
  ): BehaviorSubject<{ count: number; notifications: Notifications[] }> {
    if (!this.notificationEvents.has(userId)) {
      this.logger.info(`Creating new notification subject for user: ${userId}`);
      const subject = new BehaviorSubject<{
        count: number;
        notifications: Notifications[];
      }>({ count: 0, notifications: [] });
      this.notificationEvents.set(userId, subject);

      Promise.all([
        this.notificationsModel.findAll({
          where: {
            userId: userId,
            isRead: false,
          },
          order: [['createdAt', 'DESC']],
        }),
        this.countUnreadNotifications(userId),
      ])
        .then(([notifications, count]) => {
          const currentSubject = this.notificationEvents.get(userId);
          if (currentSubject && !currentSubject.closed) {
            currentSubject.next({ count, notifications });
            this.logger.debug(
              `Notificações iniciais carregadas para ${userId}: ${count}`,
            );
          }
        })
        .catch((error) => {
          this.logger.error(
            `Erro ao carregar notificações iniciais para ${userId}:`,
            error,
          );
        });
    }

    this.updateSubjectActivity(userId, true);

    return this.notificationEvents.get(userId);
  }

  handleNotificationEvent(data: {
    userId: string;
    count: number;
    notifications: Notifications[];
  }) {
    if (!data || !data.userId) {
      this.logger.warn('Tentativa de processar evento sem userId');
      return;
    }

    this.logger.info('Handling notification event:', data);

    try {
      const subject = this.getNotificationEventSubject(data.userId);

      setTimeout(() => {
        if (subject && !subject.closed) {
          subject.next({
            count: data.count,
            notifications: data.notifications,
          });
          this.logger.debug(`Evento emitido com sucesso para ${data.userId}`);

          this.updateSubjectActivity(data.userId, true);
        } else {
          this.logger.warn(
            `Subject fechado ou inválido para ${data.userId}, recriando...`,
          );
          this.notificationEvents.delete(data.userId);
          const newSubject = this.getNotificationEventSubject(data.userId);
          newSubject.next({
            count: data.count,
            notifications: data.notifications,
          });
        }
      }, 0);
    } catch (error) {
      this.logger.error(`Erro ao processar evento para ${data.userId}:`, error);
    }
  }

  async emitNotificationEvent(userId: string): Promise<boolean> {
    try {
      if (!userId) {
        this.logger.warn('Tentativa de emitir evento sem userId');
        return false;
      }

      const subject = this.notificationEvents.get(userId);
      const lastActivity = this.subjectLastActivity.get(userId) || 0;

      if (subject && !subject.closed && lastActivity === 0) {
        this.logger.info(`Reativando subject para userId: ${userId}`);
        this.updateSubjectActivity(userId, true);
      }

      const [notificationsResult, countResult] = await Promise.allSettled([
        this.notificationsModel.findAll({
          where: {
            userId: userId,
            isRead: false,
          },
          order: [['createdAt', 'DESC']],
          attributes: [
            'id',
            'userId',
            'title',
            'description',
            'isRead',
            'readAt',
            'notificationStatus',
            'targetId',
            'readCount',
            'targetType',
            'targetDate',
            'hasBeenViewed',
            'createdAt',
            'updatedAt',
          ],
        }),
        this.countUnreadNotifications(userId),
      ]);

      const notifications =
        notificationsResult.status === 'fulfilled'
          ? notificationsResult.value
          : [];

      const count =
        countResult.status === 'fulfilled'
          ? countResult.value
          : notifications
            ? notifications.length
            : 0;

      this.logger.info('Emitting notification event:', {
        userId,
        count,
        notifications:
          notifications?.map((n) => ({
            id: n?.id,
            targetId: n?.targetId,
            targetType: n?.targetType,
          })) || [],
      });

      if (notifications && notifications.length > 0) {
        this.handleNotificationEvent({ userId, count, notifications });
        return true;
      } else if (count > 0) {
        this.logger.warn(
          `Emitindo evento com contagem ${count} mas sem detalhes das notificações para ${userId}`,
        );
        this.handleNotificationEvent({ userId, count, notifications: [] });
        return true;
      } else {
        this.logger.info(
          `Nenhuma notificação não lida para o usuário ${userId}`,
        );

        this.handleNotificationEvent({ userId, count: 0, notifications: [] });
        return true;
      }
    } catch (error) {
      this.logger.error(
        `Erro ao emitir evento de notificação para ${userId}:`,
        error,
      );
      return false;
    }
  }

  async create(createNotificationDto: CreateNotificationDto) {
    const notification = await this.notificationsModel.create({
      ...createNotificationDto,
      targetType:
        createNotificationDto?.targetType || createNotificationDto?.type || '',
      notificationStatus:
        createNotificationDto?.notificationStatus ||
        createNotificationDto?.status,
    });

    if (
      (createNotificationDto.targetType === 'plan' ||
        createNotificationDto.type === 'plan') &&
      createNotificationDto.userId
    ) {
      const loggedUser = createNotificationDto.userId;
      const milestones =
        await this.medalConditionService.checkObjectiveCreationAchievement(
          loggedUser,
        );

      if (milestones) {
        await this.queuesService.publish(QUEUE_NAMES.ACHIEVEMENT, {
          userId: loggedUser,
          badgeCode: milestones,
        });
      }
    }

    return notification;
  }

  async readNotification(id: string) {
    const notification = await this.notificationsModel.findByPk(id);

    if (!notification) {
      this.logger.warn(`Notification not found ${id}.`);
      throw new NotFoundException({ message: 'Notification not found' });
    }

    notification.isRead = true;
    notification.readAt = new Date().toISOString();
    notification.readCount += 1;
    await notification.save();

    let publishSuccess = false;
    try {
      publishSuccess = Boolean(
        await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION_EVENTS, {
          userId: notification.userId,
          action: 'read',
        }),
      );

      if (publishSuccess) {
        this.logger.info(
          `Evento de leitura de notificação publicado com sucesso: ${id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Erro ao publicar evento após marcar notificação como lida: ${error.message}`,
      );
    }

    if (!publishSuccess) {
      this.logger.info(
        `Tentando fallback: emitir evento diretamente para userId: ${notification.userId}`,
      );
      try {
        await this.emitNotificationEvent(notification.userId);
        this.logger.info(
          `Fallback bem-sucedido: evento emitido diretamente para userId: ${notification.userId}`,
        );
      } catch (err) {
        this.logger.error(
          `Erro no fallback ao emitir evento diretamente: ${err.message}`,
        );
      }
    }

    return notification;
  }

  async handleNotificationCreation(
    message: CreateNotificationDto,
    retryCount = 0,
  ) {
    try {
      if (!message || !message.userId) {
        this.logger.warn('Missing critical data');
        throw new Error('Missing critical data');
      }
      await this.notificationsModel.create(message);
      this.logger.info('Notification created successfully:', message);
    } catch (error) {
      this.logger.error('Error creating notification:', error);
      if (retryCount < this.maxRetries) {
        setTimeout(() => {
          this.handleNotificationCreation(message, retryCount + 1);
        }, this.retryDelay);
      }
    }
  }

  async markAsReadByTargetId(activityId: string) {
    const [updatedCount] = await this.notificationsModel.update(
      {
        isRead: true,
        notificationStatus: TNotificationType.DELACTIVITY,
      },
      {
        where: {
          targetType: 'activity',
          targetId: activityId,
        },
      },
    );

    return updatedCount;
  }

  async markAsReadByTargetIdAndType(
    activityId: string,
    notificationType: TNotificationType,
  ) {
    const [updatedCount] = await this.notificationsModel.update(
      {
        isRead: true,
        notificationStatus: notificationType,
      },
      {
        where: { targetType: 'activity', targetId: activityId },
      },
    );

    return updatedCount;
  }

  async createAndMarkAsReadByTargetId(
    id: string,
    userId: string,
    title: string,
    description: string,
    notificationStatus: TNotificationType,
    targetType: string,
  ) {
    const notification = {
      userId,
      title,
      description,
      notificationStatus,
      targetType,
      targetId: id,
    };

    await this.create(notification);

    const [updatedCount] = await this.notificationsModel.update(
      {
        isRead: true,
        notificationStatus,
      },
      {
        where: {
          targetType,
          targetId: id,
        },
      },
    );

    let publishSuccess = false;
    try {
      publishSuccess = Boolean(
        await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION_EVENTS, {
          userId,
          action: 'update',
        }),
      );

      if (publishSuccess) {
        this.logger.info(
          `Evento de atualização de notificação publicado com sucesso para userId: ${userId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Erro ao publicar evento após criar e marcar notificação: ${error.message}`,
      );
    }

    if (!publishSuccess) {
      this.logger.info(
        `Tentando fallback: emitir evento diretamente para userId: ${userId}`,
      );
      try {
        await this.emitNotificationEvent(userId);
        this.logger.info(
          `Fallback bem-sucedido: evento emitido diretamente para userId: ${userId}`,
        );
      } catch (err) {
        this.logger.error(
          `Erro no fallback ao emitir evento diretamente: ${err.message}`,
        );
      }
    }

    return updatedCount;
  }

  async markPlanAndActivitiesNotificationsAsRead(
    planId: string,
    activityIds: string[],
    userId?: string,
  ) {
    await this.notificationsModel.update(
      {
        isRead: true,
        notificationStatus: TNotificationType.DELPLAN,
      },
      {
        where: { targetType: 'plan', targetId: planId },
      },
    );

    for (const activityId of activityIds) {
      await this.notificationsModel.update(
        {
          isRead: true,
          notificationStatus: TNotificationType.DELACTIVITY,
        },
        {
          where: { targetType: 'activity', targetId: activityId },
        },
      );
    }

    if (userId) {
      let publishSuccess = false;
      try {
        publishSuccess = Boolean(
          await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION_EVENTS, {
            userId,
            action: 'markAsRead',
          }),
        );

        if (publishSuccess) {
          this.logger.info(
            `Evento de marcação de notificações como lidas publicado com sucesso para userId: ${userId}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Erro ao publicar evento após marcar notificações como lidas: ${error.message}`,
        );
      }

      if (!publishSuccess) {
        this.logger.info(
          `Tentando fallback: emitir evento diretamente para userId: ${userId}`,
        );
        try {
          await this.emitNotificationEvent(userId);
          this.logger.info(
            `Fallback bem-sucedido: evento emitido diretamente para userId: ${userId}`,
          );
        } catch (err) {
          this.logger.error(
            `Erro no fallback ao emitir evento diretamente: ${err.message}`,
          );
        }
      }
    }
  }

  async getNotificationBytargetId(activity: any) {
    const notification = await this.notificationsModel.findOne({
      where: {
        userId: activity.userId,
        targetId: activity.id,
      },
    });

    return notification;
  }

  async findUnreadNotifications(userId: string): Promise<Notifications[]> {
    return this.notificationsModel.findAll({
      where: {
        userId: userId,
        isRead: false,
      },
      order: [['createdAt', 'DESC']],
    });
  }

  async updateHasBeenViewed(id: string) {
    const notification = await this.notificationsModel.findByPk(id);

    if (!notification) {
      throw new NotFoundException({ message: 'Notification not found' });
    }

    notification.hasBeenViewed = true;
    await notification.save();

    let publishSuccess = false;
    try {
      publishSuccess = Boolean(
        await this.queuesService.publish(QUEUE_NAMES.NOTIFICATION_EVENTS, {
          userId: notification.userId,
          action: 'view',
        }),
      );

      if (publishSuccess) {
        this.logger.info(
          `Evento de visualização de notificação publicado com sucesso: ${id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Erro ao publicar evento de visualização: ${error.message}`,
      );
    }

    if (!publishSuccess) {
      this.logger.info(
        `Tentando fallback: emitir evento diretamente para userId: ${notification.userId}`,
      );
      try {
        await this.emitNotificationEvent(notification.userId);
        this.logger.info(
          `Fallback bem-sucedido: evento emitido diretamente para userId: ${notification.userId}`,
        );
      } catch (err) {
        this.logger.error(
          `Erro no fallback ao emitir evento diretamente: ${err.message}`,
        );
      }
    }

    if (notification.userId) {
      if (notification.targetType === 'plan') {
        const objectiveMilestone =
          await this.medalConditionService.checkObjectiveCreationAchievement(
            notification.userId,
          );
        if (objectiveMilestone) {
          await this.queuesService.publish(QUEUE_NAMES.ACHIEVEMENT, {
            userId: notification.userId,
            badgeCode: objectiveMilestone,
          });
        }

        const completedPlanMilestone =
          await this.medalConditionService.checkCompletedPlanAchievement(
            notification.userId,
          );
        if (completedPlanMilestone) {
          await this.queuesService.publish(QUEUE_NAMES.ACHIEVEMENT, {
            userId: notification.userId,
            badgeCode: completedPlanMilestone,
          });
        }
      }

      if (notification.targetType === 'activity') {
        const taskMilestone =
          await this.medalConditionService.checkMilestoneAchievement(
            notification.userId,
          );
        if (taskMilestone) {
          await this.queuesService.publish(QUEUE_NAMES.ACHIEVEMENT, {
            userId: notification.userId,
            badgeCode: taskMilestone,
          });
        }
      }

      const pointsMilestones =
        await this.medalConditionService.checkPointsAchievementAndTier(
          notification.userId,
        );
      if (pointsMilestones && pointsMilestones.length > 0) {
        for (const milestone of pointsMilestones) {
          await this.queuesService.publish(QUEUE_NAMES.ACHIEVEMENT, {
            userId: notification.userId,
            badgeCode: milestone.badgeCode,
          });
        }
      }
    }

    return notification;
  }

  closeNotificationSubject(userId: string): boolean {
    if (!userId) return false;

    try {
      const subject = this.notificationEvents.get(userId);
      if (subject && !subject.closed) {
        this.logger.info(`Fechando subject para userId: ${userId}`);
        subject.complete();
        this.notificationEvents.delete(userId);
        this.subjectLastActivity.delete(userId);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Erro ao fechar subject para userId: ${userId}`, error);
      return false;
    }
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.notificationEvents.forEach((subject, userId) => {
      if (!subject.closed) {
        this.logger.info(
          `Fechando subject para userId: ${userId} durante shutdown`,
        );
        subject.complete();
      }
    });

    this.notificationEvents.clear();
    this.subjectLastActivity.clear();
    this.notificationBuffer.clear();

    this.bufferTimeout.forEach((timeout) => {
      clearTimeout(timeout);
    });
    this.bufferTimeout.clear();

    this.logger.info('Serviço de notificações limpo durante shutdown');
  }

  async createNotification(createNotificationDto: CreateNotificationDto) {
    try {
      this.logger.info(
        `Criando nova notificação para usuário: ${createNotificationDto.userId}`,
      );
      const notification = await this.create(createNotificationDto);

      if (notification && notification.userId) {
        setTimeout(async () => {
          try {
            await this.emitNotificationEvent(notification.userId);
            this.logger.info(
              `Evento de notificação emitido em tempo real para userId: ${notification.userId}`,
            );
          } catch (err) {
            this.logger.error(
              `Erro ao emitir evento após criar notificação: ${err.message}`,
            );
          }
        }, 100);
      }

      return notification;
    } catch (error) {
      this.logger.error(`Erro ao criar notificação: ${error.message}`);
      throw error;
    }
  }

  async markNotificationAsRead(id: string) {
    try {
      this.logger.info(`Marcando notificação ${id} como lida`);
      const result = await this.readNotification(id);

      if (result && result.userId) {
        setTimeout(async () => {
          try {
            await this.emitNotificationEvent(result.userId);
            this.logger.info(
              `Evento de atualização adicional emitido para userId: ${result.userId}`,
            );
          } catch (err) {
            this.logger.error(
              `Erro ao tentar emitir evento adicional após marcar como lido: ${err.message}`,
            );
          }
        }, 100);
      }

      this.logger.info(`Notificação ${id} marcada como lida com sucesso`);
      return result;
    } catch (error) {
      this.logger.error(
        `Erro ao marcar notificação ${id} como lida: ${error.message}`,
      );
      throw error;
    }
  }

  async markAllNotificationsAsRead(token: string) {
    try {
      this.logger.info('Marcando todas as notificações como lidas');
      const result = await this.markAllAsRead(token);

      if (result && result.success) {
        const userId = getUserId(token);
        if (userId) {
          setTimeout(async () => {
            try {
              await this.emitNotificationEvent(userId);
              this.logger.info(
                `Evento de atualização adicional emitido para userId: ${userId}`,
              );
            } catch (err) {
              this.logger.error(
                `Erro ao tentar emitir evento adicional após marcar todas como lidas: ${err.message}`,
              );
            }
          }, 100);
        }
      }

      this.logger.info(
        `${result.count} notificações marcadas como lidas com sucesso`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Erro ao marcar todas notificações como lidas: ${error.message}`,
      );
      throw error;
    }
  }

  getSSEEvents(
    userId: string,
    response: Response,
  ): Observable<SSEMessageEvent> {
    this.logger.info(`SSE connection established for user: ${userId}`);

    response.setTimeout(0);

    const heartbeat$ = interval(5000).pipe(
      map(() => {
        this.logger.debug(`Sending heartbeat for user: ${userId}`);
        return {
          type: 'heartbeat',
          data: `: heartbeat ${Date.now()}`,
          id: `hb-${Date.now()}`,
        };
      }),
    );

    const initialHeartbeat$ = of({
      type: 'heartbeat',
      data: `: initial-heartbeat ${Date.now()}`,
      id: `hb-init-${Date.now()}`,
    });

    const notificationSubject = this.getNotificationEventSubject(userId);

    const notifications$ = notificationSubject.pipe(
      retry(3),
      distinctUntilChanged((prev, curr) => {
        if (prev.count !== curr.count) return false;
        return (
          JSON.stringify(prev.notifications.map((n) => n.id)) ===
          JSON.stringify(curr.notifications.map((n) => n.id))
        );
      }),
      debounceTime(50),
      map((data) => {
        this.logger.info(
          `SSE notification event for user: ${userId}, count: ${data.count}`,
        );
        const formattedNotifications = data.notifications.map(
          (notification) => ({
            id: notification.id,
            isRead: notification.isRead,
            hasBeenViewed: notification.hasBeenViewed,
            targetType: notification.targetType,
            notificationStatus: notification.notificationStatus,
            title: notification.title,
            description: notification.description,
            creationdate: notification.createdAt,
          }),
        );

        return {
          type: 'message',
          data: JSON.stringify({
            count: data.count,
            notifications: formattedNotifications,
            timestamp: Date.now(),
          }),
          id: `msg-${Date.now()}`,
        };
      }),
      catchError((error) => {
        this.logger.error(
          `Error in SSE notification stream for user ${userId}:`,
          error,
        );
        return of({
          type: 'error',
          data: JSON.stringify({
            count: 0,
            notifications: [],
            error: 'Internal server error',
            timestamp: Date.now(),
          }),
          id: `err-${Date.now()}`,
        });
      }),
    );

    this.logger.info(`Loading initial notifications for user: ${userId}`);
    from(this.findUnreadNotifications(userId))
      .pipe(
        catchError((error) => {
          this.logger.error(
            `Error loading initial notifications for user ${userId}:`,
            error,
          );
          return of([]);
        }),
      )
      .subscribe((notifications) => {
        this.logger.info(
          `Loaded ${notifications.length} initial notifications for user: ${userId}`,
        );
        this.emitNotificationEvent(userId);
      });

    const merged$ = merge(initialHeartbeat$, heartbeat$, notifications$);

    return new Observable<SSEMessageEvent>((observer) => {
      const subscription = merged$.subscribe({
        next: (event) => observer.next(event),
        error: (err) => {
          this.logger.error(`SSE error for user ${userId}:`, err);
          observer.error(err);
        },
        complete: () => {
          this.logger.info(`SSE stream completed for user: ${userId}`);
          observer.complete();
        },
      });

      return () => {
        this.logger.info(`SSE connection closed for user: ${userId}`);
        subscription.unsubscribe();
        this.updateSubjectActivity(userId, false);
      };
    });
  }

  async markNotificationAsViewed(id: string) {
    try {
      this.logger.info(`Marcando notificação ${id} como visualizada`);
      const result = await this.updateHasBeenViewed(id);

      if (result && result.userId) {
        setTimeout(async () => {
          try {
            await this.emitNotificationEvent(result.userId);
            this.logger.info(
              `Evento de atualização adicional emitido para userId: ${result.userId}`,
            );
          } catch (err) {
            this.logger.error(
              `Erro ao tentar emitir evento adicional após marcar como visualizado: ${err.message}`,
            );
          }
        }, 100);
      }

      this.logger.info(
        `Notificação ${id} marcada como visualizada com sucesso`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Erro ao marcar notificação ${id} como visualizada: ${error.message}`,
      );
      throw error;
    }
  }
}
