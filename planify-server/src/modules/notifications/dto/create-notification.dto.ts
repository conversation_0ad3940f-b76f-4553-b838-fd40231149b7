/* eslint-disable prettier/prettier */
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { TNotificationType } from 'src/data/@types/TNotificationType';

export class CreateNotificationDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty()
  @IsNotEmpty()
  title: string;

  @ApiProperty()
  @IsNotEmpty()
  description: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isRead?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readAt?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  notificationStatus?: TNotificationType;

  @ApiProperty()
  @IsString()
  @IsOptional()
  status?: TNotificationType;

  @ApiProperty()
  @IsString()
  @IsOptional()
  targetId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  targetType?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  targetDate?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  hasBeenViewed?: boolean;

  @ApiProperty()
  @IsOptional()
  retryCount?: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  read?: boolean;
}
