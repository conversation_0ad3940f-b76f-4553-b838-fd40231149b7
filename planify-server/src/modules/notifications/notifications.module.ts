/* eslint-disable prettier/prettier */
import { forwardRef, Module } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { AuthModule } from '../auth/auth.module';
import { User } from '../users/entities/user.entity';
import { JwtModule } from '@nestjs/jwt';
import { SequelizeModule } from '@nestjs/sequelize';
import { Notifications } from './entities/notification.entity';
import { AchievementRecordsModule } from '../achievementRecords/achievementRecords.module';
import { QueuesModule } from '../queues/queues.module';
import { QueuesService } from '../queues/queues.service';

@Module({
  imports: [
    SequelizeModule.forFeature([Notifications, User]),
    forwardRef(() => AuthModule),
    JwtModule,
    forwardRef(() => AchievementRecordsModule),
    forwardRef(() => QueuesModule),
  ],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    {
      provide: QueuesService,
      useFactory: (queuesModule: any) => {
        // Se QueuesModule já tiver sido inicializado, use a instância existente
        try {
          return QueuesService.getInstance();
        } catch (e) {
          return new QueuesService();
        }
      },
    },
  ],
  exports: [NotificationsService],
})
export class NotificationsModule {}
