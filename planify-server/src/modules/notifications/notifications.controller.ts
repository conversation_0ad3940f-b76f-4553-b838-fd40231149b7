/* eslint-disable prettier/prettier */
import {
  Controller,
  Post,
  Body,
  Sse,
  Param,
  Patch,
  Query,
  UseGuards,
  Get,
  Req,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { createModuleLogger } from 'src/utils/moduleLogger';
import { Response } from 'express';
import { SSEMessageEvent } from './dto/sse-message-event.dto';

@ApiTags('Users Notifications')
@Controller('notifications')
export class NotificationsController {
  private readonly logger = createModuleLogger(NotificationsController.name);

  constructor(private readonly notificationsService: NotificationsService) {}

  @UseGuards(AuthGuard)
  @Get('/all')
  findAllPaginated(
    @Query('page') page: number,
    @Query('limit') limit: number,
    @Query('isRead') isRead: string,
    @Query('targetType') targetType: string,
    @Req() token: string,
  ) {
    return this.notificationsService.findAllPaginated(
      page,
      limit,
      token,
      isRead,
      targetType,
    );
  }

  @UseGuards(AuthGuard)
  @Get('/countUnread')
  countUnreadNotifications(@Query('userId') userId: string) {
    return this.notificationsService.countUnreadNotifications(userId);
  }

  @Post()
  async create(@Body() createNotificationDto: CreateNotificationDto) {
    return this.notificationsService.createNotification(createNotificationDto);
  }

  @Patch('/:id')
  async readNotification(@Param('id') id: string) {
    return this.notificationsService.markNotificationAsRead(id);
  }

  @UseGuards(AuthGuard)
  @Post('/markAllAsRead')
  async markAllAsRead(@Req() token: string) {
    return this.notificationsService.markAllNotificationsAsRead(token);
  }

  @Sse('/:userId')
  @Header('Cache-Control', 'no-cache, no-transform')
  @Header('Connection', 'keep-alive')
  @Header('Content-Type', 'text/event-stream')
  @Header('X-Accel-Buffering', 'no')
  findOne(@Param('userId') userId: string, @Res() response: Response) {
    return this.notificationsService.getSSEEvents(userId, response);
  }

  @Patch('/:id/hasBeenViewed')
  async updateHasBeenViewed(@Param('id') id: string) {
    return this.notificationsService.markNotificationAsViewed(id);
  }
}
