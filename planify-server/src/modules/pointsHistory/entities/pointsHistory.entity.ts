/* eslint-disable prettier/prettier */
import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { User } from 'src/modules/users/entities/user.entity';

@Table({
  tableName: 'PointsHistory',
})
export class PointsHistory extends Model<PointsHistory> {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  points: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expiration: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  origin: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  status: string;
}
