/* eslint-disable prettier/prettier */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';

import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { CreatePointsHistoryDto } from './dto/create-pointsHistory.dto';
import { PointsHistoryService } from './pointsHistory.service';
import { UpdatePointsHistoryDto } from './dto/update-pointsHistory.dto';

@ApiTags('PointsHistory')
@Controller('pointsHistory')
export class PointsHistoryController {
  constructor(private readonly pointsHistoryService: PointsHistoryService) {}

  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createPointsHistoryDto: CreatePointsHistoryDto) {
    return this.pointsHistoryService.create(createPointsHistoryDto);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll() {
    return this.pointsHistoryService.findAll();
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.pointsHistoryService.findOne(id);
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updatePointsHistoryDto: UpdatePointsHistoryDto,
  ) {
    return this.pointsHistoryService.update(id, updatePointsHistoryDto);
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.pointsHistoryService.remove(id);
  }
}
