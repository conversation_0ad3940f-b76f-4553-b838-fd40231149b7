/* eslint-disable prettier/prettier */
import { Injectable, NotFoundException } from '@nestjs/common';
import { PointsHistory } from './entities/pointsHistory.entity';
import { InjectModel } from '@nestjs/sequelize';
import { CreatePointsHistoryDto } from './dto/create-pointsHistory.dto';
import { UpdatePointsHistoryDto } from './dto/update-pointsHistory.dto';

@Injectable()
export class PointsHistoryService {
  constructor(
    @InjectModel(PointsHistory)
    private pointsHistoryModel: typeof PointsHistory,
  ) {}

  async create(pointsHistoryDto: CreatePointsHistoryDto) {
    const pointsHistory =
      await this.pointsHistoryModel.create(pointsHistoryDto);

    return pointsHistory;
  }

  async findAll() {
    const pointsHistory = await this.pointsHistoryModel.findAll();

    return pointsHistory;
  }

  async findOne(pointsHistoryId: string) {
    const pointsHistory = await this.pointsHistoryModel.findOne({
      where: { id: pointsHistoryId },
    });

    return pointsHistory;
  }

  async update(id: string, pointsHistoryDto: UpdatePointsHistoryDto) {
    const pointsHistory = await this.pointsHistoryModel.findByPk(id);

    if (!pointsHistory) {
      throw new NotFoundException('PointsHistory not found');
    }

    pointsHistory.update(pointsHistoryDto);
    return await pointsHistory.save();
  }

  async remove(id: string) {
    const pointsHistory = await this.pointsHistoryModel.findOne({
      where: { id: id },
    });

    if (!pointsHistory) {
      throw new NotFoundException('PointsHistory not found');
    }

    return await pointsHistory.destroy();
  }
}
