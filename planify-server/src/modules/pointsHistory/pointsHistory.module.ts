/* eslint-disable prettier/prettier */
import { Mo<PERSON><PERSON>, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { PointsHistoryController } from './pointsHistory.controller';
import { PointsHistoryService } from './pointsHistory.service';
import { PointsHistory } from './entities/pointsHistory.entity';

@Module({
  imports: [
    SequelizeModule.forFeature([PointsHistory]),
    forwardRef(() => AuthModule),
    JwtModule,
  ],
  controllers: [PointsHistoryController],
  providers: [PointsHistoryService],
})
export class PointsHistoryModule {}
