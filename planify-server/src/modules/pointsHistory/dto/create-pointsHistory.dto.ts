/* eslint-disable prettier/prettier */
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class CreatePointsHistoryDto {
  @ApiProperty()
  @IsString()
  userId: string;

  @ApiProperty()
  @IsString()
  points: number;

  @ApiProperty()
  @IsString()
  expiration: Date;

  @ApiProperty()
  @IsString()
  origin: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  status: string;
}
