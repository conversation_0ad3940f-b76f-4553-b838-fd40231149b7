import * as dotenv from 'dotenv';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as basicAuth from 'express-basic-auth';
import { createModuleLogger } from './utils/moduleLogger';
import sdk from './utils/tracer';

dotenv.config();

const logger = createModuleLogger('Server');

async function initializeTracing() {
  logger.info('[OpenTelemetry] Iniciando configuração do tracing...');

  try {
    await sdk.start();
    logger.info('[OpenTelemetry] Tracing inicializado com sucesso', {
      service: process.env.OTEL_SERVICE_NAME || 'planify-server',
      endpoint: process.env.OTEL_EXPORTER_OTLP_ENDPOINT,
      environment: process.env.ENV || 'development',
    });
  } catch (error) {
    logger.error('[OpenTelemetry] Erro ao inicializar o tracing:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    // Não vamos interromper a aplicação se o tracing falhar
  }
}

async function bootstrap() {
  logger.info('[Server] Iniciando aplicação...');

  try {
    await initializeTracing();
  } catch (error) {
    logger.error('[Server] Erro ao inicializar o tracing:', error);
  }

  const app = await NestFactory.create(AppModule, {
    cors: true,
  });

  // Configuração para melhorar suporte ao SSE
  const server = app.getHttpServer();
  const expressInstance = server._events.request._events.request;

  // Configurar o servidor para manter as conexões abertas por mais tempo
  if (expressInstance && expressInstance.timeout) {
    logger.info('[Server] Configurando timeout para conexões SSE');
    expressInstance.timeout = 0; // Desativa timeout para conexões SSE
  }

  // Aumentar o limite de ouvintes para evitar vazamento de memória
  server.on('connection', (socket) => {
    // Configurar socket para suportar SSE
    socket.setKeepAlive(true);
    socket.setTimeout(0); // Sem timeout
  });

  app.use(
    '/api/docs*',
    basicAuth({
      challenge: true,
      users: {
        planify: 'Planify#2014',
      },
    }),
  );

  const options = new DocumentBuilder()
    .setTitle('Planify API')
    .setVersion('1.0')
    .addTag('')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('/api/docs', app, document);

  app.useGlobalPipes(new ValidationPipe());

  await app.listen(process.env.PORT || 3000);
  logger.info(
    `Application is running on: http://localhost:${process.env.PORT || 3333}`,
  );
}

bootstrap();
