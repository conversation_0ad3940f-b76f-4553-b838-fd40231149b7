/* eslint-disable prettier/prettier */
import { BadRequestException } from '@nestjs/common';

/**
 * Interface para os parâmetros de paginação
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
}

/**
 * Interface para os parâmetros de consulta paginada
 */
export interface PaginatedQueryOptions<T = any> {
  model: any;
  whereConditions?: T;
  includeOptions?: any[];
  orderOptions?: [string, string][];
  attributes?: string[] | { include?: string[]; exclude?: string[] };
  transformResult?: (item: any) => any;
}

/**
 * Interface para o resultado paginado
 */
export interface PaginatedResult<T = any> {
  totalRecords: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  data: T[];
}

/**
 * Middleware para padronizar a paginação em diferentes serviços
 *
 * @param params Parâmetros de paginação (página e limite)
 * @param options Opções de consulta (modelo, condições, inclusões, etc)
 * @returns Resultado paginado padronizado
 */
export async function paginateResults<T = any, W = any>(
  params: PaginationParams,
  options: PaginatedQueryOptions<W>,
): Promise<PaginatedResult<T>> {
  try {
    let { page = 1, limit = 10 } = params;

    if (page <= 0) page = 1;
    if (limit <= 0) limit = 10;

    const validLimit =
      Number.isInteger(Number(limit)) && Number(limit) > 0 ? Number(limit) : 10;
    const validPage = Number(page) > 0 ? Number(page) : 1;

    const {
      model,
      whereConditions = {},
      includeOptions = [],
      orderOptions = [['createdAt', 'DESC']],
      attributes,
      transformResult,
    } = options;

    if (!model || typeof model.findAndCountAll !== 'function') {
      console.error('Erro: Modelo não inicializado corretamente', {
        model: model?.name || 'Desconhecido',
        hasFind: !!model?.findAndCountAll,
      });
      throw new BadRequestException(
        `Modelo não inicializado corretamente: "${model?.name || 'Desconhecido'}"`,
      );
    }

    const result = await model.findAndCountAll({
      where: whereConditions,
      include: includeOptions,
      order: orderOptions,
      ...(attributes && { attributes }),
      limit: Number(validLimit),
      offset: (validPage - 1) * Number(validLimit),
    });

    const data = transformResult
      ? result.rows.map(transformResult)
      : result.rows;

    return {
      data,
      totalRecords: result.count,
      totalPages: Math.ceil(result.count / validLimit),
      currentPage: validPage,
      pageSize: validLimit,
    };
  } catch (error) {
    console.error('Erro na paginação:', error.message, error.stack);
    throw new BadRequestException(
      `Erro ao recuperar dados paginados: ${error.message}`,
    );
  }
}
