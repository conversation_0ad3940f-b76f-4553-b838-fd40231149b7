/* eslint-disable prettier/prettier */
import { UnauthorizedException } from '@nestjs/common';
import { formatInTimeZone } from 'date-fns-tz';
import { JwtService } from '@nestjs/jwt';

const jwtService = new JwtService({ secret: process.env.JWT_SECRET });

export function validateUser(userId, token) {
  const currentUserId = token?.user?.userId;
  if (userId !== currentUserId) {
    throw new UnauthorizedException({ error: 'Unauthorized request' });
  }

  return currentUserId;
}

export function getUserId(token) {
  const currentUserId = token?.user?.userId || null;

  return currentUserId;
}

export function getAffiliateId(token) {
  // Se não tiver token, retorna null
  if (!token) return null;

  // Caso 1: token é um objeto de requisição completo
  if (token.user) {
    // Se token.user tem affiliateId diretamente
    if (token.user.affiliateId) {
      return token.user.affiliateId;
    }

    // Se token.user.user tem affiliateId (formato aninhado)
    if (token.user.user?.affiliateId) {
      return token.user.user.affiliateId;
    }
  }

  // Caso 2: token.affiliate do AuthGuard
  if (token.affiliate?.id) {
    return token.affiliate.id;
  }

  // Caso 3: token é o próprio token decodificado
  if (token.affiliateId) {
    return token.affiliateId;
  }

  // Caso 4: token é uma string JWT
  if (typeof token === 'string') {
    try {
      const decoded = jwtService.verify(token);
      return decoded?.affiliateId || null;
    } catch (error) {
      console.error(
        'Erro ao decodificar o token para obter affiliateId:',
        error.message,
      );
    }
  }

  // Caso 5: headers de autorização em token (quando token é o objeto req)
  if (token.headers?.authorization) {
    const authHeader = token.headers.authorization;
    if (authHeader.startsWith('Bearer ')) {
      try {
        const tokenStr = authHeader.substring(7);
        const decoded = jwtService.verify(tokenStr);
        return decoded?.affiliateId || null;
      } catch (error) {
        console.error(
          'Erro ao decodificar o token de autorização:',
          error.message,
        );
      }
    }
  }

  return null;
}

export function getUserIdFromToken(token: string) {
  try {
    const decoded: any = jwtService.verify(token);
    const currentUserId = decoded.userId;
    return currentUserId;
  } catch (error) {
    console.error('Erro ao decodificar o token:', error.message);
    return undefined;
  }
}

export const formatInTimeZoneBrazil = (date: Date) => {
  return formatInTimeZone(
    new Date(date.getTime()),
    'America/Sao_Paulo',
    "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
  );
};

export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function partialMask(
  value: string,
  visibleStart = 3,
  visibleEnd = 3,
  maskChar = '*',
): string {
  if (typeof value !== 'string' || !value.trim()) {
    return ''; // Retorna string vazia se o valor não for uma string válida
  }

  if (typeof maskChar !== 'string' || maskChar.length !== 1) {
    return ''; // Retorna string vazia se o caractere de máscara for inválido
  }

  const length = value.length;

  // Evita que os valores de início/fim sejam maiores que a string original
  if (visibleStart < 0) visibleStart = 0;
  if (visibleEnd < 0) visibleEnd = 0;
  if (visibleStart + visibleEnd >= length) {
    return value; // Se não há espaço para mascarar, retorna o valor original
  }

  const maskedPart = maskChar.repeat(length - (visibleStart + visibleEnd));
  return (
    value.substring(0, visibleStart) + maskedPart + value.slice(-visibleEnd)
  );
}

export function filterByPeriod(period: string) {
  const currentDate = new Date();
  const startDate = new Date();

  switch (period) {
    case 'year':
      startDate.setFullYear(currentDate.getFullYear() - 1);
      break;
    case '6 months':
      startDate.setMonth(currentDate.getMonth() - 6);
      break;
    case '3 months':
      startDate.setMonth(currentDate.getMonth() - 3);
      break;
    default:
      throw new Error('Invalid period');
  }

  return {
    startDate,
    currentDate,
  };
}

export function calculateExpirationDate(expiresIn: number): Date | null {
  if (isNaN(expiresIn)) {
    return null;
  }

  if (!expiresIn || expiresIn < 0) {
    return null;
  }

  // Casos especiais: períodos muito longos (como 10 anos ou mais) retornam null
  if (expiresIn >= 3650) {
    // 10 anos = 3650 dias
    return null;
  }

  const expirationDate = new Date();
  expirationDate.setDate(expirationDate.getDate() + expiresIn);
  return expirationDate;
}
