import sdk from './tracer';
import { apmService } from './apmService';

async function initializeTracing(): Promise<void> {
  try {
    await sdk.start();
    apmService.info('Tracing initialized');
  } catch (error: unknown) {
    apmService.error('Error initializing tracing', error);
  }
}

process.on('SIGTERM', async () => {
  try {
    await sdk.shutdown();
    apmService.info('Tracing terminated');
  } catch (error: unknown) {
    apmService.error('Error terminating tracing', error);
  } finally {
    process.exit(0);
  }
});

initializeTracing();
