import axios from 'axios';
import { envs } from './envsProxy';

interface IAlert {
  baseUrl: string;
  title?: string;
  message: string;
  serviceName: string;
  typeMessage?: string;
  aditionalInfos?: any;
}

export async function sendDiscordNotificationAlert({
  baseUrl,
  title,
  message,
  serviceName,
  typeMessage,
  aditionalInfos,
}: IAlert) {
  const payload = {
    embeds: [
      {
        title: `${(!typeMessage && '🟢') || typeMessage === 'success' ? '🟢' : '🔴'} ${title}`,
        description: message,
        fields: [
          {
            name: 'Informações adicionais',
            value: JSON.stringify(aditionalInfos),
          },
          {
            name: 'Ambiente',
            value: envs.ENV,
          },
        ],
        color:
          ((!typeMessage && 65280) ?? typeMessage === 'success')
            ? 65280
            : 16711680,
        timestamp: new Date().toISOString(),
      },
    ],
  };

  console.log('[sendDiscordNotificationAlert]', {
    ...payload,
    serviceName,
  });

  return axios
    .post(baseUrl, payload)
    .then((response) => {
      if (!response) {
        throw new Error('Network response was not ok');
      }
    })
    .catch((error) => {
      console.error('Error sending alert:', error);
    });
}
