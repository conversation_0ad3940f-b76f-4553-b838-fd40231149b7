import { LoggerProvider } from '@opentelemetry/sdk-logs';
import { BatchLogRecordProcessor } from '@opentelemetry/sdk-logs';
import { OTLPLogExporter } from '@opentelemetry/exporter-logs-otlp-http';
import { Resource } from '@opentelemetry/resources';
import * as winston from 'winston';
import { trace } from '@opentelemetry/api';

const logExporter = new OTLPLogExporter({
  url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT
    ? `${process.env.OTEL_EXPORTER_OTLP_ENDPOINT}/v1/logs`
    : 'http://localhost:4318/v1/logs',
});

const loggerProvider = new LoggerProvider({
  resource: new Resource({
    'service.name': 'planify-server',
    ['deployment.environment']: process.env.ENV || 'development',
  }),
});

loggerProvider.addLogRecordProcessor(new BatchLogRecordProcessor(logExporter));

export function createModuleLogger(moduleName: string) {
  const winstonLogger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
    ),
    defaultMeta: {
      service: 'planify-server',
      module: moduleName,
      environment: process.env.ENV || 'development',
    },
    transports: [new winston.transports.Console()],
  });

  const logToOtel = (level: string, message: string, meta?: any) => {
    try {
      const span = trace.getActiveSpan();

      const otelLogger = loggerProvider.getLogger(moduleName);
      otelLogger.emit({
        severityText: level,
        body: message,
        attributes: {
          ...meta,
          traceId: span?.spanContext().traceId,
          spanId: span?.spanContext().spanId,
        },
      });
    } catch (error) {
      console.error('Erro ao enviar log para OpenTelemetry:', error);
    }
  };

  return {
    debug: (message: string, ...meta: any[]) => {
      winstonLogger.debug(message, ...meta);
      logToOtel('DEBUG', message, meta);
    },
    info: (message: string, ...meta: any[]) => {
      winstonLogger.info(message, ...meta);
      logToOtel('INFO', message, meta);
    },
    warn: (message: string, ...meta: any[]) => {
      winstonLogger.warn(message, ...meta);
      logToOtel('WARN', message, meta);
    },
    error: (message: string, ...meta: any[]) => {
      winstonLogger.error(message, ...meta);
      logToOtel('ERROR', message, meta);
    },
  };
}
