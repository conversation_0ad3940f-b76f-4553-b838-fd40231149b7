import dotenv from 'dotenv';
import {
  LoggerProvider,
  SimpleLogRecordProcessor,
} from '@opentelemetry/sdk-logs';
import { OTLPLogExporter } from '@opentelemetry/exporter-logs-otlp-http';
import { OpenTelemetryTransportV3 } from '@opentelemetry/winston-transport';
import { Resource } from '@opentelemetry/resources';
import winston from 'winston';

dotenv.config();
class ApmService {
  private static instance: ApmService;
  private logger: winston.Logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
    ),
    defaultMeta: {
      service: process.env.OTEL_SERVICE_NAME || 'planify-server',
      environment: process.env.ENV || 'development',
    },
  });

  constructor() {
    if (ApmService.instance) {
      return ApmService.instance;
    }

    if (process.env.ENV !== 'production') {
      this.logger.add(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
      );
    }

    ApmService.instance = this;
  }

  public start() {
    try {
      this.info('Starting APM service', {
        endpoint: process.env.OTEL_EXPORTER_OTLP_ENDPOINT,
        service: process.env.OTEL_SERVICE_NAME,
        env: process.env.ENV,
      });

      const loggerProvider = new LoggerProvider({
        resource: new Resource({
          'service.name': process.env.OTEL_SERVICE_NAME || 'planify-server',
          'service.version': '1.0.0',
          'deployment.environment': process.env.ENV || 'development',
        }),
      });

      const collectorOptions = {
        url: `${process.env.OTEL_EXPORTER_OTLP_ENDPOINT}/v1/logs`,
        headers: {},
      };

      const logExporter = new OTLPLogExporter(collectorOptions);
      loggerProvider.addLogRecordProcessor(
        new SimpleLogRecordProcessor(logExporter),
      );

      const otelTransport = new OpenTelemetryTransportV3({
        level: 'info',
        format: winston.format.json(),
      } as winston.transport.TransportStreamOptions);

      this.logger.add(otelTransport);
      this.info('APM service started successfully');
    } catch (error) {
      this.error('Error starting APM service', error);
    }
  }

  // Métodos estáticos para logging
  public static info(message: string, ...meta: any[]): void {
    ApmService.instance?.logger.info(message, ...meta);
  }

  public static error(message: string, ...meta: any[]): void {
    ApmService.instance?.logger.error(message, ...meta);
  }

  public static warn(message: string, ...meta: any[]): void {
    ApmService.instance?.logger.warn(message, ...meta);
  }

  public static debug(message: string, ...meta: any[]): void {
    ApmService.instance?.logger.debug(message, ...meta);
  }

  public info(message: string, ...meta: any[]): void {
    this.logger.info(message, ...meta);
  }

  public error(message: string, ...meta: any[]): void {
    this.logger.error(message, ...meta);
  }

  public warn(message: string, ...meta: any[]): void {
    this.logger.warn(message, ...meta);
  }

  public debug(message: string, ...meta: any[]): void {
    this.logger.debug(message, ...meta);
  }

  public getLogger(): winston.Logger {
    return this.logger;
  }
}

export const apmService = new ApmService();
