import { differenceInYears, isValid } from 'date-fns';

/**
 * Calcula a idade com base em uma data de nascimento.
 * Retorna null se a data for inválida ou a idade for negativa.
 *
 * @param birthDate - Data de nascimento
 * @returns Idade em anos ou ''
 */
export function calculateAge(birthDate?: Date | string | null): string {
  if (!birthDate) return '';

  const date = new Date(birthDate);
  if (!isValid(date)) return '';

  const age = differenceInYears(new Date(), date);
  return age >= 0 ? `${age}` : '';
}
