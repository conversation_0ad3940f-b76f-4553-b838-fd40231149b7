import * as crypto from 'crypto';

interface UrlMapping {
  [key: string]: string;
}

class UrlShortener {
  private static instance: UrlShortener;
  private urlMapping: UrlMapping = {};

  private constructor() {}

  static getInstance(): UrlShortener {
    if (!UrlShortener.instance) {
      UrlShortener.instance = new UrlShortener();
    }
    return UrlShortener.instance;
  }

  private generateHash(url: string): string {
    const hash = crypto.createHash('md5').update(url).digest('hex');
    return hash.substring(0, 8); // Usando apenas os primeiros 8 caracteres do hash
  }

  shortenUrl(longUrl: string, baseUrl: string): string {
    const hash = this.generateHash(longUrl);

    // Armazena o mapeamento
    this.urlMapping[hash] = longUrl;

    // Remove barras duplicadas e constrói a URL curta
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
    return `${cleanBaseUrl}/r/${hash}`;
  }

  getLongUrl(hash: string): string | null {
    return this.urlMapping[hash] || null;
  }
}

export const urlShortener = UrlShortener.getInstance();
