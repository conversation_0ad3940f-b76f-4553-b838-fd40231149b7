export type TUserCredentials = {
  credential: string;
  clientId?: string;
  select_by?: string;
};

export type TDecodedCredential = {
  iss: string;
  azp: string;
  aud: string;
  sub: number;
  email: string;
  email_verified: boolean;
  nbf: number;
  name: string;
  picture: string;
  given_name: string;
  family_name: string;
  iat: number;
  exp: number;
  jti: string;
};

export type TUserReturn = {
  id: string;
  name: string;
  email: string;
  userType: string;
  sharedCode?: string;
};
