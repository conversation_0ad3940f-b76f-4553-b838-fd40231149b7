export type TScopeTags =
  | 'achievements.read'
  | 'tasks.read'
  | 'notes.delete'
  | 'plans.write'
  | 'plans_ai.delete'
  | 'plans_ai.read'
  | 'planner.read'
  | 'tasks.write'
  | 'radar.write'
  | 'plans_ai.write'
  | 'notes.write'
  | 'tips.read'
  | 'notes.read'
  | 'plans.read'
  | 'tasks.delete'
  | 'radar.read'
  | 'plans.delete';

export interface TScope {
  id: string;
  name: string;
  tag: TScopeTags;
  createdAt: string;
  updatedAt: string;
}

export type TScopes = TScope[];
