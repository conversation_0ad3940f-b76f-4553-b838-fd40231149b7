import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { formatInTimeZone } from 'date-fns-tz';

@Injectable()
export class DateFormatInterceptor implements NestInterceptor {
  private formatDateToTimezone(date: Date): string {
    return formatInTimeZone(date, 'America/Sao_Paulo', 'yyyy-MM-dd HH:mm:ss');
  }

  private formatDate(data: any): any {
    if (data === null || data === undefined) {
      return data;
    }

    // Se for um modelo do Sequelize, pegar apenas os dataValues
    if (data && data.dataValues) {
      return this.formatDate(data.dataValues);
    }

    if (data instanceof Date) {
      return this.formatDateToTimezone(data);
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.formatDate(item));
    }

    if (typeof data === 'object') {
      const formattedData = {};
      for (const key in data) {
        if (data[key] instanceof Date) {
          formattedData[key] = this.formatDateToTimezone(data[key]);
        } else if (typeof data[key] === 'object') {
          formattedData[key] = this.formatDate(data[key]);
        } else {
          formattedData[key] = data[key];
        }
      }
      return formattedData;
    }

    return data;
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        return this.formatDate(data);
      }),
    );
  }
}
