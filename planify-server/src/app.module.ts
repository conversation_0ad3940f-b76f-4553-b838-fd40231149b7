import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SequelizeModule } from '@nestjs/sequelize';
import { DatabaseModule } from './config/database/database.module';
import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { DateFormatInterceptor } from './interceptors/date-format.interceptor';
import { sequelizeConfig } from './config/sequelize.config';

// Core Modules
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { QueuesModule } from './modules/queues/queues.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { AchievementRecordsModule } from './modules/achievementRecords/achievementRecords.module';
import { PaymentPlansModule } from './modules/payment-plans/payment-plans.module';
import { PaymentsModule } from './modules/payments/payments.module';
import { UserPlansModule } from './modules/user-plans/user-plans.module';
import { RedisModule } from './modules/redis/redis.module';

// Feature Modules
import { PromptsModule } from './modules/prompts/prompts.module';
import { CategoriesModule } from './modules/categories/categories.module';
import { RadarModule } from './modules/radar/radar.module';
import { SentimentAnalysisModule } from './modules/sentiment-analysis/sentiment-analysis.module';
import { NotesModule } from './modules/notes/notes.module';
import { SubCategoryModule } from './modules/subCategory/subCategory.module';
import { TaskModule } from './modules/task/task.module';
import { SubTaskModule } from './modules/subTask/subTask.module';
import { RecommendationsModule } from './modules/recommendations/recomendations.module';
import { NetworkServicesModule } from './modules/network-services/network-services.module';
import { AchievementsModule } from './modules/achievements/achievements.module';
import { TiersModule } from './modules/tiers/tiers.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { ScopesModule } from './modules/scopes/scopes.module';
import { FilesModule } from './modules/files/files.module';
import { SettingsModule } from './modules/settings/settings.module';
import { CronModule } from './modules/cron/cron.module';
import { LlmModule } from './modules/llm/llm.module';
import { UserSupportModule } from './modules/userSupport/userSupport.module';
import { UserPermissionsModule } from './modules/userPermissions/userPermissions.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { WeightHistoriesModule } from './modules/weight-histories/weight-histories.module';
import { DiscountsModule } from './modules/discounts/discounts.module';
import { AffiliatesModule } from './modules/affiliates/affiliates.module';
import { FinancialModule } from './modules/financial/financial.module';
import { AffiliateFinancialModule } from './modules/affiliate-financial/affiliate-financial.module';
import { PointsHistoryModule } from './modules/pointsHistory/pointsHistory.module';
import { PlanActivitiesModule } from './modules/planActivities/planActivities.module';
import { WeeksModule } from './modules/weeks/weeks.module';

// Entity imports
import { User } from './modules/users/entities/user.entity';
import { UserInfos } from './modules/users/entities/user-profile.entity';
import { Affiliate } from './modules/affiliates/entities/affiliate.entity';
import { AffiliateInfo } from './modules/affiliates/entities/affiliate-info.entity';
import { Referral } from './modules/affiliates/entities/referral.entity';
import { Analytic } from './modules/affiliates/entities/analytic.entity';
import { PaymentPlans } from './modules/payment-plans/entities/payment-plan.entity';
import { Payment } from './modules/payments/entities/payment.entity';
import { Scope } from './modules/scopes/entities/scope.entity';
import { File } from './modules/files/entities/file.entity';
import { Permission } from './modules/permissions/entities/permission.entity';
import { Radar } from './modules/radar/entities/radar.entity';
import { UserPlan } from './modules/user-plans/entities/user-plan.entity';
import { PlanRecommendations } from './modules/recommendations/entities/planRecommendation.entity';
import { PlanWeek } from './modules/weeks/entities/plan-weeks.entity';
import { PlanActivity } from './modules/planActivities/entities/plan-activity.entity';
import { Prompt } from './modules/prompts/entities/prompt.entity';
import { Category } from './modules/categories/entities/category.entity';
import { SubCategory } from './modules/subCategory/entities/subCategory.entity';
import { Notifications } from './modules/notifications/entities/notification.entity';
import { AffiliateFinancial } from './modules/affiliate-financial/entities/affiliate-financial.entity';
import { UserSupport } from './modules/userSupport/entities/userSupport.entity';
import { Discount } from './modules/discounts/entities/discount.entity';
import { DiscountsHistory } from './modules/discounts/entities/discountsHistory.entity';
import { Template } from './modules/templates/entities/template.entity';
import { PointsHistory } from './modules/pointsHistory/entities/pointsHistory.entity';
import { LLMPrompt } from './modules/llm/entities/llm-prompt.entity';
import { RadarRecomendation } from './modules/recommendations/entities/radarRecomendation.entity';
import { ProductivityTips } from './modules/recommendations/entities/productivityTips.entity';
import { TipFeedback } from './modules/recommendations/entities/tipFeedback.entity';
import { SentimentAnalysis } from './modules/sentiment-analysis/entities/sentiment-analysis.entity';
import { SentimentHistory } from './modules/sentiment-analysis/entities/sentiment-history.entity';
import { SentimentAnalysisHistory } from './modules/sentiment-analysis/entities/sentiment-analysis-history.entity';
import { Tier } from './modules/tiers/entities/Tier.entity';
import { TierGroup } from './modules/tiers/entities/tierGroup.entity';
import { Task } from './modules/task/entities/task.entity';
import { SubTask } from './modules/subTask/entities/subTask.entity';
import { WeightHistory } from './modules/weight-histories/entities/weight-history.entity';
import { Settings } from './modules/settings/entities/settings.entity';
import { Note } from './modules/notes/entities/note.entity';
import { AchievementRecords } from './modules/achievementRecords/entities/achievementRecords.entity';
import { Achievement } from './modules/achievements/entities/achievement.entity';
import { Transfer } from './modules/affiliate-financial/entities/transfer.entity';
import { NegotiationsModule } from './modules/negotiations/negotiations.module';
import { Negotiation } from './modules/negotiations/entities/negotiation.entity';
import { NegotiationAffiliate } from './modules/negotiations/entities/negotiation-affiliate.entity';
import { NegotiationPaymentPlan } from './modules/negotiations/entities/negotiation-payment-plan.entity';

@Module({
  imports: [
    // Core Configuration
    SequelizeModule.forRoot({
      ...sequelizeConfig,
      autoLoadModels: false,
      models: [
        User,
        UserInfos,
        Affiliate,
        AffiliateInfo,
        Referral,
        Transfer,
        Analytic,
        PaymentPlans,
        Payment,
        Scope,
        File,
        Permission,
        Radar,
        UserPlan,
        PlanRecommendations,
        PlanWeek,
        PlanActivity,
        Prompt,
        Category,
        SubCategory,
        Notifications,
        AffiliateFinancial,
        UserSupport,
        Discount,
        DiscountsHistory,
        Template,
        PointsHistory,
        LLMPrompt,
        RadarRecomendation,
        ProductivityTips,
        TipFeedback,
        SentimentAnalysis,
        SentimentHistory,
        SentimentAnalysisHistory,
        Tier,
        TierGroup,
        Task,
        SubTask,
        WeightHistory,
        Settings,
        Note,
        AchievementRecords,
        Achievement,
        Negotiation,
        NegotiationAffiliate,
        NegotiationPaymentPlan,
      ],
    }),
    DatabaseModule,
    JwtModule,
    ScheduleModule.forRoot(),

    // Core Modules (ordem específica para resolver dependências)
    AuthModule,
    UsersModule,
    QueuesModule,
    RedisModule,
    NotificationsModule,
    AchievementRecordsModule,
    PaymentPlansModule,
    PaymentsModule,
    UserPlansModule,

    // Feature Modules
    PromptsModule,
    CategoriesModule,
    RadarModule,
    SentimentAnalysisModule,
    NotesModule,
    SubCategoryModule,
    TaskModule,
    SubTaskModule,
    RecommendationsModule,
    NetworkServicesModule,
    AchievementsModule,
    TiersModule,
    PermissionsModule,
    ScopesModule,
    FilesModule,
    SettingsModule,
    CronModule,
    LlmModule,
    UserSupportModule,
    UserPermissionsModule,
    TemplatesModule,
    WeightHistoriesModule,
    DiscountsModule,
    AffiliatesModule,
    FinancialModule,
    AffiliateFinancialModule,
    PointsHistoryModule,
    PlanActivitiesModule,
    WeeksModule,
    NegotiationsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: DateFormatInterceptor,
    },
  ],
})
export class AppModule {}
