FROM node:21-alpine

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./

# Install dependencies including dev dependencies
RUN npm install
RUN npm rebuild bcrypt --build-from-source

# Copy source code
COPY . .

RUN npm run build

# Expose the application port
EXPOSE 3333

# Start the application in development mode with hot-reload
CMD ["npm", "run", "start"]
