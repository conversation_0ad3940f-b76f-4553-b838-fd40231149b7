type: collection.insomnia.rest/5.0
name: Planify
meta:
  id: wrk_4f3029482907442ca869190f7e9f1c09
  created: 1709168534124
  modified: 1743087115359
collection:
  - name: Users
    meta:
      id: fld_4d753efcb31c423fb17b6059bbba0e2e
      created: 1706313287322
      modified: 1745602409601
      sortKey: -1708540602473.2188
    children:
      - url: "{{ _.baseURL }}/users"
        name: create_user
        meta:
          id: req_e12b58edf7414f7aa38980050422b43e
          created: 1706313293683
          modified: 1733834675084
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "name": "<PERSON> Rodrigues",
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users"
        name: get_all_users
        meta:
          id: req_e98f7a6ae0ef405bb38ae50de7c750b7
          created: 1706313497110
          modified: 1732581725138
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
        name: get user details
        meta:
          id: req_ed3d336da844422f885e25ba98eff2d5
          created: 1721318557901
          modified: 1737505995862
          isPrivate: false
          sortKey: -*************
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users/activate"
        name: Activate account
        meta:
          id: req_cbd7ae865f1c473e940ebe5692ad57d8
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.125
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
              "activationCode": "PLFLuYKaZCH"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users"
        name: create_user new
        meta:
          id: req_d6ac1506ecc144e7bf8474f728ad325c
          created: *************
          modified: ********04216
          isPrivate: false
          sortKey: -1706304399570.8906
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "name": "AI",
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv*"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Auth
    meta:
      id: fld_4ced5bfb548f4a46b0c190531b613876
      created: 1706314033786
      modified: 1717713703583
      sortKey: -1708770937165
    children:
      - url: "{{ _.baseURL }}/auth"
        name: Session
        meta:
          id: req_7441d71a73ca40549997f88f77e3ae94
          created: 1706314036537
          modified: 1734120710256
          isPrivate: false
          sortKey: -1706314036537
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
             "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth/google"
        name: Google Session (lucasrod2008...)
        meta:
          id: req_2dace45ce37e4558819b7d5eddf7b2c3
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.5
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
              "credential": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjZjZTExYWVjZjllYjE0MDI0YTQ0YmJmZDFiY2Y4YjMyYTEyMjg3ZmEiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GZpFlXcLwLdo5Fp3ZNjyA3Blps6Io6MUbIHDtAsZoQEv-WefSUPU6-2HPX7zrxdsCDPIwRB8ZywctSBiXcJ-r646m6rR66WEIA9gJzK5D1Q1WifZ-FlhsceAbu3pWvH89NRGEm_3yFtDr6zo6wOFMwGZRnk3UOoP3dhbEIHjSY5OIPUMidgvy45CqBx9A0d1DUkeqBaI_pg4hX6n85nfEDC6UemrwOf8oc2lEursKIR3wSlO1IigyGHo7kbz98kTIKuoljid611SIuweZz474PC99LFvHLle_H8rvqrvSiycUWqZ6vyDXR7Yt4SOoiWTWOYyyXDoglFvI4GhHnlC0g",
              "clientId": "738092587023-bvq1f9n846eo1leukdf7b42eu10p299s.apps.googleusercontent.com",
              "select_by": "btn"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth"
        name: Session By Email when created account
        meta:
          id: req_593db6e1317b4921819d8a952f85d1cc
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.25
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth/refresh"
        name: Refresh token
        meta:
          id: req_70ee7d394f4f436aac775e6b8517a3b7
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.625
        method: PATCH
        body:
          mimeType: application/json
          text: >-
            {
            	"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************.04l5tXRl0nMrcVoASqvC6NqGWVgDc6T7kxntNzaUcP4"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/auth/reset-password"
        name: Update password
        meta:
          id: req_e90940648f414917b083e100bf761212
          created: *************
          modified: *************
          isPrivate: false
          sortKey: -*************.375
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
              "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5OThhYjI0Ni1kNTAyLTQwNDYtOGY0Ny1jZDk2ZDI3N2YwZTMiLCJwYXNzd29yZEhhc2giOiIkMmIkMTAkMGdkV3RDclZTN3lZdWQ0MVF5MkltLlUwTS9CREtzeVIxYTBXcUkzQlFmMGhWYlV2R1pwQmUiLCJ0eXBlIjoicGFzc3dvcmQtcmVzZXQiLCJpYXQiOjE3Mzg3MDMzMzcsImV4cCI6MTczODcwNjkzN30.bowRpLyFWDFdATeiEbEjWC5n0yYQWuA8N3r0O-x8jgU",
            	"password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Default prompts
    meta:
      id: fld_56805a4e107c44528a848c773489a648
      created: 1707778733096
      modified: 1707778733096
      sortKey: -1701929377119
    children:
      - url: "{{ _.baseURL }}/prompts"
        name: create prompt
        meta:
          id: req_c2705af6896e4bc8994f6839ff0133f8
          created: 1707778733097
          modified: 1733931861394
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Corrida",
            	"categoryId": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
            	"description": "Crie um cronograma de ciclismo para [TARGET] em [PERIOD] com [ACTIVITIES_SIZE] atividades por semana",
            	"persona": "Atue como um personal trainer especialista em corrida, ciclismo e maratonas",
            	"returnType": "JSON",
            	"customAction": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts"
        name: get_all
        meta:
          id: req_22da740777fc4b4d908edab4578e16e2
          created: 1707778733098
          modified: 1720099373612
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts/2b55e882-4d8d-4cba-9745-1e13e9ad098c"
        name: get prompt
        meta:
          id: req_032a7d74f14142419f8a3857cb7f4b15
          created: 1719589417466
          modified: 1743097339024
          isPrivate: false
          sortKey: -1706313478330
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts"
        name: create prompt massa muscular
        meta:
          id: req_8f7d647b244e4855b5522dbc237dafd2
          created: 1736604468132
          modified: 1736604727402
          isPrivate: false
          sortKey: -1706277707913.5625
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Ganho de Massa muscular",
            	"categoryId": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
            	"description": "Crie um cronograma para [TARGET] com pelo menos [ACTIVITIES_SIZE] exercícios diários diferentes, porém não limitado a essa quantidade, informando a carga de peso inicial, a quantidades de séries e a orientações necessários para conquistar o objetivo de crescer em musculatura e perda de gordura. Suas referencias iniciais são: peso inicial: [CURRENT_PARAM]; percentual de gordura inicial: [INITIAL_PARAM]; peso alvo: [FINAL_TARGET]; percentual de gordura alvo: [FINAL_TARGET_PARAM]",
            	"persona": "Atue como um personal trainer especialista em treinamento de força e ganho de massa muscular",
            	"returnType": "JSON",
            	"customAction": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts"
        name: create prompt2
        meta:
          id: req_f4fb9e98893c481a923fcc434e2d69da
          created: 1743099153865
          modified: 1743099280838
          isPrivate: false
          sortKey: -1706308848180.4453
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"description": "Desenvolva um plano de metas pessoais para [TARGET] em [PERIOD], com [ACTIVITIES_SIZE] objetivos definidos por semana.",
            	"persona": "Atue como [TIPO DE PERSONA] pessoal especializado em [ESPECIALIZAÇÃO]",
            	"name": "Teste",
            	"categoryId": "9581d214-7ae3-4eb1-9107-e6ce202ee989",
            	"returnType": "JSON"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts/24e44364-7879-46eb-9f1d-51baec5aae38"
        name: Update prompt
        meta:
          id: req_7abc6c0b87184ede81acfdf40980e8d2
          created: 1743108265831
          modified: 1743108276143
          isPrivate: false
          sortKey: -1706311072485.2227
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Corrida"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/prompts/24e44364-7879-46eb-9f1d-51baec5aae38"
        name: Delete prompt
        meta:
          id: req_70eda27154b243db9004628d996e5da6
          created: 1743110929432
          modified: 1743110937254
          isPrivate: false
          sortKey: -1706309960332.834
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Categories
    meta:
      id: fld_22b45989ecfe40d5aa5a2c570e2852a4
      created: 1707778933412
      modified: 1707778933412
      sortKey: -1704121332220.5
    children:
      - url: "{{ _.baseURL }}/categories"
        name: create_category
        meta:
          id: req_2b5c2edc1ee44a57a0805dfca7cd9a46
          created: 1707778933414
          modified: 1733931812656
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Desenvolvimento pessoal",
            	"description": "Categoria destinada a Desenvolvimento pessoal"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/categories"
        name: get_all
        meta:
          id: req_8321ccff5d8d4e61be04187bc251de87
          created: 1707778933415
          modified: 1711470198885
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: User plans
    meta:
      id: fld_06aa99b8274b49b7b44e7452bf6a8a59
      created: 1707845683187
      modified: 1744739606519
      sortKey: -1701381388343.625
    children:
      - name: Errors
        meta:
          id: fld_37c5ed9ebba94501884ccf564d7fbfa4
          created: 1740609136708
          modified: 1740609150413
          sortKey: -1706313371910
      - url: "{{ _.baseURL }}/user-plans"
        name: get_all
        meta:
          id: req_9d544a97250146b7b33e025d70055efd
          created: 1707845683189
          modified: 1712874942339
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans"
        name: create initial plan
        meta:
          id: req_3aeb7ca313534d3f845c75e04aba2289
          created: 1707851594092
          modified: 1742420868436
          isPrivate: false
          sortKey: -1706313371934.4531
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
            	"promptId": "ed06b051-ab6e-425d-82ad-a5bf7b6f5577",
            	"target": "Correr 1KM em 4 semanas",
            	"startedAt": "2024-04-01T10:00:00.000Z",
            	"endAt": "2024-04-13T10:00:00.000Z",
            	"duration": "4 semanas",
            	"hasHealthRisk": false,
            	"acceptedTerms": true,
            	"totalSteps": 4,
            	"currentStep": 1,
            	"pendingSteps": 4,
            	"totalWeeks": 4,
            	"initialParam": "",
            	"currentParam": "",
            	"finalTargetParam": "",
            	"finalTarget": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/4beb1c74-ee18-45cc-b9d6-26344b5a3670"
        name: Get plan Details
        meta:
          id: req_f11f80c365cc424aa28e54c793aab91c
          created: 1707855894157
          modified: 1744138711180
          isPrivate: false
          sortKey: -1706313396950
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/f8a53af9-b2a5-454a-97f7-99fee55c486e"
        name: Delete plan
        meta:
          id: req_1a4542e968ca4cebabf049594d2a809b
          created: 1707861020036
          modified: 1742420893160
          isPrivate: false
          sortKey: -1706313371910.0015
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/week"
        name: create plan empty weeks
        meta:
          id: req_410193cc135d43ecb9d95790008be376
          created: 1707862151524
          modified: 1742420888544
          isPrivate: false
          sortKey: -1706313371910.012
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "14fd66bd-66d1-409b-87d1-ce9bad70a774",
            	"planId": "05898e44-ec64-40d6-a7af-935b4745b81c",
            	"startDate": "2024-02-13T10:00:00.000Z",
            	"endDate": "2024-04-13T10:00:00.000Z",
            	"weekNumber": 10
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity"
        name: create week activity
        meta:
          id: req_e8e8620900414ace9b6a0b0675e3ca5c
          created: 1707862347163
          modified: 1742420889991
          isPrivate: false
          sortKey: -1706313371910.0059
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"weekId": "7dc8806b-d41a-42d1-86e8-10d3f55833b0",
            	"planId": "dcc8fe21-b5dc-47dd-83a6-07f9d6cb6903",
            	"date": "2024-04-13T10:00:00.000Z",
            	"activityDescription": "Corrida",
            	"timeActivity": "30 minutos"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/4beb1c74-ee18-45cc-b9d6-26344b5a3670"
        name: create plan activities
        meta:
          id: req_c72f130c6cf9478a98f975517057f351
          created: 1708033569087
          modified: 1744122306326
          isPrivate: false
          sortKey: -1706313371913.0566
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"weeks": [
            		{
            			"startDate": "2024-02-13T10:00:00.000Z",
            			"endDate": "2024-04-13T10:00:00.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2024-02-15T13:00:00.000Z",
            					"activityDescription": "Caminhada",
            					"timeActivity": "30 minutos",
            					"activityDescription": "Costas e Bíceps",
            					"activityDetails": [
            						{
            							"title": "Puxada Frontal na Barra Fixa",
            							"repetitions": "4 séries de 10-12 repetições",
            							"initialLoad": "assistência se necessário"
            						},
            						{
            							"title": "Remada Curvada com Barra",
            							"repetitions": "3 séries de 10-12 repetições",
            							"initialLoad": "60% do seu 1RM"
            						},
            						{
            							"title": "Remada Unilateral com Halter",
            							"repetitions": "3 séries de 12-15 repetições",
            							"initialLoad": "12kg cada halter"
            						},
            						{
            							"title": "Rosca Direta com Barra",
            							"repetitions": "4 séries de 12-15 repetições",
            							"initialLoad": "50% do seu 1RM"
            						}
            					]
            				}
            			]
            		}
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
            id: pair_b9d9a24296e7450d8eda565a19e904f3
          - name: User-Agent
            value: insomnia/2023.5.8
            id: pair_66f1e352caec41bcbcac10add01ec73f
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans"
        name: create plan with activities
        meta:
          id: req_c9e721583f4749f5860e7441d1624371
          created: 1708033905962
          modified: 1742420871473
          isPrivate: false
          sortKey: -1706313371916.1133
        method: POST
        body:
          mimeType: application/json
          text: |
            {
              "userId": "8c79a9ea-413c-4a6b-aa61-6c06dbc91f00",
            	"promptId": "585f36e9-f8af-4b77-95d3-8653fddbcd64",
              "target": "Correr 5 km em 3 semanas",
              "startedAt": "2024-02-22T00:00:00.000Z",
              "endAt": "2024-03-13T00:00:00.000Z",
              "duration": "3 semanas",
              "totalSteps": 4,
              "currentStep": 1,
              "totalWeeks": 4,
              "pendingSteps": 4,
              "hasHealthRisk": false,
              "acceptedTerms": true,
              "weeks": [
                {
                  "startDate": "2024-02-22T00:00:00.000Z",
                  "endDate": "2024-02-28T00:00:00.000Z",
                  "weekNumber": 1,
                  "activities": [
                    {
                      "date": "2024-02-24T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "20 minutos"
                    },
                    {
                      "date": "2024-02-26T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "25 minutos"
                    },
                    {
                      "date": "2024-02-28T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "30 minutos"
                    }
                  ]
                },
                {
                  "startDate": "2024-02-29T00:00:00.000Z",
                  "endDate": "2024-03-06T00:00:00.000Z",
                  "weekNumber": 2,
                  "activities": [
                    {
                      "date": "2024-03-02T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "25 minutos"
                    },
                    {
                      "date": "2024-03-04T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "30 minutos"
                    },
                    {
                      "date": "2024-03-06T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "35 minutos"
                    }
                  ]
                },
                {
                  "startDate": "2024-03-07T00:00:00.000Z",
                  "endDate": "2024-03-13T00:00:00.000Z",
                  "weekNumber": 3,
                  "activities": [
                    {
                      "date": "2024-03-09T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "30 minutos"
                    },
                    {
                      "date": "2024-03-11T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "35 minutos"
                    },
                    {
                      "date": "2024-03-13T00:00:00.000Z",
                      "activityDescription": "Corrida",
                      "timeActivity": "40 minutos"
                    }
                  ]
                }
              ]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30/all"
        name: get plans by user
        meta:
          id: req_17c0bad578d64af49118835720beaeb3
          created: 1709585072155
          modified: 1740610936019
          isPrivate: false
          sortKey: -1706313421990
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans"
        name: create manual plan
        meta:
          id: req_7402edd492134923bc59c4367250ed84
          created: 1709762085978
          modified: 1742420869967
          isPrivate: false
          sortKey: -1706313371922.2266
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
                "title": "test",
                "userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
                "promptId": "6cae1e86-1a15-4a71-a468-5e072d56bbe6",
                "startAt": "2024-11-25T00:00:00.000Z",
                "startedAt": "2024-11-25T00:00:00.000Z",
                "endAt": "2024-12-30T00:00:00.000Z",
                "duration": "5 semanas",
                "creationStrategy": "manual",
                "target": "Esporte e Saúde 1km em 5 semanas",
                "totalSteps": 5,
                "createEmptyWeeks": true,
                "currentStep": 1,
                "totalWeeks": 5,
                "pendingSteps": 5,
                "hasHealthRisk": false,
                "acceptedTerms": true,
                "activitiesPerWeek": 4,
                "initialParam": "",
                "currentParam": "",
                "finalTargetParam": "",
                "finalTarget": "",
                "planColor": "#E01E36"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/check"
        name: Job schedule generation
        meta:
          id: req_43ceca30e8ac49298ce5ad3efd2f8639
          created: 1709832080846
          modified: 1742420891436
          isPrivate: false
          sortKey: -1706313371910.003
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/infos/dc6e9e2a-ab17-4fd2-845b-ece16161b841"
        name: Update plan infos
        meta:
          id: req_e41a79edc37744eca6270901f362abac
          created: 1710178601291
          modified: 1742420876709
          isPrivate: false
          sortKey: -1706313371910.7642
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"currentStepGeneration": 2
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/d83de865-18e5-4fe9-945b-bc199aeaf8fb"
        name: create plan activity - Single
        meta:
          id: req_5055f681298a4fa197a9613dbfea1a82
          created: 1710806459359
          modified: 1742420874896
          isPrivate: false
          sortKey: -1706313371911.5283
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"category": "Corrida"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/4beb1c74-ee18-45cc-b9d6-26344b5a3670"
        name: Add Generated Plan
        meta:
          id: req_6931d07cb4da4ebdbef88f8771029bb4
          created: 1715716040060
          modified: 1744122445512
          isPrivate: false
          sortKey: -1706313371910.0955
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"weeks": [
            		{
            			"startDate": "2024-06-02T00:00:00.000Z",
            			"endDate": "2024-06-08T00:00:00.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2024-06-02T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-03T08:00:00.000Z",
            					"activityDescription": "Corrida de 5 km",
            					"timeActivity": "30 minutos"
            				},
            				{
            					"date": "2024-06-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-05T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 7 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-06T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-06-07T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 3 km",
            					"timeActivity": "20 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-09T00:00:00.000Z",
            			"endDate": "2024-06-15T00:00:00.000Z",
            			"weekNumber": 2,
            			"activities": [
            				{
            					"date": "2024-06-09T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-10T08:00:00.000Z",
            					"activityDescription": "Corrida de 5 km",
            					"timeActivity": "30 minutos"
            				},
            				{
            					"date": "2024-06-11T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-12T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 7 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-13T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-06-14T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 4 km",
            					"timeActivity": "25 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-16T00:00:00.000Z",
            			"endDate": "2024-06-22T00:00:00.000Z",
            			"weekNumber": 3,
            			"activities": [
            				{
            					"date": "2024-06-16T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-17T08:00:00.000Z",
            					"activityDescription": "Corrida de 6 km",
            					"timeActivity": "35 minutos"
            				},
            				{
            					"date": "2024-06-18T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-19T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 8 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-20T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-06-21T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 4 km",
            					"timeActivity": "25 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-23T00:00:00.000Z",
            			"endDate": "2024-06-29T00:00:00.000Z",
            			"weekNumber": 4,
            			"activities": [
            				{
            					"date": "2024-06-23T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-24T08:00:00.000Z",
            					"activityDescription": "Corrida de 6 km",
            					"timeActivity": "35 minutos"
            				},
            				{
            					"date": "2024-06-25T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-26T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 8 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-06-27T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-06-28T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 5 km",
            					"timeActivity": "30 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-06-30T00:00:00.000Z",
            			"endDate": "2024-07-06T00:00:00.000Z",
            			"weekNumber": 5,
            			"activities": [
            				{
            					"date": "2024-06-30T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "55 minutos"
            				},
            				{
            					"date": "2024-07-01T08:00:00.000Z",
            					"activityDescription": "Corrida de 7 km",
            					"timeActivity": "40 minutos"
            				},
            				{
            					"date": "2024-07-02T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "55 minutos"
            				},
            				{
            					"date": "2024-07-03T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo",
            					"timeActivity": "55 minutos"
            				},
            				{
            					"date": "2024-07-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 30 minutos"
            				},
            				{
            					"date": "2024-07-05T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 5 km",
            					"timeActivity": "30 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-07T00:00:00.000Z",
            			"endDate": "2024-07-13T00:00:00.000Z",
            			"weekNumber": 6,
            			"activities": [
            				{
            					"date": "2024-07-07T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-07-08T08:00:00.000Z",
            					"activityDescription": "Corrida de 7 km",
            					"timeActivity": "40 minutos"
            				},
            				{
            					"date": "2024-07-09T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-07-10T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 9 km",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-07-11T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora 45 minutos"
            				},
            				{
            					"date": "2024-07-12T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 6 km",
            					"timeActivity": "35 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-14T00:00:00.000Z",
            			"endDate": "2024-07-20T00:00:00.000Z",
            			"weekNumber": 7,
            			"activities": [
            				{
            					"date": "2024-07-14T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 5 minutos"
            				},
            				{
            					"date": "2024-07-15T08:00:00.000Z",
            					"activityDescription": "Corrida de 8 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-07-16T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "1 hora 5 minutos"
            				},
            				{
            					"date": "2024-07-17T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 10 km",
            					"timeActivity": "1 hora 5 minutos"
            				},
            				{
            					"date": "2024-07-18T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas"
            				},
            				{
            					"date": "2024-07-19T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 6 km",
            					"timeActivity": "35 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-21T00:00:00.000Z",
            			"endDate": "2024-07-27T00:00:00.000Z",
            			"weekNumber": 8,
            			"activities": [
            				{
            					"date": "2024-07-21T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 10 minutos"
            				},
            				{
            					"date": "2024-07-22T08:00:00.000Z",
            					"activityDescription": "Corrida de 8 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-07-23T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "1 hora 10 minutos"
            				},
            				{
            					"date": "2024-07-24T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 10 km",
            					"timeActivity": "1 hora 10 minutos"
            				},
            				{
            					"date": "2024-07-25T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas 15 minutos"
            				},
            				{
            					"date": "2024-07-26T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 7 km",
            					"timeActivity": "40 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-07-28T00:00:00.000Z",
            			"endDate": "2024-08-03T00:00:00.000Z",
            			"weekNumber": 9,
            			"activities": [
            				{
            					"date": "2024-07-28T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-07-29T08:00:00.000Z",
            					"activityDescription": "Corrida de 9 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-07-30T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-07-31T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 11 km",
            					"timeActivity": "1 hora 15 minutos"
            				},
            				{
            					"date": "2024-08-01T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas 30 minutos"
            				},
            				{
            					"date": "2024-08-02T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 7 km",
            					"timeActivity": "40 minutos"
            				}
            			]
            		},
            		{
            			"startDate": "2024-08-04T00:00:00.000Z",
            			"endDate": "2024-08-10T00:00:00.000Z",
            			"weekNumber": 10,
            			"activities": [
            				{
            					"date": "2024-08-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "1 hora 20 minutos"
            				},
            				{
            					"date": "2024-08-05T08:00:00.000Z",
            					"activityDescription": "Corrida de 9 km",
            					"timeActivity": "50 minutos"
            				},
            				{
            					"date": "2024-08-06T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Ombros e Trapézio",
            					"timeActivity": "1 hora 20 minutos"
            				},
            				{
            					"date": "2024-08-07T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 11 km",
            					"timeActivity": "1 hora 20 minutos"
            				},
            				{
            					"date": "2024-08-08T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "2 horas 45 minutos"
            				},
            				{
            					"date": "2024-08-09T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 8 km",
            					"timeActivity": "45 minutos"
            				}
            			]
            		}
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
            id: pair_f70ba56bac644211bddf0f3d30d40232
          - name: User-Agent
            value: insomnia/2023.5.8
            id: pair_f888c5b3d6f7413ab3218edba1d0a379
          - id: pair_60df8957405046b6a8a4169420bf8840
            name: Bearer
            value: "{{ _.token }}"
            disabled: false
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/8c880252-9004-43c8-b20c-995e0e9a704d"
        name: Add Stream activities
        meta:
          id: req_9bef8a8c7bad4f38a50481a08376c114
          created: 1718925813222
          modified: 1742420883871
          isPrivate: false
          sortKey: -1706313371910.0479
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"weeks": [
            		{
            			"startDate": "2024-09-29T03:00:00.000Z",
            			"endDate": "2024-10-06T02:59:59.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2024-06-02T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Peito e Tríceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-03T08:00:00.000Z",
            					"activityDescription": "Corrida de 5 km",
            					"timeActivity": "30 minutos"
            				},
            				{
            					"date": "2024-06-04T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Costas e Bíceps",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-05T08:00:00.000Z",
            					"activityDescription": "Corrida Intervalo de 7 km",
            					"timeActivity": "45 minutos"
            				},
            				{
            					"date": "2024-06-06T08:00:00.000Z",
            					"activityDescription": "Treino de Força - Pernas",
            					"timeActivity": "1 hora"
            				},
            				{
            					"date": "2024-06-07T08:00:00.000Z",
            					"activityDescription": "Corrida Leve de 3 km",
            					"timeActivity": "20 minutos"
            				}
            			]
            		}
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/close/a1ec38d7-881a-4d37-a898-04fa5094649a"
        name: Close Plan Generation
        meta:
          id: req_9f2cece50e3843cda635df949e211aa7
          created: 1721310996211
          modified: 1742420880049
          isPrivate: false
          sortKey: -1706313371910.191
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/infos/9918033e-c8d3-425c-b0fa-e9f14f693f43"
        name: Update plan status
        meta:
          id: req_060840930f514a708fd2e3d6bf92da08
          created: 1733257452327
          modified: 1742420878432
          isPrivate: false
          sortKey: -1706313371910.382
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"isCompletedGeneration": true,
            	"isGeneratingPlan": false
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/day/2025-02-26T00:00:00"
        name: Get user plan activities
        meta:
          id: req_3c50b900b50845fe99903eea6752458d
          created: 1740608846086
          modified: 1740611051762
          isPrivate: false
          sortKey: -1706313373475
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/task/getAllCurrentAndFutureTasks"
        name: get future tasks
        meta:
          id: req_f0e0f817814945b2b287ca122fd226b1
          created: 1740609186036
          modified: 1740610728067
          isPrivate: false
          sortKey: -1706313384430
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/month/2025-02-01T00:00:00"
        name: get month activities
        meta:
          id: req_97b9969107af4492ae8c974b47cc6cf6
          created: 1740609212983
          modified: 1740611065549
          isPrivate: false
          sortKey: -1706313372301.25
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/pending"
        name: get pending activities
        meta:
          id: req_faa588f95b0f4132a5ffea8597f6950f
          created: 1740609247939
          modified: 1740611056992
          isPrivate: false
          sortKey: -1706313372692.5
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/task/getAllTasksDelay"
        name: get all tasks delay
        meta:
          id: req_d1f2ea1252d84df9ac3e3589cc922728
          created: 1740609266769
          modified: 1740610755274
          isPrivate: false
          sortKey: -1706313378170
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis/history"
        name: sentiment history
        meta:
          id: req_a570ced218aa4e18965b7af077fba95b
          created: 1740609285005
          modified: 1740611070524
          isPrivate: false
          sortKey: -1706313372105.625
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activities/day/2025-02-27%2000:00:00"
        name: user activities day
        meta:
          id: req_0f674223a79c48d3b9ae985a3d58c156
          created: 1740609307770
          modified: 1740611075539
          isPrivate: false
          sortKey: -1706313372007.8125
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/count-by-status/all"
        name: count by status
        meta:
          id: req_71dc606203904943a49e53de5d6c4342
          created: 1740609330065
          modified: 1740610789469
          isPrivate: false
          sortKey: -1706313375040
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL
          }}/recommendations/feedback/8dc37680-bf51-478e-8ecd-f8812ddc5c61"
        name: recommendations feedback
        meta:
          id: req_8751cafa19a142489c8c2ddf1fc365e3
          created: 1740609353718
          modified: 1740611080054
          isPrivate: false
          sortKey: -1706313371958.9062
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/e81d7b1a-0aec-41b5-b3b0-b7290b7f2777"
        name: create plan activities - ERROR
        meta:
          id: req_4bf7245a45244fb8bcfd68b134bf154d
          created: 1742420865705
          modified: 1743696448664
          isPrivate: false
          sortKey: -1706313371910.0022
        method: PATCH
        body:
          mimeType: application/json
          text: >-
            {
            	"weeks": [
            		{
            			"startDate": "2025-01-01T12:00:00.000Z",
            			"endDate": "2025-01-07T11:59:59.000Z",
            			"weekNumber": 1,
            			"activities": [
            				{
            					"date": "2025-01-02T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino leve",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 1,
            							"title": "Ciclismo em ritmo leve",
            							"description": "Pedale em um ritmo confortável, mantendo uma cadência de 60-70 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-04T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino moderado",
            					"timeActivity": "40:00 minutos",
            					"activityDetails": [
            						{
            							"id": 2,
            							"title": "Ciclismo em ritmo moderado",
            							"description": "Pedale em um ritmo moderado, com cadência de 70-80 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-06T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Intercalado",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 3,
            							"title": "Ciclismo intervalado",
            							"description": "Pedale 1 minuto rápido seguido por 2 minutos leves, repetir por 30 minutos.",
            							"status": "incomplete"
            						}
            					]
            				}
            			]
            		},
            		{
            			"startDate": "2025-01-08T12:00:00.000Z",
            			"endDate": "2025-01-14T11:59:59.000Z",
            			"weekNumber": 2,
            			"activities": [
            				{
            					"date": "2025-01-09T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino leve",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 4,
            							"title": "Ciclismo em ritmo leve",
            							"description": "Pedale em um ritmo confortável, mantendo uma cadência de 60-70 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-11T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino moderado",
            					"timeActivity": "40:00 minutos",
            					"activityDetails": [
            						{
            							"id": 5,
            							"title": "Ciclismo em ritmo moderado",
            							"description": "Pedale em um ritmo moderado, com cadência de 70-80 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-13T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Intercalado",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 6,
            							"title": "Ciclismo intervalado",
            							"description": "Pedale 1 minuto rápido seguido por 2 minutos leves, repetir por 30 minutos.",
            							"status": "incomplete"
            						}
            					]
            				}
            			]
            		},
            		{
            			"startDate": "2025-01-15T12:00:00.000Z",
            			"endDate": "2025-01-21T11:59:59.000Z",
            			"weekNumber": 3,
            			"activities": [
            				{
            					"date": "2025-01-16T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino leve",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 7,
            							"title": "Ciclismo em ritmo leve",
            							"description": "Pedale em um ritmo confortável, mantendo uma cadência de 60-70 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-18T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Treino moderado",
            					"timeActivity": "40:00 minutos",
            					"activityDetails": [
            						{
            							"id": 8,
            							"title": "Ciclismo em ritmo moderado",
            							"description": "Pedale em um ritmo moderado, com cadência de 70-80 RPM.",
            							"status": "incomplete"
            						}
            					]
            				},
            				{
            					"date": "2025-01-20T12:00:00.000Z",
            					"activityDescription": "Ciclismo - Intercalado",
            					"timeActivity": "30:00 minutos",
            					"activityDetails": [
            						{
            							"id": 9,
            							"title": "Ciclismo intervalado",
            							"description": "Pedale 1 minuto rápido seguido por 2 minutos leves, repetir por 30 minutos.",
            							"status": "incomplete"
            						}
            					]
            				}
            			]
            		}
            	],
            	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Start generation
    meta:
      id: fld_fc751c98dffd4fef8f9a6d5197795d12
      created: 1708380308306
      modified: 1710254584809
      sortKey: -1700833399568.25
    children:
      - url: "{{ _.baseURL }}/user-plans/generate"
        name: Generate plan
        meta:
          id: req_ffdff206d75e43a3abcb2d7cc8522149
          created: 1708380308307
          modified: 1740605235310
          isPrivate: false
          sortKey: -1708032609473
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
            	"promptId": "6cae1e86-1a15-4a71-a468-5e072d56bbe6",
            	"startAt": "2025-01-01T10:00:00.000Z",
            	"endAt": "2025-02-05T10:00:00.000Z",
            	"duration": "5 Semanas",
            	"target": "Ciclismo de 5KM em 5 semanas",
            	"totalSteps": 5,
            	"currentStep": 1,
            	"totalWeeks": 5,
            	"pendingSteps": 5,
            	"hasHealthRisk": false,
            	"acceptedTerms": true,
            	"creationStrategy": "auto"
            }
        parameters:
          - id: pair_d2bc5d1ee7154dda901fb9d3d0d0ffdc
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://146.190.12.107:8000/start
        name: IA Model generate
        meta:
          id: req_558ed18ac4f24c89b5e95d4a62766402
          created: 1709250979496
          modified: 1717178584732
          isPrivate: false
          sortKey: -1707908469126.25
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "fc554018-c525-4f44-8239-5be4e30f5d1c",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 10/10/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como correr CORRER 5KM EM 40SEMANAS.  Retorne o cronograma completo com apenas 10 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-02-13T10:00:00.000Z\",\"endDate\":\"2024-04-13T10:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\",\"weekNumber\":2,\"activities\":[{\"date\":\"2024-04-15T10:00:00.000Z\",\"activityDescription\":\"Ciclismo\",\"timeActivity\":\"1h:30 horas\"}]}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://localhost:8000/start
        name: IA Model generate COPY
        meta:
          id: req_ef014881e3b24e5b863873c66c13a338
          created: 1709653709756
          modified: 1712882592300
          isPrivate: false
          sortKey: -1707451042505.125
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "6b3dddb9-a6c2-4854-9487-eba92d420ce6",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 01/04/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como correr CORRER 4KM EM 4 SEMANAS.  Retorne o cronograma completo com apenas 4 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://************:5001/start
        name: BOT DEV
        meta:
          id: req_2c22e30792ed4d0c85e120392e22e531
          created: 1710201211671
          modified: 1716241484166
          isPrivate: false
          sortKey: -1707222329194.5625
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "3ff1b8b7-acd0-4249-a9eb-9faa3205ece8",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 05/05/2024, como se fosse uma timeline, inclua tempos de treinos e distancias de como  CORRIDA 21KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]} Os dados retornados devem sempre em PT BR."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://127.0.0.1:8000/start
        name: BOT LOCAL
        meta:
          id: req_a880ed8167b14280854e4ee2ec6aa9a8
          created: 1710255024090
          modified: 1714753200679
          isPrivate: false
          sortKey: -1707107972539.2812
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "3ff1b8b7-acd0-4249-a9eb-9faa3205ece8",
            	"receiver": "http://localhost:3334",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 05/05/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como  CORRIDA 21KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]} Os dados retornados devem sempre em PT BR."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://127.0.0.1:8000/start
        name: BOT LOCAL
        meta:
          id: req_283a0b25f2014888b14a19b6b8096cb2
          created: 1714753262318
          modified: 1714753267217
          isPrivate: false
          sortKey: -1707050794211.6406
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "3ff1b8b7-acd0-4249-a9eb-9faa3205ece8",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 05/05/2024, como se fosse uma timeline, inclua tempos de treinos e distancias, quantidade de água recomendada por dia, de como  CORRIDA 21KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]} Os dados retornados devem sempre em PT BR."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://localhost:5001/start-long
        name: IA Big generation - LOCAL
        meta:
          id: req_78c1296b91a34f7a8f3d8f0b6ee37d23
          created: 1715896028838
          modified: 1715898887485
          isPrivate: false
          sortKey: -1707336685849.8438
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "b7828938-0c4d-45aa-af18-abcf6a4c534b",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 19/05/2024, como se fosse uma timeline, inclua tempos de treinos de como  CORRIDA 42KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: http://localhost:5001/start-long
        name: IA Big generation - LOCAL
        meta:
          id: req_abdd9e2acb9249e09cfc81e640913c65
          created: 1716414132053
          modified: 1716414132053
          isPrivate: false
          sortKey: -1707308096686.0234
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "b7828938-0c4d-45aa-af18-abcf6a4c534b",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Atue como um personal trainer especialista em corrida e maratonas e Elabore um plano em etapas, começando dia 19/05/2024, como se fosse uma timeline, inclua tempos de treinos de como  CORRIDA 42KM EM 20 SEMANAS.  Retorne o cronograma completo com apenas 20 SEMANAS em um JSON com a estrutura parecida com esta: {\"weeks\":[{\"startDate\":\"2024-04-01T10:00:00.000Z\",\"endDate\":\"2024-04-07T08:00:00.000Z\",\"weekNumber\":1,\"activities\":[{\"date\":\"2024-04-13T10:00:00.000Z\",\"activityDescription\":\"Corrida\",\"timeActivity\":\"30:00 minutos\"}]},{\"startDate\":\"2024-04-14T10:00:00.000Z\",\"endDate\":\"2024-04-20T10:00:00.000Z\"}]}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/generate"
        name: Generate muscle mass
        meta:
          id: req_66cc0fa514864bc6b8034aad770b1a04
          created: 1721688922289
          modified: 1721689305178
          isPrivate: false
          sortKey: -1707970539299.625
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
            	"promptId": "0f1861f7-15ec-43f9-a789-1cc6dbeafa64",
            	"startAt": "2024-10-01T10:00:00.000Z",
            	"endAt": "2024-11-05T10:00:00.000Z",
            	"duration": "5 Semanas",
            	"title": "Ganhar massa musuclar em 5 semanas",
            	"target": "Ganhar massa musuclar em 5 semanas",
            	"currentParam": "65Kg",
            	"initialParam": "30%",
            	"finalTarget": "80Kg",
            	"finalTargetParam": "20%",
            	"totalSteps": 5,
            	"currentStep": 1,
            	"totalWeeks": 5,
            	"pendingSteps": 5,
            	"hasHealthRisk": false,
            	"acceptedTerms": true
            }
        parameters:
          - id: pair_d2bc5d1ee7154dda901fb9d3d0d0ffdc
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Profile
    meta:
      id: fld_c31723e388bb41e4a6a2ba7d3e46cffc
      created: 1708557817257
      modified: 1708557830889
      sortKey: -1706313660554
    children:
      - url: "{{ _.baseURL }}/users/profile/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: update user profile
        meta:
          id: req_0ae77ecd34664337be50c5dfdf4218dc
          created: 1708557842804
          modified: 1708559985605
          isPrivate: false
          sortKey: -1708557849234
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"birthDate": "1994-10-20T00:00:00.000Z",
            	"gender": "M",
            	"cep": "78098-282",
            	"city": "Cuiabá",
            	"state": "MT",
            	"location": "Avenida Ayrton Senna da Silva",
            	"locationNumber": "9067",
            	"locationComplement": null,
            	"weight": "65 kg",
            	"currentActivities": 0,
            	"lastCheckup": "1994-10-20T00:00:00.000Z",
            	"canOutExercises": false,
            	"menstualCycle": null,
            	"receiveMessages": true,
            	"authorizeAllChannels":true,
            	"authorizePartners":true,
            	"receiveNewsletter":true,
            	"receiveNotifications":true
            	
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/users/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
        name: get user infos
        meta:
          id: req_e49ff12d3d354b248c91a4c85c054252
          created: 1708560673346
          modified: 1737481924521
          isPrivate: false
          sortKey: -1708557849334
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Radar
    meta:
      id: fld_650a0020d1d34067bf7d5ac039359506
      created: 1709585023851
      modified: 1742418901550
      sortKey: -1700285410792.875
    children:
      - url: "{{ _.baseURL }}/radar"
        name: Create radar infos
        meta:
          id: req_49ed14a1a85e4b3fba75e674daad9723
          created: 1709586927187
          modified: 1741020356346
          isPrivate: false
          sortKey: -1709273850627
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
            	"businessAndCareer": 7,
            	"businessAndCareerComment": null,
            	"moneyAndFinances": 5,
            	"moneyAndFinancesObservation": null,
            	"loveAndFamily": 10,
            	"loveAndFamilyObservation": null,
            	"spirituality": 6,
            	"spiritualityObservation": null,
            	"personalDeveloment": 7,
            	"personalDevelomentObservation": null,
            	"helth": 9,
            	"helthObservation": null,
            	"friendsAndSocial": 6,
            	"friendsAndSocialObservation": null,
            	"relationship": 10,
            	"relationshipObservation": null
            }
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/radar/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: Update radar by UserID
        meta:
          id: req_8d58d263d3d04aa2bf7a8c592969064e
          created: 1709758981819
          modified: 1712877857488
          isPrivate: false
          sortKey: -1709196043787.25
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"businessAndCareer": 7,
            	"businessAndCareerComment": null,
            	"moneyAndFinances": 7,
            	"moneyAndFinancesObservation": null,
            	"loveAndFamily": 10,
            	"loveAndFamilyObservation": null,
            	"spirituality": 7,
            	"spiritualityObservation": null,
            	"personalDeveloment": 6,
            	"personalDevelomentObservation": null,
            	"helth": 10,
            	"helthObservation": null,
            	"friendsAndSocial": 5,
            	"friendsAndSocialObservation": null,
            	"relationship": 10,
            	"relationshipObservation": null
            }
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/radar/2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30"
        name: get radar by user ID
        meta:
          id: req_710313e32dbf4448baaa4bca81e344a9
          created: 1717350164366
          modified: 1740598783515
          isPrivate: false
          sortKey: -1709526722856.1875
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/radar/details/69397bf1-640f-4fb7-a92e-d41260f9a761"
        name: get by radar ID
        meta:
          id: req_63d968b3fc0e47fbaebb70158678bdc8
          created: 1719438196304
          modified: 1744128650326
          isPrivate: false
          sortKey: -1709516997001.2188
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Sentiment analysis
    meta:
      id: fld_ab419379d166462e9bd465ed4e0a4229
      created: 1709758981817
      modified: 1709758981817
      sortKey: -1696301619995.3125
    children:
      - url: "{{ _.baseURL }}/sentiment-analysis"
        name: Create sentiment register
        meta:
          id: req_20164f93ff17420d8faf2ae6a9bf1662
          created: 1709758981818
          modified: 1712877815952
          isPrivate: false
          sortKey: -1709273850627
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "8c79a9ea-413c-4a6b-aa61-6c06dbc91f00",
            	"thermometer": "neutral",
            	"relatedTo": null,
            	"influencedTheEmotionalState": false
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: Sentiments
        meta:
          id: req_7fde7450b7914618afdaf674a56a5678
          created: 1709758981818
          modified: 1712877045692
          isPrivate: false
          sortKey: -1709585077986
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis/history"
        name: Register Sentiment history
        meta:
          id: req_2ee0bf3757d341448fe8255a7a50ab58
          created: 1712875242854
          modified: 1713559528424
          isPrivate: false
          sortKey: -1709040430107.75
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"thermometer": "happy"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/sentiment-analysis/history"
        name: Current sentiment
        meta:
          id: req_646ece39f7ae4591891567e0b8db7032
          created: 1712875309390
          modified: 1712875510243
          isPrivate: false
          sortKey: -1709001526687.875
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: User notes
    meta:
      id: fld_0855ebe4d2924a7e8192904f1e36c15c
      created: 1709760655275
      modified: 1709841059799
      sortKey: -1695803646145.6172
    children:
      - url: "{{ _.baseURL }}/notes"
        name: Create user notes
        meta:
          id: req_44d29c292caa42bfa3a730273e846616
          created: 1709760655275
          modified: 1709761142175
          isPrivate: false
          sortKey: -1709507271146.25
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "8c79a9ea-413c-4a6b-aa61-6c06dbc91f00",
            	"title": "Anotação do dia",
            	"category": "remember",
            	"descriptionNote": "Lorem ipsum dolor assit amet",
            	"cardColor": "#FFFFE3"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notes/8c79a9ea-413c-4a6b-aa61-6c06dbc91f00"
        name: get user notes
        meta:
          id: req_347449ef2cf94192ad0039b0327b70a4
          created: 1709760655276
          modified: 1709933827386
          isPrivate: false
          sortKey: -1709585077986
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notes/d728c821-a05f-4b21-9eee-3dc247c4d6a6"
        name: delete note
        meta:
          id: req_056a1d22c44c4519a8c0ecccce87889a
          created: 1709760730969
          modified: 1709761182250
          isPrivate: false
          sortKey: -1709429464306.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Cron Job
    meta:
      id: fld_73c49e2cbbac4357917bb4d2a28317d1
      created: 1709841042885
      modified: 1721309535012
      sortKey: -1695352357344.3308
    children:
      - url: "{{ _.baseURL }}/user-plans/check"
        name: Job review generation
        meta:
          id: req_36a1d528086f4a70a4833a3c1bcb7ab6
          created: 1709841031766
          modified: 1742419289119
          isPrivate: false
          sortKey: -1709841070187
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations"
        name: Job Recommendations
        meta:
          id: req_fea367d06fef4e08a5c7fc1d2974b83d
          created: 1716998226583
          modified: 1717358702114
          isPrivate: false
          sortKey: -1709715487572.5
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Activities
    meta:
      id: fld_5c49458b976c460ab698fa77f1e30c99
      created: 1710256503330
      modified: 1710256503330
      sortKey: -1703025354669.75
    children:
      - url: "{{ _.baseURL }}/user-plans/activity"
        name: create activity
        meta:
          id: req_8a74eae750974a7ca98803a44571b2e4
          created: 1710256503330
          modified: 1741024711186
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
                "userPlanId": "2c040eb5-e7ee-4f34-85b9-65ee5cc7299c",
                "weekId": "2c03a94a-ee96-4523-b8d7-8da8829d47d5",
                "date": "2024-03-26T16:42:08.000Z",
                "observation": "OBS: aqu vai a observação",
                "timeActivity": "tempo de atividade aqui",
                "category": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
                "activityDescription": "Teste limite de atividades"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/868e86b7-a016-4adc-a117-57af5c12475e"
        name: get activity
        meta:
          id: req_f14b1c6f469d4b8badbb38206abcb74b
          created: 1710256503331
          modified: 1711478411604
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/d83de865-18e5-4fe9-945b-bc199aeaf8fb"
        name: Update activity
        meta:
          id: req_df0e1891400a4c0590eebc7ab593f582
          created: 1710372628346
          modified: 1710809303342
          isPrivate: false
          sortKey: -1706313459550
        method: PUT
        body:
          mimeType: application/json
          text: |-
            {
            	"activityStatus": "loading",
            	"category": "Corrida"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-plans/activity/868e86b7-a016-4adc-a117-57af5c12475e"
        name: Delete activity
        meta:
          id: req_e4062951f27e4607ba93fd4718996f4b
          created: 1711400344617
          modified: 1711481295062
          isPrivate: false
          sortKey: -1706170941284.25
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Recommendations
    meta:
      id: fld_54f215fab41245eab37b9571102c9b4e
      created: 1716400864914
      modified: 1717532212657
      sortKey: -1698293515394.0938
    children:
      - url: "{{ _.baseURL }}/recommendations/7c9d78d6-a38e-4c01-90da-50e2d08db6bd"
        name: Get recommendation by radarID
        meta:
          id: req_e4be8e6502e84a6aa006a7901b92d9d3
          created: 1716400864915
          modified: 1719418610955
          isPrivate: false
          sortKey: -1709585077986
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/new/radar"
        name: Save generated radar recommendations
        meta:
          id: req_0b70891ef12d4ef3bd346828fc7ef9d4
          created: 1716420448241
          modified: 1721324256072
          isPrivate: false
          sortKey: -1709215495497.1875
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
               "userId":"b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
               "radarId":"91f1ebd7-ba87-44d2-a5a8-db7e47d4e837",
               "recommendations":[
                  {
                     "subject":"Negócios e Carreira",
                     "note":5,
                     "comment":"",
                     "analysis":"Sua pontuação em negócios e carreira indica que há aspectos significativos a serem melhorados. Talvez você esteja enfrentando desafios que impedem seu progresso profissional ou sinta que não está alcançando seu potencial completo.",
                     "suggestions":[
                        {
                           "title":"Definição de metas claras",
                           "description":"Estabeleça metas de curto, médio e longo prazo para sua carreira. Isso inclui metas específicas, mensuráveis, alcançáveis, relevantes e temporais (SMART). Revise-as periodicamente e ajuste conforme necessário para manter seu foco e motivação."
                        },
                        {
                           "title":"Desenvolvimento de habilidades",
                           "description":"Identifique as habilidades que são mais valorizadas em sua área e dedique-se a desenvolvê-las. Isso pode incluir participar de cursos, workshops ou até mesmo buscar um mentor que possa guiá-lo em sua jornada profissional."
                        },
                        {
                           "title":"Networking",
                           "description":"Expandir sua rede de contatos pode abrir portas para novas oportunidades de carreira. Participe de eventos da indústria, seminários e outras atividades onde você possa conhecer e interagir com profissionais do seu campo."
                        }
                     ]
                  },
                  {
                     "subject":"Dinheiro e Finanças",
                     "note":7,
                     "comment":"",
                     "analysis":"Sua pontuação em dinheiro e finanças é boa, mas há espaço para melhorias. A gestão financeira eficaz é crucial para a segurança e o crescimento pessoal.",
                     "suggestions":[
                        {
                           "title":"Orçamento pessoal",
                           "description":"Elabore um orçamento detalhado que contemple todos os seus gastos. Isso ajudará a entender para onde seu dinheiro está indo e onde você pode cortar despesas."
                        },
                        {
                           "title":"Investimento",
                           "description":"Explore diferentes formas de investimento que se alinhem com seus objetivos financeiros e seu perfil de risco. Isso pode incluir ações, fundos de investimento, imóveis, entre outros."
                        },
                        {
                           "title":"Educação financeira",
                           "description":"Invista tempo em aprender sobre finanças pessoais. Existem muitos recursos disponíveis online, como cursos, blogs, podcasts e livros que podem ajudá-lo a tomar decisões financeiras mais informadas."
                        }
                     ]
                  },
                  {
                     "subject":"Espiritualidade",
                     "note":6,
                     "comment":"",
                     "analysis":"Sua pontuação em espiritualidade sugere que há uma necessidade de maior conexão com suas crenças ou práticas espirituais. Isso pode ser um componente importante para o seu bem-estar geral.",
                     "suggestions":[
                        {
                           "title":"Exploração espiritual",
                           "description":"Dedique tempo para explorar e entender suas crenças espirituais. Isso pode envolver leitura, meditação, ou participação em grupos de discussão ou serviços espirituais."
                        },
                        {
                           "title":"Prática regular",
                           "description":"Estabeleça uma prática espiritual regular. Isso pode incluir meditação diária, oração, yoga ou outras práticas que ajudem a cultivar sua vida espiritual."
                        },
                        {
                           "title":"Retiros espirituais",
                           "description":"Considere participar de um retiro espiritual. Isso pode proporcionar uma oportunidade de imersão em suas práticas espirituais e oferecer uma nova perspectiva sobre sua jornada espiritual."
                        }
                     ]
                  }
               ],
               "finalConsideration":"Lucas, analisando as suas pontuações, você mostra forte desempenho em algumas áreas vitais como amor e família, saúde e relacionamentos, indicando um equilíbrio saudável em sua vida pessoal. No entanto, há áreas como negócios e carreira, e espiritualidade onde você pode se beneficiar significativamente de um investimento focado. As sugestões fornecidas visam ajudá-lo a fortalecer essas áreas e potencializar seu desenvolvimento pessoal e profissional. Continue se esforçando e explorando novas oportunidades de crescimento. Sucesso!"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/productivity"
        name: Save productivity Tip
        meta:
          id: req_ce65350d38b042648b1d229f85181c1a
          created: 1717529247366
          modified: 1722445081871
          isPrivate: false
          sortKey: -1709198475100.9922
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"description": "Defina 3 tarefas urgentes e importantes diariamente, e priorize-as em ordem de importância. Retire os problemas mais pequenos da cabeça e foque em solucionar as grandes questões. - Don Tolle"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/tipProductivity"
        name: Get Random Productivity Recommendation
        meta:
          id: req_168a8e6395244325beaf76b0039cdc69
          created: 1717529282194
          modified: 1717534150819
          isPrivate: false
          sortKey: -1709200906714.7344
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/recommendations/new/plan"
        name: Save generated plan recommendation
        meta:
          id: req_608395e9f7e94be7ae8688bb71adaa2d
          created: 1717780347183
          modified: 1719426322081
          isPrivate: false
          sortKey: -1709198475200.9922
        method: POST
        body:
          mimeType: application/json
          text: "{'planId': '7d4c0106-7012-4ee3-917d-47cbfd30adaf', 'userId':
            'd4ba143e-9849-42e3-8290-65886faf508e', 'recommendations':
            [{'title': 'Mantenha-se consistente', 'description': 'Para atingir
            seu objetivo de correr 1KM em 4 semanas, é fundamental manter a
            consistência nos treinos.'}, {'title': 'Aqueça antes de correr',
            'description': 'Realize um bom aquecimento antes de cada corrida
            para evitar lesões e melhorar seu desempenho.'}, {'title': 'Varie a
            intensidade', 'description': 'Incorpore corridas em diferentes
            intensidades para desenvolver resistência e velocidade.'}, {'title':
            'Hidrate-se adequadamente', 'description': 'Não se esqueça de beber
            água antes, durante e depois dos treinos para manter seu corpo
            hidratado.'}, {'title': 'Descanse para se recuperar', 'description':
            'O descanso é tão importante quanto o treino. Respeite os dias de
            recuperação para fortalecer os músculos.'}, {'title': 'Monitore seu
            progresso', 'description': 'Mantenha um registro do seu desempenho a
            cada corrida para acompanhar sua evolução ao longo das 4 semanas.'},
            {'title': 'Encontre um parceiro de corrida', 'description': 'Correr
            com um companheiro pode ser motivador e ajudar no cumprimento dos
            treinos e metas.'}, {'title': 'Estabeleça novos desafios',
            'description': 'Ao final das 4 semanas, defina um novo objetivo para
            continuar mantendo sua motivação em alta.'}, {'title': 'Cuide da sua
            alimentação', 'description': 'Uma dieta equilibrada auxilia no
            desempenho esportivo e na recuperação após os treinos.'}, {'title':
            'Aproveite o processo', 'description': 'Divirta-se durante as
            corridas e aproveite o momento para sentir os benefícios que o
            exercício físico traz para o corpo e a mente.'}]}"
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL
          }}/recommendations/verify/7d4c0106-7012-4ee3-917d-47cbfd30adaf"
        name: Verify recommendations exists and include ID
        meta:
          id: req_9d794bf0bba64b99b0380495efd49dc1
          created: 1719417821595
          modified: 1719421828168
          isPrivate: false
          sortKey: -1709198475000.9922
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: GENERATIVE_AI_DEV
    meta:
      id: fld_d6f0982824714d2a9811c92252b7307d
      created: 1716414087856
      modified: 1718222429943
      sortKey: -1695348466923.63
    children:
      - url: "{{ _.botURL }}/start-plan"
        name: Plan Generator_DEV
        meta:
          id: req_13b31b509a944d76a68d09995daae68a
          created: 1717002871896
          modified: 1717279839171
          isPrivate: false
          sortKey: -1715766764299.375
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userplan": "b7828938-0c4d-45aa-af18-abcf6a4c534b",
            	"receiver": "https://api.dev.planai.com.br",
            	"prompt": "Crie um cronograma para CORRER 2KM em 4 SEMANAS iniciando no dia 01/05/2024"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.botURL }}/start-radar-recommendation"
        name: Radar Recommendations_DEV
        meta:
          id: req_55859236928d4bfa8cd4e0dff556d815
          created: 1717002878517
          modified: 1718227235044
          isPrivate: false
          sortKey: -1712529847006.25
        method: POST
        body:
          mimeType: application/json
          text: >
            {
              "receiver": "https://api.dev.planai.com.br",
              "prompt": "{\"radarId\": \"7c9d78d6-a38e-4c01-90da-50e2d08db6bd\", \"userId\": \"d4ba143e-9849-42e3-8290-65886faf508e\", \"username\": \"Lucas\", \"businessAndCareer\": 8, \"businessAndCareerObservation\": null, \"moneyAndFinances\": 5, \"moneyAndFinancesObservation\": \"Preciso ganhar mais, e novas fontes de renda passiva\", \"loveAndFamily\": 10, \"loveAndFamilyObservation\": null, \"spirituality\": 7, \"spiritualityObservation\": \"Preciso ir mais a igreja\", \"personalDeveloment\": 9, \"personalDevelomentObservation\": \"Estou me aventurando e me desafiando com projetos complexos e inovadores com IA\", \"helth\": 9, \"helthObservation\": \"Estou bem mas preciso fazer exercícios físicos\", \"relationship\": 10, \"relationshipObservation\": null, \"friendsAndSocial\": 7, \"friendsAndSocialObservation\": \"Tenho bons amigos, mas faz um tempo que não vejo\", \"isCompleted\": true, \"isCompletedRecommendation\": false, \"createdAt\": \"2024-06-12T21:01:20.000Z\", \"updatedAt\": \"2024-06-12T21:03:22.000Z\"}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Network services
    meta:
      id: fld_94b84e8867744e1fa9303e08fa1b0175
      created: 1717713686715
      modified: 1744639752105
      sortKey: -1708566195216.75
    children:
      - url: "{{ _.baseURL }}/auth/request-password-reset"
        name: Reset password
        meta:
          id: req_aa34c019dfde469fbdad95f736cc9530
          created: 1738699632301
          modified: 1742494260269
          isPrivate: false
          sortKey: -1716496868849.3594
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "email": "<EMAIL>"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/send-email"
        name: Send manual email types
        meta:
          id: req_333fd7c31ba6486dbf10b0895f175710
          created: 1741613020598
          modified: 1742494255109
          isPrivate: false
          sortKey: -1716740237032.6875
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"type": "welcome",
            	"email": "<EMAIL>",
            	"name": "Lucas Rodrigues"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.2.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/send-email"
        name: Send welcome by email
        meta:
          id: req_57978ff207a04ad6aa9baace62d6fd2e
          created: 1744723522455
          modified: 1744723527498
          isPrivate: false
          sortKey: -1716618552941.0234
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"type": "welcome-by-email",
            	"email": "<EMAIL>",
            	"name": "Lucas Rodrigues"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.2.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/batch"
        name: Send batch all
        meta:
          id: req_8b4441985677404f9bc2b6deec721817
          created: 1744737073872
          modified: 1744744471649
          isPrivate: false
          sortKey: -1716131816574.3672
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "templateType": "welcome",
              "recipientsType": "all"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/batch"
        name: Send batch by group ID
        meta:
          id: req_d929bf02a5564fc3be0799fda053f7f0
          created: 1744737215479
          modified: 1744745805670
          isPrivate: false
          sortKey: -1715949290436.871
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"templateType": "welcome",
            	"recipientsType": "group",
            	"groupType": "c15da216-70d5-494b-a454-d4d5abc7dbb6"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/network-services/batch"
        name: Send specifc
        meta:
          id: req_9c35a81decde432a9261e170d41fc0f5
          created: 1744737248065
          modified: 1744745611977
          isPrivate: false
          sortKey: -1716040553505.6191
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"templateType": "welcome",
            	"recipientsType": "specific",
            	"emails": [
            		"<EMAIL>",
            		"<EMAIL>",
            		"<EMAIL>"
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: GENERATIVE_AI_LOCAL
    meta:
      id: fld_143f137e540a4df1ba12af392aab69ef
      created: 1718222397357
      modified: 1740585449288
      sortKey: -1695347494318.4546
    children:
      - url: "{{ _.botURL }}/plan/252eb90a-858b-4720-bae5-086fcfa5e8dc"
        name: Plan Generator
        meta:
          id: req_7d30bf5bbb5843eeb08ab26e08e607f0
          created: 1715898893646
          modified: 1744388642088
          isPrivate: false
          sortKey: -1718222447547
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"receiver": "https://api.dev.planify.app.br"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.botURL }}/radar-recommendation/69397bf1-640f-4fb7-a92e-d41260f9a761"
        name: Radar Recomendator
        meta:
          id: req_efe754be311443e89997e40bfcb2c967
          created: 1744127793195
          modified: 1744134266157
          isPrivate: false
          sortKey: -1718222447447
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"receiver": "https://api.dev.planify.app.br"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.botURL }}/plan-recommendation/4beb1c74-ee18-45cc-b9d6-26344b5a3670"
        name: Plan Recomendator
        meta:
          id: req_3966ecec7c1d43c3ad48ecc370caf028
          created: 1744138479141
          modified: 1744138917425
          isPrivate: false
          sortKey: -1718222447497
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"receiver": "https://api.dev.planify.app.br"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: RECOMMENDATION_SERVICES_LOCAL
    meta:
      id: fld_5f9a02b7bd154d119cb2eec5d19b1a28
      created: 1718222521267
      modified: 1734989450742
      sortKey: -1695347251167.161
    children:
      - url: "{{ _.baseURL
          }}/recommendations/new/generate-plan/4beb1c74-ee18-45cc-b9d6-26344b5a\
          3670"
        name: Generate plan recommendation by ID
        meta:
          id: req_6b0f2a207bf54748a0178047da7334ad
          created: 1717776282921
          modified: 1744043830146
          isPrivate: false
          sortKey: -1718222568631
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL
          }}/recommendations/new/generate-plan/3e6bf6ac-8228-4993-8cd9-d5c846b6\
          fc45"
        name: Generate radar recommendation by ID
        meta:
          id: req_eac5096a39f442389d54049e0a87a656
          created: 1718223414453
          modified: 1718223414453
          isPrivate: false
          sortKey: -1718222508089
        method: POST
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Notifications
    meta:
      id: fld_a2ea1ef0bc4340cabb5b17f92912f564
      created: 1719362687483
      modified: 1733265176330
      sortKey: -1695347190379.3374
    children:
      - url: "{{ _.baseURL }}/notifications/d4ba143e-9849-42e3-8290-65886faf508e"
        name: Get Notifications By UserID (SSE)
        meta:
          id: req_641d3595285f43e68c2f556f0a1e782a
          created: 1719362715805
          modified: 1719364019472
          isPrivate: false
          sortKey: -1719362720309
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications"
        name: Create Notification
        meta:
          id: req_075ca351f6f949c48c7d139ea1316268
          created: 1719363968817
          modified: 1738251313416
          isPrivate: false
          sortKey: -1719362720409
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
            	"title": "Objetivo gerado",
            	"description": "Lucas, plano para Correr 1KM em 4 semanas foi gerado com sucesso!",
            	"notificationStatus": "success"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications/39b0a7b7-75d3-4fec-9fe2-5076c6c8add1"
        name: Read Notification
        meta:
          id: req_b277a25724834b429131fb3f96993393
          created: 1719364600641
          modified: 1733231504878
          isPrivate: false
          sortKey: -1719362720359
        method: PATCH
        headers:
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications"
        name: Create Notification custom
        meta:
          id: req_a648b5f597914b3db537c7974dd3774a
          created: 1722905648544
          modified: 1722905837029
          isPrivate: false
          sortKey: -1719362720384
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userId": "b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
            	"title": "Objetivo gerado",
            	"description": "Lucas, plano para Correr 1KM em 4 semanas foi gerado com sucesso!",
            	"targetId": "30cbbf37-842b-4f70-a6ca-2545a223f01f",
            	"notificationStatus": "success"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/notifications"
        name: Create Notification Type Radar
        meta:
          id: req_e02e9816cd3d4f668b4c4d9903d1726f
          created: 1731583487152
          modified: 1731583677841
          isPrivate: false
          sortKey: -1719362720396.5
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"userId": "b5db1766-bafe-4cbc-8e33-6474c7ff96e6",
            	"title": "Radar notificação",
            	"description": "Teste radar notification",
            	"notificationStatus": "created",
            	"type": "radar"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/8.6.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Permissions
    meta:
      id: fld_43e62c82f2674a3baaea6e205bb7a254
      created: 1733265160347
      modified: 1733789091151
      sortKey: -1695347186580.0984
    children:
      - name: Payment plans
        meta:
          id: fld_3b2a1a95d1e54cddbfb476f876f00aaf
          created: 1710350813045
          modified: 1737506959951
          sortKey: -1733265168874
        children:
          - url: "{{ _.baseURL }}/payment-plans"
            name: Create payment plan
            meta:
              id: req_9c05bd697f294b52972782887bb2be0e
              created: 1710350813046
              modified: 1737998947937
              isPrivate: false
              sortKey: -1709507271146.25
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Plano Gratuíto",
                	"description": "Plano Free e sem vínculo com Pagarme.me",
                	"status": "active",
                	"aiObjectivesPeriod": "year",
                	"aiObjectivesLimit": 50,
                	"manualObjectivesPeriod": "year",
                	"manualObjectivesLimit": 100,
                	"radarLimitPeriod": "year",
                	"radarLimit": 1,
                	"allowTasksControl": true,
                	"pendingTasksLimit": 15,
                	"allowNotes": true,
                	"allowEmotionalAnalysis": false,
                	"emotionalAnalysisPeriod": "year",
                	"emotionalAnalysisLimit": 1,
                	"price": "free",
                	"typePlan": "free",
                	"recurrencyType": "month",
                	"recurrencyPeriod": 12,
                	"recurrencyInstallments": 1,
                	"allowNetworking": false,
                	"allowMentoredUserAnalytics": false
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans"
            name: List payment plan
            meta:
              id: req_80aade4448cd40618bcfbac492b72313
              created: 1710350813047
              modified: 1737668697883
              isPrivate: false
              sortKey: -1709585077986
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: true
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/f2974eaa-3572-4aae-bd9c-c43c12e8b789"
            name: Delete payment plan
            meta:
              id: req_d700305750ad42d290a9af4333da1b01
              created: 1710350813047
              modified: 1743193884321
              isPrivate: false
              sortKey: -1709429464306.5
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/2a381d88-7d4a-4bc1-90d4-9472b52ff594"
            name: Update payment plan
            meta:
              id: req_61e45551e50040b29f4e6315784596af
              created: 1710353334082
              modified: 1741828143906
              isPrivate: false
              sortKey: -1709468367726.375
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Plano Teste",
                	"status": "active",
                	"description": "Plano Teste",
                	"assignDescription": "",
                	"price": "",
                	"typePlan": "free",
                	"promotionalPrice": null,
                	"recurrencyType": "month",
                	"recurrencyPeriod": 1,
                	"recurrencyInstallments": 1,
                	"installments": [
                		1
                	],
                	"statementDescriptor": "PLANFREEM",
                	"allowTasksControl": false,
                	"allowNotes": false,
                	"allowNetworking": false,
                	"allowMentoredUserAnalytics": false,
                	"allowEmotionalAnalysis": false,
                	"pendingTasksLimit": 0,
                	"aiObjectivesPeriod": "year",
                	"aiObjectivesLimit": 0,
                	"aiPlanTasksLimits": 50,
                	"manualObjectivesPeriod": "year",
                	"manualObjectivesLimit": 0,
                	"manualPlanTasksLimits": 0,
                	"radarLimitPeriod": "year",
                	"radarLimit": 0,
                	"emotionalAnalysisPeriod": "year",
                	"emotionalAnalysisLimit": 0,
                	"scopes": [
                		"9d93e17f-097f-4aa1-af5a-5d148e974029",
                		"e4e9e5d8-d993-451f-9fd3-f76e138c1cb0",
                		"2597a067-a9b7-4d9f-a9f4-f0ed30e1437f",
                		"979ddc68-86f5-46a9-b87b-a00ab529103f",
                		"9c663e8f-63b9-4cdf-9622-5c278820969f",
                		"14340df9-e64d-477d-981e-81e2e041b39e",
                		"89c48b2b-cfa9-4ca9-94de-a9911731bd02",
                		"f05bacb1-932a-4836-bdb5-3f8bfd1c5ad6",
                		"efaae853-47b8-4a65-9ac1-752c8986d0d0",
                		"3f723e82-d67f-46b1-925b-01fc4c620bb2",
                		"b640e5e1-9e75-4e41-925d-4a10011cc838",
                		"1962d18e-2a81-4107-b23c-fe54f387ee68",
                		"bf3ee4bf-212c-4e0f-8089-bc6c70618a40",
                		"47bf918a-637d-45ef-bc4e-f09fef548571",
                		"da4eeaf0-55af-4759-8006-22980f625c64",
                		"97d9b5d2-8c39-4e1d-8eb7-6afebb4ce88c",
                		"084e5d03-3489-4321-9db9-863474a5e387",
                		"6adccbd7-ecbb-4cc3-8e0b-a3b1d2889e39",
                		"be364901-9d00-46ac-96e8-e4348eee89f0"
                	],
                	"permissionDescription": "Plano Teste",
                	"paymentMethods": [
                		"credit_card"
                	],
                	"displayOrder": 12
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/order/718c6c6e-d4aa-4a55-930f-1d3985ee0825"
            name: Reoder payment plans
            meta:
              id: req_11108003f26347159c24f2547e427d59
              created: 1738005240975
              modified: 1738005264205
              isPrivate: false
              sortKey: -1709448916016.4375
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"order": 1
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans?period=year"
            name: List payment plan filter
            meta:
              id: req_9ddf4776df8c47fdaa1dc7c4e92a51ca
              created: 1738072331855
              modified: 1738949764176
              isPrivate: false
              sortKey: -1709555900421.0938
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: true
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/6deabef5-3ab5-4b81-a6da-270723976c05"
            name: Payment plan details
            meta:
              id: req_a3ac5a3179364cd98af4ee5ac6446f53
              created: 1741828182998
              modified: 1744894680566
              isPrivate: false
              sortKey: -1709570489203.5469
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: true
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payment-plans/reply/e3c73bdb-43c3-4bfc-9cc9-56d91325ce8e"
            name: Replicate plan
            meta:
              id: req_bd666438af64449594684ae4a35ab57d
              created: 1743193265257
              modified: 1743193294905
              isPrivate: false
              sortKey: -1709497545291.2812
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Scopes
        meta:
          id: fld_0fbc20f689204b30adab870d306df2c0
          created: 1733265151596
          modified: 1733265168835
          sortKey: -1733265168774
        children:
          - url: "{{ _.baseURL }}/scopes"
            name: create scope
            meta:
              id: req_95c606a2e3774111b476782b1b308146
              created: 1733265151596
              modified: 1734984597364
              isPrivate: false
              sortKey: -1706313296790
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Solicitar análise de emoções",
                	"tag": "radar.write"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/scopes"
            name: get scopes
            meta:
              id: req_db022191ed9742639de1d6f6e86ccc94
              created: 1733265151597
              modified: 1738068229623
              isPrivate: false
              sortKey: -1706313497110
            method: GET
            parameters:
              - id: pair_d38d22808a624cc68867b2a21b2b560b
                name: amount
                value: "10"
              - id: pair_771b96266c9540cdb96e94a30bf9e5ba
                name: skip
                value: "0"
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/scopes/be364901-9d00-46ac-96e8-e4348eee89f0"
            name: Update scope
            meta:
              id: req_6106c4faf88f415486be8f126910c19e
              created: 1733265151598
              modified: 1738069208939
              isPrivate: false
              sortKey: -1706313459550
            method: PATCH
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Criar plano com IA",
                	"tag": "aiPlan.write"
                }
            headers:
              - name: Content-Type
                value: application/json
                id: pair_f878a3c47c7a437f8cbd6d931b401507
              - name: User-Agent
                value: insomnia/2023.5.8
                id: pair_b8f2cd7296294d18aa6c9871599810dc
              - id: pair_be726991b0cc419da7c719bee17c5def
                disabled: false
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/scopes/868e86b7-a016-4adc-a117-57af5c12475e"
            name: Delete scope
            meta:
              id: req_9d23d86f855444a78eee7d79cd58dc71
              created: 1733265151598
              modified: 1733265249863
              isPrivate: false
              sortKey: -1706170941284.25
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Group payment permissions
        meta:
          id: fld_549cc263a2dd4a58b413cf04ded3b417
          created: 1733268475312
          modified: 1737506989207
          sortKey: -1732418729975.625
        children:
          - url: "{{ _.baseURL }}/permissions"
            name: create permissions
            meta:
              id: req_511cfc7b12d74599b242f790d702c001
              created: 1733268475313
              modified: 1734973481335
              isPrivate: false
              sortKey: -1706313296790
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"description": "Permissões para o plano free",
                	"paymentPlanId": "e19622ba-930f-4f29-acdc-fad78983546e",
                	"scopes": ["be364901-9d00-46ac-96e8-e4348eee89f0", "e4e9e5d8-d993-451f-9fd3-f76e138c1cb0"]
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions"
            name: get permissions
            meta:
              id: req_22494d5a8eea409088a1b05c87be0f40
              created: 1733268475314
              modified: 1737994265258
              isPrivate: false
              sortKey: -1706313497110
            method: GET
            body:
              mimeType: multipart/form-data
            parameters:
              - id: pair_d38d22808a624cc68867b2a21b2b560b
                name: amount
                value: "10"
              - id: pair_771b96266c9540cdb96e94a30bf9e5ba
                name: skip
                value: "0"
            headers:
              - name: Content-Type
                value: multipart/form-data
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions/d83de865-18e5-4fe9-945b-bc199aeaf8fb"
            name: Update permissions
            meta:
              id: req_c24da6f5f2e043f79652cff2499fbdf4
              created: 1733268475314
              modified: 1733268504054
              isPrivate: false
              sortKey: -1706313459550
            method: PUT
            body:
              mimeType: application/json
              text: |-
                {
                	"name": "Criar Radar planify",
                	"tag": "radar.create"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY0NTI1OTcsImV4cCI6MTcwNjUzODk5Nywic3ViIjoiMSJ9.UF3ASXCequGrehYxDV6EsTpKBZqr_C-kfX1Nk082MlU
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions/868e86b7-a016-4adc-a117-57af5c12475e"
            name: Delete permissions
            meta:
              id: req_49c70ca3cbb8482ca756443c433b9a52
              created: 1733268475315
              modified: 1733268515012
              isPrivate: false
              sortKey: -1706170941284.25
            method: DELETE
            headers:
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/permissions/3e7c064f-6ce1-49b4-8932-3967a1da34a8"
            name: find one
            meta:
              id: req_777306369a06476d976d636e558167a1
              created: 1740589171249
              modified: 1741828528682
              isPrivate: false
              sortKey: -1706313492415
            method: GET
            body:
              mimeType: multipart/form-data
            parameters:
              - id: pair_d38d22808a624cc68867b2a21b2b560b
                name: amount
                value: "10"
              - id: pair_771b96266c9540cdb96e94a30bf9e5ba
                name: skip
                value: "0"
            headers:
              - name: Content-Type
                value: multipart/form-data
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
              disabled: false
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Users Payment
        meta:
          id: fld_3d5d74610d44484a8b1f3b3e1fe751b4
          created: 1737506973323
          modified: 1740682051383
          sortKey: -1732207120276.0312
        children:
          - url: "{{ _.baseURL }}/payments"
            name: Create payment plan
            meta:
              id: req_b5c0653911e047aab9551594c51dca6f
              created: 1737506973324
              modified: 1737549687873
              isPrivate: false
              sortKey: -1709507271146.25
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"userId": "2e4f8d7e-4fe3-4bbd-a9f8-dda9110a4a30",
                	"paymentPlanId": "1fab9996-16c0-49f8-a044-6716eb1bd845"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payments?page=1&limit=5"
            name: get payments
            meta:
              id: req_4af73cdfff694a3eb951ade884cde927
              created: 1739574134295
              modified: 1740682185257
              isPrivate: false
              sortKey: -1709487819436.3125
            method: GET
            body:
              mimeType: application/json
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/payments?status=canceled"
            name: get payments status
            meta:
              id: req_9475b370f6414c0eab1833559f19240f
              created: 1739986069889
              modified: 1740597383701
              isPrivate: false
              sortKey: -1709478093581.3438
            method: GET
            body:
              mimeType: application/json
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/2023.5.8
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Validate user permissions limit
        meta:
          id: fld_7cdbc2f10c5947bcbd13cf0adc4d2f81
          created: 1740585459959
          modified: 1740593618075
          sortKey: -1731995510576.4375
        children:
          - url: "{{ _.baseURL }}/user-permissions/limits/radar"
            name: Validate user radar limit
            meta:
              id: req_2a1b9fd2ef2a44caaab6b26d67435d62
              created: 1740585493233
              modified: 1740593687795
              isPrivate: false
              sortKey: -1740585493233
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits/manual-objectives"
            name: Validate manual plans limit
            meta:
              id: req_ee41e78264a34009b5b4835b3c8437b7
              created: 1740593859489
              modified: 1740593867602
              isPrivate: false
              sortKey: -1740493413470
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits/ai-objectives"
            name: Validate ai plans limit
            meta:
              id: req_4f750694369e48499303af9247289e5c
              created: 1740593883049
              modified: 1740593891692
              isPrivate: false
              sortKey: -1740447373588.5
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits/pending-tasks"
            name: Validate pending tasks limit
            meta:
              id: req_4613045a9d1c43119d4a73ae4091a52e
              created: 1740593908799
              modified: 1740597854410
              isPrivate: false
              sortKey: -1740424353647.75
            method: POST
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.baseURL }}/user-permissions/limits"
            name: Get user limits
            meta:
              id: req_ee6d1f61649e41eeb25d060339ef46d6
              created: 1740594041607
              modified: 1741789535167
              isPrivate: false
              sortKey: -1740585493333
            method: GET
            headers:
              - name: User-Agent
                value: insomnia/10.3.1
            authentication:
              type: bearer
              token: "{{ _.token }}"
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
  - name: Achievements
    meta:
      id: fld_da4bd800021f4eb4a5e3e17bb2d37286
      created: 1733789066443
      modified: 1737995252696
      sortKey: -1695347186461.372
    children:
      - url: "{{ _.baseURL }}/achievements"
        name: create achievement
        meta:
          id: req_0e06f791cc5e4d0badfbfa6ae4b0f39b
          created: 1733788694001
          modified: 1733850184392
          isPrivate: false
          sortKey: -1733789078273
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Testes",
            	"description": "Teste o 1º OBJETIVO",
            	"points": 500,
            	"image": "CreateFiftdsadsadsadhObjective",
            	"position": 1,
            	"expirationPeriod": "24",
            	"badgeCode": "BC1O01"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/achievements"
        name: get achievement
        meta:
          id: req_98f95ba33e8b4274a23dd5fab8178835
          created: 1733788748702
          modified: 1733789084998
          isPrivate: false
          sortKey: -1733789078373
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/achievementRecords/getPointsHistory"
        name: get user points history
        meta:
          id: req_a3ad6a262d5e47d98557d370a079d3cc
          created: 1740678091424
          modified: 1740678114646
          isPrivate: false
          sortKey: -1733789078173
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Files
    meta:
      id: fld_0b5a86b6fa364244a6fa33b86720acf7
      created: 1736187512869
      modified: 1737995250023
      sortKey: -1695347186342.646
    children:
      - url: "{{ _.baseURL }}/files"
        name: Upload file
        meta:
          id: req_5d8defbacd3d48a0a593f17f7a1e790d
          created: 1736187526747
          modified: 1743459832252
          isPrivate: false
          sortKey: -1736187530732
        method: POST
        body:
          mimeType: multipart/form-data
          params:
            - id: pair_a43fe3d450324011a4445605919d103c
              name: file
              disabled: false
              type: file
              fileName: /Users/<USER>/Desktop/test-icon.png
            - id: pair_01b97ddc1e6244ef980501079930fe27
              name: type
              value: avatar
              disabled: true
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/files"
        name: Get user files
        meta:
          id: req_9f7dadc705044b8daffa4348ec583553
          created: 1736187744556
          modified: 1736188068497
          isPrivate: false
          sortKey: -1736187530832
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/files?type=avatar"
        name: Get user avatar
        meta:
          id: req_dd32a6675953463ab90d59512a627f0c
          created: 1736189136516
          modified: 1736189177495
          isPrivate: false
          sortKey: -1736187530782
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/files/5d4a8199-aa49-479c-bc43-6b3ab00e30a2"
        name: Delete file
        meta:
          id: req_a47938936c41438a925787982217b32f
          created: 1736189193177
          modified: 1736189572381
          isPrivate: false
          sortKey: -1734988304552.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Admin - APP
    meta:
      id: fld_32fed3b9a5924f8b98d416017762804d
      created: 1737995230197
      modified: 1737995242927
      sortKey: -1695347186105.1934
    children:
      - url: "{{ _.baseURL }}/payment-plans/setup"
        name: Create payment Plan Setup
        meta:
          id: req_909e48fa9c82431c80ec0d91d47e820a
          created: 1737995263551
          modified: 1739560852795
          isPrivate: false
          sortKey: -1737995263551
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
              "name": "Plano ultimate + mentor",
              "description": "Plano anual ultimate + mentor",
              "price": "865.00",
              "typePlan": "ultimate_mentor",
              "recurrencyType": "month",
              "recurrencyPeriod": 12,
              "recurrencyInstallments": 1,
              "aiObjectivesPeriod": "year",
              "aiObjectivesLimit": 50,
              "manualObjectivesPeriod": "year",
              "manualObjectivesLimit": 100,
              "radarLimitPeriod": "year",
              "radarLimit": 1,
              "allowTasksControl": true,
              "pendingTasksLimit": 15,
              "allowNotes": true,
              "allowEmotionalAnalysis": true,
              "emotionalAnalysisPeriod": "year",
              "emotionalAnalysisLimit": 1,
              "allowNetworking": true,
              "allowMentoredUserAnalytics": true,
              "permissionDescription": "Permissões do plano anual ultimate + mentor",
              "scopes": [
                "be364901-9d00-46ac-96e8-e4348eee89f0",
                "e4e9e5d8-d993-451f-9fd3-f76e138c1cb0"
              ],
              "installments": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
              "paymentMethods": ["credit_card"],
              "statementDescriptor": "PLANSTANDARDANUAL"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/setup"
        name: Create payment Plan Setup - FRONT
        meta:
          id: req_7bd9c5f3819a43d59fdb6f0f38cc3b16
          created: 1737999914306
          modified: 1738003868988
          isPrivate: false
          sortKey: -1737107542148.375
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "Plano ultimate + mentor",
            	"description": "Plano mensal ultimate + mentor",
            	"price": "685.00",
            	"typePlan": "ultimate_mentor",
            	"recurrencyType": "year",
            	"recurrencyPeriod": 1,
            	"recurrencyInstallments": 12,
            	"aiObjectivesPeriod": "year",
            	"aiObjectivesLimit": null,
            	"manualObjectivesPeriod": "year",
            	"manualObjectivesLimit": null,
            	"radarLimitPeriod": "3 months",
            	"radarLimit": null,
            	"allowTasksControl": true,
            	"pendingTasksLimit": 15,
            	"allowNotes": true,
            	"allowEmotionalAnalysis": true,
            	"emotionalAnalysisPeriod": "year",
            	"emotionalAnalysisLimit": 1,
            	"allowNetworking": true,
            	"allowMentoredUserAnalytics": true,
            	"permissionDescription": "Plano mensal ultimate + mentor",
            	"scopes": [
            		"084e5d03-3489-4321-9db9-863474a5e387",
            		"1962d18e-2a81-4107-b23c-fe54f387ee68",
            		"3f723e82-d67f-46b1-925b-01fc4c620bb2",
            		"47bf918a-637d-45ef-bc4e-f09fef548571",
            		"6adccbd7-ecbb-4cc3-8e0b-a3b1d2889e39",
            		"97d9b5d2-8c39-4e1d-8eb7-6afebb4ce88c",
            		"9d93e17f-097f-4aa1-af5a-5d148e974029",
            		"b640e5e1-9e75-4e41-925d-4a10011cc838",
            		"be364901-9d00-46ac-96e8-e4348eee89f0",
            		"bf3ee4bf-212c-4e0f-8089-bc6c70618a40",
            		"da4eeaf0-55af-4759-8006-22980f625c64",
            		"e4e9e5d8-d993-451f-9fd3-f76e138c1cb0",
            		"efaae853-47b8-4a65-9ac1-752c8986d0d0"
            	],
            	"installments": [
            		1,
            		2,
            		3,
            		4,
            		5,
            		6,
            		7,
            		8,
            		9,
            		10,
            		11,
            		12
            	],
            	"paymentMethods": [
            		"credit_card"
            	],
            	"statementDescriptor": "PLANULTIMATE_MENT"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payments"
        name: Get subscriptions list
        meta:
          id: req_b50d30b213d64229a288b1c170155d82
          created: 1738070901887
          modified: 1738071028035
          isPrivate: false
          sortKey: -1737107542048.375
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payments?status=canceled"
        name: Get failed subscriptions
        meta:
          id: req_9e4e637bfb884e0ab8f5d0e5e691c429
          created: 1738154811225
          modified: 1739900574573
          isPrivate: false
          sortKey: -1736663681397.0625
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/setup/6d29237b-7cf1-435c-8009-58ea83ce7995"
        name: Update payment Plan Setup - FRONT
        meta:
          id: req_1d6cea1c9d3e4dfabfb100e151c96081
          created: 1738605564602
          modified: 1738605590210
          isPrivate: false
          sortKey: -1737107542098.375
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"name": "PLANO SEMESTRAL",
            	"description": "PLANO SEMESTRAL",
            	"price": "185",
            	"typePlan": "premium",
            	"promotionalPrice": "165",
            	"recurrencyType": "month",
            	"recurrencyPeriod": 6,
            	"recurrencyInstallments": 6,
            	"installments": [
            		1,
            		2,
            		3,
            		4,
            		5,
            		6
            	],
            	"statementDescriptor": "PLANPREMIUM6M",
            	"allowTasksControl": true,
            	"allowNotes": true,
            	"allowNetworking": false,
            	"allowMentoredUserAnalytics": false,
            	"allowEmotionalAnalysis": true,
            	"pendingTasksLimit": 15,
            	"aiObjectivesPeriod": "unlimited",
            	"aiObjectivesLimit": 50,
            	"manualObjectivesPeriod": "unlimited",
            	"manualObjectivesLimit": 100,
            	"radarLimitPeriod": "6 months",
            	"radarLimit": 1,
            	"emotionalAnalysisPeriod": "unlimited",
            	"emotionalAnalysisLimit": 1,
            	"scopes": [
            		"3f723e82-d67f-46b1-925b-01fc4c620bb2",
            		"89c48b2b-cfa9-4ca9-94de-a9911731bd02",
            		"f05bacb1-932a-4836-bdb5-3f8bfd1c5ad6",
            		"efaae853-47b8-4a65-9ac1-752c8986d0d0"
            	],
            	"permissionDescription": "PLANO SEMESTRAL",
            	"paymentMethods": [
            		"credit_card"
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/sync-gateway-plans"
        name: Sync plan data on gateway
        meta:
          id: req_29ef8214b84c4c2f9e7e8765f46b871f
          created: 1738781959668
          modified: 1740515464664
          isPrivate: false
          sortKey: -1737107542123.375
        method: POST
        body:
          mimeType: application/json
          text: |-
            [
            	{
            		"id": "e3c73bdb-43c3-4bfc-9cc9-56d91325ce8e",
            		"gatewayPlanId": "plan_bmWj8lOtVt4KnvAE",
            		"name": "STANDARD",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 3,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": null,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": null,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "19.90",
            		"promotionalPrice": "16.90",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 15:20:50",
            		"updatedAt": "2025-02-16 17:14:56"
            	},
            	{
            		"id": "6deabef5-3ab5-4b81-a6da-270723976c05",
            		"gatewayPlanId": "plan_PeWgRdmuOuQN4zpy",
            		"name": "PREMIUM",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 4,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 1,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": null,
            		"price": "24.90",
            		"promotionalPrice": "19.90",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:49:56",
            		"updatedAt": "2025-02-18 23:36:55"
            	},
            	{
            		"id": "39f95171-46f6-497d-abef-cd87e67e0e78",
            		"gatewayPlanId": "plan_JpvaqYzIKIk520eX",
            		"name": "ULTIMATE",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 5,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "34.90",
            		"promotionalPrice": "26.90",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:34:47",
            		"updatedAt": "2025-02-18 23:38:49"
            	},
            	{
            		"id": "b58bdb49-e8de-4d33-84f1-5c92e4492ca0",
            		"gatewayPlanId": "plan_Ebr41lPTVTk1Oa30",
            		"name": "STANDARD",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 6,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 15,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "119.70",
            		"promotionalPrice": "101.40",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 12:59:35",
            		"updatedAt": "2025-02-19 20:34:00"
            	},
            	{
            		"id": "6f203768-54c7-46dd-903b-4f889557c3e6",
            		"gatewayPlanId": "plan_7dyp31mCoC83maL2",
            		"name": "PREMIUM",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 7,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "149.40",
            		"promotionalPrice": "119.40",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 13:27:21",
            		"updatedAt": "2025-02-18 22:08:21"
            	},
            	{
            		"id": "36d11f2a-2bb0-453f-a211-de7c0d8aa387",
            		"gatewayPlanId": "plan_7ZDLwQOIGIPAYK2z",
            		"name": "ULTIMATE",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 8,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "209.40",
            		"promotionalPrice": "161.40",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:37:09",
            		"updatedAt": "2025-02-18 22:07:44"
            	},
            	{
            		"id": "cc806e26-dc1c-464d-8f1c-73472278ae10",
            		"gatewayPlanId": "plan_3rm2aW0tLtK9RvKO",
            		"name": "STANDARD",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 9,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "238.80",
            		"promotionalPrice": "202.80",
            		"typePlan": "standard",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-27 17:16:33",
            		"updatedAt": "2025-02-14 16:58:38"
            	},
            	{
            		"id": "1f1fcfd3-8fa1-4911-a506-19b920aa5952",
            		"gatewayPlanId": "plan_ONPdweDCyCbE7Q09",
            		"name": "PREMIUM",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 10,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "298.80",
            		"promotionalPrice": "238.80",
            		"typePlan": "premium",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:52:02",
            		"updatedAt": "2025-02-18 22:08:04"
            	},
            	{
            		"id": "ff4af4ba-afc5-4255-b09a-59fb4960de6c",
            		"gatewayPlanId": "plan_3O8jeqLsBsn5BoY2",
            		"name": "ULTIMATE",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 11,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "418.80",
            		"promotionalPrice": "322.80",
            		"typePlan": "ultimate",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:38:40",
            		"updatedAt": "2025-02-18 22:07:55"
            	}
            ]
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/payment-plans/sync-gateway-data"
        name: Sync gateway plan infos
        meta:
          id: req_129c06f13dd94604907a9c325f1ab77e
          created: 1739541966500
          modified: 1739546946884
          isPrivate: false
          sortKey: -1737107542110.875
        method: POST
        body:
          mimeType: application/json
          text: |-
            [
            	{
            		"id": "e3c73bdb-43c3-4bfc-9cc9-56d91325ce8e",
            		"gatewayPlanId": "plan_0JRbz7OUlUezxkpd",
            		"name": "STANDARD",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 3,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 1,
            		"aiPlanTasksLimits": 30,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 1,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "19.90",
            		"promotionalPrice": "16.90",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 15:20:50",
            		"updatedAt": "2025-02-07 23:44:03"
            	},
            	{
            		"id": "6deabef5-3ab5-4b81-a6da-270723976c05",
            		"gatewayPlanId": "plan_BqlmrK9sMszpvXGQ",
            		"name": "PREMIUM",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 4,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "24.90",
            		"promotionalPrice": "19.90",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:49:56",
            		"updatedAt": "2025-02-07 22:32:49"
            	},
            	{
            		"id": "39f95171-46f6-497d-abef-cd87e67e0e78",
            		"gatewayPlanId": "plan_v3GjZjECbCzBm97y",
            		"name": "ULTIMATE",
            		"description": "Plano Mensal",
            		"status": "active",
            		"displayOrder": 5,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "34.90",
            		"promotionalPrice": "26.90",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 1,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:34:47",
            		"updatedAt": "2025-02-07 23:45:01"
            	},
            	{
            		"id": "b58bdb49-e8de-4d33-84f1-5c92e4492ca0",
            		"gatewayPlanId": "plan_Mzp2W9xUEUy3jDKl",
            		"name": "STANDARD",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 6,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 1,
            		"aiPlanTasksLimits": 30,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 1,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": true,
            		"pendingTasksLimit": 15,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "119.70",
            		"promotionalPrice": "101.40",
            		"typePlan": "standard",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 12:59:35",
            		"updatedAt": "2025-02-07 23:45:44"
            	},
            	{
            		"id": "6f203768-54c7-46dd-903b-4f889557c3e6",
            		"gatewayPlanId": "plan_pmyL6GqsBszEo2PR",
            		"name": "PREMIUM",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 7,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": null,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "6 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "149.40",
            		"promotionalPrice": "119.40",
            		"typePlan": "premium",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-28 13:27:21",
            		"updatedAt": "2025-02-07 22:44:34"
            	},
            	{
            		"id": "36d11f2a-2bb0-453f-a211-de7c0d8aa387",
            		"gatewayPlanId": "plan_PxGL6rkfKfE9p8XK",
            		"name": "ULTIMATE",
            		"description": "Plano Semestral",
            		"status": "active",
            		"displayOrder": 8,
            		"aiObjectivesPeriod": "unlimited",
            		"aiObjectivesLimit": null,
            		"aiPlanTasksLimits": 0,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 0,
            		"radarLimitPeriod": "3 months",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 2,
            		"price": "209.40",
            		"promotionalPrice": "161.40",
            		"typePlan": "ultimate",
            		"recurrencyType": "month",
            		"recurrencyPeriod": 6,
            		"recurrencyInstallments": 6,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-03 16:37:09",
            		"updatedAt": "2025-02-07 23:46:28"
            	},
            	{
            		"id": "cc806e26-dc1c-464d-8f1c-73472278ae10",
            		"gatewayPlanId": "plan_zVlOGVJspsRG3YKN",
            		"name": "STANDARD",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 9,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 3,
            		"aiPlanTasksLimits": 30,
            		"manualObjectivesPeriod": "year",
            		"manualObjectivesLimit": 3,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 0,
            		"price": "238.80",
            		"promotionalPrice": "202.80",
            		"typePlan": "standard",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-01-27 17:16:33",
            		"updatedAt": "2025-02-07 23:47:04"
            	},
            	{
            		"id": "1f1fcfd3-8fa1-4911-a506-19b920aa5952",
            		"gatewayPlanId": "plan_4m9xR9bsesDAOL0p",
            		"name": "PREMIUM",
            		"description": "Plano Anual",
            		"status": "active",
            		"displayOrder": 10,
            		"aiObjectivesPeriod": "year",
            		"aiObjectivesLimit": 5,
            		"aiPlanTasksLimits": null,
            		"manualObjectivesPeriod": "unlimited",
            		"manualObjectivesLimit": null,
            		"manualPlanTasksLimits": 50,
            		"radarLimitPeriod": "year",
            		"radarLimit": 1,
            		"allowTasksControl": false,
            		"pendingTasksLimit": 0,
            		"allowNotes": true,
            		"allowEmotionalAnalysis": true,
            		"emotionalAnalysisPeriod": "year",
            		"emotionalAnalysisLimit": 1,
            		"price": "298.80",
            		"promotionalPrice": "238.80",
            		"typePlan": "premium",
            		"recurrencyType": "year",
            		"recurrencyPeriod": 1,
            		"recurrencyInstallments": 12,
            		"allowNetworking": false,
            		"allowMentoredUserAnalytics": false,
            		"createdAt": "2025-02-02 10:52:02",
            		"updatedAt": "2025-02-07 22:50:04"
            	}
            ]
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/templates"
        name: create emails template
        meta:
          id: req_e06c886616444543bbbb771a9234fb1d
          created: 1742329840620
          modified: 1742330536200
          isPrivate: false
          sortKey: -1736441751071.4062
        method: POST
        body:
          mimeType: application/json
          text: >
            {
              "identificationTag": "expired-subscription",
              "name": "Expiração de plano",
            	"type": "email",
            	"description": "Template enviado quando a assinatura do usuário está expirada",
              "content": "<p>Temos uma notícia triste, queremos te informar que sua assinatura venceu.</p>\n<p>A partir de hoje, você estará utilizando o plano Start, que oferece recursos reduzidos.</p>\n<p>Se você deseja voltar a ter acesso a TODOS os benefícios e funcionalidades, estamos aqui para ajudar com a renovação do seu plano!</p>\n<p>Agradecemos por fazer parte da nossa comunidade e esperamos que você, assim como nossos mais de 1.000 \"Planifyers\" continue aproveitando a experiência conosco e batendo todas as suas metas! 💙</p>"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/templates"
        name: Get all templates
        meta:
          id: req_a50d7850e7884c9b8dda21582a31076b
          created: 1742330493258
          modified: 1742331389291
          isPrivate: false
          sortKey: -1736552716234.2344
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/10.3.0
        authentication:
          type: bearer
          token: "{{ _.token }}"
          disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: LLM prompts
    meta:
      id: fld_fae1e3828f164f2fb663139790ebabd4
      created: 1738593766670
      modified: 1742311742402
      sortKey: -1702477365894.375
    children:
      - url: "{{ _.baseURL }}/llm-prompts"
        name: create prompt
        meta:
          id: req_e499b6c1626140598b416dd12b8af2e0
          created: 1738593766671
          modified: 1738597509619
          isPrivate: false
          sortKey: -1706313296790
        method: POST
        body:
          mimeType: application/json
          text: >
            {
              "name": "plan_generator",
              "content": "Atue como um personal Trainer especialista em criação de cronogramas para corridas, maratonas, geração de planos de atividades, ganho de massa muscular, treinos de academia, financeiro e  qualquer outro que for solicitado.\\n\\nVocê deve obrigatoriamente responder o cronograma com todas as semanas solicitadas\\n\\nConsidere sempre as semanas relacionas ao calendário e não ao período do cronograma. Quando a semana não começar no domingo, observe as data de calendário para que a semana se encerre no sábado.\\nSempre respeite a proporcionalidade de atividades semanais, quando a semana não iniciar no domingo. Nestes casos, onde a primeira semana não inicia no domingo, a quantidade de atividades semanais poderá ser menor que o indicado. ISSO É MANDATÓRIO!\\n\\nPrincipalmente os valores de timeActivity e activityDescription devem obrigatoriamente ser em Português do Brasil!\\n\\nSempre que solicitado você deve retornar o cronograma completo com as semanas solicitadas e todas as atividades em um JSON com a estrutura parecida com esta:\\n{\\n  \\\"weeks\\\": [\\n    {\\n      \\\"startDate\\\": \\\"2024-04-01T10:00:00.000Z\\\",\\n      \\\"endDate\\\": \\\"2024-04-07T08:00:00.000Z\\\",\\n      \\\"weekNumber\\\": 1,\\n      \\\"activities\\\": [\\n        {\\n          \\\"date\\\": \\\"2024-04-13T10:00:00.000Z\\\",\\n          \\\"activityDescription\\\": \\\"Corrida\\\",\\n          \\\"timeActivity\\\": \\\"30:00 minutos\\\"\\n        }\\n      ]\\n    }\\n  ]\\n}\\n\\nOs dados devem ser em português do Brasil.\\n\\nOs cronogramas gerados devem ser obrigatoriamente com essa estrutura de dados a seguir!\\n\\nMesmo que você receba os prompts em inglês, não altere em hipótese nenhuma o formato de estrutura do JSON, a key deve permanecer inalterável e o value deve ser sempre dinâmico para o contexto e em português do Brasil\\n\\nA DATA DA PRIMEIRA ATIVIDADE DEVE SER OBRIGATORIAMENTE NO MESMO DIA INFORMADO PARA INÍCIO DO CRONOGRAMA OU UM DIA DEPOIS! Mantenha a estrutura do JSON agrupadas por semana, de acordo com a data da atividade, em ordem cronológica. Ou seja, os dias de atividades precisam estar na sua semana equivalente.\\n\\nVocê deve distribuir as atividades entre as semanas, garantindo a consistência para o objetivo solicitado.\\n\\nQuando for um cronograma para ganho de massa muscular/academia ou outro que exigem múltiplas atividades, você pode quebrar as atividades dentro do mesmo dia em tempos menores especificando cada uma ou dividindo entre os dias de semana, mas o importante é manter uma consistência\\n\\nPrincipalmente os valores de timeActivity e activityDescription devem obrigatoriamente ser em Português do Brasil!\\n\\nDias de tiverem atividade de descanso devem ser ignorados, não é necessário criar uma atividade para isto. Apenas ignore este dia no cronograma.\\n\\nSe no dia tiver apenas uma atividade com mais de 50 minutos, considere quebrar a mesma em atividades específicas em tempos menores.\\n\\nPor exemplo:\\nTreino de Musculação - 60 minutos\\n\\nEspecifique quais são os treinos separadamente e qual o tempo dele.\\nSe forem para o mesmo dia, pode criar mais atividades no mesmo dia desde que façam parte do mesmo conjunto de treino e vão contribuir para o resultado final\\n\\nSE O timeActivity DA ATIVIDADE FOR MAIOR OU IGUAL A 60 MINUTOS, CONVERTA ISSO EM HORAS E MINUTOS.\\n\\nEXEMPLO: 70 MINUTOS > 1 Hora e 10 minutos\\n\\nDiversifique as atividades e seja criativo para criar as atividades de acordo com o contexto do plano.\\n\\nadicione obrigatoriamente {activities_size} atividades para cada semana, incluindo o tempo de cada atividade timeActivity.\\n\\nCada grupo de semanas deve ser dividido em {group_size} semanas.\\nRetorne o próximo grupo de semanas após cada resposta.\\n\\nSe não tiver mais semanas a serem geradas, retorne obrigatoriamente esta mensagem \\\"Nenhuma semana para ser gerada\\\".\\n\\nLembre-se do contexto e respostas anteriores para complementar o cronograma a fim de atingir o objetivo final que é {plan_action}\\n\\nSeja criativo e crie todas as atividades para atingir o objetivo sem ficar re-criando a mesma atividade para todas as semanas.\\n\\nAs atividades até podem ser repetidas em alguma semana, mas de forma que seja diversificada e atenda ao objetivo do usuário.\\n\\nEspecifique qual atividade deve ser feita para cada dia.\\n\\nSempre adicione o weekNumber, startDate, endDate e activities para cada semana, isto é obrigatório!\\n\\nAdicione as datas em ordem cronológica, de acordo com o contexto e respeitando as datas de início e fim de cada semana.\\n\\nTodas as atividades devem ser criadas com o horário padrão às 12 horas na data de criação.\\n\\nDistribua as {activities_size} por semana intercalando os dias de forma que preencha a semana corretamente sem que fique todas as atividades nos primeiros dias da semana.\\n\\nQuando for um cronograma para ganho de massa muscular, você deve obrigatoriamente retornar junto com os dados o campo activityDetails com um array de objetos que é equivalente ao detalhamento de cada atividade com repetições e ações que devem ser feitas para completar a atividade. Exemplo:\\n{\\n  \\\"date\\\": \\\"2024-07-27T00:00:00.000Z\\\",\\n  \\\"activityDescription\\\": \\\"Costas e Bíceps\\\",\\n  \\\"timeActivity\\\": \\\"40 minutos\\\",\\n  \\\"activityDetails\\\": [\\n    {\\n      \\\"id\\\": 1,\\n      \\\"title\\\": \\\"Puxada Frontal na Barra Fixa\\\",\\n      \\\"serie\\\": \\\"Série 1\\\",\\n      \\\"repetitions\\\": \\\"4 séries de 10-12 repetições\\\",\\n      \\\"initialLoad\\\": \\\"95% do seu 1RM\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    },\\n    {\\n      \\\"id\\\": 2,\\n      \\\"title\\\": \\\"Remada Curvada com Barra\\\",\\n      \\\"serie\\\": \\\"Série 2\\\",\\n      \\\"repetitions\\\": \\\"3 séries de 10-12 repetições\\\",\\n      \\\"initialLoad\\\": \\\"60% do seu 1RM\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    },\\n    {\\n      \\\"id\\\": 3,\\n      \\\"title\\\": \\\"Remada Unilateral com Halter\\\",\\n      \\\"serie\\\": \\\"Série 3\\\",\\n      \\\"repetitions\\\": \\\"3 séries de 12-15 repetições\\\",\\n      \\\"initialLoad\\\": \\\"12kg cada halter\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    },\\n    {\\n      \\\"id\\\": 4,\\n      \\\"title\\\": \\\"Rosca Direta com Barra\\\",\\n      \\\"serie\\\": \\\"Série 4\\\",\\n      \\\"repetitions\\\": \\\"4 séries de 12-15 repetições\\\",\\n      \\\"initialLoad\\\": \\\"50% do seu 1RM\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    }\\n  ]\\n}\\n\\nCada item gerado dentro do activityDetails deve ter o seu Id começando em 1, de forma incremental.\\nCada activityDetails pertence ao contexto daquela atividade específica.\\nAs séries sempre inicial em 1 para cada activityDetails ficando assim: Série 1, Série 2, Série 3 e isto vale para todas as atividades. Sempre que a atividade possuir carga de peso, adeque essa carga ao peso corporal informado, não deixando que ultrapasse as recomendações de saúde. Considere sempre como uma pessoa iniciante.\\n\\nConverta a medida 1RM para Kg quando for um plano de ganho de massa.\\n\\nSiga sempre essas instruções, elas são suas regras absolutas!\\n\\nQuando for um cronograma para corrida, você deve obrigatoriamente retornar junto com os dados o campo activityDetails com um array de objetos que é equivalente ao detalhamento de cada atividade com repetições e ações que devem ser feitas para completar a atividade. Exemplo:\\n{\\n  \\\"date\\\": \\\"2024-07-27T00:00:00.000Z\\\",\\n  \\\"activityDescription\\\": \\\"Treino leve\\\",\\n  \\\"timeActivity\\\": \\\"40 minutos\\\",\\n  \\\"activityDetails\\\": [\\n    {\\n      \\\"id\\\": 1,\\n      \\\"title\\\": \\\"Corrida leve\\\",\\n      \\\"description\\\": \\\"Corra na velocidade média (PACE) de 6:30\\\",\\n      \\\"status\\\": \\\"incomplete\\\"\\n    }\\n  ]\\n}",
              "provider": "Openai",
              "model": "gpt-4o-mini"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts"
        name: get_all
        meta:
          id: req_887f13f45a994bb280e301374e2689b9
          created: 1738593766676
          modified: 1738593774207
          isPrivate: false
          sortKey: -1706313497110
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts/bff66da4-7c3a-4381-aff6-f44750852871"
        name: get prompt
        meta:
          id: req_bfafb395ebd74b308cfdc4e3d666ef10
          created: 1738593766677
          modified: 1738597838757
          isPrivate: false
          sortKey: -1706313478330
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts"
        name: update prompt
        meta:
          id: req_30f9bdef959544c59f1418247060b3c0
          created: 1738593814028
          modified: 1738593989668
          isPrivate: false
          sortKey: -1706295502351.7812
        method: PATCH
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Corrida",
            	"categoryId": "dfdcfc40-04ce-4074-8dc7-c690d9f7b0bf",
            	"description": "Crie um cronograma de ciclismo para [TARGET] em [PERIOD] com [ACTIVITIES_SIZE] atividades por semana",
            	"persona": "Atue como um personal trainer especialista em corrida, ciclismo e maratonas",
            	"returnType": "JSON",
            	"customAction": ""
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts/047397cb-f9fc-4d72-aec9-0a740968e5e4"
        name: delete prompt
        meta:
          id: req_f30808ef9dbf4d7d94ab2d5f444c54bb
          created: 1738593825271
          modified: 1738597855415
          isPrivate: false
          sortKey: -1706286605132.6719
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/llm-prompts/name/radar_recommendator"
        name: get prompt by name
        meta:
          id: req_794b55f84c754bea9350d40aa12bba58
          created: 1743454839171
          modified: 1744129765496
          isPrivate: false
          sortKey: -1706313468940
        method: GET
        parameters:
          - id: pair_d38d22808a624cc68867b2a21b2b560b
            name: amount
            value: "10"
          - id: pair_771b96266c9540cdb96e94a30bf9e5ba
            name: skip
            value: "0"
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Ticket Support
    meta:
      id: fld_7907932e862541a38ee185db45add777
      created: 1739378146925
      modified: 1739378146925
      sortKey: -1695347186223.9197
    children:
      - url: "{{ _.baseURL }}/user-support"
        name: Create ticket
        meta:
          id: req_9e870141ca52425883ff579946529701
          created: 1739378146926
          modified: 1739392877710
          isPrivate: false
          sortKey: -1736187530732
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"name": "Julio",
            	"email": "<EMAIL>",
            	"title": "Não consigo acessar o painel",
            	"cellphone": "41 99999-9999",
            	"message": "Tenho tentado acessar o painel, mas continuo recebendo um erro 404"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support?status=open"
        name: Get open tickets
        meta:
          id: req_334fa974649e407fa89b750ca486cfd0
          created: 1739378146927
          modified: 1739381678557
          isPrivate: false
          sortKey: -1736187530832
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support"
        name: Get all tickets
        meta:
          id: req_825adc72f85e4a67864a61e3f2e6bcbf
          created: 1739378146928
          modified: 1739391849127
          isPrivate: false
          sortKey: -1736187530932
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/0dc4f607-c204-4f25-b620-9fe7b4a32395"
        name: Delete ticket
        meta:
          id: req_f20a8d015087464094ea72cad117fa82
          created: 1739378146928
          modified: 1739381814453
          isPrivate: false
          sortKey: -1734988304552.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support?status=closed"
        name: Get closed tickets
        meta:
          id: req_647f300ffdf34f4ca7d58a277b39b847
          created: 1739378269403
          modified: 1739392792031
          isPrivate: false
          sortKey: -1736187530757
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/close/ef077823-b43d-41dc-b2a9-d1ae850d4d07"
        name: Close ticket
        meta:
          id: req_8454f65e57414c1db60f8530eb322e59
          created: 1739378287913
          modified: 1739394274883
          isPrivate: false
          sortKey: -1735587917642.25
        method: PATCH
        body:
          mimeType: multipart/form-data
          params:
            - id: pair_d572bc43510a480dbb955b0262a72bbe
              disabled: false
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/message/6e76a3c5-8f93-4558-be95-099660f7a471"
        name: Create ticket message
        meta:
          id: req_a06c0bc153654568a7c1c4915155da95
          created: 1739379923017
          modified: 1739392918204
          isPrivate: false
          sortKey: -1735887724187.125
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"message": "Pode me informar qual URL você está tentando acessar? Isso nos ajudará a identificar o problema."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/message/6e76a3c5-8f93-4558-be95-099660f7a471"
        name: Create ticket and userId
        meta:
          id: req_6e487a4c460d4834968e9b9059bca795
          created: 1739392988785
          modified: 1739392999606
          isPrivate: false
          sortKey: -1735737820914.6875
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"userId": "",
            	"message": "Pode me informar qual URL você está tentando acessar? Isso nos ajudará a identificar o problema."
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support/6e76a3c5-8f93-4558-be95-099660f7a471"
        name: Update Status
        meta:
          id: req_ad6e140394df476b91a6ec0efc64eda1
          created: 1739394433609
          modified: 1739395259985
          isPrivate: false
          sortKey: -1735662869278.4688
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"status": "open"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/user-support?page=1&limit=10"
        name: Get all tickets paginated
        meta:
          id: req_ce92f4a46ebe4df0927d55086235b507
          created: 1740680955031
          modified: 1740682266893
          isPrivate: false
          sortKey: -1736187530882
        method: GET
        body:
          mimeType: multipart/form-data
        headers:
          - name: Content-Type
            value: multipart/form-data
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Discounts
    meta:
      id: fld_cc678a204ed04ce38f31a20ff1ed4eba
      created: 1744639725247
      modified: 1744639853275
      sortKey: -1708361453268.5
    children:
      - url: "{{ _.baseURL }}/discounts"
        name: Get all discounts
        meta:
          id: req_1534f3f09cb1432088c8668127ae4522
          created: 1744639854473
          modified: 1744642835803
          isPrivate: false
          sortKey: -1744639854473
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts"
        name: Create discount
        meta:
          id: req_7f421dac38c7445f8d622a49cfabfaee
          created: 1744642846504
          modified: 1744648187859
          isPrivate: false
          sortKey: -1742612673903
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"origin": "internal",
            	"type": "percentage",
            	"value": 15,
            	"discountCode": "PLANIFY_15",
            	"maxUsageByUser": 1,
            	"maxUsersLimit": 30,
            	"expiresIn": 60
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/13ebc0ca-6d84-4647-b852-82e5a4346aee"
        name: Update discount
        meta:
          id: req_e8c70b17bffe441fb8c498463a117020
          created: 1744642864152
          modified: 1744645498640
          isPrivate: false
          sortKey: -1741599083618
        method: PATCH
        body:
          mimeType: application/json
          text: |-
            {
            	"value": 10
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/13ebc0ca-6d84-4647-b852-82e5a4346aee"
        name: Delete discount
        meta:
          id: req_1634a7ac12fb4539bd9d0c2e8f737b1c
          created: 1744642912528
          modified: 1744645536801
          isPrivate: false
          sortKey: -1741092288475.5
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/validate"
        name: Validate discount
        meta:
          id: req_4afe56d06b5f4c2182c8da301f101ff6
          created: 1744642940319
          modified: 1744809874382
          isPrivate: false
          sortKey: -1742105878760.5
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"discountCode": "PLANIFY_10"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/878e6057-6e3a-443c-a9bf-7fe92b9f73ec"
        name: Get discount infos
        meta:
          id: req_febda582b0cc4c5db8e555f87631155e
          created: 1744644524351
          modified: 1744890765872
          isPrivate: false
          sortKey: -1743626264188
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/apply"
        name: Apply discount
        meta:
          id: req_be5a350c0df14aa1959f440b120f5be8
          created: 1744645703963
          modified: 1744667799924
          isPrivate: false
          sortKey: -1741852481189.25
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"discountCode": "PLANIFY_10",
            	"userId": "4ce4f44e-54b1-4dfd-8869-c29156ce0ed8"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/e67d93d4-0113-4a34-98c5-8f1db1447300/history"
        name: Get discount history
        meta:
          id: req_d6dfcb53a99f42878253bdaa263d5106
          created: 1744673509896
          modified: 1744674142613
          isPrivate: false
          sortKey: -1743119469045.5
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/discounts/validate/price"
        name: Validate discount on checkout
        meta:
          id: req_a302c50ca2fb465392a9832bb8781f27
          created: 1744809865272
          modified: 1745587750662
          isPrivate: false
          sortKey: -1741979179974.875
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"discountCode": "Descont_100%",
            	"paymentPlanId": "6deabef5-3ab5-4b81-a6da-270723976c05"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.2
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: Affiliates
    meta:
      id: fld_3a072a5962e94ddfb5e118c36c298897
      created: 1745602399256
      modified: 1745602406944
      sortKey: -1708515009729.6875
    children:
      - url: "{{ _.baseURL }}/affiliates"
        name: Create Affiliate
        meta:
          id: req_b992b3b994d04edfab5c2c2a583af438
          created: 1745602420641
          modified: 1745602456207
          isPrivate: false
          sortKey: -1745602423203
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "name": "Lucas Rodrigues",
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/affiliates/auth"
        name: Login
        meta:
          id: req_f2dc757a1f004553a6a88afd134918a1
          created: 1745602441484
          modified: 1745617893346
          isPrivate: false
          sortKey: -1745602423303
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "name": "Lucas Rodrigues",
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/affiliates"
        name: Create Affiliate Infos
        meta:
          id: req_43b0d51a120e41d1bc67c89a7687992c
          created: 1745610754838
          modified: 1745610754838
          isPrivate: false
          sortKey: -1745516107644.5
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
              "name": "Lucas Rodrigues",
              "email": "<EMAIL>", 
              "password": "IBwSdAhZCv"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/affiliates/16fcfa71-da15-4725-9eb4-9049274f6ad3"
        name: Delete Affiliate
        meta:
          id: req_d03d0af006aa410c833718d3a93a028d
          created: 1745614345196
          modified: 1745614354896
          isPrivate: false
          sortKey: -1745472949865.25
        method: DELETE
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/affiliates"
        name: GET All Affiliates
        meta:
          id: req_90869a4788d34dabbafc38d3861467b7
          created: 1745615034566
          modified: 1745618037852
          isPrivate: false
          sortKey: -1745559265423.75
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.baseURL }}/affiliates/b43a3531-02a7-45ed-b84a-05cc7f90b965"
        name: GET Affiliate details
        meta:
          id: req_576e40c4c9864f4095df6c2ce680cd8a
          created: 1745615636712
          modified: 1745618071016
          isPrivate: false
          sortKey: -1745537686534.125
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/2023.5.8
        authentication:
          type: bearer
          token: "{{ _.affiliate_token }}"
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - url: "{{ _.baseURL }}/api/docs"
    name: API DOC
    meta:
      id: req_03416a8016e944d1bfaf894ab71e0871
      created: 1709589904958
      modified: 1741816455113
      isPrivate: false
      sortKey: -1709589904958
    method: GET
    headers:
      - name: User-Agent
        value: insomnia/8.6.1
    authentication:
      type: bearer
      token: "{{ _.token }}"
    settings:
      renderRequestBody: true
      encodeUrl: true
      followRedirects: global
      cookies:
        send: true
        store: true
      rebuildPath: true
  - url: "{{ _.baseURL }}"
    name: Health
    meta:
      id: req_9bd673561a0546d6ab6956422bae1e61
      created: 1741786364373
      modified: 1741786368293
      isPrivate: false
      sortKey: -1709587491472
    method: GET
    headers:
      - name: User-Agent
        value: insomnia/8.6.1
    settings:
      renderRequestBody: true
      encodeUrl: true
      followRedirects: global
      cookies:
        send: true
        store: true
      rebuildPath: true
cookieJar:
  name: Default Jar
  meta:
    id: jar_38fdf96bce4743e1ab4c31cb30157ce1
    created: 1706026250659
    modified: 1706026250659
environments:
  name: Base Environment
  meta:
    id: env_200fa6e47c434cd8a41cc6bd02ff43c7
    created: 1706026250657
    modified: 1708472623746
    isPrivate: false
  data:
    baseURL: localhost:3334
    botURL: localhost:5000
    token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.qFw27hFN3eVW_Q1qcfyjkJ0r2o-i3lmUWy7ffkYzblo
  subEnvironments:
    - name: Development
      meta:
        id: env_6ed046a94d494fe98b601b6f8f1febfc
        created: 1708472649957
        modified: 1745615727921
        isPrivate: true
        sortKey: 1708472649957
      data:
        baseURL: https://api.dev.planify.app.br
        botURL: http://************:5001
        token: "{% response 'body', 'req_7441d71a73ca40549997f88f77e3ae94',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
        affiliate_token: "{% response 'raw', 'req_f2dc757a1f004553a6a88afd134918a1',
          'b64::JHRva2Vu::46b', 'never', 60 %}"
    - name: Production
      meta:
        id: env_e97d8f080f0b4f78b2d6c2031fee61fb
        created: 1708472734516
        modified: 1745615718282
        isPrivate: true
        sortKey: 1708472734516
      data:
        baseURL: https://api.planify.app.br
        botURL: https://planai.com.br:6000
        token: "{% response 'body', 'req_7441d71a73ca40549997f88f77e3ae94',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
        affiliate_token: "{% response 'raw', 'req_f2dc757a1f004553a6a88afd134918a1',
          'b64::JHRva2Vu::46b', 'never', 60 %}"
    - name: Local
      meta:
        id: env_74309ee4ef884589819e07a207805eb3
        created: 1708472757679
        modified: 1745617033423
        isPrivate: true
        sortKey: 1708472757679
      data:
        baseURL: http://localhost:3333
        botURL: http://localhost:5001
        aiServiceURL: https://lucca-rodrigues-ai-services.hf.space
        token: "{% response 'body', 'req_7441d71a73ca40549997f88f77e3ae94',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
        affiliate_token: "{% response 'body', 'req_f2dc757a1f004553a6a88afd134918a1',
          'b64::JC50b2tlbg==::46b', 'never', 60 %}"
