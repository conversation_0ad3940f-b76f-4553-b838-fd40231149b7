# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a monorepo containing two main applications:

- **admin.planify.app.br/**: React admin dashboard (frontend)
- **planify-server/**: NestJS API server (backend)

## Development Commands

### Frontend (admin.planify.app.br)
```bash
cd admin.planify.app.br
npm run dev        # Start development server
npm run build      # Build for production
npm run lint       # Run ESLint
npm run gen        # Generate new page using Plop templates
```

### Backend (planify-server)
```bash
cd planify-server
npm run start:dev      # Start in watch mode
npm run start:prod     # Start in production mode
npm run build          # Build the application
npm run lint           # Run ESLint with auto-fix
npm run format         # Format code with Prettier
npm run test           # Run unit tests
npm run test:watch     # Run tests in watch mode
npm run test:cov       # Run tests with coverage
npm run test:e2e       # Run end-to-end tests

# Database migrations
npm run migrate        # Run pending migrations
npm run migrate:undo   # Undo last migration
npm run migrate:undoAll # Undo all migrations

# Generate NestJS resources
npx nest g resource modules/moduleName
npx nest g module modules/moduleName
npx nest g service modules/moduleName
npx nest g controller modules/moduleName
```

## Architecture Overview

### Frontend Architecture (admin.planify.app.br)
- **Framework**: React 18 + TypeScript + Vite
- **Styling**: TailwindCSS
- **State Management**: Zustand + React Context
- **HTTP Client**: Axios
- **Routing**: React Router DOM
- **Form Handling**: React Hook Form

**Page Structure Pattern** (Domain-Driven Design):
```
src/pages/[page-name]/
├── domain/
│   ├── models/          # Type definitions
│   ├── services/        # Business logic and API calls
│   └── index.ts         # Domain exports
├── functions/           # Utility functions
├── hooks/              # Custom React hooks
├── mocks/              # Mock data and table columns
├── template/
│   ├── components/     # Page-specific components
│   └── index.tsx       # Main template component
└── index.tsx           # Page entry point
```

**Code Generation**: Use `npm run gen` to scaffold new pages following the established pattern. This uses Plop templates located in `src/infra/templates/`.

### Backend Architecture (planify-server)
- **Framework**: NestJS + TypeScript
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT + Passport
- **Cache**: Redis (ioredis)
- **File Storage**: MinIO
- **Message Queue**: RabbitMQ with @planify/queues
- **Observability**: OpenTelemetry + Winston logging

**Module Structure Pattern**:
```
src/modules/[module-name]/
├── dto/                 # Data Transfer Objects
├── entities/           # Sequelize models
├── [module].controller.ts
├── [module].service.ts
├── [module].module.ts
└── [module].controller.spec.ts
```

**Core Modules Load Order** (important for dependency resolution):
1. Auth, Users, Queues, Redis
2. Notifications, Payments, UserPlans
3. Feature modules (alphabetical)

### Database Management
- **Migrations**: Located in `src/config/database/migrations/`
- **Migration Naming**: `YYYYMMDDHHMMSS-description.js`
- **Entity Registration**: All entities must be registered in `app.module.ts` models array

### Key Patterns

**Frontend**:
- Use domain services for API communication
- Implement React hooks for data fetching and state management
- Follow the established folder structure for consistency
- Use the Table component for data display with pagination

**Backend**:
- All controllers use DTOs for request/response validation
- Services handle business logic and database operations
- Guards handle authentication and authorization
- Interceptors handle cross-cutting concerns (date formatting)
- Use Sequelize transactions for complex operations

### Authentication & Authorization
- JWT-based authentication with role-based permissions
- Frontend stores tokens in cookies and manages auth state via context
- Backend uses guards for route protection and permission validation
- Affiliate system with separate authentication flow

### Important File Locations
- Frontend routes: `admin.planify.app.br/src/routes/routes.tsx`
- Backend module registration: `planify-server/src/app.module.ts`
- Database config: `planify-server/src/config/database/`
- Frontend HTTP client: `admin.planify.app.br/src/infra/httpRequest/`

### Development Workflow
1. For new frontend pages: Use `npm run gen` to scaffold with proper structure
2. For new backend modules: Use `npx nest g resource` to generate boilerplate
3. Always run migrations before starting backend development
4. Both projects support hot reloading in development mode
5. Use the established patterns for consistency across the codebase