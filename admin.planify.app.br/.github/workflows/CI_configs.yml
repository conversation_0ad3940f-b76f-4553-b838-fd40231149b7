name: CI_CD

permissions:
  contents: write
  issues: write
  pull-requests: write
  id-token: write
  deployments: write
  actions: write
  checks: write
  statuses: write

on:
  push:
    branches:
      - dev
      - main
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  generate_version:
    if: github.event_name == 'push' && (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main')
    runs-on: ubuntu-latest
    outputs:
      new_tag: ${{ steps.tag_version.outputs.new_tag }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN }}

      - name: Get current branch
        id: branch
        run: echo "branch=$(echo ${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}})" >> $GITHUB_OUTPUT

      - name: Generate SEMVER tag
        id: tag_version
        run: |
          # Define default versions for each environment
          if [[ "${{ github.event.pull_request.base.ref }}" == "dev" ]]; then
            default_version="0.4.3"
            env_prefix="dev-v"
          else
            default_version="1.0.0"
            env_prefix="v"  # prod não tem prefixo especial
          fi

          # Fetch all tags
          git fetch --tags

          # Get latest tag for this environment
          latest_tag=$(git tag -l "${env_prefix}*" | sort -V | tail -n 1)

          if [ -z "$latest_tag" ]; then
            # No tags found for this environment, use default
            latest_tag="${env_prefix}${default_version}"
          fi

          echo "Found latest tag for environment: $latest_tag"

          # Remove prefix to get just the version number
          version_number="${latest_tag#${env_prefix}}"

          # Extract version components
          IFS='.' read -r -a version_parts <<< "$version_number"
          major="${version_parts[0]}"
          minor="${version_parts[1]}"
          patch="${version_parts[2]:-0}"

          # Increment patch version
          new_patch=$((patch + 1))
          if [[ $new_patch -gt 9 ]]; then
            new_patch=0
            new_minor=$((minor + 1))
            if [[ $new_minor -gt 9 ]]; then
              new_minor=0
              new_major=$((major + 1))
            else
              new_major=$major
            fi
          else
            new_major=$major
            new_minor=$minor
          fi

          # Format new version with environment prefix
          new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"

          # Verify if new tag already exists
          while git rev-parse "$new_tag" >/dev/null 2>&1; do
            echo "Tag $new_tag already exists, incrementing..."
            new_patch=$((new_patch + 1))
            new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"
          done

          # Debug output
          echo "Current version: $latest_tag"
          echo "New version: $new_tag"

          # Set output
          echo "new_tag=${new_tag}" >> $GITHUB_OUTPUT

          # Configure git
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

          # Create and push tag
          git tag $new_tag
          git push origin $new_tag

  deploy_dev:
    needs: [generate_version]
    if: github.event.release.target_commitish == 'dev' || github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    steps:
      - name: Repository checkout
        uses: actions/checkout@v2
      - name: Connects to VPS via SSH
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{secrets.SERVER_HOST}}
          username: ${{secrets.SERVER_USER}}
          password: ${{secrets.SERVER_PASS}}
          script: |
            cd /home/<USER>/admin-planify
            git checkout dev --force
            git pull
            rm -rf node_modules package-lock.json
            npm install -f
            export VITE_API_CORE_URL=${{ secrets.VITE_API_CORE_URL_DEV }}
            export VITE_API_GATEWAY_URL=${{ secrets.VITE_API_GATEWAY_URL_DEV }}
            export VITE_GATEWAY_SECRET_KEY=${{ secrets.VITE_GATEWAY_SECRET_KEY }}
            export VITE_QUEUE_MANAGER_URL=${{ secrets.VITE_QUEUE_MANAGER_URL_DEV }}
            export VITE_APP_VERSION=${{ needs.generate_version.outputs.new_tag }}

            npm run build

            cp -r /home/<USER>/admin-planify/dist/* /home/<USER>/htdocs/admin.dev.planify.app.br
            cd /home/<USER>/htdocs/admin.dev.planify.app.br
            ls -la
            sudo systemctl reload nginx

  deploy_prod:
    needs: [generate_version]
    if: github.event.release.target_commitish == 'main' || github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Repository checkout
        uses: actions/checkout@v2
      - name: Connects to VPS via SSH
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{secrets.SERVER_HOST}}
          username: ${{secrets.SERVER_USER}}
          password: ${{secrets.SERVER_PASS}}
          script: |
            cd /home/<USER>/admin-planify
            git checkout main --force
            git pull
            rm -rf node_modules package-lock.json
            npm install
            export VITE_API_CORE_URL=${{ secrets.VITE_API_CORE_URL }}
            export VITE_API_GATEWAY_URL=${{ secrets.VITE_API_GATEWAY_URL }}
            export VITE_GATEWAY_SECRET_KEY=${{ secrets.VITE_GATEWAY_SECRET_KEY }}
            export VITE_QUEUE_MANAGER_URL=${{ secrets.VITE_QUEUE_MANAGER_URL }}
            export VITE_APP_VERSION=${{ needs.generate_version.outputs.new_tag }}

            npm run build

            cp -r /home/<USER>/admin-planify/dist/* /home/<USER>/htdocs/admin.planify.app.br
            cd /home/<USER>/htdocs/admin.planify.app.br
            ls -la
            sudo systemctl reload nginx

  notify_discord:
    needs: [generate_version, deploy_dev, deploy_prod]
    if: always()
    runs-on: ubuntu-latest
    env:
      DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK_URL }}
    steps:
      - name: Check Discord Webhook URL
        run: |
          if [ -z "$DISCORD_WEBHOOK_URL" ]; then
            echo "⚠️ DISCORD_WEBHOOK_URL não está configurado nos secrets"
            exit 1
          fi

      - name: Install jq
        run: |
          sudo apt-get update
          sudo apt-get install -y jq

      - name: Send Discord Notification
        shell: bash
        run: |
          echo "Preparando notificação do Discord..."

          # Escapar caracteres especiais na mensagem do commit
          COMMIT_MESSAGE=$(echo '${{ github.event.head_commit.message }}' | sed 's/"/\\"/g')
          COMMIT_AUTHOR='${{ github.event.head_commit.author.name }}'

          # Formatar a data para DD-MM-YYYY HH:MM
          FORMATTED_DATE=$(date -u +"%d-%m-%Y %H:%M")
          CURRENT_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

          # Determinar o ambiente com base na branch
          if [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            ENVIRONMENT="DEV"
            DEPLOY_RESULT="${{ needs.deploy_dev.result }}"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            ENVIRONMENT="PROD"
            DEPLOY_RESULT="${{ needs.deploy_prod.result }}"
          else
            ENVIRONMENT="UNKNOWN"
            DEPLOY_RESULT="skipped"
          fi

          echo "Ambiente detectado: $ENVIRONMENT"
          echo "Resultado do deploy: $DEPLOY_RESULT"

          # Verificando resultado dos jobs de deploy
          if [[ "$DEPLOY_RESULT" == "success" ]]; then
            echo "Deploy bem sucedido, enviando notificação de sucesso..."
            STATUS='🟢 Novo deploy PLANIFY ADMIN finalizado!'
            DESCRIPTION='🚀 App: PLANIFY ADMIN '
            COLOR=65280
          else
            echo "Deploy falhou, enviando notificação de erro..."
            STATUS='🔴 Erro ao realizar deploy de PLANIFY ADMIN!'
            DESCRIPTION='App: PLANIFY ADMIN'
            COLOR=16711680
          fi

          # Construir o payload JSON usando jq para garantir um JSON válido
          PAYLOAD=$(jq -n \
            --arg title "$STATUS" \
            --arg desc "$DESCRIPTION" \
            --arg ambiente "$ENVIRONMENT" \
            --arg autor "$COMMIT_AUTHOR" \
            --arg alteracoes "$COMMIT_MESSAGE" \
            --arg data "$FORMATTED_DATE" \
            --arg timestamp "$CURRENT_TIME" \
            --argjson color "$COLOR" \
            '{
              embeds: [{
                title: $title,
                description: $desc,
                color: $color,
                timestamp: $timestamp,
                fields: [
                  {
                    name: "Ambiente",
                    value: $ambiente,
                    inline: false
                  },
                  {
                    name: "Autor",
                    value: $autor,
                    inline: false
                  },
                  {
                    name: "Alterações",
                    value: $alteracoes,
                    inline: false
                  }
                ]
              }]
            }')

          echo "Enviando notificação para o Discord..."

          # Enviar a requisição para o Discord e capturar resposta
          RESPONSE=$(curl -s -w "\n%{http_code}" -H "Content-Type: application/json" -X POST -d "${PAYLOAD}" "${DISCORD_WEBHOOK_URL}")
          HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
          BODY=$(echo "$RESPONSE" | sed '$d')

          if [[ "$HTTP_CODE" == 2* ]]; then
            echo "✅ Notificação enviada com sucesso! Código HTTP: $HTTP_CODE"
          else
            echo "❌ Falha ao enviar notificação para o Discord. Código HTTP: $HTTP_CODE"
            echo "Resposta do Discord: $BODY"
            exit 1
          fi
