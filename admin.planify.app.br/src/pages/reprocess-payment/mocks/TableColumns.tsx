import { formatCurrency } from "@/shared/functions/formatCurrency";
import { WebhookData } from "../domain/models";
import { Eye } from "lucide-react";

interface TableColumnsProps {
  onSelectHook: (hookId: string) => void;
  onSelectAll: () => void;
  selectedHooks: string[];
  webhooks: WebhookData[];
  onViewDetails: (webhook: WebhookData) => void;
}

export function TableColumns({ onSelectHook, selectedHooks, onViewDetails }: TableColumnsProps) {
  return [
    {
      id: "select",
      label: "",
      render: (rowData: WebhookData) => (
        <div className="flex items-center">
          <input
            type="checkbox"
            checked={selectedHooks.includes(rowData.id)}
            onChange={() => onSelectHook(rowData.id)}
            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200"
          />
        </div>
      ),
    },
    {
      id: "name",
      label: "NOME",
      render: (rowData: WebhookData) => rowData?.data?.customer?.name,
    },
    {
      id: "email",
      label: "E-MAIL",
      render: (rowData: WebhookData) => rowData?.data?.customer?.email,
    },
    {
      id: "cpf",
      label: "CPF",
      render: (rowData: WebhookData) => rowData?.data?.customer?.document,
    },
    // {
    //   id: "event",
    //   label: "EVENTO",
    //   render: (rowData: WebhookData) => rowData.event,
    // },
    {
      id: "price",
      label: "VALOR",
      render: (rowData: WebhookData) => formatCurrency((rowData.data.amount / 100).toFixed(2)),
    },
    {
      id: "attempts",
      label: "TENTATIVAS",
      render: (rowData: WebhookData) => rowData.attempts,
    },
    {
      id: "status",
      label: "STATUS",
      render: (rowData: WebhookData) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            {
              success: "bg-green-100 text-green-800",
              pending: "bg-yellow-100 text-yellow-800",
              error: "bg-red-100 text-red-800",
            }[rowData.status] || "bg-gray-100 text-gray-800"
          }`}
        >
          {rowData.status}
        </span>
      ),
    },
    {
      id: "last_attempt",
      label: "ÚLTIMA TENTATIVA",
      render: (rowData: WebhookData) => new Date(rowData.last_attempt).toLocaleString(),
    },
    {
      id: "response_status",
      label: "ERROR",
      render: (rowData: WebhookData) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          {rowData.response_status}
        </span>
      ),
    },
    {
      id: "actions",
      label: "AÇÕES",
      render: (rowData: WebhookData) => (
        <button
          onClick={() => onViewDetails(rowData)}
          className="p-2 text-gray-600 hover:text-primary hover:bg-gray-100 rounded-full transition-colors"
          title="Ver Detalhes"
        >
          <Eye className="h-5 w-5" />
        </button>
      ),
    },
  ];
}
