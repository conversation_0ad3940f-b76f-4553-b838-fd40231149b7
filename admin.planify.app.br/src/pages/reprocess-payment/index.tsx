import { Template } from "./template";
import { useReprocessPayment } from "./hooks/useReprocessPayment";
import { useEffect } from "react";

export default function ReprocessPaymentPage() {
  const hookParams = useReprocessPayment();

  const { getHooks } = hookParams;

  const sharedProps = {
    ...hookParams,
  };

  useEffect(() => {
    getHooks();
  }, []);
  return <Template {...sharedProps} />;
}
