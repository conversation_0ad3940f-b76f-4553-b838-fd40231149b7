import { useState } from "react";
import { toast } from "react-toastify";
import { ReprocessPaymentServices } from "../domain";
import { WebhookData } from "../domain/models";

export function useReprocessPayment() {
  const service = new ReprocessPaymentServices();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [webhookData, setWebhookData] = useState<string>("");
  const [webhooks, setWebhooks] = useState<WebhookData[]>([]);
  const [selectedHooks, setSelectedHooks] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const getHooks = async () => {
    try {
      setIsLoading(true);
      const response = await service.getFailedWebhooks();
      setWebhooks(response.data);
    } catch (error) {
      console.error("Erro ao buscar webhooks:", error);
      toast.error("Erro ao buscar webhooks. Por favor, tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectHook = (hookId: string) => {
    setSelectedHooks((prev) => {
      if (prev.includes(hookId)) {
        return prev.filter((id) => id !== hookId);
      }
      return [...prev, hookId];
    });
  };

  const handleSelectAll = () => {
    if (selectedHooks.length === webhooks.length) {
      setSelectedHooks([]);
    } else {
      setSelectedHooks(webhooks.map((hook) => hook.id));
    }
  };

  const handleRetryBatch = async () => {
    try {
      setIsLoading(true);
      await service.retryBatch(selectedHooks);
      toast.success("Itens enviados para re-processamento com sucesso!");
      setSelectedHooks([]);
      getHooks();
    } catch (error) {
      console.error("Erro ao reprocessar", error);
      toast.error("Erro ao reprocessar. Por favor, tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      let parsedData: WebhookData;
      try {
        parsedData = JSON.parse(webhookData);
      } catch {
        toast.error("JSON inválido. Por favor, verifique o formato dos dados.");
        return;
      }

      await service.reprocessSingle(parsedData);
      toast.success("Pagamento reprocessado com sucesso!");
      setIsModalOpen(false);
      setWebhookData("");
    } catch (error) {
      console.error("Erro ao reprocessar pagamento:", error);
      toast.error("Erro ao reprocessar pagamento. Por favor, tente novamente.");
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setWebhookData("");
  };

  return {
    isModalOpen,
    setIsModalOpen,
    webhookData,
    setWebhookData,
    handleSubmit,
    webhooks,
    selectedHooks,
    handleSelectHook,
    handleSelectAll,
    handleRetryBatch,
    isLoading,
    getHooks,
    handleCloseModal,
  };
}
