import { RotateCw } from "lucide-react";
import { Table } from "@/components/Table";
import { TableColumns } from "../mocks/TableColumns";
import { WebhookDetailsModal } from "../components/WebhookDetailsModal";
import { WebhookData } from "../domain/models";
import { useState } from "react";

export function Template({ ...sharedProps }) {
  const {
    isModalOpen,
    setIsModalOpen,
    webhookData,
    setWebhookData,
    handleSubmit,
    webhooks,
    selectedHooks,
    handleSelectHook,
    handleSelectAll,
    handleRetryBatch,
    isLoading,
    handleCloseModal,
  } = sharedProps;

  const [selectedWebhook, setSelectedWebhook] = useState<WebhookData | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const handleViewDetails = (webhook: WebhookData) => {
    setSelectedWebhook(webhook);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedWebhook(null);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Reprocessamento</h1>
        <button
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600"
        >
          <RotateCw className="h-4 w-4 mr-2" />
          Re-enviar webhook
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : webhooks.length === 0 ? (
        <div className="flex justify-center items-center h-64 text-gray-500">Nenhum item disponível para reprocessamento</div>
      ) : (
        <div>
          <div className="mb-4">
            <button
              onClick={handleRetryBatch}
              disabled={selectedHooks.length === 0}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                selectedHooks.length === 0 ? "bg-gray-400 cursor-not-allowed" : "bg-primary hover:bg-red-600"
              }`}
            >
              <RotateCw className="h-4 w-4 mr-2" />
              Reprocessar Selecionados
            </button>
          </div>
          <Table
            columns={TableColumns({
              onSelectHook: handleSelectHook,
              onSelectAll: handleSelectAll,
              selectedHooks,
              webhooks,
              onViewDetails: handleViewDetails,
            })}
            data={webhooks}
          />
        </div>
      )}

      {isModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
              <div className="flex flex-col max-h-[90vh]">
                <div className="px-6 py-3 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Reprocessamento</h3>
                    <button onClick={handleCloseModal} className="p-2 hover:bg-gray-100 rounded-full transition-colors" title="Fechar">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>

                <form onSubmit={handleSubmit} className="flex flex-col flex-1">
                  <div className="flex-1 overflow-y-auto p-6">
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="webhookData" className="block text-sm font-medium text-gray-700 mb-1">
                          Dados do Webhook (JSON)
                        </label>
                        <textarea
                          name="webhookData"
                          id="webhookData"
                          required
                          value={webhookData}
                          onChange={(e) => setWebhookData(e.target.value)}
                          className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md h-96"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
                    <button
                      type="submit"
                      className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      Reprocessar
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedWebhook && <WebhookDetailsModal webhook={selectedWebhook} isOpen={isDetailsModalOpen} onClose={handleCloseDetailsModal} />}
    </div>
  );
}
