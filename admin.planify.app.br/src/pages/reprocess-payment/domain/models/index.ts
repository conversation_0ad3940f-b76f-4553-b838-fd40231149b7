export interface WebhookData {
  id: string;
  url: string;
  event: string;
  status: string;
  attempts: string;
  last_attempt: string;
  created_at: string;
  response_status: number;
  response_raw: string;
  account: {
    id: string;
    name: string;
  };
  data: {
    id: string;
    code: string;
    amount: number;
    paid_amount: number;
    status: string;
    customer: {
      name: string;
      email: string;
      document: string;
      phone: string;
    };
    subscription: {
      id: string;
      customer_id: string;
      gateway_plan_id: string;
    };
    payment_method: string;
    phones: { mobile_phone: { country_code: string; number: string; area_code: string } };
  };
}

export interface WebhookResponse {
  data: WebhookData[];
  paging: {
    total: number;
  };
}
