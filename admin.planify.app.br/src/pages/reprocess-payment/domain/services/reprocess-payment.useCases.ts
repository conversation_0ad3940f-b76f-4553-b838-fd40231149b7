import HttpClient from "@/infra/httpRequest";
import { envs } from "@/shared/functions/envsProxy";
import { WebhookData, WebhookResponse } from "../models";

export interface ReprocessResponse {
  message: string;
  totalProcessed: number;
  successCount: number;
  errorCount: number;
  results: Array<{
    hookId: string;
    success: boolean;
    data: any;
  }>;
  errors: any[];
}

const BASE_URL = envs.VITE_API_GATEWAY_URL;
const httpClient = new HttpClient(BASE_URL);
export default class ReprocessPaymentServices {
  async getFailedWebhooks(): Promise<WebhookResponse> {
    const response = await httpClient.get(`/webhooks/hooks?status=failed`);
    return response?.data;
  }

  async retryBatch(hookIds: string[]): Promise<ReprocessResponse> {
    const response = await httpClient.post(`/webhooks/hooks/retry-batch`, {
      hookIds,
    });
    return response?.data;
  }

  async reprocessSingle(webhookData: WebhookData): Promise<ReprocessResponse> {
    const response = await httpClient.post(`/webhooks/payment`, webhookData);
    return response?.data;
  }
}
