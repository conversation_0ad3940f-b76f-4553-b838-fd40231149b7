import { formatCurrency } from "@/shared/functions/formatCurrency";
import { WebhookData } from "../domain/models";

interface WebhookDetailsModalProps {
  webhook: WebhookData;
  onClose: () => void;
  isOpen: boolean;
}

export function WebhookDetailsModal({ webhook, onClose, isOpen }: WebhookDetailsModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed z-10 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-6 pt-6 pb-4">
            <div className="flex justify-between items-center mb-6 border-b pb-4">
              <h3 className="text-xl font-semibold text-gray-900">Detalhes do Webhook</h3>
              <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-full transition-colors" title="Fechar">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-2 gap-8 overflow-y-auto max-h-[70vh] pr-2">
              <div className="space-y-8">
                <div>
                  <h4 className="text-base font-semibold text-gray-900 mb-3">Informações do Cliente</h4>
                  <div className="bg-gray-50 p-5 rounded-lg space-y-3">
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">Nome:</span>
                      <span className="text-gray-900">{webhook.data.customer.name}</span>
                    </p>
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">Email:</span>
                      <span className="text-gray-900">{webhook.data.customer.email}</span>
                    </p>
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">CPF:</span>
                      <span className="text-gray-900">{webhook.data.customer.document}</span>
                    </p>
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">Telefone:</span>
                      <span className="text-gray-900">{`(${webhook.data.customer.phones?.mobile_phone?.area_code}) ${webhook.data.customer.phones?.mobile_phone?.number}`}</span>
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-base font-semibold text-gray-900 mb-3">Detalhes do Pagamento</h4>
                  <div className="bg-gray-50 p-5 rounded-lg space-y-3">
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">ID da Transação:</span>
                      <span className="text-gray-900">{webhook.data.id}</span>
                    </p>
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">Código:</span>
                      <span className="text-gray-900">{webhook.data.code}</span>
                    </p>
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">Valor:</span>
                      <span className="text-gray-900">R$ {(webhook.data.amount / 100).toFixed(2)}</span>
                    </p>
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">Valor Pago:</span>
                      <span className="text-gray-900">R$ {(webhook.data.paid_amount / 100).toFixed(2)}</span>
                      <span className="text-gray-900">R$ {formatCurrency((webhook.data.paid_amount / 100).toFixed(2))}</span>
                    </p>
                    <p className="flex justify-between">
                      <span className="font-medium text-gray-600">Status:</span>
                      <span className="text-gray-900">{webhook.data.status}</span>
                    </p>
                  </div>
                </div>
              </div>
              <div className="space-y-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Informações do Webhook</h4>
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <p>
                      <span className="font-medium">ID:</span> {webhook.id}
                    </p>
                    <p>
                      <span className="font-medium">Evento:</span> {webhook.event}
                    </p>
                    <p>
                      <span className="font-medium">Status:</span> {webhook.status}
                    </p>
                    <p>
                      <span className="font-medium">Tentativas:</span> {webhook.attempts}
                    </p>
                    <p>
                      <span className="font-medium">Última Tentativa:</span> {new Date(webhook.last_attempt).toLocaleString()}
                    </p>
                    <p>
                      <span className="font-medium">Criado em:</span> {new Date(webhook.created_at).toLocaleString()}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Resposta do Servidor</h4>
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <p>
                      <span className="font-medium">Status:</span> {webhook.response_status}
                    </p>
                    <div className="mt-2">
                      <p className="font-medium mb-1">Resposta Raw:</p>
                      <pre className="text-sm bg-gray-100 p-2 rounded overflow-x-auto">{webhook.response_raw}</pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
