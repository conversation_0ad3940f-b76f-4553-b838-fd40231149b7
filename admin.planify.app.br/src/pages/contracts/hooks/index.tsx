import { useRef, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { 
  IAffiliateLink, 
  INegotiation, 
  INegotiationFormData, 
  INegotiationResponse, 
  IPlan, 
  IPlanConfiguration, 
  IUser,
  INegotiationsResponse
} from "../domain/models";
import { toast } from "react-toastify";
import { ContractsServices } from "../domain";
import PlanServices from "../../plans/domain/services/plan.useCases";
import AfiliatesServices from "../../afiliates/domain/services/afiliates.useCases";

export default function useContracts() {
  // Services
  const contractsService = new ContractsServices();
  const plansService = new PlanServices();
  const afiliatesService = new AfiliatesServices();

  // State
  const [pageData, setPageData] = useState<INegotiationResponse | undefined>();
  const [selectedNegotiation, setSelectedNegotiation] = useState<INegotiation | null>(null);
  const [affiliates, setAffiliates] = useState<IAffiliateLink[]>([]);
  const [availableAffiliates, setAvailableAffiliates] = useState<IAffiliateLink[]>([]);
  const [availablePlans, setAvailablePlans] = useState<IPlan[]>([]);
  const [selectedPlans, setSelectedPlans] = useState<IPlan[]>([]);
  const [availableUsers, setAvailableUsers] = useState<IUser[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<IUser[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchAffiliatesTerm, setSearchAffiliatesTerm] = useState("");
  const [searchPlansTerm, setSearchPlansTerm] = useState("");
  const [searchUsersTerm, setSearchUsersTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [isLoading, setIsLoading] = useState(false);
  const [isAffiliatesLoading, setIsAffiliatesLoading] = useState(false);
  const [isPlansLoading, setIsPlansLoading] = useState(false);
  const [isUsersLoading, setIsUsersLoading] = useState(false);

  // Modais
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isAffiliatesModalOpen, setIsAffiliatesModalOpen] = useState(false);
  const [isPlansModalOpen, setIsPlansModalOpen] = useState(false);
  const [isUsersModalOpen, setIsUsersModalOpen] = useState(false);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [currentAffiliateId, setCurrentAffiliateId] = useState<string | null>(null);

  // Formulário
  const { control, handleSubmit, watch, setValue, reset } = useForm<INegotiationFormData>();
  const watchFields = watch();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Upload de comprovante
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const navigate = useNavigate();

  // Buscar todos os planos disponíveis
  async function getAvailablePlans() {
    setIsPlansLoading(true);
    try {
      const plans = await plansService.get();
      
      // Adaptar os dados dos planos para o formato esperado
      let adaptedPlans = plans.map(plan => ({
        id: plan.id,
        name: plan.name,
        description: plan.description || "",
        price: parseFloat(plan.price) || 0,
        status: plan.status as "active" | "inactive"
      }));

      // Filtrando por termo de busca, se existir
      if (searchPlansTerm) {
        const searchLower = searchPlansTerm.toLowerCase();
        adaptedPlans = adaptedPlans.filter((plan) => 
          plan.name.toLowerCase().includes(searchLower) || 
          plan.description.toLowerCase().includes(searchLower)
        );
      }

      setAvailablePlans(adaptedPlans);
      setIsPlansLoading(false);
    } catch (error) {
      console.error("Erro ao buscar planos disponíveis:", error);
      toast.error("Erro ao buscar planos disponíveis");
      setIsPlansLoading(false);
    }
  }

  // Buscar planos de uma negociação
  async function getNegotiationPlans(negotiationId: string) {
    setIsPlansLoading(true);
    try {
      // Buscar a negociação completa para obter os planos associados
      const negotiation = await contractsService.getById(negotiationId);
      
      if (negotiation.paymentPlans) {
        const adaptedPlans = negotiation.paymentPlans.map((plan: any) => ({
          id: plan.id,
          name: plan.name,
          description: plan.description || "",
          price: typeof plan.price === 'string' ? parseFloat(plan.price) : plan.price || 0,
          status: plan.status as "active" | "inactive"
        }));
        
        setSelectedPlans(adaptedPlans);
      } else {
        setSelectedPlans([]);
      }
      
      setIsPlansLoading(false);
    } catch (error) {
      console.error("Erro ao buscar planos da negociação:", error);
      toast.error("Erro ao buscar planos da negociação");
      setSelectedPlans([]);
      setIsPlansLoading(false);
    }
  }

  // Adicionar plano à negociação
  async function addPlanToNegotiation(negotiationId: string, planId: string) {
    setIsPlansLoading(true);
    try {
      // Encontrar o plano pelo ID
      const planToAdd = availablePlans.find((p) => p.id === planId);

      if (!planToAdd) {
        toast.error("Plano não encontrado");
        setIsPlansLoading(false);
        return;
      }

      // Buscar negociação atual para verificar planos existentes
      const currentNegotiation = await contractsService.getById(negotiationId);
      const currentPlanIds = currentNegotiation.paymentPlans?.map((p: any) => p.id) || [];
      
      // Verificar se o plano já está associado
      if (currentPlanIds.includes(planId)) {
        toast.error("Este plano já está associado à negociação");
        setIsPlansLoading(false);
        return;
      }

      // Adicionar o novo plano aos IDs existentes
      const updatedPlanIds = [...currentPlanIds, planId];

      // Configuração do novo plano
      const newPlanConfiguration = {
        paymentPlanId: planId,
        type: selectedNegotiation?.type || "fixed",
        value: selectedNegotiation?.value || 0,
        recurrencyType: selectedNegotiation?.recurrencyType || "limited",
        recurrencyPeriod: selectedNegotiation?.recurrencyPeriod || "monthly",
        recurrencyLimit: selectedNegotiation?.recurrencyLimit || 1,
      };

      // Manter configurações existentes dos planos atuais
      const existingConfigurations = currentNegotiation.paymentPlans?.map((plan: any) => {
        const planConfig = plan.NegotiationPaymentPlan || {};
        return {
          paymentPlanId: plan.id,
          type: planConfig.type || selectedNegotiation?.type || "fixed",
          value: planConfig.value || selectedNegotiation?.value || 0,
          recurrencyType: planConfig.recurrencyType || selectedNegotiation?.recurrencyType || "limited",
          recurrencyPeriod: planConfig.recurrencyPeriod || selectedNegotiation?.recurrencyPeriod || "monthly",
          recurrencyLimit: planConfig.recurrencyLimit || selectedNegotiation?.recurrencyLimit || 1,
        };
      }) || [];

      const allConfigurations = [...existingConfigurations, newPlanConfiguration];

      // Atualizar a negociação com novos planos e configurações
      await contractsService.update(negotiationId, {
        paymentPlanIds: updatedPlanIds,
        planConfigurations: allConfigurations
      });
      
      toast.success("Plano adicionado com sucesso!");
      getNegotiationPlans(negotiationId);
      setIsPlansLoading(false);
    } catch (error) {
      console.error("Erro ao adicionar plano:", error);
      toast.error("Erro ao adicionar plano");
      setIsPlansLoading(false);
    }
  }

  // Remover plano da negociação
  async function removePlanFromNegotiation(negotiationId: string, planId: string) {
    setIsPlansLoading(true);
    try {
      // Buscar negociação atual
      const currentNegotiation = await contractsService.getById(negotiationId);
      const currentPlanIds = currentNegotiation.paymentPlans?.map((p: any) => p.id) || [];
      
      // Remover o plano específico
      const updatedPlanIds = currentPlanIds.filter((id: string) => id !== planId);
      
      // Manter apenas as configurações dos planos que não foram removidos
      const updatedConfigurations = currentNegotiation.paymentPlans
        ?.filter((plan: any) => plan.id !== planId)
        .map((plan: any) => {
          const planConfig = plan.NegotiationPaymentPlan || {};
          return {
            paymentPlanId: plan.id,
            type: planConfig.type || selectedNegotiation?.type || "fixed",
            value: planConfig.value || selectedNegotiation?.value || 0,
            recurrencyType: planConfig.recurrencyType || selectedNegotiation?.recurrencyType || "limited",
            recurrencyPeriod: planConfig.recurrencyPeriod || selectedNegotiation?.recurrencyPeriod || "monthly",
            recurrencyLimit: planConfig.recurrencyLimit || selectedNegotiation?.recurrencyLimit || 1,
          };
        }) || [];

      // Atualizar a negociação com planos removidos
      await contractsService.update(negotiationId, {
        paymentPlanIds: updatedPlanIds,
        planConfigurations: updatedConfigurations
      });
      
      toast.success("Plano removido com sucesso!");
      getNegotiationPlans(negotiationId);
      setIsPlansLoading(false);
    } catch (error) {
      console.error("Erro ao remover plano:", error);
      toast.error("Erro ao remover plano");
      setIsPlansLoading(false);
    }
  }

  // Configurar negociação como aplicável a todos os planos
  async function setApplicableToAllPlans(negotiationId: string, applicable: boolean) {
    setIsPlansLoading(true);
    try {
      const updateData = {
        allowDefault: applicable
      };

      await contractsService.update(negotiationId, updateData);
      
      // Se está tornando aplicável a todos os planos, limpar configurações específicas
      if (applicable) {
        await contractsService.update(negotiationId, {
          paymentPlanIds: [],
          planConfigurations: []
        });
      }
      
      toast.success(`Negociação ${applicable ? "agora é aplicável a todos os planos" : "agora requer seleção de planos específicos"}`);
      
      // Atualizar negociação selecionada
      if (selectedNegotiation) {
        setSelectedNegotiation({
          ...selectedNegotiation,
          allowDefault: applicable
        });
      }
      
      getNegotiationPlans(negotiationId);
      setIsPlansLoading(false);
    } catch (error) {
      console.error("Erro ao configurar aplicabilidade a todos os planos:", error);
      toast.error("Erro ao configurar aplicabilidade");
      setIsPlansLoading(false);
    }
  }

  // Desativar/ativar plano na negociação
  async function togglePlanStatus(negotiationId: string, planId: string, disabled: boolean) {
    setIsPlansLoading(true);
    try {
      // Buscar negociação atual
      const currentNegotiation = await contractsService.getById(negotiationId);
      
      // Atualizar configurações dos planos
      const updatedConfigurations = currentNegotiation.paymentPlans?.map((plan: any) => {
        const planConfig = plan.NegotiationPaymentPlan || {};
        
        if (plan.id === planId) {
          return {
            paymentPlanId: plan.id,
            type: planConfig.type || selectedNegotiation?.type || "fixed",
            value: planConfig.value || selectedNegotiation?.value || 0,
            recurrencyType: planConfig.recurrencyType || selectedNegotiation?.recurrencyType || "limited",
            recurrencyPeriod: planConfig.recurrencyPeriod || selectedNegotiation?.recurrencyPeriod || "monthly",
            recurrencyLimit: planConfig.recurrencyLimit || selectedNegotiation?.recurrencyLimit || 1,
            disabled: disabled
          };
        } else {
          return {
            paymentPlanId: plan.id,
            type: planConfig.type || selectedNegotiation?.type || "fixed",
            value: planConfig.value || selectedNegotiation?.value || 0,
            recurrencyType: planConfig.recurrencyType || selectedNegotiation?.recurrencyType || "limited",
            recurrencyPeriod: planConfig.recurrencyPeriod || selectedNegotiation?.recurrencyPeriod || "monthly",
            recurrencyLimit: planConfig.recurrencyLimit || selectedNegotiation?.recurrencyLimit || 1,
            disabled: planConfig.disabled || false
          };
        }
      }) || [];

      // Atualizar a negociação
      await contractsService.update(negotiationId, {
        planConfigurations: updatedConfigurations
      });
      
      toast.success(`Plano ${disabled ? "desativado" : "ativado"} com sucesso!`);
      getNegotiationPlans(negotiationId);
      setIsPlansLoading(false);
    } catch (error) {
      console.error("Erro ao alterar status do plano:", error);
      toast.error("Erro ao alterar status do plano");
      setIsPlansLoading(false);
    }
  }

  // Buscar negociações com paginação e filtros
  async function getNegotiations(paramName?: string, paramValue?: unknown) {
    setIsLoading(true);
    try {
      const currentParams = {
        page: pageData?.currentPage || 1,
        limit: pageData?.pageSize || 10,
        status: activeTab !== "all" ? activeTab : undefined,
      };

      if (paramName && paramValue !== undefined) {
        Object.assign(currentParams, { [paramName]: paramValue });
      }

      const response = await contractsService.get(currentParams);
      
      // Validar e sanear os dados da API antes de usar
      const validatedData = response.data.map((negotiation: any) => ({
        ...negotiation,
        id: negotiation.id || '',
        name: negotiation.name || 'Nome não informado',
        type: ['fixed', 'percentage'].includes(negotiation.type) ? negotiation.type : 'fixed',
        value: typeof negotiation.value === 'number' ? negotiation.value : 0,
        recurrencyType: ['limited', 'unlimited'].includes(negotiation.recurrencyType) ? negotiation.recurrencyType : 'limited',
        recurrencyPeriod: ['monthly', 'yearly', 'first_payment'].includes(negotiation.recurrencyPeriod) ? negotiation.recurrencyPeriod : 'monthly',
        recurrencyLimit: typeof negotiation.recurrencyLimit === 'number' ? negotiation.recurrencyLimit : 1,
        status: ['active', 'inactive'].includes(negotiation.status) ? negotiation.status : 'active',
        allowDefault: Boolean(negotiation.allowDefault),
        createdAt: negotiation.createdAt || new Date().toISOString(),
        updatedAt: negotiation.updatedAt || new Date().toISOString(),
        affiliates: Array.isArray(negotiation.affiliates) ? negotiation.affiliates : [],
        paymentPlans: Array.isArray(negotiation.paymentPlans) ? negotiation.paymentPlans : []
      }));
      
      // Adaptar a resposta da API para o formato esperado pelo frontend
      const adaptedResponse: INegotiationResponse = {
        data: validatedData,
        currentPage: response.currentPage || 1,
        pageSize: response.itemsPerPage || 10,
        totalRecords: response.totalItems || 0,
      };

      setPageData(adaptedResponse);
      setIsLoading(false);
    } catch (error) {
      console.error("Erro ao buscar negociações:", error);
      toast.error("Erro ao buscar negociações");
      setIsLoading(false);
    }
  }

  // Buscar detalhes de uma negociação específica
  async function getNegotiationById(id: string) {
    setIsLoading(true);
    try {
      const negotiation = await contractsService.getById(id);
      
      // Validar dados da negociação específica
      const validatedNegotiation = {
        ...negotiation,
        id: negotiation.id || '',
        name: negotiation.name || 'Nome não informado',
        type: ['fixed', 'percentage'].includes(negotiation.type) ? negotiation.type : 'fixed',
        value: typeof negotiation.value === 'number' ? negotiation.value : 0,
        recurrencyType: ['limited', 'unlimited'].includes(negotiation.recurrencyType) ? negotiation.recurrencyType : 'limited',
        recurrencyPeriod: ['monthly', 'yearly', 'first_payment'].includes(negotiation.recurrencyPeriod) ? negotiation.recurrencyPeriod : 'monthly',
        recurrencyLimit: typeof negotiation.recurrencyLimit === 'number' ? negotiation.recurrencyLimit : 1,
        status: ['active', 'inactive'].includes(negotiation.status) ? negotiation.status : 'active',
        allowDefault: Boolean(negotiation.allowDefault),
        createdAt: negotiation.createdAt || new Date().toISOString(),
        updatedAt: negotiation.updatedAt || new Date().toISOString(),
        affiliates: Array.isArray(negotiation.affiliates) ? negotiation.affiliates : [],
        paymentPlans: Array.isArray(negotiation.paymentPlans) ? negotiation.paymentPlans : []
      };
      
      setSelectedNegotiation(validatedNegotiation);
      setIsLoading(false);
      return validatedNegotiation;
    } catch (error) {
      console.error("Erro ao buscar detalhes da negociação:", error);
      toast.error("Erro ao buscar detalhes da negociação");
      setIsLoading(false);
      return null;
    }
  }

  // Buscar todos os usuários disponíveis
  async function getAvailableUsers() {
    setIsUsersLoading(true);
    try {
      // Por enquanto vamos usar uma lista vazia até termos o endpoint de usuários
      // TODO: Implementar chamada para endpoint de usuários quando disponível
      setAvailableUsers([]);
      setIsUsersLoading(false);
    } catch (error) {
      console.error("Erro ao buscar usuários disponíveis:", error);
      toast.error("Erro ao buscar usuários disponíveis");
      setIsUsersLoading(false);
    }
  }

  // Buscar usuários de uma negociação
  async function getNegotiationUsers(negotiationId: string) {
    setIsUsersLoading(true);
    try {
      // TODO: Implementar quando houver endpoint para usuários de negociação
      setSelectedUsers([]);
      setIsUsersLoading(false);
    } catch (error) {
      console.error("Erro ao buscar usuários da negociação:", error);
      toast.error("Erro ao buscar usuários da negociação");
      setIsUsersLoading(false);
    }
  }

  // Adicionar usuário à negociação
  async function addUserToNegotiation(negotiationId: string, userId: string) {
    setIsUsersLoading(true);
    try {
      // TODO: Implementar quando houver endpoint
      toast.success("Usuário adicionado com sucesso!");
      getNegotiationUsers(negotiationId);
      setIsUsersLoading(false);
    } catch (error) {
      console.error("Erro ao adicionar usuário:", error);
      toast.error("Erro ao adicionar usuário");
      setIsUsersLoading(false);
    }
  }

  // Adicionar vários usuários à negociação
  async function addManyUsersToNegotiation(negotiationId: string, userIds: string[]) {
    setIsUsersLoading(true);
    try {
      // TODO: Implementar quando houver endpoint
      toast.success(`${userIds.length} usuário(s) adicionado(s) com sucesso!`);
      getNegotiationUsers(negotiationId);
      setIsUsersLoading(false);
    } catch (error) {
      console.error("Erro ao adicionar usuários:", error);
      toast.error("Erro ao adicionar usuários");
      setIsUsersLoading(false);
    }
  }

  // Remover usuário da negociação
  async function removeUserFromNegotiation(negotiationId: string, userId: string) {
    setIsUsersLoading(true);
    try {
      // TODO: Implementar quando houver endpoint
      toast.success("Usuário removido com sucesso!");
      getNegotiationUsers(negotiationId);
      setIsUsersLoading(false);
    } catch (error) {
      console.error("Erro ao remover usuário:", error);
      toast.error("Erro ao remover usuário");
      setIsUsersLoading(false);
    }
  }

  // Remover vários usuários da negociação
  async function removeManyUsersFromNegotiation(negotiationId: string, userIds: string[]) {
    setIsUsersLoading(true);
    try {
      // TODO: Implementar quando houver endpoint
      toast.success(`${userIds.length} usuário(s) removido(s) com sucesso!`);
      getNegotiationUsers(negotiationId);
      setIsUsersLoading(false);
    } catch (error) {
      console.error("Erro ao remover usuários:", error);
      toast.error("Erro ao remover usuários");
      setIsUsersLoading(false);
    }
  }

  // Obter divisões únicas dos usuários
  function getUniqueDivisions() {
    const divisions = availableUsers.map((user) => user.division);
    return Array.from(new Set(divisions));
  }

  // Filtrar usuários por divisão
  function filterUsersByDivision(division: string) {
    return availableUsers.filter((user) => user.division === division);
  }

  // Criar nova negociação
  async function createNegotiation(data: INegotiationFormData) {
    setIsLoading(true);
    try {
      const newNegotiation = await contractsService.create(data);
      
      toast.success("Negociação criada com sucesso!");
      setIsFormModalOpen(false);
      reset();
      getNegotiations();
      setIsLoading(false);
      
      return newNegotiation;
    } catch (error) {
      console.error("Erro ao criar negociação:", error);
      toast.error("Erro ao criar negociação");
      setIsLoading(false);
    }
  }

  // Atualizar negociação existente
  async function updateNegotiation(id: string, data: Partial<INegotiationFormData>) {
    setIsLoading(true);
    try {
      const updatedNegotiation = await contractsService.update(id, data);
      
      toast.success("Negociação atualizada com sucesso!");
      setIsFormModalOpen(false);
      reset();
      getNegotiations();
      setIsLoading(false);
      
      return updatedNegotiation;
    } catch (error) {
      console.error("Erro ao atualizar negociação:", error);
      toast.error("Erro ao atualizar negociação");
      setIsLoading(false);
    }
  }

  // Excluir negociação
  async function deleteNegotiation() {
    if (!selectedNegotiation?.id) return;

    setIsLoading(true);
    try {
      await contractsService.delete(selectedNegotiation.id);
      
      toast.success("Negociação excluída com sucesso!");
      setIsDeleteModalOpen(false);
      setSelectedNegotiation(null);
      getNegotiations();
      setIsLoading(false);
    } catch (error) {
      console.error("Erro ao excluir negociação:", error);
      toast.error("Erro ao excluir negociação");
      setIsLoading(false);
    }
  }

  // Alterar status da negociação
  async function toggleNegotiationStatus(id: string, status: "active" | "inactive") {
    setIsLoading(true);
    try {
      await contractsService.update(id, { status });
      
      toast.success(`Negociação ${status === "active" ? "ativada" : "desativada"} com sucesso!`);
      getNegotiations();
      setIsLoading(false);
    } catch (error) {
      console.error("Erro ao alterar status da negociação:", error);
      toast.error("Erro ao alterar status da negociação");
      setIsLoading(false);
    }
  }

  // Buscar todos os afiliados disponíveis
  async function getAvailableAffiliates() {
    setIsAffiliatesLoading(true);
    try {
      const affiliatesData = await afiliatesService.get();
      
      // Adaptar os dados dos afiliados para o formato esperado
      let adaptedAffiliates = affiliatesData.data.map(affiliate => ({
        id: affiliate.id,
        name: affiliate.name,
        email: affiliate.email,
        status: affiliate.status as "active" | "inactive"
      }));

      // Filtrando por termo de busca, se existir
      if (searchAffiliatesTerm) {
        const searchLower = searchAffiliatesTerm.toLowerCase();
        adaptedAffiliates = adaptedAffiliates.filter(
          (affiliate) => 
            affiliate.name.toLowerCase().includes(searchLower) || 
            affiliate.email.toLowerCase().includes(searchLower)
        );
      }

      setAvailableAffiliates(adaptedAffiliates);
      setIsAffiliatesLoading(false);
    } catch (error) {
      console.error("Erro ao buscar afiliados disponíveis:", error);
      toast.error("Erro ao buscar afiliados disponíveis");
      setIsAffiliatesLoading(false);
    }
  }

  // Buscar afiliados de uma negociação e filtrar os disponíveis
  async function getNegotiationAffiliates(negotiationId: string) {
    setIsAffiliatesLoading(true);
    try {
      const negotiationAffiliates = await contractsService.getAffiliates(negotiationId);
      
      const adaptedAffiliates = negotiationAffiliates.map((affiliate: any) => ({
        id: affiliate.id,
        name: affiliate.name,
        email: affiliate.email,
        status: affiliate.status as "active" | "inactive"
      }));
      
      setAffiliates(adaptedAffiliates);

      // Atualizar lista de afiliados disponíveis, excluindo os já associados
      const associatedAffiliateIds = adaptedAffiliates.map((a: IAffiliateLink) => a.id);
      const filtered = availableAffiliates.filter((a) => !associatedAffiliateIds.includes(a.id));
      setAvailableAffiliates(filtered);
      
      setIsAffiliatesLoading(false);
    } catch (error) {
      console.error("Erro ao buscar afiliados da negociação:", error);
      toast.error("Erro ao buscar afiliados da negociação");
      setAffiliates([]);
      setIsAffiliatesLoading(false);
    }
  }

  // Adicionar afiliado à negociação
  async function addAffiliateToNegotiation(negotiationId: string, affiliateId: string) {
    setIsAffiliatesLoading(true);
    try {
      // Buscar afiliados atuais e adicionar o novo
      const currentAffiliates = await contractsService.getAffiliates(negotiationId);
      const currentAffiliateIds = currentAffiliates.map((a: any) => a.id);
      const updatedAffiliateIds = [...currentAffiliateIds, affiliateId];

      await contractsService.updateAffiliates(negotiationId, updatedAffiliateIds);
      
      toast.success("Afiliado adicionado com sucesso!");
      getNegotiationAffiliates(negotiationId);
      setIsAffiliatesLoading(false);
    } catch (error) {
      console.error("Erro ao adicionar afiliado:", error);
      toast.error("Erro ao adicionar afiliado");
      setIsAffiliatesLoading(false);
    }
  }

  // Remover afiliado da negociação
  async function removeAffiliateFromNegotiation(negotiationId: string, affiliateId: string) {
    setIsAffiliatesLoading(true);
    try {
      // Buscar afiliados atuais e remover o específico
      const currentAffiliates = await contractsService.getAffiliates(negotiationId);
      const updatedAffiliateIds = currentAffiliates
        .filter((a: any) => a.id !== affiliateId)
        .map((a: any) => a.id);

      await contractsService.updateAffiliates(negotiationId, updatedAffiliateIds);
      
      toast.success("Afiliado removido com sucesso!");
      getNegotiationAffiliates(negotiationId);
      setIsAffiliatesLoading(false);
    } catch (error) {
      console.error("Erro ao remover afiliado:", error);
      toast.error("Erro ao remover afiliado");
      setIsAffiliatesLoading(false);
    }
  }

  // Upload de comprovante de pagamento
  async function uploadPaymentReceipt() {
    if (!selectedFile || !selectedNegotiation?.id || !currentAffiliateId) return;

    setIsUploading(true);
    try {
      // TODO: Implementar upload quando houver endpoint
      setTimeout(() => {
        toast.success("Comprovante enviado com sucesso!");
        setIsUploadModalOpen(false);
        setSelectedFile(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
        setIsUploading(false);
      }, 1000);
    } catch (error) {
      console.error("Erro ao enviar comprovante:", error);
      toast.error("Erro ao enviar comprovante");
      setIsUploading(false);
    }
  }

  function handleSearchPlansChange(term: string) {
    setSearchPlansTerm(term);
    // A filtragem será feita na próxima chamada de getAvailablePlans
  }

  // Funções auxiliares para manipulação de modais
  async function openFormModal(negotiation?: INegotiation) {
    if (negotiation) {
      setSelectedNegotiation(negotiation);
      setValue("name", negotiation.name);
      setValue("calculationType", negotiation.type);
      setValue("value", negotiation.value);
      setValue("recurrenceType", mapRecurrenceType(negotiation.recurrencyPeriod));

      if (negotiation.recurrencyType === "limited") {
        setValue("periodMonths", negotiation.recurrencyLimit);
      }

      // Configurar dados de planos
      setValue("applicableToAllPlans", negotiation.allowDefault || false);
      
      if (negotiation.paymentPlans) {
        setValue(
          "planIds",
          negotiation.paymentPlans.map((p: any) => p.id)
        );

        // Se não é aplicável a todos os planos, usar configurações específicas dos planos
        if (!negotiation.allowDefault && negotiation.paymentPlans) {
          const planConfigurations = negotiation.paymentPlans.map((plan: any) => {
            // Usar dados de NegotiationPaymentPlan se disponível, caso contrário usar dados da negociação
            const planConfig = plan.NegotiationPaymentPlan || {};
            
            return {
              planId: plan.id,
              calculationType: planConfig.type || negotiation.type || "fixed",
              value: planConfig.value || negotiation.value || 0,
              recurrenceType: mapRecurrenceType(planConfig.recurrencyPeriod || negotiation.recurrencyPeriod),
              periodMonths: planConfig.recurrencyLimit || negotiation.recurrencyLimit
            };
          });

          setValue("planConfigurations", planConfigurations);
        } else {
          setValue("planConfigurations", []);
        }
      }
    } else {
      setSelectedNegotiation(null);
      reset();
      // Valores padrão para novos registros
      setValue("applicableToAllPlans", false);
      setValue("planIds", []);
      setValue("planConfigurations", []);
      setValue("userIds", []);
    }
    setIsFormModalOpen(true);
  }

  // Mapear recurrencyPeriod da API para recurrenceType do frontend
  function mapRecurrenceType(recurrencyPeriod?: string): "first_payment" | "recurring" | "period" {
    switch (recurrencyPeriod) {
      case "first_payment":
        return "first_payment";
      case "monthly":
      case "yearly":
        return "recurring";
      default:
        return "period";
    }
  }

  function openDetailModal(negotiation: INegotiation) {
    setSelectedNegotiation(negotiation);
    setIsDetailModalOpen(true);
  }

  function openDeleteModal(negotiation: INegotiation) {
    setSelectedNegotiation(negotiation);
    setIsDeleteModalOpen(true);
  }

  function openAffiliatesModal(negotiation: INegotiation) {
    setSelectedNegotiation(negotiation);
    getNegotiationAffiliates(negotiation.id!);
    setIsAffiliatesModalOpen(true);
  }

  function openPlansModal(negotiation: INegotiation) {
    setSelectedNegotiation(negotiation);
    getNegotiationPlans(negotiation.id!);
    setIsPlansModalOpen(true);
  }

  function openUsersModal(negotiation: INegotiation) {
    setSelectedNegotiation(negotiation);
    getNegotiationUsers(negotiation.id!);
    setIsUsersModalOpen(true);
  }

  function openUploadModal(negotiation: INegotiation, affiliateId: string) {
    setSelectedNegotiation(negotiation);
    setCurrentAffiliateId(affiliateId);
    setIsUploadModalOpen(true);
  }

  function handleFileChange(event: React.ChangeEvent<HTMLInputElement>) {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    }
  }

  function handleClearSearch() {
    setSearchTerm("");
    getNegotiations("search", "");
  }

  function handleStatusChange(status: string | null) {
    setActiveTab(status || "all");
    getNegotiations("status", status);
  }

  function handleSearchAffiliatesChange(term: string) {
    setSearchAffiliatesTerm(term);
    // A filtragem será feita na próxima chamada de getAvailableAffiliates
  }

  // Função para lidar com a busca de usuários
  function handleSearchUsersChange(term: string) {
    setSearchUsersTerm(term);
    // A filtragem será feita na próxima chamada de getAvailableUsers
  }

  // Efeito para inicializar dados quando o componente montar
  useEffect(() => {
    getNegotiations();
    getAvailablePlans();
    getAvailableAffiliates();
  }, []);

  // Efeito para buscar negociações quando o termo de busca mudar (com debounce)
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchTerm !== undefined) {
        getNegotiations("search", searchTerm);
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm]);

  // Efeito para buscar planos quando o termo de busca mudar (com debounce)
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchPlansTerm !== undefined) {
        getAvailablePlans();
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchPlansTerm]);

  // Efeito para buscar afiliados quando o termo de busca mudar (com debounce)
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchAffiliatesTerm !== undefined) {
        getAvailableAffiliates();
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchAffiliatesTerm]);

  const statusTabs = [
    { label: "Todos", value: "all" },
    { label: "Ativos", value: "active" },
    { label: "Inativos", value: "inactive" },
  ];

  return {
    isLoading,
    isAffiliatesLoading,
    isPlansLoading,
    isUsersLoading,
    isUploading,
    pageData,
    selectedNegotiation,
    affiliates,
    availableAffiliates,
    availablePlans,
    selectedPlans,
    availableUsers,
    selectedUsers,
    searchTerm,
    setSearchTerm,
    searchAffiliatesTerm,
    setSearchAffiliatesTerm,
    searchPlansTerm,
    setSearchPlansTerm,
    searchUsersTerm,
    setSearchUsersTerm,
    handleSearchAffiliatesChange,
    handleSearchPlansChange,
    handleSearchUsersChange,
    handleClearSearch,
    activeTab,
    setActiveTab,
    statusTabs,
    handleStatusChange,

    // Formulário
    control,
    handleSubmit,
    watch,
    setValue,
    watchFields,
    reset,

    // API calls
    getNegotiations,
    getNegotiationById,
    createNegotiation,
    updateNegotiation,
    deleteNegotiation,
    toggleNegotiationStatus,
    getNegotiationAffiliates,
    getAvailableAffiliates,
    getAvailablePlans,
    getNegotiationPlans,
    addPlanToNegotiation,
    removePlanFromNegotiation,
    setApplicableToAllPlans,
    togglePlanStatus,
    getAvailableUsers,
    getNegotiationUsers,
    addUserToNegotiation,
    addManyUsersToNegotiation,
    removeUserFromNegotiation,
    removeManyUsersFromNegotiation,
    getUniqueDivisions,
    filterUsersByDivision,
    uploadPaymentReceipt,
    addAffiliateToNegotiation,
    removeAffiliateFromNegotiation,

    // Modais
    isFormModalOpen,
    setIsFormModalOpen,
    isDeleteModalOpen,
    setIsDeleteModalOpen,
    isDetailModalOpen,
    setIsDetailModalOpen,
    isAffiliatesModalOpen,
    setIsAffiliatesModalOpen,
    isPlansModalOpen,
    setIsPlansModalOpen,
    isUsersModalOpen,
    setIsUsersModalOpen,
    isUploadModalOpen,
    setIsUploadModalOpen,
    openFormModal,
    openDetailModal,
    openDeleteModal,
    openAffiliatesModal,
    openPlansModal,
    openUsersModal,
    openUploadModal,

    // Upload
    selectedFile,
    setSelectedFile,
    handleFileChange,
    fileInputRef,

    navigate,
  };
}