import React, { useState } from "react";
import { Search, X, AlertTriangle, Plus, Check } from "lucide-react";
import { Table } from "@/components/Table";
import { TableColumns } from "../mocks/TableColumns";
import { IAffiliateLink, INegotiationFormData, IPlan, IPlanConfiguration, TCalculationType } from "../domain/models";
import PaymentReceipt from "@/components/FileUpload/PaymentReceipt";

export default function TemplatePage({ ...sharedProps }) {
  const {
    isLoading,
    isAffiliatesLoading,
    isPlansLoading,
    isUploading,
    pageData,
    selectedNegotiation,
    affiliates,
    availableAffiliates,
    availablePlans,
    selectedPlans,
    searchTerm,
    setSearchTerm,
    searchAffiliatesTerm,
    searchPlansTerm,
    handleSearchAffiliatesChange,
    handleSearchPlansChange,
    handleClearSearch,
    activeTab,
    statusTabs,
    handleStatusChange,
    handleSubmit,
    watchFields,
    setValue,
    getNegotiations,
    createNegotiation,
    updateNegotiation,
    deleteNegotiation,
    toggleNegotiationStatus,
    addAffiliateToNegotiation,
    removeAffiliateFromNegotiation,
    addPlanToNegotiation,
    removePlanFromNegotiation,
    setApplicableToAllPlans,
    togglePlanStatus,
    uploadPaymentReceipt,
    isFormModalOpen,
    setIsFormModalOpen,
    isDeleteModalOpen,
    setIsDeleteModalOpen,
    isDetailModalOpen,
    setIsDetailModalOpen,
    isAffiliatesModalOpen,
    setIsAffiliatesModalOpen,
    isPlansModalOpen,
    setIsPlansModalOpen,
    isUploadModalOpen,
    setIsUploadModalOpen,
    openFormModal,
    openDetailModal,
    openDeleteModal,
    openAffiliatesModal,
    openPlansModal,
    openUploadModal,
    selectedFile,
    handleFileChange,
    fileInputRef,
  } = sharedProps;

  // Estado para o plano selecionado
  const [selectedPlanId, setSelectedPlanId] = useState<string>("");
  
  // Estado para erros de validação
  const [formErrors, setFormErrors] = useState<{
    globalValue?: string;
    planValues?: { [planId: string]: string };
  }>({});

  // Função para limpar a seleção de plano
  const clearPlanSelection = () => {
    setSelectedPlanId("");
  };

  // Função para validar valor percentual
  const validatePercentageValue = (value: number): string | null => {
    if (value < 0) {
      return "O valor percentual não pode ser negativo";
    }
    if (value > 100) {
      return "O valor percentual não pode ser maior que 100%";
    }
    return null;
  };

  // Função para validar e atualizar valor global
  const handleGlobalValueChange = (value: number, calculationType: TCalculationType) => {
    setValue("value", value);
    
    if (calculationType === "percentage") {
      const error = validatePercentageValue(value);
      setFormErrors(prev => ({
        ...prev,
        globalValue: error
      }));
    } else {
      setFormErrors(prev => ({
        ...prev,
        globalValue: undefined
      }));
    }
  };

  // Função para validar e atualizar valor do plano individual
  const handlePlanValueChange = (planId: string, value: number, calculationType: TCalculationType) => {
    const currentConfigs = watchFields.planConfigurations || [];
    const updatedConfigs = currentConfigs.map((config: IPlanConfiguration) => {
      if (config.planId === planId) {
        return { ...config, value };
      }
      return config;
    });
    setValue("planConfigurations", updatedConfigs);
    
    if (calculationType === "percentage") {
      const error = validatePercentageValue(value);
      setFormErrors(prev => ({
        ...prev,
        planValues: {
          ...prev.planValues,
          [planId]: error || undefined
        }
      }));
    } else {
      setFormErrors(prev => ({
        ...prev,
        planValues: {
          ...prev.planValues,
          [planId]: undefined
        }
      }));
    }
  };

  // Função para adicionar plano selecionado
  const handleAddSelectedPlan = () => {
    if (selectedNegotiation?.id && selectedPlanId) {
      addPlanToNegotiation(selectedNegotiation.id, selectedPlanId);
      clearPlanSelection();
    }
  };

  // Função para alternar aplicabilidade a todos os planos
  const handleToggleApplicableToAll = () => {
    if (selectedNegotiation?.id) {
      setApplicableToAllPlans(selectedNegotiation.id, !selectedNegotiation.allowDefault);
    }
  };

  // Função para formatar o valor com base no tipo de cálculo
  const formatValue = (value: number, type: "fixed" | "percentage") => {
    if (type === "fixed") {
      return `R$ ${value.toFixed(2).replace(".", ",")}`;
    } else {
      return `${value}%`;
    }
  };

  // Função para formatar o tipo de recorrência
  const formatRecurrenceType = (type: string, months?: number) => {
    switch (type) {
      case "first_payment":
        return "Primeiro Pagamento";
      case "recurring":
        return "Recorrente";
      case "period":
        return months ? `Período (${months} meses)` : "Período";
      default:
        return type;
    }
  };

  // Função para formatar recorrência a partir dos dados da API
  const formatApiRecurrenceType = (recurrencyPeriod?: string, recurrencyType?: string, recurrencyLimit?: number) => {
    if (!recurrencyPeriod) return "Não definido";
    
    switch (recurrencyPeriod) {
      case "first_payment":
        return "Primeiro Pagamento";
      case "monthly":
        if (recurrencyType === "unlimited") {
          return "Recorrente Mensal";
        } else if (recurrencyType === "limited" && recurrencyLimit) {
          return `Mensal (${recurrencyLimit} meses)`;
        }
        return "Mensal";
      case "yearly":
        if (recurrencyType === "unlimited") {
          return "Recorrente Anual";
        } else if (recurrencyType === "limited" && recurrencyLimit) {
          return `Anual (${recurrencyLimit} anos)`;
        }
        return "Anual";
      default:
        return recurrencyPeriod;
    }
  };

  const onSubmitNegotiation = (data: INegotiationFormData) => {
    // Validar valores percentuais antes de submeter
    let hasErrors = false;
    const errors: { globalValue?: string; planValues?: { [planId: string]: string } } = {};

    // Validar valor global se for percentual
    if (data.applicableToAllPlans && data.calculationType === "percentage" && data.value !== undefined) {
      const globalError = validatePercentageValue(data.value);
      if (globalError) {
        errors.globalValue = globalError;
        hasErrors = true;
      }
    }

    // Validar valores individuais dos planos se forem percentuais
    if (!data.applicableToAllPlans && data.planConfigurations) {
      errors.planValues = {};
      data.planConfigurations.forEach((config: IPlanConfiguration) => {
        if (config.calculationType === "percentage" && config.value !== undefined) {
          const planError = validatePercentageValue(config.value);
          if (planError) {
            errors.planValues![config.planId] = planError;
            hasErrors = true;
          }
        }
      });
    }

    if (hasErrors) {
      setFormErrors(errors);
      return;
    }

    // Limpar erros e submeter
    setFormErrors({});
    
    if (selectedNegotiation?.id) {
      updateNegotiation(selectedNegotiation.id, data);
    } else {
      createNegotiation(data);
    }
  };

  if (isLoading && !pageData) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Negociações</h1>
        <button onClick={() => openFormModal()} className="px-4 py-2 bg-primary text-white rounded-lg flex items-center">
          <Plus className="mr-2 h-4 w-4" />
          Nova Negociação
        </button>
      </div>

      <div className="mb-6">
        <nav className="flex" aria-label="Tabs">
          <div className="flex space-x-2">
            {statusTabs.map((tab: { value: string; label: string }) => (
              <button
                key={tab.value}
                onClick={() => handleStatusChange(tab.value === "all" ? null : (tab.value as "active" | "inactive"))}
                className={`px-6 py-3 text-sm font-medium rounded-lg ${
                  activeTab === tab.value
                    ? "bg-red-50 text-red-600 border border-red-200"
                    : "bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100 border border-gray-200"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </nav>
      </div>

      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Buscar por nome..."
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm"
          />
          {searchTerm && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button onClick={handleClearSearch} className="text-gray-400 hover:text-gray-500 focus:outline-none">
                <X className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="mt-6">
        <Table
          showPagination
          columns={TableColumns({
            onView: openDetailModal,
            onEdit: openFormModal,
            onDelete: openDeleteModal,
            onViewAffiliates: openAffiliatesModal,
            onViewPlans: openPlansModal,
            onToggleStatus: toggleNegotiationStatus,
          })}
          data={pageData?.data || []}
          defaultRows={pageData?.pageSize || 10}
          totalItems={pageData?.totalRecords || 0}
          changeRowsPerPage={(pageSize) => getNegotiations("pageSize", pageSize)}
          onChangePage={(page) => getNegotiations("page", page)}
          loading={isLoading}
          currentPage={pageData?.currentPage}
        />
      </div>

      {/* Modal de Criação/Edição de Negociação */}
      {isFormModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{selectedNegotiation ? "Editar Negociação" : "Nova Negociação"}</h3>
                  <button onClick={() => {
                    setIsFormModalOpen(false);
                    setFormErrors({});
                  }} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit(onSubmitNegotiation)}>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Nome da Negociação
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={watchFields.name || ""}
                        onChange={(e) => setValue("name", e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                        maxLength={30}
                        required
                      />
                    </div>

                    {/* Início da seção de aplicabilidade de planos - MOVIDO PARA AQUI */}
                    <div className="border-t pt-4 mt-4">
                      <h4 className="text-md font-medium text-gray-700 mb-2">Planos Aplicáveis</h4>

                      <div className="mb-4">
                        <div className="flex items-center">
                          <input
                            id="applicableToAllPlans"
                            type="checkbox"
                            checked={watchFields.applicableToAllPlans || false}
                            onChange={(e) => setValue("applicableToAllPlans", e.target.checked)}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="applicableToAllPlans" className="ml-2 block text-sm text-gray-700">
                            Aplicar a todos os planos
                          </label>
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                          {watchFields.applicableToAllPlans
                            ? "Esta negociação será aplicada a todos os planos de assinatura"
                            : "Selecione planos específicos para esta negociação"}
                        </p>
                      </div>

                      {!watchFields.applicableToAllPlans && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Selecione os planos</label>
                          <div className="mt-1 border border-gray-200 rounded-md max-h-72 overflow-y-auto">
                            {availablePlans.map((plan: IPlan) => {
                              const isPlanSelected = (watchFields.planIds || []).includes(plan.id);
                              const planConfig = (watchFields.planConfigurations || []).find(
                                (config: IPlanConfiguration) => config.planId === plan.id
                              );

                              return (
                                <div key={plan.id} className="p-3 border-b border-gray-200 last:border-b-0">
                                  <div className="flex items-start">
                                    <div className="flex items-center h-5">
                                      <input
                                        type="checkbox"
                                        id={`plan-${plan.id}`}
                                        checked={isPlanSelected}
                                        onChange={(e) => {
                                          const currentPlanIds = watchFields.planIds || [];
                                          const currentPlanConfigs = watchFields.planConfigurations || [];

                                          if (e.target.checked) {
                                            // Adicionar plano
                                            setValue("planIds", [...currentPlanIds, plan.id]);

                                            // Adicionar configuração padrão baseada na configuração global
                                            const defaultConfig: IPlanConfiguration = {
                                              planId: plan.id,
                                              calculationType: watchFields.calculationType || "fixed",
                                              value: watchFields.value || 0,
                                            };

                                            setValue("planConfigurations", [...currentPlanConfigs, defaultConfig]);
                                          } else {
                                            // Remover plano
                                            setValue(
                                              "planIds",
                                              currentPlanIds.filter((id: string) => id !== plan.id)
                                            );

                                            // Remover configuração
                                            setValue(
                                              "planConfigurations",
                                              currentPlanConfigs.filter((config: IPlanConfiguration) => config.planId !== plan.id)
                                            );
                                          }
                                        }}
                                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                      />
                                    </div>
                                    <div className="ml-3 flex-1">
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <label htmlFor={`plan-${plan.id}`} className="text-sm font-medium text-gray-900">
                                            {plan.name}
                                          </label>
                                          <p className="text-xs text-gray-500">{plan.description}</p>
                                          <p className="text-xs font-medium text-gray-900 mt-1">
                                            Preço: R$ {typeof plan?.price === 'number' ? plan.price.toFixed(2).replace(".", ",") : typeof plan?.price === 'string' ? parseFloat(plan.price).toFixed(2).replace(".", ",") : '0,00'}
                                          </p>
                                        </div>
                                        <span
                                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                            plan.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                                          }`}
                                        >
                                          {plan.status === "active" ? "Ativo" : "Inativo"}
                                        </span>
                                      </div>

                                      {/* Configuração individual do plano */}
                                      {isPlanSelected && (
                                        <div className="mt-3 p-3 bg-gray-50 rounded-md">
                                          <h5 className="text-sm font-medium text-gray-700 mb-2">Parametrização individual</h5>
                                          <div className="grid grid-cols-2 gap-3">
                                            <div>
                                              <label htmlFor={`plan-${plan.id}-type`} className="block text-xs font-medium text-gray-700">
                                                Tipo de cálculo
                                              </label>
                                              <select
                                                id={`plan-${plan.id}-type`}
                                                value={planConfig?.calculationType || "fixed"}
                                                onChange={(e) => {
                                                  const newType = e.target.value as TCalculationType;
                                                  const currentConfigs = watchFields.planConfigurations || [];
                                                  const updatedConfigs = currentConfigs.map((config: IPlanConfiguration) => {
                                                    if (config.planId === plan.id) {
                                                      return { ...config, calculationType: newType };
                                                    }
                                                    return config;
                                                  });
                                                  setValue("planConfigurations", updatedConfigs);
                                                  // Limpar erros quando mudar o tipo de cálculo do plano
                                                  setFormErrors(prev => ({
                                                    ...prev,
                                                    planValues: {
                                                      ...prev.planValues,
                                                      [plan.id]: undefined
                                                    }
                                                  }));
                                                }}
                                                className="mt-1 block w-full text-xs border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-primary focus:border-primary"
                                              >
                                                <option value="fixed">Valor Fixo</option>
                                                <option value="percentage">Valor Percentual</option>
                                              </select>
                                            </div>
                                            <div>
                                              <label htmlFor={`plan-${plan.id}-value`} className="block text-xs font-medium text-gray-700">
                                                Valor {planConfig?.calculationType === "fixed" ? "(R$)" : "(%)"}
                                              </label>
                                              <div className="mt-1 relative">
                                                {planConfig?.calculationType === "fixed" && (
                                                  <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                                                    <span className="text-gray-500 text-xs">R$</span>
                                                  </div>
                                                )}
                                                <input
                                                  type="number"
                                                  id={`plan-${plan.id}-value`}
                                                  value={planConfig?.value || 0}
                                                  onChange={(e) => {
                                                    const newValue = parseFloat(e.target.value) || 0;
                                                    handlePlanValueChange(plan.id, newValue, planConfig?.calculationType || "fixed");
                                                  }}
                                                  className={`block w-full text-xs border ${
                                                    formErrors.planValues?.[plan.id] ? "border-red-300" : "border-gray-300"
                                                  } rounded-md shadow-sm py-1 ${
                                                    planConfig?.calculationType === "fixed" ? "pl-7" : "px-2"
                                                  } focus:outline-none focus:ring-primary focus:border-primary`}
                                                  min={0}
                                                  max={planConfig?.calculationType === "percentage" ? 100 : undefined}
                                                  step={planConfig?.calculationType === "fixed" ? "0.01" : "0.1"}
                                                />
                                                {planConfig?.calculationType === "percentage" && (
                                                  <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
                                                    <span className="text-gray-500 text-xs">%</span>
                                                  </div>
                                                )}
                                              </div>
                                              {formErrors.planValues?.[plan.id] && (
                                                <p className="mt-1 text-xs text-red-600">{formErrors.planValues[plan.id]}</p>
                                              )}
                                            </div>
                                          </div>

                                          {/* Adicionando recorrência individual para cada plano */}
                                          <div className="mt-3">
                                            <label htmlFor={`plan-${plan.id}-recurrence`} className="block text-xs font-medium text-gray-700">
                                              Recorrência
                                            </label>
                                            <select
                                              id={`plan-${plan.id}-recurrence`}
                                              value={planConfig?.recurrenceType || "first_payment"}
                                              onChange={(e) => {
                                                const newRecurrenceType = e.target.value as "first_payment" | "recurring" | "period";
                                                const currentConfigs = watchFields.planConfigurations || [];
                                                const updatedConfigs = currentConfigs.map((config: IPlanConfiguration) => {
                                                  if (config.planId === plan.id) {
                                                    return { ...config, recurrenceType: newRecurrenceType };
                                                  }
                                                  return config;
                                                });
                                                setValue("planConfigurations", updatedConfigs);
                                              }}
                                              className="mt-1 block w-full text-xs border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-primary focus:border-primary"
                                            >
                                              <option value="first_payment">Primeiro Pagamento</option>
                                              <option value="recurring">Recorrente</option>
                                              <option value="period">Período</option>
                                            </select>
                                          </div>

                                          {planConfig?.recurrenceType === "period" && (
                                            <div className="mt-2">
                                              <label htmlFor={`plan-${plan.id}-period`} className="block text-xs font-medium text-gray-700">
                                                Quantidade de Meses
                                              </label>
                                              <input
                                                type="number"
                                                id={`plan-${plan.id}-period`}
                                                value={planConfig?.periodMonths || 1}
                                                onChange={(e) => {
                                                  const newPeriodMonths = parseInt(e.target.value);
                                                  const currentConfigs = watchFields.planConfigurations || [];
                                                  const updatedConfigs = currentConfigs.map((config: IPlanConfiguration) => {
                                                    if (config.planId === plan.id) {
                                                      return { ...config, periodMonths: newPeriodMonths };
                                                    }
                                                    return config;
                                                  });
                                                  setValue("planConfigurations", updatedConfigs);
                                                }}
                                                className="mt-1 block w-full text-xs border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-primary focus:border-primary"
                                                min={1}
                                              />
                                            </div>
                                          )}

                                          <div className="mt-2">
                                            <button
                                              type="button"
                                              className="text-xs text-primary hover:text-red-700"
                                              onClick={() => {
                                                // Redefinir a configuração com base na configuração global
                                                const currentConfigs = watchFields.planConfigurations || [];
                                                const updatedConfigs = currentConfigs.map((config: IPlanConfiguration) => {
                                                  if (config.planId === plan.id) {
                                                    return {
                                                      planId: plan.id,
                                                      calculationType: watchFields.calculationType || "fixed",
                                                      value: watchFields.value || 0,
                                                      recurrenceType: watchFields.recurrenceType || "first_payment",
                                                      periodMonths: watchFields.periodMonths,
                                                    };
                                                  }
                                                  return config;
                                                });
                                                setValue("planConfigurations", updatedConfigs);
                                              }}
                                            >
                                              Redefinir para padrão global
                                            </button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              );
                            })}

                            {availablePlans.length === 0 && <div className="p-4 text-center text-sm text-gray-500">Nenhum plano disponível</div>}
                          </div>
                        </div>
                      )}
                    </div>
                    {/* Fim da seção de aplicabilidade de planos */}

                    {/* Mostrar campos de valor e recorrência apenas se "Aplicar a todos os planos" estiver selecionado */}
                    {watchFields.applicableToAllPlans && (
                      <>
                        <div>
                          <label htmlFor="calculationType" className="block text-sm font-medium text-gray-700">
                            Tipo de Negociação
                          </label>
                          <select
                            id="calculationType"
                            value={watchFields.calculationType || "fixed"}
                            onChange={(e) => {
                              const newType = e.target.value as "fixed" | "percentage";
                              setValue("calculationType", newType);
                              // Limpar erros quando mudar o tipo de cálculo
                              setFormErrors(prev => ({
                                ...prev,
                                globalValue: undefined
                              }));
                            }}
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                            required
                          >
                            <option value="fixed">Valor Fixo</option>
                            <option value="percentage">Valor Percentual</option>
                          </select>
                        </div>

                        <div>
                          <label htmlFor="value" className="block text-sm font-medium text-gray-700">
                            Valor {watchFields.calculationType === "fixed" ? "(R$)" : "(%)"}
                          </label>
                          <div className="mt-1 relative rounded-md shadow-sm">
                            {watchFields.calculationType === "fixed" && (
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span className="text-gray-500 sm:text-sm">R$</span>
                              </div>
                            )}
                            <input
                              type="number"
                              id="value"
                              value={watchFields.value || 0}
                              onChange={(e) => {
                                const value = parseFloat(e.target.value) || 0;
                                handleGlobalValueChange(value, watchFields.calculationType || "fixed");
                              }}
                              className={`mt-1 block w-full border ${
                                formErrors.globalValue ? "border-red-300" : "border-gray-300"
                              } rounded-md shadow-sm py-2 ${
                                watchFields.calculationType === "fixed" ? "pl-10" : "px-3"
                              } focus:outline-none focus:ring-primary focus:border-primary sm:text-sm`}
                              min={0}
                              max={watchFields.calculationType === "percentage" ? 100 : undefined}
                              step={watchFields.calculationType === "fixed" ? "0.01" : "0.1"}
                              required
                            />
                            {watchFields.calculationType === "percentage" && (
                              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span className="text-gray-500 sm:text-sm">%</span>
                              </div>
                            )}
                          </div>
                          {formErrors.globalValue && (
                            <p className="mt-1 text-sm text-red-600">{formErrors.globalValue}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="recorrenceType" className="block text-sm font-medium text-gray-700">
                            Recorrência
                          </label>
                          <select
                            id="recorrenceType"
                            value={watchFields.recurrenceType || "first_payment"}
                            onChange={(e) => setValue("recurrenceType", e.target.value as "first_payment" | "recurring" | "period")}
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                            required
                          >
                            <option value="first_payment">Primeiro Pagamento</option>
                            <option value="recurring">Recorrente</option>
                            <option value="period">Período</option>
                          </select>
                        </div>

                        {watchFields.recurrenceType === "period" && (
                          <div>
                            <label htmlFor="periodMonths" className="block text-sm font-medium text-gray-700">
                              Quantidade de Meses
                            </label>
                            <input
                              type="number"
                              id="periodMonths"
                              value={watchFields.periodMonths || 1}
                              onChange={(e) => setValue("periodMonths", parseInt(e.target.value))}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                              min={1}
                              required
                            />
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button
                      type="submit"
                      className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      {selectedNegotiation ? "Atualizar" : "Criar"}
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setIsFormModalOpen(false);
                        setFormErrors({});
                      }}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:w-auto sm:text-sm"
                    >
                      Cancelar
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Detalhes de Negociação */}
      {isDetailModalOpen && selectedNegotiation && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Detalhes da Negociação</h3>
                  <button onClick={() => setIsDetailModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <dl className="divide-y divide-gray-200">
                    <div className="py-3 grid grid-cols-3 gap-4">
                      <dt className="text-sm font-medium text-gray-500">Nome</dt>
                      <dd className="text-sm text-gray-900 col-span-2">{selectedNegotiation.name}</dd>
                    </div>
                    <div className="py-3 grid grid-cols-3 gap-4">
                      <dt className="text-sm font-medium text-gray-500">Tipo</dt>
                      <dd className="text-sm text-gray-900 col-span-2">
                        {selectedNegotiation.type === "fixed" ? "Valor Fixo" : selectedNegotiation.type === "percentage" ? "Valor Percentual" : "Não definido"}
                      </dd>
                    </div>
                    <div className="py-3 grid grid-cols-3 gap-4">
                      <dt className="text-sm font-medium text-gray-500">Valor</dt>
                      <dd className="text-sm text-gray-900 col-span-2">
                        {selectedNegotiation.value ? formatValue(selectedNegotiation.value, selectedNegotiation.type || "fixed") : "Não definido"}
                      </dd>
                    </div>
                    <div className="py-3 grid grid-cols-3 gap-4">
                      <dt className="text-sm font-medium text-gray-500">Recorrência</dt>
                      <dd className="text-sm text-gray-900 col-span-2">
                        {formatApiRecurrenceType(selectedNegotiation.recurrencyPeriod, selectedNegotiation.recurrencyType, selectedNegotiation.recurrencyLimit)}
                      </dd>
                    </div>
                    <div className="py-3 grid grid-cols-3 gap-4">
                      <dt className="text-sm font-medium text-gray-500">Status</dt>
                      <dd className="text-sm text-gray-900 col-span-2">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            selectedNegotiation.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {selectedNegotiation.status === "active" ? "Ativo" : "Inativo"}
                        </span>
                      </dd>
                    </div>
                    <div className="py-3 grid grid-cols-3 gap-4">
                      <dt className="text-sm font-medium text-gray-500">Aplicabilidade</dt>
                      <dd className="text-sm text-gray-900 col-span-2">
                        {selectedNegotiation.allowDefault ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Todos os planos
                          </span>
                        ) : (
                          <div>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Planos específicos
                            </span>
                            <button onClick={() => openPlansModal(selectedNegotiation)} className="ml-2 text-xs text-primary hover:text-red-700">
                              Ver planos
                            </button>

                            {/* Mostrar planos associados */}
                            {selectedNegotiation.paymentPlans && selectedNegotiation.paymentPlans.length > 0 && (
                              <div className="mt-3 border border-gray-200 rounded-md overflow-hidden">
                                <div className="bg-gray-50 px-3 py-2 border-b border-gray-200">
                                  <h4 className="text-xs font-medium text-gray-700">Planos associados</h4>
                                </div>
                                <div className="p-2 max-h-40 overflow-y-auto">
                                  {selectedNegotiation.paymentPlans.map((plan: IPaymentPlan) => {
                                    return (
                                      <div key={plan.id} className="mb-2 p-2 border border-gray-100 rounded-md bg-gray-50 text-xs">
                                        <div className="font-medium">{plan.name}</div>
                                        <div className="grid grid-cols-2 gap-2 mt-1">
                                          <div>
                                            <span className="text-gray-500">Descrição: </span>
                                            <span>{plan.description}</span>
                                          </div>
                                          <div>
                                            <span className="text-gray-500">Preço: </span>
                                            <span>R$ {typeof plan?.price === 'number' ? plan.price.toFixed(2).replace(".", ",") : typeof plan?.price === 'string' ? parseFloat(plan.price).toFixed(2).replace(".", ",") : '0,00'}</span>
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </dd>
                    </div>
                    {selectedNegotiation.createdAt && (
                      <div className="py-3 grid grid-cols-3 gap-4">
                        <dt className="text-sm font-medium text-gray-500">Data de Criação</dt>
                        <dd className="text-sm text-gray-900 col-span-2">{new Date(selectedNegotiation.createdAt).toLocaleDateString("pt-BR")}</dd>
                      </div>
                    )}
                  </dl>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => setIsDetailModalOpen(false)}
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Fechar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Planos */}
      {isPlansModalOpen && selectedNegotiation && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Planos da Negociação: {selectedNegotiation.name}</h3>
                  <button onClick={() => setIsPlansModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="mb-6">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-md font-medium text-gray-700">Configuração de Aplicabilidade</h4>
                    <button
                      onClick={handleToggleApplicableToAll}
                      className={`px-4 py-2 text-sm font-medium rounded-lg inline-flex items-center ${
                        selectedNegotiation.allowDefault
                          ? "bg-blue-100 text-blue-800 hover:bg-blue-200"
                          : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                      }`}
                    >
                      {selectedNegotiation.allowDefault ? (
                        <>
                          <Check className="mr-2 h-4 w-4" />
                          Aplicável a todos os planos
                        </>
                      ) : (
                        "Tornar aplicável a todos os planos"
                      )}
                    </button>
                  </div>

                  {!selectedNegotiation.allowDefault && (
                    <>
                      <h4 className="text-md font-medium text-gray-700 mb-2">Adicionar Plano</h4>

                      {/* Campo de busca de planos */}
                      <div className="relative mb-4">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Search className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          value={searchPlansTerm}
                          onChange={(e) => handleSearchPlansChange(e.target.value)}
                          placeholder="Buscar planos por nome ou descrição..."
                          className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm"
                        />
                        {searchPlansTerm && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button onClick={() => handleSearchPlansChange("")} className="text-gray-400 hover:text-gray-500 focus:outline-none">
                              <X className="h-5 w-5" />
                            </button>
                          </div>
                        )}
                      </div>

                      {/* Lista de planos disponíveis */}
                      <div className="border border-gray-200 rounded-md max-h-48 overflow-y-auto mb-4">
                        <div className="p-2">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">Planos Disponíveis</h5>

                          {isPlansLoading ? (
                            <div className="flex justify-center items-center h-24">
                              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                            </div>
                          ) : availablePlans.length > 0 ? (
                            <div className="grid grid-cols-1 gap-2">
                              {availablePlans
                                .filter((plan: IPlan) => !(selectedPlans || []).some((p: IPlan) => p.id === plan.id))
                                .map((plan: IPlan) => (
                                  <div
                                    key={plan.id}
                                    className={`p-2 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors ${
                                      selectedPlanId === plan.id ? "border-primary bg-red-50" : "border-gray-200"
                                    }`}
                                    onClick={() => setSelectedPlanId(plan.id)}
                                  >
                                    <div className="flex justify-between items-center">
                                      <div>
                                        <p className="text-sm font-medium">{plan.name}</p>
                                        <p className="text-xs text-gray-500">{plan.description}</p>
                                      </div>
                                      <div className="flex items-center">
                                        <span className="text-sm font-medium text-gray-900 mr-2">R$ {typeof plan?.price === 'number' ? plan.price.toFixed(2).replace(".", ",") : typeof plan?.price === 'string' ? parseFloat(plan.price).toFixed(2).replace(".", ",") : '0,00'}</span>
                                        <span
                                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                            plan.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                                          }`}
                                        >
                                          {plan.status === "active" ? "Ativo" : "Inativo"}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          ) : (
                            <p className="text-sm text-gray-500 text-center py-4">Nenhum plano disponível</p>
                          )}
                        </div>
                      </div>

                      {/* Botão para adicionar plano selecionado */}
                      <div className="flex justify-end">
                        <button
                          type="button"
                          onClick={handleAddSelectedPlan}
                          disabled={!selectedPlanId}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Adicionar Plano
                        </button>
                      </div>
                    </>
                  )}
                </div>

                <div className="mt-4">
                  <h4 className="text-md font-medium text-gray-700 mb-2">
                    {selectedNegotiation.allowDefault ? "Aplicável a Todos os Planos" : "Planos Associados"}
                  </h4>

                  {isPlansLoading ? (
                    <div className="flex justify-center items-center h-32">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : selectedNegotiation.allowDefault ? (
                    <div className="bg-blue-50 p-4 rounded-md text-blue-800">
                      <p className="text-sm">Esta negociação é aplicável a todos os planos de assinatura disponíveis.</p>
                    </div>
                  ) : (
                    <>
                      {selectedPlans && selectedPlans.length > 0 ? (
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Nome
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Descrição
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Preço
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Configuração
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ações
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {selectedPlans.map((plan: IPlan) => {
                              const planConfig = selectedNegotiation.planConfigurations?.find(
                                (config: IPlanConfiguration) => config.planId === plan.id
                              );

                              return (
                                <tr key={plan.id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{plan.name}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{plan.description}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R$ {typeof plan?.price === 'number' ? plan.price.toFixed(2).replace(".", ",") : typeof plan?.price === 'string' ? parseFloat(plan.price).toFixed(2).replace(".", ",") : '0,00'}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {planConfig ? (
                                      <div>
                                        <div className="text-xs font-medium">
                                          {planConfig.calculationType === "fixed" ? "Valor Fixo:" : "Percentual:"}
                                        </div>
                                        <div className="text-sm font-bold">{formatValue(planConfig.value, planConfig.calculationType)}</div>
                                      </div>
                                    ) : (
                                      <span className="text-xs text-gray-500">Configuração padrão</span>
                                    )}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span
                                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                        plan.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                                      }`}
                                    >
                                      {plan.status === "active" ? "Ativo" : "Inativo"}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div className="flex justify-end space-x-2">
                                      <button
                                        onClick={() => {
                                          const planConfig = selectedNegotiation.paymentPlans?.find((p: any) => p.id === plan.id)?.NegotiationPaymentPlan;
                                          const isCurrentlyDisabled = planConfig?.disabled || false;
                                          togglePlanStatus(selectedNegotiation.id!, plan.id, !isCurrentlyDisabled);
                                        }}
                                        className={`${
                                          (selectedNegotiation.paymentPlans?.find((p: any) => p.id === plan.id)?.NegotiationPaymentPlan?.disabled) 
                                            ? "text-green-600 hover:text-green-900" 
                                            : "text-yellow-600 hover:text-yellow-900"
                                        }`}
                                        title={
                                          (selectedNegotiation.paymentPlans?.find((p: any) => p.id === plan.id)?.NegotiationPaymentPlan?.disabled) 
                                            ? "Ativar Plano" 
                                            : "Desativar Plano"
                                        }
                                      >
                                        {(selectedNegotiation.paymentPlans?.find((p: any) => p.id === plan.id)?.NegotiationPaymentPlan?.disabled) 
                                          ? "Ativar" 
                                          : "Desativar"
                                        }
                                      </button>
                                      <button
                                        onClick={() => removePlanFromNegotiation(selectedNegotiation.id!, plan.id)}
                                        className="text-red-600 hover:text-red-900"
                                        title="Remover Plano"
                                      >
                                        <X className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      ) : (
                        <div className="bg-yellow-50 p-4 rounded-md text-yellow-800">
                          <p className="text-sm">
                            Nenhum plano associado a esta negociação. Adicione planos específicos ou torne aplicável a todos os planos.
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => setIsPlansModalOpen(false)}
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Fechar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Afiliados */}
      {isAffiliatesModalOpen && selectedNegotiation && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Afiliados da Negociação: {selectedNegotiation.name}</h3>
                  <button onClick={() => setIsAffiliatesModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-700 mb-2">Adicionar Afiliado</h4>

                  {/* Campo de busca de afiliados */}
                  <div className="relative mb-4">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={searchAffiliatesTerm}
                      onChange={(e) => handleSearchAffiliatesChange(e.target.value)}
                      placeholder="Buscar afiliados por nome ou email..."
                      className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm"
                    />
                    {searchAffiliatesTerm && (
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button onClick={() => handleSearchAffiliatesChange("")} className="text-gray-400 hover:text-gray-500 focus:outline-none">
                          <X className="h-5 w-5" />
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Layout melhorado da seleção de afiliados */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Afiliados Disponíveis</h5>
                      <div className="border border-gray-200 rounded-md overflow-hidden">
                        <div className="p-2 bg-red-50 border-b border-gray-200">
                          <button
                            className="flex items-center justify-center w-full text-red-600 hover:text-red-700"
                            onClick={() => {
                              if (selectedNegotiation?.id) {
                                const allIds = availableAffiliates.map((a: IAffiliateLink) => a.id);
                                if (allIds.length > 0) {
                                  allIds.forEach((id: string) => {
                                    addAffiliateToNegotiation(selectedNegotiation.id!, id);
                                  });
                                }
                              }
                            }}
                          >
                            <div className="mr-2 rounded-full bg-red-600 w-5 h-5 flex items-center justify-center text-white">
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.41 11.59L11 8L7.41 4.41L6 5.83L8.17 8L6 10.17L7.41 11.59Z" fill="currentColor" />
                              </svg>
                            </div>
                            Adicionar todos
                          </button>
                        </div>
                        <div className="max-h-48 overflow-y-auto">
                          {isAffiliatesLoading ? (
                            <div className="flex justify-center items-center h-24">
                              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                            </div>
                          ) : availableAffiliates.length > 0 ? (
                            <div>
                              {availableAffiliates.map((affiliate: IAffiliateLink) => (
                                <div key={affiliate.id} className="p-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                  <div className="flex items-center">
                                    <button
                                      onClick={() => {
                                        if (selectedNegotiation?.id) {
                                          addAffiliateToNegotiation(selectedNegotiation.id, affiliate.id);
                                        }
                                      }}
                                      className="mr-2 flex-shrink-0 rounded-full bg-green-500 w-5 h-5 flex items-center justify-center text-white"
                                      title="Adicionar afiliado"
                                    >
                                      <Plus className="h-3 w-3" />
                                    </button>
                                    <div>
                                      <div className="flex items-center">
                                        <div className="w-1 h-4 mr-2 rounded" style={{ backgroundColor: "#FF9800" }}></div>
                                        <p className="text-sm font-medium">{affiliate.name}</p>
                                      </div>
                                      <p className="text-xs text-gray-500">{affiliate.email}</p>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-sm text-gray-500 text-center py-4">Nenhum afiliado disponível</p>
                          )}
                        </div>
                      </div>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Afiliados Selecionados</h5>
                      <div className="border border-gray-200 rounded-md overflow-hidden">
                        <div className="p-2 bg-red-50 border-b border-gray-200">
                          <button
                            className="flex items-center justify-center w-full text-red-600 hover:text-red-700"
                            onClick={() => {
                              if (selectedNegotiation?.id && affiliates && affiliates.length > 0) {
                                const allIds = affiliates.map((a: IAffiliateLink) => a.id);
                                if (allIds.length > 0) {
                                  allIds.forEach((id: string) => {
                                    removeAffiliateFromNegotiation(selectedNegotiation.id!, id);
                                  });
                                }
                              }
                            }}
                          >
                            <div className="mr-2 rounded-full bg-red-600 w-5 h-5 flex items-center justify-center text-white">
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.59 11.59L7.17 10.17L5 8L7.17 5.83L8.59 4.41L7.17 3L3.17 7L7.17 11L8.59 11.59Z" fill="currentColor" />
                              </svg>
                            </div>
                            Remover todos
                          </button>
                        </div>
                        <div className="max-h-48 overflow-y-auto">
                          {isAffiliatesLoading ? (
                            <div className="flex justify-center items-center h-24">
                              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                            </div>
                          ) : (
                            <>
                              {affiliates && affiliates.length > 0 ? (
                                <div>
                                  {affiliates.map((affiliate: IAffiliateLink) => (
                                    <div key={affiliate.id} className="p-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                          <button
                                            onClick={() => {
                                              if (selectedNegotiation?.id) {
                                                removeAffiliateFromNegotiation(selectedNegotiation.id, affiliate.id);
                                              }
                                            }}
                                            className="mr-2 flex-shrink-0 rounded-full bg-red-500 w-5 h-5 flex items-center justify-center text-white"
                                            title="Remover afiliado"
                                          >
                                            <X className="h-3 w-3" />
                                          </button>
                                          <div>
                                            <div className="flex items-center">
                                              <div className="w-1 h-4 mr-2 rounded" style={{ backgroundColor: "#3F51B5" }}></div>
                                              <p className="text-sm font-medium">{affiliate.name}</p>
                                            </div>
                                            <p className="text-xs text-gray-500">{affiliate.email}</p>
                                          </div>
                                        </div>

                                        {/* Botão para enviar comprovante */}
                                        <button
                                          onClick={() => openUploadModal(selectedNegotiation, affiliate.id)}
                                          className="text-indigo-600 hover:text-indigo-900"
                                          title="Enviar Comprovante"
                                        >
                                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                              d="M14 10V12.6667C14 13.0203 13.8595 13.3594 13.6095 13.6095C13.3594 13.8595 13.0203 14 12.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V10"
                                              stroke="currentColor"
                                              strokeWidth="1.33"
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                            />
                                            <path
                                              d="M11.3333 5.33333L8 2L4.66667 5.33333"
                                              stroke="currentColor"
                                              strokeWidth="1.33"
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                            />
                                            <path d="M8 2V10" stroke="currentColor" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round" />
                                          </svg>
                                        </button>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-sm text-gray-500 text-center py-4">Nenhum afiliado associado</p>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => setIsAffiliatesModalOpen(false)}
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Fechar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Upload de Comprovante */}
      {isUploadModalOpen && selectedNegotiation && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Enviar Comprovante de Pagamento</h3>
                  <button onClick={() => setIsUploadModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="mt-4">
                  <PaymentReceipt onUpload={handleFileChange} fileInputRef={fileInputRef} selectedFile={selectedFile} />
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  disabled={!selectedFile || isUploading}
                  onClick={uploadPaymentReceipt}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
                >
                  {isUploading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Enviando...
                    </>
                  ) : (
                    "Enviar Comprovante"
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setIsUploadModalOpen(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmação de Exclusão */}
      {isDeleteModalOpen && selectedNegotiation && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Excluir Negociação</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Tem certeza que deseja excluir a negociação "{selectedNegotiation.name}"? Esta ação não pode ser desfeita.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={deleteNegotiation}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Excluir
                </button>
                <button
                  type="button"
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
