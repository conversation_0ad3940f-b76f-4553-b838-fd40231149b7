import { IUser } from "../domain/models";

export const mockUsers: IUser[] = [
  {
    id: "user1",
    name: "<PERSON>",
    email: "<EMAIL>",
    division: "BELA VISTA",
    status: "active",
  },
  {
    id: "user2",
    name: "<PERSON>",
    email: "<EMAIL>",
    division: "BELA VISTA",
    status: "active",
  },
  {
    id: "user3",
    name: "<PERSON>",
    email: "<EMAIL>",
    division: "BOCAVERÁ",
    status: "active",
  },
  {
    id: "user4",
    name: "<PERSON>",
    email: "<EMAIL>",
    division: "BOCAVERÁ",
    status: "active",
  },
  {
    id: "user5",
    name: "<PERSON>",
    email: "<EMAIL>",
    division: "NOVO HORIZONTE",
    status: "active",
  },
  {
    id: "user6",
    name: "<PERSON>",
    email: "<EMAIL>",
    division: "NOVO HORIZONTE",
    status: "active",
  },
  {
    id: "user7",
    name: "<PERSON>",
    email: "rod<PERSON><EMAIL>",
    division: "ACESSÓRIO DE FAXINAL",
    status: "active",
  },
  {
    id: "user8",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    division: "SÃO LUIZ GONZAGA",
    status: "active",
  },
  {
    id: "user9",
    name: "Leonardo Martins",
    email: "<EMAIL>",
    division: "ACESSÓRIO DE FAXINAL",
    status: "inactive",
  },
  {
    id: "user10",
    name: "Fernanda Gomes",
    email: "<EMAIL>",
    division: "SÃO LUIZ GONZAGA",
    status: "inactive",
  },
];
