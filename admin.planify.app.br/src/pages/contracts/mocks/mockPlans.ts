interface IPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  status: "active" | "inactive";
}

export const mockPlans: IPlan[] = [
  {
    id: "plan1",
    name: "Plano Básico",
    description: "Plano básico com funcionalidades essenciais",
    price: 19.9,
    status: "active",
  },
  {
    id: "plan2",
    name: "Plano Profissional",
    description: "Plano para uso profissional com recursos avançados",
    price: 49.9,
    status: "active",
  },
  {
    id: "plan3",
    name: "Plano Enterprise",
    description: "Plano para empresas com suporte premium",
    price: 99.9,
    status: "active",
  },
  {
    id: "plan4",
    name: "Plano Promocional",
    description: "Plano com preço promocional por tempo limitado",
    price: 29.9,
    status: "inactive",
  },
  {
    id: "plan5",
    name: "Plano Anual",
    description: "Plano com pagamento anual e desconto",
    price: 199.9,
    status: "active",
  },
];
