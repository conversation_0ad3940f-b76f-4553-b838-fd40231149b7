import { INegotiation, INegotiationResponse } from "../domain/models";

export const mockNegotiations: INegotiation[] = [
  {
    id: "1",
    name: "Programa Básico",
    calculationType: "fixed",
    value: 100,
    recurrenceType: "first_payment",
    createdAt: "2023-10-15T10:00:00.000Z",
    status: "active",
    affiliates: [
      {
        id: "aff1",
        name: "<PERSON>",
        email: "<EMAIL>",
        status: "active",
      },
      {
        id: "aff2",
        name: "<PERSON>",
        email: "<EMAIL>",
        status: "active",
      },
    ],
  },
  {
    id: "2",
    name: "Programa Premium",
    calculationType: "percentage",
    value: 15,
    recurrenceType: "recurring",
    createdAt: "2023-09-20T14:30:00.000Z",
    status: "active",
    affiliates: [
      {
        id: "aff3",
        name: "<PERSON>",
        email: "<EMAIL>",
        status: "active",
      },
    ],
  },
  {
    id: "3",
    name: "Parceria Especial",
    calculationType: "fixed",
    value: 250,
    recurrenceType: "period",
    periodMonths: 6,
    createdAt: "2023-11-05T09:45:00.000Z",
    status: "inactive",
    affiliates: [],
  },
  {
    id: "4",
    name: "Indicação VIP",
    calculationType: "percentage",
    value: 20,
    recurrenceType: "recurring",
    createdAt: "2023-08-10T16:20:00.000Z",
    status: "active",
    affiliates: [
      {
        id: "aff4",
        name: "Ana Pereira",
        email: "<EMAIL>",
        status: "active",
      },
      {
        id: "aff5",
        name: "Roberto Santos",
        email: "<EMAIL>",
        status: "inactive",
      },
    ],
  },
  {
    id: "5",
    name: "Parceria Sazonal",
    calculationType: "fixed",
    value: 180,
    recurrenceType: "period",
    periodMonths: 3,
    createdAt: "2023-12-01T11:15:00.000Z",
    status: "active",
    affiliates: [
      {
        id: "aff6",
        name: "Patricia Lima",
        email: "<EMAIL>",
        status: "active",
      },
    ],
  },
];

export const getMockPaginatedData = (page: number = 1, pageSize: number = 10, status?: string, search?: string): INegotiationResponse => {
  let filteredData = [...mockNegotiations];

  if (status && status !== "all") {
    filteredData = filteredData.filter((item) => item.status === status);
  }

  if (search) {
    const searchLower = search.toLowerCase();
    filteredData = filteredData.filter((item) => item.name.toLowerCase().includes(searchLower));
  }

  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  return {
    data: paginatedData,
    currentPage: page,
    pageSize,
    totalRecords: filteredData.length,
  };
};
