import { Info, Edit, Trash, UserRound, File } from "lucide-react";
import { INegotiation } from "../domain/models";

type TableColumnsProps = {
  onView: (negotiation: INegotiation) => void;
  onEdit: (negotiation: INegotiation) => void;
  onDelete: (negotiation: INegotiation) => void;
  onViewAffiliates: (negotiation: INegotiation) => void;
  onViewPlans: (negotiation: INegotiation) => void;
  onToggleStatus: (id: string, status: "active" | "inactive") => void;
};

export function TableColumns({ onView, onEdit, onDelete, onViewAffiliates, onViewPlans, onToggleStatus }: TableColumnsProps) {
  return [
    {
      id: "name",
      label: "Nome da Negociação",
      render: (rowData: INegotiation) => <div className="font-medium text-gray-900">{rowData.name}</div>,
    },
    {
      id: "calculationType",
      label: "T<PERSON><PERSON> de Cálculo",
      render: (rowData: INegotiation) => <div>{rowData.type === "fixed" ? "Valor Fixo" : "Valor Percentual"}</div>,
    },
    {
      id: "value",
      label: "Valor",
      render: (rowData: INegotiation) => {
        if (!rowData.value) return <div>-</div>;
        return (
          <div>{rowData.type === "fixed" ? `R$ ${rowData.value.toFixed(2).replace(".", ",")}` : `${rowData.value}%`}</div>
        );
      },
    },
    {
      id: "recurrenceType",
      label: "Recorrência",
      render: (rowData: INegotiation) => {
        let recurrenceText = "";

        switch (rowData.recurrencyPeriod) {
          case "first_payment":
            recurrenceText = "Primeiro Pagamento";
            break;
          case "monthly":
            recurrenceText = rowData.recurrencyType === "unlimited" ? "Recorrente Mensal" : "Mensal";
            if (rowData.recurrencyLimit && rowData.recurrencyType === "limited") {
              recurrenceText += ` (${rowData.recurrencyLimit} meses)`;
            }
            break;
          case "yearly":
            recurrenceText = rowData.recurrencyType === "unlimited" ? "Recorrente Anual" : "Anual";
            if (rowData.recurrencyLimit && rowData.recurrencyType === "limited") {
              recurrenceText += ` (${rowData.recurrencyLimit} anos)`;
            }
            break;
          default:
            recurrenceText = rowData.recurrencyPeriod || "Não definido";
        }

        return <div>{recurrenceText}</div>;
      },
    },
    {
      id: "applicablePlans",
      label: "Planos",
      align: "center" as const,
      render: (rowData: INegotiation) => {
        return (
          <div className="text-center">
            {rowData.allowDefault ? (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Todos os planos
              </span>
            ) : (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                {rowData.paymentPlans?.length || 0} plano(s) específico(s)
              </span>
            )}
          </div>
        );
      },
    },
    {
      id: "status",
      label: "Status",
      align: "center" as const,
      render: (rowData: INegotiation) => {
        const status = rowData.status || "active";
        const isActive = status === "active";

        return (
          <div className="flex items-center justify-center">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
              }`}
            >
              {isActive ? "Ativo" : "Inativo"}
            </span>

            <button onClick={() => onToggleStatus(rowData.id!, isActive ? "inactive" : "active")} className="ml-2 text-gray-400 hover:text-gray-500">
              {isActive ? "Desativar" : "Ativar"}
            </button>
          </div>
        );
      },
    },
    {
      id: "actions",
      label: "Ações",
      align: "center" as const,
      render: (rowData: INegotiation) => (
        <div className="flex items-center justify-center space-x-2">
          <button onClick={() => onView(rowData)} className="p-1 text-blue-600 hover:text-blue-800" title="Visualizar Detalhes">
            <Info size={18} />
          </button>

          <button onClick={() => onEdit(rowData)} className="p-1 text-yellow-600 hover:text-yellow-800" title="Editar">
            <Edit size={18} />
          </button>

          <button onClick={() => onDelete(rowData)} className="p-1 text-red-600 hover:text-red-800" title="Excluir">
            <Trash size={18} />
          </button>

          <button onClick={() => onViewAffiliates(rowData)} className="p-1 text-purple-600 hover:text-purple-800" title="Gerenciar Afiliados">
            <UserRound size={18} />
          </button>

          <button onClick={() => onViewPlans(rowData)} className="p-1 text-green-600 hover:text-green-800" title="Gerenciar Planos">
            <File size={18} />
          </button>
        </div>
      ),
    },
  ];
}
