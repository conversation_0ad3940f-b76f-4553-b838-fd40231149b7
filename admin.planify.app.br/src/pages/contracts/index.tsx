import { useEffect } from "react";
import TemplatePage from "./template";
import useContracts from "./hooks";

export default function Contracts() {
  const hookParams = useContracts();
  const { getNegotiations } = hookParams;

  const sharedProps = {
    ...hookParams,
  };

  useEffect(() => {
    getNegotiations();
  }, []);

  return (
    <>
      <TemplatePage {...sharedProps} />
    </>
  );
}
