import HttpClient from "@infra/httpRequest";
import { 
  INegotiation, 
  INegotiationFormData, 
  ICreateNegotiationDto, 
  IUpdateNegotiationDto,
  INegotiationsResponse,
  INegotiationPlanDto
} from "../models";

const service = new HttpClient();

export default class ContractsServices {
  // Listar negociações com paginação e filtros
  async get(params?: { page?: number; limit?: number; status?: string }): Promise<INegotiationsResponse> {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.status) queryParams.append("status", params.status);

    const query = queryParams.toString() ? `?${queryParams.toString()}` : "";
    const response = await service.get(`/negotiations${query}`);

    return response?.data;
  }

  // Obter negociação específica
  async getById(id: string): Promise<INegotiation> {
    const response = await service.get(`/negotiations/${id}`);
    return response?.data;
  }

  // Criar nova negociação
  async create(data: INegotiationFormData): Promise<INegotiation> {
    // Converter dados do formulário para DTO da API
    const createDto: ICreateNegotiationDto = {
      name: data.name,
      allowDefault: data.applicableToAllPlans || false,
      type: data.calculationType,
      value: data.value,
      recurrencyType: this.mapRecurrencyType(data.recurrenceType),
      recurrencyPeriod: this.mapRecurrencyPeriod(data.recurrenceType),
      recurrencyLimit: data.periodMonths,
      affiliateIds: [], // Será preenchido posteriormente via endpoints específicos
      paymentPlanIds: data.applicableToAllPlans ? data.planIds : undefined,
      planConfigurations: !data.applicableToAllPlans ? this.mapPlanConfigurations(data.planConfigurations) : undefined,
    };

    const response = await service.post(`/negotiations`, createDto);
    return response?.data;
  }

  // Atualizar negociação
  async update(id: string, data: Partial<INegotiationFormData>): Promise<INegotiation> {
    const updateDto: IUpdateNegotiationDto = {
      name: data.name,
      allowDefault: data.applicableToAllPlans,
      type: data.calculationType,
      value: data.value,
      recurrencyType: data.recurrenceType ? this.mapRecurrencyType(data.recurrenceType) : undefined,
      recurrencyPeriod: data.recurrenceType ? this.mapRecurrencyPeriod(data.recurrenceType) : undefined,
      recurrencyLimit: data.periodMonths,
      paymentPlanIds: data.applicableToAllPlans ? data.planIds : undefined,
      planConfigurations: !data.applicableToAllPlans ? this.mapPlanConfigurations(data.planConfigurations) : undefined,
    };

    const response = await service.patch(`/negotiations/${id}`, updateDto);
    return response?.data;
  }

  // Excluir negociação
  async delete(id: string): Promise<{ id: string }> {
    const response = await service.delete(`/negotiations/${id}`);
    return response?.data;
  }

  // Obter afiliados de uma negociação
  async getAffiliates(negotiationId: string) {
    const response = await service.get(`/negotiations/${negotiationId}/affiliates`);
    return response?.data;
  }

  // Obter planos de pagamento de uma negociação
  async getPaymentPlans(negotiationId: string) {
    const response = await service.get(`/negotiations/${negotiationId}/payment-plans`);
    return response?.data;
  }

  // Atualizar afiliados de uma negociação
  async updateAffiliates(negotiationId: string, affiliateIds: string[]) {
    const response = await service.patch(`/negotiations/${negotiationId}/affiliates`, affiliateIds);
    return response?.data;
  }

  // Atualizar planos de pagamento de uma negociação
  async updatePaymentPlans(negotiationId: string, planConfigurations: INegotiationPlanDto[]) {
    const response = await service.patch(`/negotiations/${negotiationId}/payment-plans`, planConfigurations);
    return response?.data;
  }

  // Métodos auxiliares para conversão de tipos

  private mapRecurrencyType(frontendType?: string): "limited" | "unlimited" | undefined {
    switch (frontendType) {
      case "period":
        return "limited";
      case "recurring":
        return "unlimited";
      case "first_payment":
        return "limited";
      default:
        return undefined;
    }
  }

  private mapRecurrencyPeriod(frontendType?: string): "monthly" | "yearly" | "first_payment" | undefined {
    switch (frontendType) {
      case "period":
        return "monthly";
      case "recurring":
        return "monthly";
      case "first_payment":
        return "first_payment";
      default:
        return undefined;
    }
  }

  private mapPlanConfigurations(configurations?: any[]): INegotiationPlanDto[] | undefined {
    if (!configurations) return undefined;

    return configurations.map(config => ({
      paymentPlanId: config.planId,
      type: config.calculationType,
      value: config.value,
      recurrencyType: this.mapRecurrencyType(config.recurrenceType) || "limited",
      recurrencyPeriod: this.mapRecurrencyPeriod(config.recurrenceType) || "monthly",
      recurrencyLimit: config.periodMonths || 1,
    }));
  }
}