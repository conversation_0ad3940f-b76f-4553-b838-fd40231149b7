// Tipos baseados na API de negotiations do backend

export type TNegotiationType = "fixed" | "percentage";
export type TRecurrencyType = "limited" | "unlimited"; 
export type TRecurrencyPeriod = "monthly" | "yearly" | "first_payment";
export type TNegotiationStatus = "active" | "inactive";

// Tipos auxiliares para outras entidades
export interface IAffiliate {
  id: string;
  name: string;
  email: string;
  status: "active" | "inactive";
}

export interface IPaymentPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  status: "active" | "inactive";
}

export interface IUser {
  id: string;
  name: string;
  email: string;
  division: string;
  status: "active" | "inactive";
}

// DTO para configuração específica de plano na negociação
export interface INegotiationPlanDto {
  paymentPlanId: string;
  type: TNegotiationType;
  value: number;
  recurrencyType: TRecurrencyType;
  recurrencyPeriod: TRecurrencyPeriod;
  recurrencyLimit?: number;
}

// Entidade principal da negociação
export interface INegotiation {
  id: string;
  name: string;
  type?: TNegotiationType;
  value?: number;
  recurrencyType?: TRecurrencyType;
  recurrencyPeriod?: TRecurrencyPeriod;
  recurrencyLimit?: number;
  status: TNegotiationStatus;
  allowDefault: boolean;
  createdAt: string;
  updatedAt: string;
  affiliates?: IAffiliate[];
  paymentPlans?: IPaymentPlan[];
}

// DTO para criação de negociação
export interface ICreateNegotiationDto {
  name: string;
  allowDefault: boolean;
  type?: TNegotiationType;
  value?: number;
  recurrencyType?: TRecurrencyType;
  recurrencyPeriod?: TRecurrencyPeriod;
  recurrencyLimit?: number;
  status?: TNegotiationStatus;
  affiliateIds: string[];
  paymentPlanIds?: string[];
  planConfigurations?: INegotiationPlanDto[];
}

// DTO para atualização de negociação
export interface IUpdateNegotiationDto extends Partial<ICreateNegotiationDto> {}

// Response paginada da API
export interface INegotiationsResponse {
  data: INegotiation[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  itemsPerPage: number;
}

// Tipos para uso no frontend (compatibilidade com código existente)
export type TCalculationType = TNegotiationType;
export type TRecurrenceType = "first_payment" | "recurring" | "period"; // Mantendo compatibilidade frontend

export interface IPlanConfiguration {
  planId: string;
  calculationType: TCalculationType;
  value: number;
  recurrenceType?: TRecurrenceType;
  periodMonths?: number;
}

export interface IPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  status: "active" | "inactive";
}

export interface IAffiliateLink {
  id: string;
  name: string;
  email: string;
  status: "active" | "inactive";
}

export interface INegotiationFormData {
  name: string;
  calculationType?: TCalculationType;
  value?: number;
  recurrenceType?: TRecurrenceType;
  periodMonths?: number;
  periodStartDate?: string;
  periodEndDate?: string;
  applicableToAllPlans?: boolean;
  planIds?: string[];
  planConfigurations?: IPlanConfiguration[];
  userIds?: string[];
}

export interface INegotiationResponse {
  data: INegotiation[];
  currentPage: number;
  pageSize: number;
  totalRecords: number;
}
