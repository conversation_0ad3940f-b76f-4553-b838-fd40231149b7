import { TPermission } from "../domain/models";
import { Edit, Trash2 } from "lucide-react";

interface TableColumnsProps {
  onEdit: (permission: TPermission) => void;
  onDelete: (id: string) => void;
}

export function TableColumns({ onEdit, onDelete }: TableColumnsProps) {
  return [
    {
      id: "name",
      label: "Nome",
      render: (rowData: TPermission) => <p>{rowData.name}</p>,
    },
    {
      id: "tag",
      label: "Tag",
      render: (rowData: TPermission) => (
        <div className="text-sm text-gray-900">
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-[#E11E37]">{rowData.tag}</span>
        </div>
      ),
    },
    {
      align: "center" as const,
      id: "actions",
      label: "Ações",
      render: (rowData: TPermission) => (
        <div className="flex items-center justify-center gap-2">
          <button onClick={() => onEdit(rowData)} className="p-1 text-yellow-600 hover:text-yellow-700 transition-colors" title="Editar">
            <Edit size={18} />
          </button>
          <button onClick={() => onDelete(rowData.id)} className="p-1 text-red-600 hover:text-red-700 transition-colors" title="Excluir">
            <Trash2 size={18} />
          </button>
        </div>
      ),
    },
  ];
}
