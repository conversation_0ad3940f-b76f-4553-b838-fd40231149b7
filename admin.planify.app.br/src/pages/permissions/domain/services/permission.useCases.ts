import HttpClient from "@/infra/httpRequest";
import { TPermission, TPermissionForm } from "../models";

const httpClient = new HttpClient();
const BASE_URL = "scopes";

export class PermissionService {
  getPermissions = async (): Promise<TPermission[]> => {
    const response = await httpClient.get(BASE_URL);
    return response?.data;
  };

  createPermission = async (permission: TPermissionForm): Promise<TPermission> => {
    const response = await httpClient.post(BASE_URL, permission);
    return response?.data;
  };

  updatePermission = async (id: string, permission: TPermissionForm): Promise<TPermission> => {
    const response = await httpClient.patch(`${BASE_URL}/${id}`, permission);
    return response?.data;
  };

  deletePermission = async (id: string): Promise<void> => {
    await httpClient.delete(`${BASE_URL}/${id}`);
  };
}
