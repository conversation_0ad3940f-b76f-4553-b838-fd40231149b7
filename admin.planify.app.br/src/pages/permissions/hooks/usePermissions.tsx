import { useState } from "react";
import { TPermission, TPermissionForm } from "../domain/models";
import { PermissionService } from "../domain/services/permission.useCases";
import { toast } from "react-toastify";
import { useGlobalStore } from "@/contexts/globalContext";

export function usePermissions() {
  const service = new PermissionService();
  const { permissions, getPermissions } = useGlobalStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState<TPermission | null>(null);

  const [formData, setFormData] = useState<TPermissionForm>({
    name: "",
    tag: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingPermission) {
        await service.updatePermission(editingPermission.id, formData);
        toast.success("Permissão atualizada com sucesso!");
      } else {
        await service.createPermission(formData);
        toast.success("Permissão criada com sucesso!");
      }
      setIsModalOpen(false);
      resetForm();
      getPermissions();
    } catch (error) {
      toast.error(editingPermission ? "Erro ao atualizar permissão" : "Erro ao criar permissão");
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await service.deletePermission(id);
      toast.success("Permissão removida com sucesso!");
      getPermissions();
    } catch (error) {
      toast.error("Erro ao remover permissão");
    }
  };

  const openEditModal = (permission: TPermission) => {
    setEditingPermission(permission);
    setFormData({
      name: permission.name,
      tag: permission.tag,
    });
    setIsModalOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      tag: "",
    });
    setEditingPermission(null);
  };

  return {
    permissions,
    isModalOpen,
    setIsModalOpen,
    editingPermission,
    formData,
    setFormData,
    handleSubmit,
    handleDelete,
    openEditModal,
    resetForm,
  };
}
