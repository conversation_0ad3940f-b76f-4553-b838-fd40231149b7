export type TAfiliate = {
  id: string;
  name: string;
  email: string;
  document: string | null;
  phone: string | null;
  status: "pending" | "approved" | "rejected" | "active";
  affiliateCode: string;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  clicks?: number;
  registrations?: number;
  subscriptions?: number;
  payments?: number;
  revenue?: number;
  info?: {
    id: string;
    birthDate: string;
    gender: string;
    cep: string;
    state: string;
    city: string;
    location: string;
    locationNumber: string;
    locationComplement: string;
    profileImage: string | null;
    createdAt: string;
    updatedAt: string;
  };
  referrals?: TAfiliateLinkType[];
  metricsClicks?: number;
  metricsRegistrations?: number;
  metricsSubscriptions?: number;
  metricsPayments?: number;
  metricsRevenue?: number;
  analytics?: {
    clicks: number;
    registrations: number;
    subscriptions: number;
    payments: number;
    revenue: number;
  };
  financialInfo?: TAffiliateFinancialInfo;
  payouts?: TAfiliatePayout[];
};

export type TAffiliateFinancialInfo = {
  id: string;
  affiliateId: string;
  bankName?: string;
  bankBranch?: string;
  bankAccount?: string;
  bankAccountType?: string;
  pixKey?: string;
  pixKeyType?: string;
  documentType?: string;
  documentNumber?: string;
  createdAt: string;
  updatedAt: string;
};

export type TAfiliatePaginated = {
  data: TAfiliate[];
  totalRecords: number;
  totalPages: number;
  pageSize: number;
  currentPage: number;
};

export type TAfiliateLinkType = {
  id: string;
  affiliateId: string;
  referralCode: string;
  referralUrl: string;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  metricsClicks?: number;
  metricsRegistrations?: number;
  metricsSubscriptions?: number;
  metricsPayments?: number;
  metricsRevenue?: number;
  clicks?: number;
  registrations?: number;
  subscriptions?: number;
  payments?: number;
  revenue?: number;
  affiliate?: {
    id: string;
    name: string;
    email: string;
    affiliateCode: string;
  };
};

export type TPayoutStatus = "pending" | "approved" | "paid" | "rejected" | "failed" | "canceled" | "refunded";

export type TAfiliatePayout = {
  id: string;
  userName: string;
  productName: string;
  amount: number;
  date: string;
  status: TPayoutStatus;
  paymentReceiptUrl?: string;
};

export type TAfiliateTabs = "all" | "pending" | "approved" | "rejected" | "active";

export type TUpdatePayoutStatusPayload = {
  status: TPayoutStatus;
  paymentReceiptUrl?: string;
};
