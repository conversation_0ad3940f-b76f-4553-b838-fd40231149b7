import HttpClient from "@infra/httpRequest";
import {
  TAfiliate,
  TAfiliateLinkType,
  TAfiliatePaginated,
  TAfiliateTabs,
  TAfiliatePayout,
  TAffiliateFinancialInfo,
  TUpdatePayoutStatusPayload,
} from "../models";

const service = new HttpClient();

type TAfiliateAnalytics = TAfiliate["analytics"];

export default class AfiliatesServices {
  async get(): Promise<TAfiliatePaginated> {
    const response = await service.get(`/affiliates`);
    return response?.data || { data: [], totalRecords: 0, totalPages: 0, pageSize: 10, currentPage: 1 };
  }

  async getById(id: string): Promise<TAfiliate | null> {
    const response = await service.get(`/affiliates/${id}`);
    return response?.data || null;
  }

  async create(data: Record<string, unknown>): Promise<TAfiliate | null> {
    const response = await service.post(`/affiliates`, data);
    return response?.data || null;
  }

  async getPaginated(page: number, pageSize: number, searchTerm?: string, status?: TAfiliateTabs | null): Promise<TAfiliatePaginated> {
    let url = `/affiliates?page=${page}&pageSize=${pageSize}`;

    if (searchTerm) {
      url += `&search=${searchTerm}`;
    }

    if (status && status !== "all") {
      url += `&status=${status}`;
    }

    const response = await service.get(url);
    return response?.data || { data: [], totalRecords: 0, totalPages: 0, pageSize, currentPage: page };
  }

  async approveAfiliate(id: string): Promise<boolean> {
    const response = await service.post(`/affiliates/approve`, { id });
    return !!response?.data;
  }

  async rejectAfiliate(id: string): Promise<boolean> {
    const response = await service.patch(`/affiliates/${id}/reject`, {});
    return !!response?.data;
  }

  async getAfiliateLinks(affiliateId: string): Promise<TAfiliateLinkType[]> {
    const response = await service.get(`/affiliates/referrals/${affiliateId}`);
    return response?.data?.referrals || [];
  }

  async getAfiliatePayouts(affiliateCode: string, month?: string, year?: string): Promise<TAfiliatePayout[]> {
    let url = `/affiliate-financial/transfers/by-referral/${affiliateCode}`;
    const params = new URLSearchParams();
    if (month) {
      params.append("month", month);
    }
    if (year) {
      params.append("year", year);
    }
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    const response = await service.get(url);
    return response?.data || [];
  }

  async getAfiliateMetrics(id: string): Promise<TAfiliateAnalytics> {
    const response = await service.get(`/affiliates/${id}/metrics`);
    return response?.data || { clicks: 0, registrations: 0, subscriptions: 0, payments: 0, revenue: 0 };
  }

  async getAffiliateFinancialInfo(id: string): Promise<TAffiliateFinancialInfo | null> {
    const response = await service.get(`/affiliate-financial/${id}`);
    return response?.data || null;
  }
  async updatePayoutStatus(transferId: string, payload: TUpdatePayoutStatusPayload): Promise<TAfiliatePayout | null> {
    const response = await service.patch(`/affiliate-financial/transfers/${transferId}/status`, payload);
    return response?.data || null;
  }
}
