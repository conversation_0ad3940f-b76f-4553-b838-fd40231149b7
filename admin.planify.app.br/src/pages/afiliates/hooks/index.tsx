import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { AfiliatesServices } from "../domain";
import {
  TAfiliate,
  TAfiliateLinkType,
  TAfiliatePaginated,
  TAfiliateTabs,
  TUpdatePayoutStatusPayload,
  TPayoutStatus,
  TAfiliatePayout,
} from "../domain/models";
import { toast } from "react-toastify";

export default function useAfiliates() {
  const service = new AfiliatesServices();
  const [pageData, setPageData] = useState<TAfiliatePaginated>({
    data: [],
    totalRecords: 0,
    totalPages: 0,
    pageSize: 10,
    currentPage: 1,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isPayoutsLoading, setIsPayoutsLoading] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentStatus, setCurrentStatus] = useState<TAfiliateTabs | null>(null);
  const [selectedAfiliate, setSelectedAfiliate] = useState<TAfiliate | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPayoutsModalOpen, setIsPayoutsModalOpen] = useState(false);
  const [isLinksModalOpen, setIsLinksModalOpen] = useState(false);
  const [isLinksLoading, setIsLinksLoading] = useState(false);
  const [isMetricsModalOpen, setIsMetricsModalOpen] = useState(false);
  const [afiliateLinks, setAfiliateLinks] = useState<TAfiliateLinkType[]>([]);
  const [currentReferralCode, setCurrentReferralCode] = useState<string | null>(null); // Novo estado
  const [currentMonth, setCurrentMonth] = useState(() => {
    const date = new Date();
    const month = date.getMonth() + 1;
    return month < 10 ? `0${month}` : `${month}`;
  });
  const [currentYear, setCurrentYear] = useState(() => new Date().getFullYear().toString());
  const [activeTab, setActiveTab] = useState<TAfiliateTabs>("all");
  const [isEditPayoutModalOpen, setIsEditPayoutModalOpen] = useState(false);
  const [editingPayout, setEditingPayout] = useState<TAfiliatePayout | null>(null);
  const [editStatus, setEditStatus] = useState<TPayoutStatus>("pending");
  const [editReceiptUrl, setEditReceiptUrl] = useState<string>("");
  const [isUpdating, setIsUpdating] = useState(false);

  const statusTabs: { label: string; value: TAfiliateTabs }[] = [
    { label: "Todos", value: "all" },
    { label: "Pendentes", value: "pending" },
    { label: "Aprovados", value: "approved" },
    { label: "Ativos", value: "active" },
    { label: "Rejeitados", value: "rejected" },
  ];

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const openPayoutsModalHandler = (afiliate: TAfiliate) => {
    handleOpenPayoutsModal(afiliate);
  };

  const navigate = useNavigate();

  useEffect(() => {
    if (searchTerm.length > 0 || currentStatus) {
      const timer = setTimeout(() => {
        getPaginatedAfiliates("page", 1);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [searchTerm, currentStatus]);

  async function getAfiliates() {
    setIsLoading(true);
    try {
      const response = await service.get();
      if (response && response.data) {
        setPageData(response);
      }
    } catch (error) {
      console.error("Erro ao buscar afiliados:", error);
      toast.error("Erro ao buscar afiliados");
    } finally {
      setIsLoading(false);
    }
  }

  async function getPaginatedAfiliates(paramName: string, value: number) {
    setIsLoading(true);
    try {
      let response;
      if (currentStatus) {
        response = await service.getPaginated(value, pageData.pageSize, searchTerm, currentStatus);
      } else {
        response = await service.getPaginated(value, pageData.pageSize, searchTerm);
      }
      if (response && response.data) {
        setPageData(response);
      }
    } catch (error) {
      console.error("Erro ao buscar afiliados paginados:", error);
      toast.error("Erro ao buscar afiliados");
    } finally {
      setIsLoading(false);
    }
  }

  async function handleStatusChange(status: TAfiliateTabs | null) {
    setCurrentStatus(status);
    setIsLoading(true);
    try {
      const response = await service.getPaginated(1, pageData.pageSize, searchTerm, status);
      if (response && response.data) {
        setPageData(response);
      }
    } catch (error) {
      console.error("Erro ao filtrar afiliados por status:", error);
      toast.error("Erro ao filtrar afiliados");
    } finally {
      setIsLoading(false);
    }
  }

  async function handleViewDetails(afiliate: TAfiliate) {
    setSelectedAfiliate(afiliate);
    setIsModalOpen(true);
    try {
      const financialInfo = await service.getAffiliateFinancialInfo(afiliate.id);
      if (financialInfo) {
        setSelectedAfiliate((prevAfiliate) => (prevAfiliate ? { ...prevAfiliate, financialInfo } : null));
      }
    } catch (error) {
      console.error("Erro ao buscar informações financeiras do afiliado:", error);
      toast.error("Erro ao buscar informações financeiras do afiliado");
    }
  }

  async function handleApproveAfiliate(id: string) {
    setIsLoading(true);
    try {
      const result = await service.approveAfiliate(id);
      if (result) {
        await getAfiliates();
        toast.success("Afiliado aprovado com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao aprovar afiliado:", error);
      toast.error("Erro ao aprovar afiliado");
    } finally {
      setIsLoading(false);
    }
  }

  async function handleRejectAfiliate(id: string) {
    setIsLoading(true);
    try {
      const result = await service.rejectAfiliate(id);
      if (result) {
        await getAfiliates();
        toast.success("Afiliado rejeitado com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao rejeitar afiliado:", error);
      toast.error("Erro ao rejeitar afiliado");
    } finally {
      setIsLoading(false);
    }
  }

  async function handleOpenLinksModal(afiliate: TAfiliate) {
    setSelectedAfiliate(afiliate);
    setIsLinksModalOpen(true);
    setAfiliateLinks([]);
    setIsLinksLoading(true);
    try {
      const links = await service.getAfiliateLinks(afiliate.id);
      if (links && links.length >= 0) {
        setAfiliateLinks(links);
      } else {
        setAfiliateLinks([]);
      }
    } catch (error) {
      console.error("Erro ao buscar links do afiliado:", error);
      setAfiliateLinks([]);
      toast.error("Erro ao buscar links do afiliado");
    } finally {
      setIsLinksLoading(false);
    }
  }

  async function handleOpenMetricsModal(afiliate: TAfiliate) {
    setSelectedAfiliate(afiliate);
    setIsMetricsModalOpen(true);
  }

  async function fetchAfiliatePayouts(referralCode: string, month: string, year: string) {
    if (!selectedAfiliate) {
      toast.error("Nenhum afiliado selecionado para buscar pagamentos.");
      return;
    }
    if (!referralCode) {
      setSelectedAfiliate((prev) => (prev ? { ...prev, payouts: [] } : null));
      return;
    }

    setIsPayoutsLoading(true);
    try {
      const payouts = await service.getAfiliatePayouts(referralCode, month, year);
      setSelectedAfiliate((prev) => (prev ? { ...prev, payouts: payouts || [] } : prev));
    } catch (error) {
      console.error("Erro ao buscar pagamentos do afiliado:", error);
      toast.error("Erro ao buscar pagamentos do afiliado");
      setSelectedAfiliate((prev) => (prev ? { ...prev, payouts: [] } : prev));
    } finally {
      setIsPayoutsLoading(false);
    }
  }

  async function handleOpenPayoutsModal(afiliate: TAfiliate) {
    setSelectedAfiliate({ ...afiliate, payouts: [] });
    setIsPayoutsModalOpen(true);

    setIsLinksLoading(true);
    try {
      const links = await service.getAfiliateLinks(afiliate.id);
      const validLinks = links || [];
      setAfiliateLinks(validLinks);

      if (validLinks.length > 0) {
        const primaryReferral = validLinks.find((link) => link.referralCode === afiliate.affiliateCode);
        const initialReferralCode = primaryReferral ? primaryReferral.referralCode : validLinks[0].referralCode;
        setCurrentReferralCode(initialReferralCode);
        fetchAfiliatePayouts(initialReferralCode, currentMonth, currentYear);
      } else {
        if (afiliate.affiliateCode) {
          setCurrentReferralCode(afiliate.affiliateCode);
          fetchAfiliatePayouts(afiliate.affiliateCode, currentMonth, currentYear);
        } else {
          setCurrentReferralCode(null);
          setSelectedAfiliate((prev) => (prev ? { ...prev, payouts: [] } : null));
          toast.info("Nenhum código de referência encontrado para este afiliado.");
        }
      }
    } catch (error) {
      console.error("Erro ao buscar links do afiliado para o modal de pagamentos:", error);
      setAfiliateLinks([]);
      setCurrentReferralCode(null);
      setSelectedAfiliate((prev) => (prev ? { ...prev, payouts: [] } : null));
      toast.error("Erro ao buscar códigos de referência do afiliado.");
    } finally {
      setIsLinksLoading(false);
    }
  }

  async function handleUpdatePayoutStatus(transferId: string, status: TPayoutStatus, paymentReceiptUrl?: string) {
    if (!selectedAfiliate || !currentReferralCode) {
      toast.error("Dados do afiliado ou código de referência ausentes para atualizar o pagamento.");
      return;
    }

    setIsUpdatingStatus(true);
    try {
      const payload: TUpdatePayoutStatusPayload = { status };
      if (paymentReceiptUrl) {
        payload.paymentReceiptUrl = paymentReceiptUrl;
      }
      console.log("[hook:handleUpdatePayoutStatus] Payload da requisição:", payload);

      await service.updatePayoutStatus(transferId, payload);

      toast.success("Status do pagamento atualizado com sucesso!");
      await fetchAfiliatePayouts(currentReferralCode, currentMonth, currentYear);
      return true;
    } catch (error) {
      console.error("Erro ao atualizar status do pagamento:", error);
      toast.error("Erro ao atualizar status do pagamento.");
      return false;
    } finally {
      setIsUpdatingStatus(false);
    }
  }

  function openEditPayoutModal(payout: TAfiliatePayout) {
    setEditingPayout(payout);
    setEditStatus(payout.status);
    setEditReceiptUrl(payout.paymentReceiptUrl || "");
    setIsEditPayoutModalOpen(true);
  }

  function closeEditPayoutModal() {
    setIsEditPayoutModalOpen(false);
    setEditingPayout(null);
    setEditStatus("pending");
    setEditReceiptUrl("");
  }

  async function handleEditPayoutStatus() {
    if (!editingPayout) return;
    setIsUpdating(true);
    await handleUpdatePayoutStatus(editingPayout.id, editStatus, editReceiptUrl || undefined);
    setIsUpdating(false);
    setIsEditPayoutModalOpen(false);
  }

  return {
    isLoading,
    setIsLoading,
    isPayoutsLoading,
    setIsPayoutsLoading,
    pageData,
    getAfiliates,
    getPaginatedAfiliates,
    handleStatusChange,
    handleApproveAfiliate,
    handleRejectAfiliate,
    handleViewDetails,
    searchTerm,
    setSearchTerm,
    selectedAfiliate,
    setSelectedAfiliate,
    isModalOpen,
    setIsModalOpen,
    isPayoutsModalOpen,
    setIsPayoutsModalOpen,
    isLinksModalOpen,
    setIsLinksModalOpen,
    isLinksLoading,
    setIsLinksLoading,
    isMetricsModalOpen,
    setIsMetricsModalOpen,
    currentMonth,
    setCurrentMonth,
    currentYear,
    setCurrentYear,
    currentReferralCode,
    setCurrentReferralCode,
    navigate,
    afiliateLinks,
    handleOpenLinksModal,
    handleOpenMetricsModal,
    handleOpenPayoutsModal,
    fetchAfiliatePayouts,
    currentStatus,
    service,
    handleUpdatePayoutStatus,
    isUpdatingStatus,
    activeTab,
    setActiveTab,
    statusTabs,
    handleClearSearch,
    openPayoutsModalHandler,
    isEditPayoutModalOpen,
    setIsEditPayoutModalOpen,
    editingPayout,
    setEditingPayout,
    editStatus,
    setEditStatus,
    editReceiptUrl,
    setEditReceiptUrl,
    isUpdating,
    openEditPayoutModal,
    closeEditPayoutModal,
    handleEditPayoutStatus,
  };
}
