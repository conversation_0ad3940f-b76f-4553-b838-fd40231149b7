import { Eye, CheckCircle, XCircle, BarChart2, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { TAfiliate } from "../domain/models";

type TableColumnsProps = {
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
  onViewDetails: (afiliate: TAfiliate) => void;
  onViewPayouts: (afiliate: TAfiliate) => void;
  onViewLinks: (afiliate: TAfiliate) => void;
  onViewMetrics: (afiliate: TAfiliate) => void;
};

export function TableColumns({ onApprove, onReject, onViewDetails, onViewPayouts, onViewLinks, onViewMetrics }: TableColumnsProps) {
  return [
    {
      id: "name",
      label: "Nome",
      render: (rowData: TAfiliate) => (
        <div className="flex items-center">
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{rowData.name}</div>
            <div className="text-sm text-gray-500">{rowData.email}</div>
          </div>
        </div>
      ),
    },
    {
      id: "metrics",
      label: "Analytics",
      align: "left" as const,
      render: (rowData: TAfiliate) => (
        <div className="text-sm text-left  text-gray-900">
          <div>Cliques: {rowData.clicks || 0}</div>
          <div>Registros: {rowData.registrations || 0}</div>
          <div>Assinaturas: {rowData.subscriptions || 0}</div>
        </div>
      ),
    },
    {
      id: "financialMetrics",
      label: "Métricas financeiras",
      align: "left" as const,
      render: (rowData: TAfiliate) => (
        <div className="text-sm text-left text-gray-900">
          <div>Comissões: {rowData.payments || 0}</div>
          <div>Receita total: R$ {(rowData.revenue || 0).toLocaleString("pt-BR", { minimumFractionDigits: 2 })}</div>
        </div>
      ),
    },
    {
      id: "createdAt",
      label: "Data de Cadastro",
      align: "center" as const,
      render: (rowData: TAfiliate) => <div className="text-sm text-gray-900">{new Date(rowData.createdAt).toLocaleDateString()}</div>,
    },
    {
      align: "center" as const,
      id: "status",
      label: "Status",
      render: (rowData: TAfiliate) => {
        let statusClass = "";
        let statusText = "";

        switch (rowData.status) {
          case "approved":
            statusClass = "bg-green-100 text-green-800";
            statusText = "Aprovado";
            break;
          case "rejected":
            statusClass = "bg-red-100 text-red-800";
            statusText = "Rejeitado";
            break;
          case "active":
            statusClass = "bg-blue-100 text-blue-800";
            statusText = "Ativo";
            break;
          default:
            statusClass = "bg-yellow-100 text-yellow-800";
            statusText = "Pendente";
        }

        return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}`}>{statusText}</span>;
      },
    },
    {
      align: "center" as const,
      id: "action",
      label: "Ações",
      render: (rowData: TAfiliate) => (
        <div className="flex items-center justify-center space-x-2">
          <button onClick={() => onViewDetails(rowData)} className="text-gray-600 hover:text-gray-900" title="Ver detalhes">
            <Eye className="h-5 w-5" />
          </button>

          {rowData.status === "pending" && (
            <>
              <button onClick={() => onApprove(rowData.id)} className="text-green-600 hover:text-green-900" title="Aprovar">
                <CheckCircle className="h-5 w-5" />
              </button>
              <button onClick={() => onReject(rowData.id)} className="text-red-600 hover:text-red-900" title="Rejeitar">
                <XCircle className="h-5 w-5" />
              </button>
            </>
          )}

          <button onClick={() => onViewMetrics(rowData)} className="text-blue-600 hover:text-blue-900" title="Ver métricas">
            <BarChart2 className="h-5 w-5" />
          </button>

          <button onClick={() => onViewPayouts(rowData)} className="text-yellow-600 hover:text-yellow-900" title="Ver pagamentos">
            <DollarSign className="h-5 w-5" />
          </button>

          <button onClick={() => onViewLinks(rowData)} className="text-purple-600 hover:text-purple-900" title="Ver links">
            <Link className="h-5 w-5" />
          </button>
        </div>
      ),
    },
  ];
}
