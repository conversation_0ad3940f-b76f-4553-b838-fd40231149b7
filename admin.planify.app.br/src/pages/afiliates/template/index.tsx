import { Search, X, Pencil } from "lucide-react";
import { Table } from "@/components/Table";
import { TableColumns } from "../mocks/TableColumns";
import { TAfiliateLinkType, TAfiliatePayout, TPayoutStatus } from "../domain/models";
import FileUpload from "@/components/FileUpload";

export default function TemplatePage({ ...sharedProps }) {
  const {
    isLoading,
    isPayoutsLoading,
    pageData,
    handleStatusChange,
    handleApproveAfiliate,
    handleRejectAfiliate,
    handleViewDetails,
    searchTerm,
    setSearchTerm,
    selectedAfiliate,
    isModalOpen,
    setIsModalOpen,
    isPayoutsModalOpen,
    setIsPayoutsModalOpen,
    isLinksModalOpen,
    setIsLinksModalOpen,
    isLinksLoading,
    isMetricsModalOpen,
    setIsMetricsModalOpen,
    currentMonth,
    setCurrentMonth,
    currentYear,
    setCurrentYear,
    currentReferralCode,
    setCurrentReferralCode,
    getPaginatedAfiliates,
    handleOpenLinksModal,
    handleOpenMetricsModal,
    fetchAfiliatePayouts,
    afiliateLinks,
    activeTab,
    setActiveTab,
    statusTabs,
    handleClearSearch,
    openPayoutsModalHandler,
    openEditPayoutModal,
    isEditPayoutModalOpen,
    editingPayout,
    closeEditPayoutModal,
    editStatus,
    setEditStatus,
    setEditReceiptUrl,
    editReceiptUrl,
    isUpdating,
    handleEditPayoutStatus,
  } = sharedProps;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Afiliados</h1>
      </div>

      <div className="mb-6">
        <nav className="flex" aria-label="Tabs">
          <div className="flex space-x-2">
            {statusTabs.map((tab: { label: string; value: string }) => (
              <button
                key={tab.value}
                onClick={() => {
                  handleStatusChange(tab.value === "all" ? null : tab.value);
                  setActiveTab(tab.value);
                }}
                className={`px-6 py-3 text-sm font-medium rounded-lg ${
                  activeTab === tab.value
                    ? "bg-red-50 text-red-600 border border-red-200"
                    : "bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100 border border-gray-200"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </nav>
      </div>

      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Buscar por nome ou email..."
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm"
          />
          {searchTerm && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button onClick={handleClearSearch} className="text-gray-400 hover:text-gray-500 focus:outline-none">
                <X className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>

      <Table
        showPagination
        columns={TableColumns({
          onApprove: handleApproveAfiliate,
          onReject: handleRejectAfiliate,
          onViewDetails: handleViewDetails,
          onViewPayouts: openPayoutsModalHandler,
          onViewLinks: handleOpenLinksModal,
          onViewMetrics: handleOpenMetricsModal,
        })}
        data={pageData?.data || []}
        defaultRows={pageData?.pageSize || 10}
        totalItems={pageData?.totalRecords || 0}
        changeRowsPerPage={(pageSize) => getPaginatedAfiliates("pageSize", pageSize)}
        onChangePage={(page) => getPaginatedAfiliates("page", page)}
        loading={isLoading}
        currentPage={pageData?.currentPage}
      />

      {/* Modal de Detalhes do Afiliado */}
      {isModalOpen && selectedAfiliate && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full sm:w-1/2">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Detalhes do Afiliado</h3>
                  <button onClick={() => setIsModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Nome</p>
                    <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Email</p>
                    <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.email}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Status</p>
                    <p className="mt-1 text-sm text-gray-900">
                      {(() => {
                        let statusClass = "";
                        let statusText = "";

                        switch (selectedAfiliate.status) {
                          case "approved":
                            statusClass = "bg-green-100 text-green-800";
                            statusText = "Aprovado";
                            break;
                          case "rejected":
                            statusClass = "bg-red-100 text-red-800";
                            statusText = "Rejeitado";
                            break;
                          case "active":
                            statusClass = "bg-blue-100 text-blue-800";
                            statusText = "Ativo";
                            break;
                          default:
                            statusClass = "bg-yellow-100 text-yellow-800";
                            statusText = "Pendente";
                        }

                        return (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}`}>
                            {statusText}
                          </span>
                        );
                      })()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Data de Cadastro</p>
                    <p className="mt-1 text-sm text-gray-900">{new Date(selectedAfiliate.createdAt).toLocaleDateString()}</p>
                  </div>

                  <div className="mt-6">
                    <h4 className="text-base font-medium text-gray-900 mb-3">Informações Financeiras</h4>

                    {!selectedAfiliate.financialInfo ? (
                      <div className="flex justify-center items-center h-20">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      </div>
                    ) : Object.keys(selectedAfiliate.financialInfo).length === 0 ? (
                      <p className="text-sm text-gray-500">Nenhuma informação financeira cadastrada.</p>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {selectedAfiliate.financialInfo.bankName && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Banco</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.bankName}</p>
                          </div>
                        )}

                        {selectedAfiliate.financialInfo.bankBranch && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Agência</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.bankBranch}</p>
                          </div>
                        )}

                        {selectedAfiliate.financialInfo.bankAccount && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Conta</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.bankAccount}</p>
                          </div>
                        )}

                        {selectedAfiliate.financialInfo.bankAccountType && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Tipo de Conta</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.bankAccountType}</p>
                          </div>
                        )}

                        {selectedAfiliate.financialInfo.pixKey && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Chave PIX</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.pixKey}</p>
                          </div>
                        )}

                        {selectedAfiliate.financialInfo.pixKeyType && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Tipo de Chave PIX</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.pixKeyType}</p>
                          </div>
                        )}

                        {selectedAfiliate.financialInfo.documentType && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Tipo de Documento</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.documentType}</p>
                          </div>
                        )}

                        {selectedAfiliate.financialInfo.documentNumber && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Número do Documento</p>
                            <p className="mt-1 text-sm text-gray-900">{selectedAfiliate.financialInfo.documentNumber}</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-5 sm:mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsModalOpen(false)}
                    className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm"
                  >
                    Fechar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Pagamentos */}
      {isPayoutsModalOpen && selectedAfiliate && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full sm:w-1/2">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Pagamentos - {selectedAfiliate.name}</h3>
                  <button onClick={() => setIsPayoutsModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="mb-4">
                  <label htmlFor="referralCode" className="block text-sm font-medium text-gray-700">
                    Código de Referência
                  </label>
                  <select
                    id="referralCode"
                    value={currentReferralCode || ""}
                    onChange={async (e) => {
                      const newReferralCode = e.target.value;
                      setCurrentReferralCode(newReferralCode);
                      if (selectedAfiliate && newReferralCode) {
                        fetchAfiliatePayouts(newReferralCode, currentMonth, currentYear);
                      }
                    }}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                    disabled={
                      isLinksLoading || ((!afiliateLinks || afiliateLinks.length === 0) && !(selectedAfiliate && selectedAfiliate.affiliateCode))
                    }
                  >
                    {isLinksLoading && <option value="">Carregando códigos...</option>}
                    {!isLinksLoading && (!afiliateLinks || afiliateLinks.length === 0) && !(selectedAfiliate && selectedAfiliate.affiliateCode) && (
                      <option value="">Nenhum código disponível</option>
                    )}
                    {!isLinksLoading &&
                      afiliateLinks &&
                      afiliateLinks.map((link: TAfiliateLinkType) => (
                        <option key={link.id} value={link.referralCode}>
                          {link.referralCode}
                        </option>
                      ))}
                    {!isLinksLoading &&
                      selectedAfiliate &&
                      selectedAfiliate.affiliateCode &&
                      (!afiliateLinks || !afiliateLinks.some((link: TAfiliateLinkType) => link.referralCode === selectedAfiliate.affiliateCode)) && (
                        <option key={`principal-${selectedAfiliate.affiliateCode}`} value={selectedAfiliate.affiliateCode}>
                          {selectedAfiliate.affiliateCode} (Principal)
                        </option>
                      )}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="month" className="block text-sm font-medium text-gray-700">
                      Mês
                    </label>
                    <select
                      id="month"
                      value={currentMonth}
                      onChange={async (e) => {
                        const newMonth = e.target.value;
                        setCurrentMonth(newMonth);
                        if (selectedAfiliate && currentReferralCode) {
                          fetchAfiliatePayouts(currentReferralCode, newMonth, currentYear);
                        }
                      }}
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                    >
                      <option value="01">Janeiro</option>
                      <option value="02">Fevereiro</option>
                      <option value="03">Março</option>
                      <option value="04">Abril</option>
                      <option value="05">Maio</option>
                      <option value="06">Junho</option>
                      <option value="07">Julho</option>
                      <option value="08">Agosto</option>
                      <option value="09">Setembro</option>
                      <option value="10">Outubro</option>
                      <option value="11">Novembro</option>
                      <option value="12">Dezembro</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="year" className="block text-sm font-medium text-gray-700">
                      Ano
                    </label>
                    <select
                      id="year"
                      value={currentYear}
                      onChange={async (e) => {
                        const newYear = e.target.value;
                        setCurrentYear(newYear);
                        if (selectedAfiliate && currentReferralCode) {
                          fetchAfiliatePayouts(currentReferralCode, currentMonth, newYear);
                        }
                      }}
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                    >
                      {Array.from({ length: 7 }, (_, i) => new Date().getFullYear() - 5 + i + 1)
                        .sort((a, b) => b - a)
                        .map((yearVal) => (
                          <option key={yearVal} value={yearVal.toString()}>
                            {yearVal}
                          </option>
                        ))}
                    </select>
                  </div>
                </div>
                {isPayoutsLoading ? (
                  <div className="flex justify-center items-center h-40">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Usuário
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Produto
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Data
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Valor
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Ação
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {selectedAfiliate.payouts && selectedAfiliate.payouts.length > 0 ? (
                          selectedAfiliate.payouts.map((payout: TAfiliatePayout) => (
                            <tr key={payout.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payout.userName}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{payout.productName}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(payout.date).toLocaleDateString()}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(payout.amount / 100)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span
                                  className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    payout.status === "paid"
                                      ? "bg-green-100 text-green-800"
                                      : payout.status === "rejected"
                                      ? "bg-red-100 text-red-800"
                                      : "bg-yellow-100 text-yellow-800"
                                  }`}
                                >
                                  {payout.status === "paid" ? "Pago" : payout.status === "rejected" ? "Rejeitado" : "Pendente"}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-center">
                                <button onClick={() => openEditPayoutModal(payout)} className="text-primary hover:text-red-600">
                                  <Pencil className="h-5 w-5 mx-auto" />
                                </button>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                              Nenhum pagamento encontrado para este período.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                )}
                <div className="mt-5 sm:mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsPayoutsModalOpen(false)}
                    className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm"
                  >
                    Fechar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Links */}
      {isLinksModalOpen && selectedAfiliate && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full sm:w-1/2">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Links de Afiliado - {selectedAfiliate.name}</h3>
                  <button onClick={() => setIsLinksModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {isLinksLoading ? (
                  <div className="flex justify-center items-center h-40">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Código
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            URL
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Data de Criação
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {afiliateLinks && afiliateLinks.length > 0 ? (
                          afiliateLinks.map((link: TAfiliateLinkType) => (
                            <tr key={link.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{link.referralCode}</td>
                              <td className="px-6 py-4 text-sm text-gray-500">
                                <a
                                  href={link.referralUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 underline break-all"
                                >
                                  {link.referralUrl}
                                </a>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(link.createdAt).toLocaleDateString()}</td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                              Nenhum link encontrado.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                )}

                <div className="mt-5 sm:mt-6 flex justify-end">
                  <button
                    type="button"
                    onClick={() => setIsLinksModalOpen(false)}
                    className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm"
                  >
                    Fechar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Métricas */}
      {isMetricsModalOpen && selectedAfiliate && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full sm:w-1/2">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Métricas - {selectedAfiliate.name}</h3>
                  <button onClick={() => setIsMetricsModalOpen(false)} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-blue-800">Cliques</p>
                    <p className="mt-2 text-3xl font-bold text-blue-600">{selectedAfiliate.clicks || 0}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-green-800">Registros</p>
                    <p className="mt-2 text-3xl font-bold text-green-600">{selectedAfiliate.registrations || 0}</p>
                  </div>
                  <div className="bg-indigo-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-indigo-800">Assinaturas</p>
                    <p className="mt-2 text-3xl font-bold text-indigo-600">{selectedAfiliate.subscriptions || 0}</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-yellow-800">Comissões (Qtde)</p>
                    <p className="mt-2 text-3xl font-bold text-yellow-600">{selectedAfiliate.payments || 0}</p>
                  </div>
                  <div className="bg-pink-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-pink-800">Receita Total</p>
                    <p className="mt-2 text-3xl font-bold text-pink-600">
                      R$ {(selectedAfiliate.revenue || 0).toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                </div>

                <div className="mt-5 sm:mt-6 flex justify-end">
                  <button
                    type="button"
                    onClick={() => setIsMetricsModalOpen(false)}
                    className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm"
                  >
                    Fechar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de edição de status de pagamento */}
      {isEditPayoutModalOpen && editingPayout && (
        <div className="fixed z-20 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full sm:w-[400px]">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Alterar Status do Pagamento</h3>
                  <button onClick={closeEditPayoutModal} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                    value={editStatus}
                    onChange={(e) => setEditStatus(e.target.value as TPayoutStatus)}
                  >
                    <option value="pending">Pendente</option>
                    <option value="approved">Aprovado</option>
                    <option value="paid">Pago</option>
                    <option value="rejected">Rejeitado</option>
                    <option value="failed">Falhou</option>
                    <option value="canceled">Cancelado</option>
                    <option value="refunded">Reembolsado</option>
                  </select>
                </div>
                <FileUpload label="Comprovante de Pagamento" onFileUploaded={setEditReceiptUrl} currentFileUrl={editReceiptUrl} />
                <div className="mt-5 sm:mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={closeEditPayoutModal}
                    className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm"
                    disabled={isUpdating}
                  >
                    Cancelar
                  </button>
                  <button
                    type="button"
                    onClick={handleEditPayoutStatus}
                    className="inline-flex justify-center rounded-md border border-primary shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm"
                    disabled={isUpdating}
                  >
                    {isUpdating ? "Atualizando..." : "Atualizar Status"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
