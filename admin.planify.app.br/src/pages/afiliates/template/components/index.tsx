import { Eye, CheckCircle, XCircle, BarChart2, <PERSON>Sign, Link } from "lucide-react";
import { TAfiliate } from "../../domain/models";

type AfiliateBadgeProps = {
  status: "pending" | "approved" | "rejected";
};

export function AfiliateBadge({ status }: AfiliateBadgeProps) {
  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        status === "approved" ? "bg-green-100 text-green-800" : status === "rejected" ? "bg-red-100 text-red-800" : "bg-yellow-100 text-yellow-800"
      }`}
    >
      {status === "approved" ? "Aprovado" : status === "rejected" ? "Rejeitado" : "Pendente"}
    </span>
  );
}

type MetricsCardProps = {
  title: string;
  value: number;
};

export function MetricsCard({ title, value }: MetricsCardProps) {
  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
      <h4 className="text-sm font-medium text-gray-500">{title}</h4>
      <p className="mt-2 text-3xl font-semibold text-gray-900">{value}</p>
    </div>
  );
}

type ActionButtonProps = {
  icon: React.ReactNode;
  onClick: () => void;
  title: string;
  color: string;
};

export function ActionButton({ icon, onClick, title, color }: ActionButtonProps) {
  return (
    <button onClick={onClick} className={`text-${color}-600 hover:text-${color}-900`} title={title}>
      {icon}
    </button>
  );
}
