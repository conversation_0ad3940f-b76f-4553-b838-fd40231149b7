import TemplatePage from "./template";
import useDashboard from "./hooks";
import { useEffect } from "react";

export default function Dashboard() {
  const hookParams = useDashboard();

  const { subscriptions, getPaymentPlans, getSubscriptions, getScopes, getTickets, getPermissions } = hookParams;

  const sharedProps = {
    ...hookParams,
  };

  useEffect(() => {
    if (!subscriptions) {
      Promise.all([getPaymentPlans(), getSubscriptions(), getScopes(), getTickets(), getPermissions(), getTickets()]);
    }
  }, [subscriptions]);
  return (
    <>
      <TemplatePage {...sharedProps} />
    </>
  );
}
