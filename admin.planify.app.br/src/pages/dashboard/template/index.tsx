import { CreditCard, TrendingUp, Users } from "lucide-react";

export default function TemplatePage({ ...sharedProps }) {
  const { plans, subscriptions, monthlyRevenue } = sharedProps;

  const stats = [
    {
      name: "Total de Planos Cadastrados",
      value: plans.length ?? 0,
      icon: CreditCard,
    },
    {
      name: "Assinaturas Ativas",
      value: Array.isArray(subscriptions)
        ? subscriptions?.filter((sub) => sub?.status === "paid").length ?? 0
        : subscriptions?.data
        ? subscriptions.data?.filter((sub) => sub?.status === "paid").length ?? 0
        : 0,
      icon: Users,
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      value: new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
      }).format(monthlyRevenue / 100),
      icon: TrendingUp,
    },
  ];

  const getStatusStyle = (status) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "canceled":
        return "bg-red-100 text-red-800";
      case "expired":
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "paid":
        return "Pago";
      case "canceled":
        return "Cancelado";
      case "expired":
        return "Expirado";
      case "pending":
        return "Pendente";
      default:
        return status;
    }
  };

  return (
    <div>
      <div className="page-header">
        <h1 className="text-2xl font-semibold text-text-primary">Painel de Controle</h1>
      </div>

      <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {stats.map((item) => (
          <div key={item.name} className="stat-card animate-slide-up">
            <div className="icon-container">
              <item.icon className="h-5 w-5" aria-hidden="true" />
            </div>
            <p className="stat-title">{item.name}</p>
            <p className="stat-value">{item.value}</p>
          </div>
        ))}
      </div>

      <div className="mt-10">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-text-primary">Assinaturas Recentes</h2>
        </div>
        <div className="card-modern animate-fade-in">
          <table className="min-w-full divide-y divide-gray-200 table-modern">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Usuário</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Plano</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Data Início</th>
              </tr>
            </thead>
            <tbody>
              {(Array.isArray(subscriptions) ? subscriptions : subscriptions?.data || [])
                ?.sort((a, b) => new Date(b?.transactionDate).getTime() - new Date(a?.transactionDate).getTime())
                ?.slice(0, 3)
                ?.map((item) => (
                  <tr key={item?.id} className="hover:bg-background-light transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-text-primary">{item?.user?.name || "Usuário não encontrado"}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-text-primary">{item?.paymentPlan?.name ?? "VIEW"}</div>
                      <div className="text-sm text-text-secondary">
                        {new Intl.NumberFormat("pt-BR", {
                          style: "currency",
                          currency: "BRL",
                        }).format(Number(item.paymentPlan?.promotionalPrice || item.paymentPlan?.price || item.amount))}
                        /{item.paymentPlan?.recurrencyType === "month" ? "mês" : "ano"}
                      </div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`status-badge ${item?.status === "paid" ? "success" : item?.status === "canceled" ? "danger" : "warning"}`}>
                        {getStatusText(item?.status)}
                      </span>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                      {new Date(item?.transactionDate).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
