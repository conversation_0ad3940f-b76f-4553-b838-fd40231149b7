import { useNavigate } from "react-router-dom";
import { useGlobalStore } from "@/contexts/globalContext";

export default function useDashboard() {
  const { plans, getPaymentPlans, subscriptions, getSubscriptions, getScopes, getTickets, getPermissions } = useGlobalStore();

  const navigate = useNavigate();

  const calculateMonthlyRevenue = () => {
    // Verifica se subscriptions existe e se é um array
    if (Array.isArray(subscriptions)) {
      return subscriptions.reduce((total, subscription) => {
        if (subscription.status === "paid") {
          return total + Number(subscription.amount);
        }
        return total;
      }, 0);
    } 
    // Verifica se subscriptions é um objeto com propriedade data que é um array
    else if (subscriptions?.data && Array.isArray(subscriptions.data)) {
      return subscriptions.data.reduce((total, subscription) => {
        if (subscription.status === "paid") {
          return total + Number(subscription.amount);
        }
        return total;
      }, 0);
    }
    // Caso não seja possível calcular, retorna 0
    return 0;
  };

  return {
    navigate,
    plans,
    subscriptions,
    monthlyRevenue: calculateMonthlyRevenue(),
    getPaymentPlans,
    getSubscriptions,
    getScopes,
    getTickets,
    getPermissions,
  };
}
