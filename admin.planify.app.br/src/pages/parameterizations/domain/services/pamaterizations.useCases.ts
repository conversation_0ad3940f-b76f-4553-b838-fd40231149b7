import HttpClient from "@infra/httpRequest";

const service = new HttpClient();

export default class ParameterizationsServices {
  async get() {
    const response = await service.get(`/Parameterizations`);

    return response?.data;
  }

  async getById(id: string) {
    const response = await service.get(`/Parameterizations/${id}`);

    return response?.data;
  }

  async create(data: any) {
    const response = await service.post(`/Parameterizations`, data);

    return response?.data;
  }

  async update(id: string, data: any) {
    const response = await service.put(`/Parameterizations/${id}`, data);
    return response?.data;
  }

  async delete(id: string) {
    const response = await service.delete(`/Parameterizations/${id}`);
    return response?.data;
  }
}
