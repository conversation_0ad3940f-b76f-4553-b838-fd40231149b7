import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { ParameterizationsServices } from "../domain";

export default function useParameterizations() {
  const service = new ParameterizationsServices();
  const [pageData, setPageData] = useState();

  const navigate = useNavigate();
  const { control, handleSubmit, watch, setValue } = useForm();
  const watchFields = watch();

  const [isLoading, setIsLoading] = useState(false);

  async function getParameterizations() {
    const response = await service.get();
    setPageData(response);
  }

  async function createPamaterization(data: any) {
    setIsLoading(true);
    try {
      await service.create(data);
      await getParameterizations();
    } finally {
      setIsLoading(false);
    }
  }

  async function updatePamaterization(id: string, data: any) {
    setIsLoading(true);
    try {
      await service.update(id, data);
      await getParameterizations();
    } finally {
      setIsLoading(false);
    }
  }

  async function deletePamaterization(id: string) {
    setIsLoading(true);
    try {
      await service.delete(id);
      await getParameterizations();
    } finally {
      setIsLoading(false);
    }
  }

  return {
    isLoading,
    setIsLoading,
    control,
    handleSubmit,
    watch,
    setValue,
    watchFields,
    navigate,
    getParameterizations,
    pageData,
    createPamaterization,
    updatePamaterization,
    deletePamaterization,
  };
}
