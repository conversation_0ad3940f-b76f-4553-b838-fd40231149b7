import { useState } from "react";
import { Table } from "@components/Table";

export default function TemplatePage({ pageData, createPamaterization, updatePamaterization, deletePamaterization, isLoading }) {
  const [editing, setEditing] = useState(null);
  const [formOpen, setFormOpen] = useState(false);
  const [formData, setFormData] = useState({ name: "", age: "" });

  function handleEdit(row) {
    setEditing(row);
    setFormData({ name: row.name, age: row.age });
    setFormOpen(true);
  }

  function handleDelete(row) {
    if (window.confirm("Deseja realmente excluir?")) {
      deletePamaterization(row.id);
    }
  }

  function handleFormSubmit(e) {
    e.preventDefault();
    if (editing) {
      updatePamaterization(editing.id, formData);
    } else {
      createPamaterization(formData);
    }
    setFormOpen(false);
    setEditing(null);
    setFormData({ name: "", age: "" });
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-xl font-bold">Parametrizações</h1>
        <Button
          onClick={() => {
            setFormOpen(true);
            setEditing(null);
            setFormData({ name: "", age: "" });
          }}
        >
          Nova Parametrização
        </Button>
      </div>
      <Table
        data={pageData || []}
        columns={[
          { id: "name", label: "Nome" },
          { id: "age", label: "Idade" },
          {
            id: "action",
            label: "Ações",
            render: (row) => (
              <div className="flex gap-2">
                <Button size="sm" onClick={() => handleEdit(row)}>
                  Editar
                </Button>
                <Button size="sm" variant="danger" onClick={() => handleDelete(row)}>
                  Excluir
                </Button>
              </div>
            ),
          },
        ]}
        loading={isLoading}
      />
      {formOpen && (
        <form className="mt-6 bg-white p-4 rounded shadow" onSubmit={handleFormSubmit}>
          <div className="mb-2">
            <label className="block mb-1">Nome</label>
            <input
              className="border p-2 w-full"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>
          <div className="mb-2">
            <label className="block mb-1">Idade</label>
            <input
              className="border p-2 w-full"
              type="number"
              value={formData.age}
              onChange={(e) => setFormData({ ...formData, age: e.target.value })}
              required
            />
          </div>
          <div className="flex gap-2 mt-4">
            <Button type="submit">{editing ? "Salvar" : "Criar"}</Button>
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                setFormOpen(false);
                setEditing(null);
              }}
            >
              Cancelar
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}
