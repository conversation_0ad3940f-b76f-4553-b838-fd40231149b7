import { useEffect } from "react";
import TemplatePage from "./template";
import useParameterizations from "./hooks";

export default function Parameterizations() {
  const hookParams = useParameterizations();
  const { getParameterizations } = hookParams;

  const sharedProps = {
    ...hookParams,
  };

  // useEffect(() => {
  //   getParameterizations();
  // }, []);

  return (
    <>
      <TemplatePage {...sharedProps} />
    </>
  );
}
