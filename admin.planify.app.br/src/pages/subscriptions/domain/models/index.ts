export interface TSubscription {
  id: string;
  customerId: string | null;
  customerDocument: string | null;
  cardId: string | null;
  gatewayPlanId: string | null;
  subscriptionId: string | null;
  userId: string;
  user: {
    id: string;
    name: string;
  };
  paymentPlan: {
    id: string;
    name: string;
    price: string;
    promotionalPrice: string | null;
    typePlan: string;
    recurrencyType: string;
    recurrencyPeriod: number;
  };
  hookType: string;
  amount: string;
  status: "paid" | "pending" | "failed" | "canceled";
  cycleReference: string | null;
  transactionDate: string;
  expirationAt: string;
  log: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface ISubscriptionCancelResponse {
  success: boolean;
  data: {
    success: boolean;
    message: string;
  };
}
