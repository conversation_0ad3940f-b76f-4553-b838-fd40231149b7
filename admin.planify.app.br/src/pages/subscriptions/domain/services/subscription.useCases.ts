import HttpClient from "@/infra/httpRequest";
import { ISubscriptionCancelResponse, TSubscription } from "../models";
import { envs } from "@/shared/functions/envsProxy";
import { TPaginatedData } from "@/shared/types/paginatedData";

const httpClient = new HttpClient();

const GATEWAY_SECRET_KEY = envs.GATEWAY_SECRET_KEY;

const GATWAY_BASE_URL = envs.VITE_API_GATEWAY_URL;
const httpGatewayCliend = new HttpClient(GATWAY_BASE_URL, GATEWAY_SECRET_KEY);

export class SubscriptionService {
  async getSubscriptions(): Promise<TSubscription[]> {
    const response = await httpClient.get("/payments");
    return response?.data;
  }

  async getSubscriptionsPaginated(page: number = 1, pageSize: number = 10): Promise<TPaginatedData> {
    const response = await httpClient.get(`/payments?page=${page}&pageSize=${pageSize}`);
    return response?.data;
  }

  async getFilteredSubscriptions(status?: string, page: number = 1, pageSize: number = 10): Promise<TPaginatedData> {
    if (!status || status === "all") {
      return this.getSubscriptionsPaginated(page, pageSize);
    }

    const response = await httpClient.get(`/payments?status=${status}&page=${page}&pageSize=${pageSize}`);
    return response?.data;
  }

  async cancellSubscription(subscriptionId: string): Promise<ISubscriptionCancelResponse> {
    console.log("GATEWAY_SECRET_KEY", GATEWAY_SECRET_KEY);

    const response = await httpGatewayCliend.delete(`/api/recurrence/subscriptions/${subscriptionId}`);
    return response?.data;
  }
}
