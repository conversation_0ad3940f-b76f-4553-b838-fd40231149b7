import { Eye, Trash2 } from "lucide-react";
import { TSubscription } from "../domain/models";
import { format } from "date-fns";

export function TableColumns({ onCancelSubscription, onViewDetails }) {
  const statusMap = {
    paid: "Pago",
    pending: "Pendente",
    failed: "Fal<PERSON>",
    canceled: "Cancelado",
  };

  const formatCurrency = (value: string) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(Number(value));
  };

  const getRecurrencyText = (type: string, period: number) => {
    if (type === "month") {
      return period === 1 ? "Mensal" : `${period} meses`;
    }
    return period === 1 ? "Anual" : `${period} anos`;
  };

  return [
    {
      id: "name",
      label: "Nome do Usuário",
      render: (rowData: TSubscription) => <p>{rowData?.user?.name ?? "-"}</p>,
    },
    {
      id: "plan",
      label: "Plano",
      render: (rowData: TSubscription) => (
        <div className="flex flex-col">
          <p className="font-medium">{rowData?.paymentPlan?.name ?? "VIEW"}</p>
          <p className="text-xs text-gray-500">
            {!rowData?.paymentPlan?.name ? "Anual" : getRecurrencyText(rowData?.paymentPlan?.recurrencyType, rowData?.paymentPlan?.recurrencyPeriod)}
          </p>
        </div>
      ),
    },
    {
      id: "amount",
      label: "Valor Pago",
      align: "center" as const,
      render: (rowData: TSubscription) => <p>{rowData?.paymentPlan?.price ? formatCurrency(rowData?.amount / 100) : "-"}</p>,
    },
    {
      id: "status",
      label: "Status",
      align: "center" as const,
      render: (rowData: TSubscription) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            rowData?.status === "paid"
              ? "bg-green-100 text-green-800"
              : rowData?.status === "pending"
              ? "bg-yellow-100 text-yellow-800"
              : rowData?.status === "failed" || rowData?.status === "canceled"
              ? "bg-red-100 text-red-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {statusMap[rowData?.status]}
        </span>
      ),
    },
    {
      id: "transactionDate",
      label: "Data da Assinatura",
      align: "center" as const,
      render: (rowData: TSubscription) => <p>{format(new Date(rowData?.transactionDate), "dd/MM/yyyy HH:mm")}</p>,
    },
    {
      id: "transactionDate",
      label: "Data de Expiração",
      align: "center" as const,
      render: (rowData: TSubscription) => <p>{format(new Date(rowData?.expirationAt), "dd/MM/yyyy HH:mm")}</p>,
    },
    {
      id: "actions",
      label: "AÇÕES",
      align: "left",
      render: (rowData: TSubscription) => (
        <div className="flex items-center space-x-2">
          <button onClick={() => onViewDetails(rowData)} className="p-1 text-gray-600 hover:text-gray-800 transition-colors" title="Ver Detalhes">
            <Eye size={18} />
          </button>
          {rowData?.paymentPlan?.typePlan !== "free" && rowData?.paymentPlan?.typePlan !== "start" && rowData?.status === "paid" && (
            <button
              onClick={() => onCancelSubscription(rowData?.subscriptionId || rowData.id)}
              className="p-1 text-red-600 hover:text-red-800 transition-colors"
              title="Cancelar Assinatura"
            >
              <Trash2 size={18} />
            </button>
          )}
        </div>
      ),
    },
  ];
}
