import { Table } from "@/components/Table";
import { TableColumns } from "../mocks/TableColumns";
import { useState } from "react";
import { TSubscription } from "../domain/models";
import { SubscriptionDetailsModal } from "../components/SubscriptionDetailsModal";
import { CancelSubscriptionModal } from "../components/CancelSubscriptionModal";

type StatusType = "all" | "paid" | "failed" | "pending" | "canceled" | "expired";

export function Template({ ...sharedProps }) {
  const { isLoadingData, onCancelSubscription, handleStatusChange, subscriptionsPaginated, getPaginatedSubscriptions } = sharedProps;
  const [selectedSubscription, setSelectedSubscription] = useState<TSubscription | null>(null);
  const [activeStatus, setActiveStatus] = useState<StatusType>("all");
  const [subscriptionToCancel, setSubscriptionToCancel] = useState<string | null>(null);

  const handleViewDetails = (subscription: TSubscription) => {
    setSelectedSubscription(subscription);
  };
  const statusTabs: { label: string; value: StatusType }[] = [
    { label: "Todos", value: "all" },
    { label: "Pagos", value: "paid" },
    { label: "Falha", value: "failed" },
    { label: "Pendente", value: "pending" },
    { label: "Cancelado", value: "canceled" },
    { label: "Expirado", value: "expired" },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Assinaturas</h1>
      </div>

      <div className="mb-6">
        <nav className="flex" aria-label="Tabs">
          <div className="flex space-x-2">
            {statusTabs.map((tab) => (
              <button
                key={tab.value}
                onClick={() => {
                  handleStatusChange(tab.value === "all" ? null : tab.value);
                  setActiveStatus(tab.value);
                }}
                className={`px-6 py-3 text-sm font-medium rounded-lg ${
                  activeStatus === tab.value 
                    ? "bg-red-50 text-red-600 border border-red-200" 
                    : "bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100 border border-gray-200"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </nav>
      </div>

      <Table
        showPagination
        columns={TableColumns({
          onCancelSubscription,
          onViewDetails: handleViewDetails,
        })}
        data={subscriptionsPaginated.data}
        defaultRows={subscriptionsPaginated?.pageSize || 10}
        totalItems={subscriptionsPaginated?.totalRecords || 0}
        changeRowsPerPage={(pageSize) => getPaginatedSubscriptions("pageSize", pageSize)}
        onChangePage={(page) => getPaginatedSubscriptions("page", page)}
        loading={isLoadingData}
        currentPage={subscriptionsPaginated?.currentPage}
      />

      <SubscriptionDetailsModal
        isOpen={!!selectedSubscription}
        subscription={selectedSubscription}
        onClose={() => setSelectedSubscription(null)}
        onCancelSubscription={(subscriptionId) => {
          setSubscriptionToCancel(subscriptionId);
        }}
      />

      <CancelSubscriptionModal
        isOpen={!!subscriptionToCancel}
        subscriptionId={subscriptionToCancel || ""}
        onClose={() => setSubscriptionToCancel(null)}
        onConfirm={() => {
          if (subscriptionToCancel) {
            onCancelSubscription(subscriptionToCancel);
            setSubscriptionToCancel(null);
            setSelectedSubscription(null);
          }
        }}
      />
    </div>
  );
}
