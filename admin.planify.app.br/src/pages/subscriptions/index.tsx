import { Template } from "./template";
import { useSubscriptions } from "./hooks/useSubscriptions";
import { useEffect } from "react";

export default function Subscriptions() {
  const hookParams = useSubscriptions();
  const { getSubscriptionsPaginated } = hookParams;
  const sharedProps = {
    ...hookParams,
  };

  useEffect(() => {
    getSubscriptionsPaginated(1, 10);
  }, []);

  return <Template {...sharedProps} />;
}
