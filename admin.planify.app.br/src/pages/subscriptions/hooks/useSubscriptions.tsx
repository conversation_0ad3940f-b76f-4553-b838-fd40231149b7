import { useGlobalStore } from "@/contexts/globalContext";
import { SubscriptionService } from "../domain/services/subscription.useCases";
import { toast } from "react-toastify";
import { useState, useCallback, useEffect } from "react";

export function useSubscriptions() {
  const subscriptionService = new SubscriptionService();
  const {
    loading: globalLoading,
    subscriptionsPaginated,
    getSubscriptionsPaginated,
    getFilteredSubscriptionsPaginated,
    getPaymentPlans,
  } = useGlobalStore();
  const [selectedStatus, setSelectedStatus] = useState<React.SetStateAction<string | null | undefined>>(null);

  async function onCancelSubscription(subscriptionId: string) {
    if (window.confirm("Tem certeza que deseja cancelar esta assinatura?")) {
      try {
        const response = await subscriptionService.cancellSubscription(subscriptionId);
        if (!response?.success) {
          return toast.error("Erro ao cancelar assinatura");
        }
        await getSubscriptionsPaginated(1, subscriptionsPaginated.pageSize);
        return toast.success("Cancelamento realizado com sucesso!");
      } catch (error) {
        console.error("Error cancelling subscription:", error);
        toast.error("Erro ao cancelar assinatura");
      }
    }
  }

  const handleStatusChange = async (status: string) => {
    setSelectedStatus(status as string);
    try {
      await getFilteredSubscriptionsPaginated(status, 1, subscriptionsPaginated.pageSize);
    } catch (error) {
      console.error("Error fetching filtered subscriptions:", error);
      toast.error("Erro ao buscar assinaturas");
    }
  };

  // Função removida pois agora usamos getFilteredSubscriptionsPaginated do globalContext

  const getPaginatedSubscriptions = useCallback(
    (field: string, value: number) => {
      if (selectedStatus && selectedStatus !== "all") {
        // Se tiver um filtro ativo, usa a função de filtro com paginação
        if (field === "page") {
          getFilteredSubscriptionsPaginated(selectedStatus as string, value, subscriptionsPaginated.pageSize);
        } else if (field === "pageSize") {
          getFilteredSubscriptionsPaginated(selectedStatus as string, subscriptionsPaginated.currentPage, value);
        }
      } else {
        // Se não tiver filtro, usa a paginação normal
        if (field === "page") {
          getSubscriptionsPaginated(value, subscriptionsPaginated.pageSize);
        } else if (field === "pageSize") {
          getSubscriptionsPaginated(subscriptionsPaginated.currentPage, value);
        }
      }
    },
    [
      getSubscriptionsPaginated,
      getFilteredSubscriptionsPaginated,
      selectedStatus,
      subscriptionsPaginated.currentPage,
      subscriptionsPaginated.pageSize,
    ]
  );

  return {
    subscriptionsPaginated,
    isLoadingData: globalLoading,
    selectedStatus,
    handleStatusChange,
    getPaymentPlans,
    onCancelSubscription,
    getPaginatedSubscriptions,
    getSubscriptionsPaginated,
  };
}
