import { X } from "lucide-react";
import { formatCurrency } from "@/shared/functions/formatCurrency";

interface SubscriptionDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  subscription: any;
  onCancelSubscription: (subscriptionId: string) => void;
}

export function SubscriptionDetailsModal({ isOpen, onClose, subscription }: SubscriptionDetailsModalProps) {
  if (!subscription) return null;

  if (!isOpen) return null;

  return (
    <div className="fixed z-50 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true" onClick={onClose}>
          <div className="absolute inset-0 bg-black bg-opacity-75"></div>
        </div>

        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Detalhes da Assinatura</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="mt-6 space-y-6">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-500">Informações do Cliente</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">ID do usuário</p>
                  <p className="text-sm font-medium">{subscription.user?.id}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">Nome</p>
                  <p className="text-sm font-medium">{subscription.user?.name}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">CPF</p>
                  <p className="text-sm font-medium">{subscription.customerDocument ?? "-"}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">ID do Cliente</p>
                  <p className="text-sm font-medium">{subscription.customerId ?? "-"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">ID do Cartão</p>
                  <p className="text-sm font-medium">{subscription.cardId ?? "-"}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-500">Informações do Plano</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Plano</p>
                  <p className="text-sm font-medium">{subscription.paymentPlan?.name ?? "-"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Tipo do Plano</p>
                  <p className="text-sm font-medium">{subscription.paymentPlan?.typePlan ?? "-"}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">Valor Pago</p>
                  <p className="text-sm font-medium">{formatCurrency((Number(subscription.amount) / 100).toString())}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      subscription.status === "paid" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    }`}
                  >
                    {subscription.status === "paid" ? "Pago" : "Cancelado"}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Descrição</p>
                  <p className="text-sm font-medium">{subscription.paymentPlan?.description ?? "-"}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-500">Informações da Transação</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">ID da Transação</p>
                  <p className="text-sm font-medium">{subscription.transactionId ?? "-"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">ID da Assinatura</p>
                  <p className="text-sm font-medium">{subscription.subscriptionId ?? "-"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">ID do Plano (Gateway)</p>
                  <p className="text-sm font-medium">{subscription.gatewayPlanId ?? "-"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Data da Transação</p>
                  <p className="text-sm font-medium">{new Date(subscription.transactionDate).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Parcelas</p>
                  <p className="text-sm font-medium">{subscription.installments}x</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Expira em</p>
                  <p className="text-sm font-medium">{new Date(subscription.expirationAt).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Log</p>
                  <p className="text-sm font-medium">{subscription.log ?? "-"}</p>
                </div>
                {subscription?.reason && (
                  <div>
                    <p className="text-sm text-gray-500">Cancelamento</p>
                    <p className="text-sm font-medium">{subscription?.reason ?? "-"}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm text-gray-500">Tipo do Hook</p>
                  <p className="text-sm font-medium">{subscription.hookType ?? "-"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Criado em</p>
                  <p className="text-sm font-medium">{new Date(subscription.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Atualizado em</p>
                  <p className="text-sm font-medium">{new Date(subscription.updatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
