import { useEffect } from "react";
import TemplatePage from "./template";
import usePlannerPrompts from "./hooks";

export default function PlannerPrompts() {
  const hookParams = usePlannerPrompts();
  const { getPlannerPrompts, getCategories } = hookParams;

  const sharedProps = {
    ...hookParams,
  };

  useEffect(() => {
    getPlannerPrompts();
    getCategories();
  }, []);

  return (
    <>
      <TemplatePage {...sharedProps} />
    </>
  );
}
