import { useEffect } from "react";
import { Plus, X, Search } from "lucide-react";
import FileUpload from "../../../components/FileUpload";
import { TableColumns } from "../mocks/TableColumns";

export default function TemplatePage({ ...sharedProps }) {
  const {
    isLoading,
    formData,
    setFormData,
    pageData,
    categories,
    selectedPrompt,
    isModalOpen,
    setIsModalOpen,
    isViewMode,
    getPlannerPrompts,
    getCategories,
    createPrompt,
    updatePrompt,
    deletePrompt,
    handleEdit,
    handleView,
    handleCreate,
    searchTerm,
    setSearchTerm,
    handleClearSearch,
  } = sharedProps;

  useEffect(() => {
    getPlannerPrompts();
    getCategories();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedPrompt) {
        await updatePrompt(selectedPrompt.id, formData);
      } else {
        await createPrompt(formData);
      }
    } catch (error) {
      console.error("Erro ao salvar o prompt:", error);
    }
  };

  const handleDelete = (id: string) => {
    if (window.confirm("Tem certeza que deseja excluir este prompt?")) {
      deletePrompt(id);
    }
  };

  const columns = TableColumns({ handleEdit, handleDelete, handleView });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="page-header">
        <h1 className="text-2xl font-semibold text-text-primary">Prompts</h1>
        <button
          onClick={handleCreate}
          className="btn-modern primary inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium transition-all duration-200"
        >
          <Plus className="h-4 w-4 mr-2" />
          Novo Prompt
        </button>
      </div>

      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-text-secondary" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Buscar por nome, descrição ou categoria..."
            className="input-modern block w-full pl-10 pr-10 py-2.5 leading-5 bg-white placeholder-text-secondary focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm rounded-lg border border-gray-300 shadow-sm"
          />
          {searchTerm && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button
                onClick={handleClearSearch}
                className="text-text-secondary hover:text-text-primary focus:outline-none transition-colors duration-200"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="mt-6 card-modern animate-fade-in p-0 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200 table-modern">
          <thead>
            <tr>
              {columns.map((column) => (
                <th
                  key={column.id}
                  className={`px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider ${
                    column.align ? `text-${column.align}` : ""
                  }`}
                  style={{ padding: column.padding }}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {pageData.map((prompt: any) => (
              <tr key={prompt.id} className="hover:bg-background-light transition-colors duration-150">
                {columns.map((column) => (
                  <td
                    key={column?.id}
                    className={`px-6 py-4 whitespace-nowrap text-sm text-text-primary ${column.align ? `text-${column.align}` : ""}`}
                    style={{ padding: column?.padding }}
                  >
                    {column.render(prompt)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {isModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom modal-modern bg-white rounded-card text-left overflow-hidden shadow-modal transform transition-all sm:my-8 sm:align-middle w-full sm:w-1/2 animate-slide-up">
              <div className="modal-header bg-white border-b border-gray-200">
                <h3 className="text-lg font-medium text-text-primary">
                  {isViewMode ? "Visualizar Prompt" : selectedPrompt ? "Editar Prompt" : "Novo Prompt"}
                </h3>
                <button onClick={() => setIsModalOpen(false)} className="text-text-secondary hover:text-text-primary transition-colors duration-200">
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="modal-body">
                <form onSubmit={handleSubmit} className="space-y-5">
                  <div className="form-group">
                    <label className="block text-sm font-medium text-text-secondary">Nome</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => !isViewMode && setFormData({ ...formData, name: e.target.value })}
                      disabled={isViewMode}
                      className="input-modern mt-1 block w-full py-2.5 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary rounded-lg border border-gray-300"
                      placeholder="Digite o nome do prompt"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label className="block text-sm font-medium text-text-secondary">Status</label>
                    <select
                      value={formData.isActive ? "true" : "false"}
                      onChange={(e) => !isViewMode && setFormData({ ...formData, isActive: e.target.value === "true" })}
                      disabled={isViewMode}
                      className="input-modern mt-1 block w-full py-2.5 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary rounded-lg border border-gray-300"
                      required
                    >
                      <option value="true">Ativo</option>
                      <option value="false">Inativo</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label className="block text-sm font-medium text-text-secondary">Tipo de Retorno</label>
                    <select
                      value={formData.returnType}
                      onChange={(e) => !isViewMode && setFormData({ ...formData, returnType: e.target.value })}
                      disabled={isViewMode}
                      className="input-modern mt-1 block w-full py-2.5 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary rounded-lg border border-gray-300"
                      required
                    >
                      <option value="JSON">JSON</option>
                      <option value="TEXT">Texto</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label className="block text-sm font-medium text-text-secondary">Categoria</label>
                    <select
                      value={formData.categoryId}
                      onChange={(e) => !isViewMode && setFormData({ ...formData, categoryId: e.target.value })}
                      disabled={isViewMode}
                      className="input-modern mt-1 block w-full py-2.5 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary rounded-lg border border-gray-300"
                      required
                    >
                      <option value="">Selecione a categoria</option>
                      {categories.map((cat: any) => (
                        <option key={cat.id} value={cat.id}>
                          {cat.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label className="block text-sm font-medium text-text-secondary">Descrição</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => !isViewMode && setFormData({ ...formData, description: e.target.value })}
                      disabled={isViewMode}
                      rows={4}
                      className="input-modern mt-1 block w-full py-2.5 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary rounded-lg border border-gray-300"
                      placeholder="Digite a descrição do prompt"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="block text-sm font-medium text-text-secondary">Persona</label>
                    <textarea
                      value={formData.persona}
                      onChange={(e) => !isViewMode && setFormData({ ...formData, persona: e.target.value })}
                      disabled={isViewMode}
                      rows={4}
                      className="input-modern mt-1 block w-full py-2.5 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary rounded-lg border border-gray-300"
                      placeholder="Digite a persona do prompt"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="block text-sm font-medium text-text-secondary">Ação Customizada</label>
                    <textarea
                      value={formData.customAction}
                      onChange={(e) => !isViewMode && setFormData({ ...formData, customAction: e.target.value })}
                      disabled={isViewMode}
                      rows={4}
                      className="input-modern mt-1 block w-full py-2.5 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary rounded-lg border border-gray-300"
                      placeholder="Digite a ação customizada (opcional)"
                    />
                  </div>

                  <div className="form-group">
                    <FileUpload
                      onFileUploaded={(fileUrl) => !isViewMode && setFormData({ ...formData, iconUrl: fileUrl })}
                      label="Ícone do Prompt"
                      currentFileUrl={formData.iconUrl}
                      disabled={isViewMode}
                    />
                  </div>

                  <div className="modal-footer bg-gray-50 border-t border-gray-200">
                    {!isViewMode && (
                      <div className="flex justify-end gap-3 w-full">
                        <button
                          type="button"
                          onClick={() => setIsModalOpen(false)}
                          className="btn-modern secondary px-4 py-2 text-sm font-medium transition-all duration-200"
                        >
                          Cancelar
                        </button>
                        <button type="submit" className="btn-modern primary px-4 py-2 text-sm font-medium transition-all duration-200">
                          {selectedPrompt ? "Atualizar" : "Criar"}
                        </button>
                      </div>
                    )}
                    {isViewMode && (
                      <div className="flex justify-end w-full">
                        <button
                          type="button"
                          onClick={() => setIsModalOpen(false)}
                          className="btn-modern secondary px-4 py-2 text-sm font-medium transition-all duration-200"
                        >
                          Fechar
                        </button>
                      </div>
                    )}
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
