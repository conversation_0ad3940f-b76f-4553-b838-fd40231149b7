import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { PlannerPromptsServices } from "../domain";
import { Search, X } from "lucide-react";
import { toast } from "react-toastify";

interface Prompt {
  id: string;
  name: string;
  description: string;
  persona: string;
  customAction: string;
  returnType: string;
  position: number;
  categoryId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  category: {
    id: string;
    name: string;
    position: number;
    description: string;
  };
}

interface Category {
  id: string;
  name: string;
  position: number;
  description: string;
  createdAt: string;
  updatedAt: string;
}

interface PromptForm {
  name: string;
  description: string;
  persona: string;
  customAction: string;
  returnType: string;
  categoryId: string;
  isActive: boolean;
  iconUrl?: string;
}

export default function usePlannerPrompts() {
  const service = new PlannerPromptsServices();
  const [pageData, setPageData] = useState<Prompt[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isViewMode, setIsViewMode] = useState(false);
  const [viewPrompt, setViewPrompt] = useState<Prompt | null>(null);
  const [formData, setFormData] = useState<PromptForm>({
    name: "",
    description: "",
    persona: "",
    customAction: "",
    returnType: "JSON",
    categoryId: "",
    isActive: true,
    iconUrl: "",
  });

  const navigate = useNavigate();

  const filteredPrompts = pageData.filter((prompt) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      prompt.name.toLowerCase().includes(searchLower) ||
      prompt.description.toLowerCase().includes(searchLower) ||
      prompt.category?.name.toLowerCase().includes(searchLower)
    );
  });

  async function getPlannerPrompts() {
    setIsLoading(true);
    try {
      const response = await service.get();
      setPageData(response);
    } catch (error) {
      console.error("Erro ao buscar prompts:", error);
    } finally {
      setIsLoading(false);
    }
  }

  async function getCategories() {
    try {
      const response = await service.getCategories();
      setCategories(response);
    } catch (error) {
      console.error("Erro ao buscar categorias:", error);
    }
  }

  async function createPrompt(data: PromptForm) {
    setIsLoading(true);
    try {
      const response = await service.create(data);
      await getPlannerPrompts();
      setIsModalOpen(false);

      if (response) {
        resetForm();
        toast.success("Prompt criado com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao criar prompt:", error);
      toast.error("Erro ao criar prompt");
    } finally {
      setIsLoading(false);
    }
  }

  async function updatePrompt(id: string, data: PromptForm) {
    setIsLoading(true);
    try {
      await service.update(id, data);
      await getPlannerPrompts();
      setIsModalOpen(false);
      setSelectedPrompt(null);
      resetForm();
      toast.success("Prompt atualizado com sucesso!");
    } catch (error) {
      console.error("Erro ao atualizar prompt:", error);
      toast.error("Erro ao atualizar prompt");
    } finally {
      setIsLoading(false);
    }
  }

  async function deletePrompt(id: string) {
    setIsLoading(true);
    try {
      await service.delete(id);
      await getPlannerPrompts();
      toast.success("Prompt removido com sucesso!");
    } catch (error) {
      console.error("Erro ao deletar prompt:", error);
      toast.error("Erro ao remover prompt");
    } finally {
      setIsLoading(false);
    }
  }

  function handleEdit(prompt: Prompt) {
    setSelectedPrompt(prompt);
    setFormData({
      name: prompt.name,
      description: prompt.description,
      persona: prompt.persona,
      categoryId: prompt.categoryId,
      returnType: prompt.returnType,
      customAction: prompt.customAction,
      isActive: prompt.isActive,
      iconUrl: prompt.iconUrl || "",
    });
    setIsViewMode(false);
    setIsModalOpen(true);
  }

  function handleView(prompt: Prompt) {
    setSelectedPrompt(prompt);
    setFormData({
      name: prompt.name,
      description: prompt.description,
      persona: prompt.persona,
      categoryId: prompt.categoryId,
      returnType: prompt.returnType,
      customAction: prompt.customAction,
      isActive: prompt.isActive,
      iconUrl: prompt.iconUrl || "",
    });
    setIsViewMode(true);
    setIsModalOpen(true);
  }

  function handleCreate() {
    setSelectedPrompt(null);
    setFormData({
      name: "",
      description: "Desenvolva um plano de metas pessoais para [TARGET] em [PERIOD], com [ACTIVITIES_SIZE] objetivos definidos por semana.",
      persona: "Atue como [TIPO DE PERSONA] pessoal especializado em [ESPECIALIZAÇÃO]",
      customAction: "",
      returnType: "JSON",
      categoryId: "",
      isActive: true,
      iconUrl: "",
    });
    setIsViewMode(false);
    setIsModalOpen(true);
  }

  function handleClearSearch() {
    setSearchTerm("");
  }

  function resetForm() {
    setFormData({
      name: "",
      description: "",
      persona: "",
      customAction: "",
      returnType: "JSON",
      categoryId: "",
      isActive: true,
      iconUrl: "",
    });
    setSelectedPrompt(null);
    setViewPrompt(null);
    setIsViewMode(false);
  }

  return {
    isLoading,
    formData,
    setFormData,
    pageData: filteredPrompts,
    categories,
    selectedPrompt,
    isModalOpen,
    setIsModalOpen,
    isViewMode,
    setIsViewMode,
    viewPrompt,
    setViewPrompt,
    getPlannerPrompts,
    getCategories,
    createPrompt,
    updatePrompt,
    deletePrompt,
    handleEdit,
    handleView,
    handleCreate,
    searchTerm,
    setSearchTerm,
    handleClearSearch,
    resetForm,
  };
}
