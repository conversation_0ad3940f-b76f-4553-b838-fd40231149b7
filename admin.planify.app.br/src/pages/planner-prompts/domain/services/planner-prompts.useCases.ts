import HttpClient from "@infra/httpRequest";

const service = new HttpClient();

interface Prompt {
  id: string;
  name: string;
  description: string;
  persona: string;
  customAction: string;
  returnType: string;
  position: number;
  categoryId: string;
  createdAt: string;
  updatedAt: string;
  isActive?: boolean;
  iconUrl?: string;
  category: {
    id: string;
    name: string;
    position: number;
    description: string;
  };
}

interface Category {
  id: string;
  name: string;
  position: number;
  description: string;
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse<T> {
  data: T;
}

interface CreatePromptData {
  name: string;
  description: string;
  persona: string;
  customAction: string;
  returnType: string;
  categoryId: string;
  isActive?: boolean;
  iconUrl?: string;
}

export default class PlannerPromptsServices {
  async get(): Promise<Prompt[]> {
    const response = (await service.get(`/prompts`)) as ApiResponse<Prompt[]>;
    return response?.data;
  }

  async getById(id: string): Promise<Prompt> {
    const response = (await service.get(`/prompts/${id}`)) as ApiResponse<Prompt>;
    return response?.data;
  }

  async create(data: CreatePromptData): Promise<Prompt> {
    const response = (await service.post(`/prompts`, data)) as ApiResponse<Prompt>;
    return response?.data;
  }

  async update(id: string, data: Partial<CreatePromptData>): Promise<Prompt> {
    const response = (await service.patch(`/prompts/${id}`, data)) as ApiResponse<Prompt>;
    return response?.data;
  }

  async delete(id: string): Promise<void> {
    await service.delete(`/prompts/${id}`);
  }

  async getCategories(): Promise<Category[]> {
    const response = (await service.get(`/categories`)) as ApiResponse<Category[]>;
    return response?.data;
  }
}
