import { Edit, Trash2, Eye } from "lucide-react";

interface Prompt {
  id: string;
  name: string;
  description: string;
  persona: string;
  customAction: string;
  returnType: string;
  position: number;
  categoryId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  category: {
    id: string;
    name: string;
    position: number;
    description: string;
  };
}

interface TableColumnsProps {
  handleEdit: (prompt: Prompt) => void;
  handleDelete: (id: string) => void;
  handleView?: (prompt: Prompt) => void;
}

export function TableColumns({ handleEdit, handleDelete, handleView }: TableColumnsProps) {
  return [
    {
      id: "name",
      label: "Nome",
      render: (rowData: Prompt) => <div className="text-sm text-gray-500">{rowData.name}</div>,
    },
    {
      id: "category",
      label: "Categoria",
      render: (rowData: Prompt) => <div className="text-sm text-gray-500">{rowData.category?.name}</div>,
    },
    {
      id: "description",
      label: "Descrição",
      render: (rowData: Prompt) => (
        <div className="text-sm text-gray-500 max-w-[300px] truncate" title={rowData.description}>
          {rowData.description}
        </div>
      ),
    },
    {
      id: "status",
      label: "Status",
      align: "center",
      render: (rowData: Prompt) => (
        <div className="flex justify-center">
          <span className={`status-badge ${rowData.isActive ? "active" : "inactive"}`}>{rowData.isActive ? "Ativo" : "Inativo"}</span>
        </div>
      ),
    },
    {
      align: "center",
      id: "actions",
      label: "Ações",
      render: (rowData: Prompt) => (
        <div className="flex gap-2 justify-center">
          <button onClick={() => (handleView ? handleView(rowData) : handleEdit(rowData))} className="text-gray-800 hover:text-black">
            <Eye size={20} />
          </button>
          <button onClick={() => handleEdit(rowData)} className="text-yellow-600 hover:text-yellow-700">
            <Edit size={20} />
          </button>
          <button onClick={() => handleDelete(rowData.id)} className="text-red-600 hover:text-red-700">
            <Trash2 size={20} />
          </button>
        </div>
      ),
    },
  ];
}
