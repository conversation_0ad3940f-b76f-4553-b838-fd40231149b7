import { TPrompt } from "../domain/models";
import { Edit, Trash2, Eye } from "lucide-react";
import { format } from "date-fns";

interface TableColumnsProps {
  onEdit: (prompt: TPrompt) => void;
  onDelete: (id: string) => void;
  onView: (prompt: TPrompt) => void;
}

export function TableColumns({ onEdit, onDelete, onView }: TableColumnsProps) {
  return [
    {
      id: "name",
      label: "Nome",
      render: (rowData: TPrompt) => <p className="font-medium">{rowData.name}</p>,
    },
    {
      id: "provider",
      label: "Provider",
      render: (rowData: TPrompt) => <p>{rowData.provider}</p>,
    },
    {
      id: "model",
      label: "Modelo",
      render: (rowData: TPrompt) => <p>{rowData.model}</p>,
    },
    {
      id: "status",
      label: "Status",
      align: "center" as const,
      render: (rowData: TPrompt) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            rowData.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
          }`}
        >
          {rowData.status === "active" ? "Ativo" : "Inativo"}
        </span>
      ),
    },
    {
      id: "updatedAt",
      label: "Última Atualização",
      align: "center" as const,
      render: (rowData: TPrompt) => <p>{format(new Date(rowData.updatedAt), "dd/MM/yyyy HH:mm")}</p>,
    },
    {
      align: "center" as const,
      id: "actions",
      label: "Ações",
      render: (rowData: TPrompt) => (
        <div className="flex items-center justify-center gap-2">
          <button onClick={() => onView(rowData)} className="p-1 text-gray-800 hover:text-black transition-colors" title="Visualizar">
            <Eye size={18} />
          </button>
          <button onClick={() => onEdit(rowData)} className="p-1 text-yellow-600 hover:text-yellow-700 transition-colors" title="Editar">
            <Edit size={18} />
          </button>
          <button onClick={() => onDelete(rowData.id)} className="p-1 text-red-600 hover:text-red-700 transition-colors" title="Excluir">
            <Trash2 size={18} />
          </button>
        </div>
      ),
    },
  ];
}
