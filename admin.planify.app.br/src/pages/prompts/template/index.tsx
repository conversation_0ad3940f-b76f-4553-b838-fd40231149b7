import { Table } from "@/components/Table";
import { Plus, X } from "lucide-react";
import { TableColumns } from "../mocks/TableColumns";
import { TOpenAIModel, TDeepseekModel } from "../domain/models";
import { HtmlEditor } from "../../../components/HtmlEditor";

export function Template({ ...sharedProps }) {
  const {
    prompts,
    isModalOpen,
    setIsModalOpen,
    editingPrompt,
    viewPrompt,
    setViewPrompt,
    formData,
    setFormData,
    handleSubmit,
    handleDelete,
    openEditModal,
    openViewModal,
    resetForm,
    loading,
    handleProviderChange,
    handleModelChange,
  } = sharedProps;
  const handleCloseModal = () => {
    setIsModalOpen(false);
    resetForm();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">Prompts</h1>
        <button
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Novo Prompt
        </button>
      </div>

      <Table columns={TableColumns({ onEdit: openEditModal, onDelete: handleDelete, onView: openViewModal })} data={prompts} />

      {viewPrompt && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full sm:p-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg leading-6 font-medium text-gray-900">Visualizar Prompt</h3>
                <button onClick={() => setViewPrompt(null)} className="text-gray-400 hover:text-gray-500">
                  <X size={24} />
                </button>
              </div>
              <div className="mt-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nome</label>
                  <p className="mt-1 p-2 block w-full rounded-md border border-gray-300 bg-gray-50">{viewPrompt.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Conteúdo</label>
                  <div
                    className="mt-1 p-2 block w-full rounded-md border border-gray-300 bg-gray-50 whitespace-pre-wrap"
                    dangerouslySetInnerHTML={{ __html: viewPrompt?.content ?? null }}
                  ></div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Provider</label>
                  <p className="mt-1 p-2 block w-full rounded-md border border-gray-300 bg-gray-50">{viewPrompt.provider}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Modelo</label>
                  <p className="mt-1 p-2 block w-full rounded-md border border-gray-300 bg-gray-50">{viewPrompt.model}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <p className="mt-1 p-2 block w-full rounded-md border border-gray-300 bg-gray-50">
                    {viewPrompt.status === "active" ? "Ativo" : "Inativo"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {isModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0" onClick={handleCloseModal}>
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <div
              className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full sm:p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">{editingPrompt ? "Editar Prompt" : "Novo Prompt"}</h3>
                <form onSubmit={handleSubmit} className="mt-5 space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Nome
                    </label>
                    <input
                      type="text"
                      name="name"
                      id="name"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                        Conteúdo do Prompt
                      </label>
                    </div>
                    <HtmlEditor
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      value={formData.content}
                      onChange={(value) => setFormData({ ...formData, content: value })}
                    />
                  </div>
                  <div>
                    <label htmlFor="provider" className="block text-sm font-medium text-gray-700">
                      Provider
                    </label>
                    <select
                      name="provider"
                      id="provider"
                      required
                      value={formData.provider}
                      onChange={(e) => handleProviderChange(e.target.value as "openai" | "deepseek")}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    >
                      <option value="openai">OpenAI</option>
                      <option value="deepseek">Deepseek</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="model" className="block text-sm font-medium text-gray-700">
                      Modelo
                    </label>
                    <select
                      name="model"
                      id="model"
                      required
                      value={formData.model}
                      onChange={(e) => handleModelChange(e.target.value as TOpenAIModel | TDeepseekModel)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    >
                      {formData.provider === "openai" ? (
                        <>
                          <option value="gpt-4o-mini">GPT-4o-Mini</option>
                          <option value="gpt-4o">GPT-4o</option>
                          <option value="gpt-4">GPT-4</option>
                        </>
                      ) : (
                        <>
                          <option value="deepseek-r1-zero">Deepseek R1 Zero</option>
                          <option value="deepseek-r1">Deepseek R1</option>
                          <option value="deepseek-v3">Deepseek V3</option>
                        </>
                      )}
                    </select>
                  </div>
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                      Status
                    </label>
                    <select
                      name="status"
                      id="status"
                      required
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value as "active" | "inactive" })}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    >
                      <option value="active">Ativo</option>
                      <option value="inactive">Inativo</option>
                    </select>
                  </div>
                  <div className="mt-5 sm:mt-6">
                    <button
                      type="submit"
                      className="inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm"
                    >
                      {editingPrompt ? "Atualizar" : "Criar"}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
