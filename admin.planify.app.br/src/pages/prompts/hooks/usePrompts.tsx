import { useState, useCallback } from "react";
import { T<PERSON>rom<PERSON>, T<PERSON>rompt<PERSON>orm, TProvider, TOpenAIModel, TDeepseekModel } from "../domain/models";
import { PromptService } from "../domain/services/prompt.useCases";
import { toast } from "react-toastify";

const promptService = new PromptService();

export function usePrompts() {
  const [prompts, setPrompts] = useState<TPrompt[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<TPrompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewPrompt, setViewPrompt] = useState<TPrompt | null>(null);
  const [formData, setFormData] = useState<TPromptForm>({
    name: "",
    content: "",
    provider: "openai",
    model: "gpt-4o-mini" as TOpenAIModel,
    status: "active",
  });

  async function getPrompts() {
    try {
      setLoading(true);
      const data = await promptService.getPrompts();
      setPrompts(data);
    } catch (error) {
      toast.error("Erro ao carregar prompts");
    } finally {
      setLoading(false);
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    try {
      if (editingPrompt) {
        await promptService.updatePrompt(editingPrompt.id, formData);
        toast.success("Prompt atualizado com sucesso!");
      } else {
        await promptService.createPrompt(formData);
        toast.success("Prompt criado com sucesso!");
      }
      setIsModalOpen(false);
      resetForm();
      getPrompts();
    } catch (error) {
      toast.error(editingPrompt ? "Erro ao atualizar prompt" : "Erro ao criar prompt");
    }
  }

  async function handleDelete(id: string) {
    if (window.confirm("Tem certeza que deseja excluir este prompt?")) {
      try {
        await promptService.deletePrompt(id);
        toast.success("Prompt excluído com sucesso!");
        getPrompts();
      } catch (error) {
        toast.error("Erro ao excluir prompt");
      }
    }
  }

  const openEditModal = useCallback((prompt: TPrompt) => {
    setEditingPrompt(prompt);
    setFormData({
      name: prompt.name,
      content: prompt.content,
      provider: prompt.provider,
      model: prompt.model,
      status: prompt.status,
    });
    setIsModalOpen(true);
  }, []);

  const openViewModal = useCallback((prompt: TPrompt) => {
    setViewPrompt(prompt);
  }, []);

  const resetForm = useCallback(() => {
    setFormData({
      name: "",
      content: "",
      provider: "openai",
      model: "gpt-4o-mini" as TOpenAIModel,
      status: "active",
    });
    setEditingPrompt(null);
    setViewPrompt(null);
  }, []);

  const handleProviderChange = useCallback((provider: TProvider) => {
    setFormData((prev) => ({
      ...prev,
      provider,
      model: provider === "openai" ? ("gpt-4o-mini" as TOpenAIModel) : ("deepseek-r1-zero" as TDeepseekModel),
    }));
  }, []);

  const handleModelChange = useCallback((model: TOpenAIModel | TDeepseekModel) => {
    setFormData((prev) => ({
      ...prev,
      model,
    }));
  }, []);

  return {
    prompts,
    isModalOpen,
    setIsModalOpen,
    editingPrompt,
    viewPrompt,
    setViewPrompt,
    formData,
    setFormData,
    handleSubmit,
    handleDelete,
    openEditModal,
    openViewModal,
    resetForm,
    loading,
    handleProviderChange,
    handleModelChange,
    getPrompts,
  };
}
