export type TProvider = "openai" | "deepseek";

export type TOpenAIModel = "gpt-4o-mini" | "gpt-4o" | "gpt-4";
export type TDeepseekModel = "deepseek-r1-zero" | "deepseek-r1" | "deepseek-v3";

export type TPrompt = {
  id: string;
  name: string;
  content: string;
  provider: TProvider;
  model: TOpenAIModel | TDeepseekModel;
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
};

export type TPromptForm = Omit<TPrompt, "id" | "createdAt" | "updatedAt">;
