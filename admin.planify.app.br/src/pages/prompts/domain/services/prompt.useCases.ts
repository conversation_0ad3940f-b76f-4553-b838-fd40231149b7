import HttpClient from "@/infra/httpRequest";
import { TPrompt, TPromptForm } from "../models";

const httpClient = new HttpClient();
const BASE_URL = "llm-prompts";

export class PromptService {
  getPrompts = async (): Promise<TPrompt[]> => {
    const response = await httpClient.get(BASE_URL);
    return response?.data;
  };

  createPrompt = async (prompt: TPromptForm): Promise<TPrompt> => {
    const response = await httpClient.post(BASE_URL, prompt);
    return response?.data;
  };

  updatePrompt = async (id: string, prompt: TPromptForm): Promise<TPrompt> => {
    const response = await httpClient.patch(`${BASE_URL}/${id}`, prompt);
    return response?.data;
  };

  deletePrompt = async (id: string): Promise<void> => {
    await httpClient.delete(`${BASE_URL}/${id}`);
  };
}
