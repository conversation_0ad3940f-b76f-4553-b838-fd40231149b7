import HttpClient from "@infra/httpRequest";
import { TPaymentPlan, TPlan, TScope, TPermission } from "../models";

interface ApiResponse<T> {
  data: T;
}

const service = new HttpClient();

export default class PlanServices {
  async get(): Promise<TPlan[]> {
    const response = await service.get<ApiResponse<TPlan[]>>(`/payment-plans`);
    return response?.data;
  }

  async getScopes(): Promise<TScope[]> {
    const response = await service.get<ApiResponse<TScope[]>>(`/scopes`);
    return response?.data;
  }

  async getPermissions(): Promise<TPermission[]> {
    const response = await service.get<ApiResponse<TPermission[]>>(`/permissions`);
    return response?.data;
  }

  async getPlanDetails(id: string): Promise<TPlan> {
    const response = await service.get<ApiResponse<TPlan>>(`/payment-plans/${id}`);
    return response?.data;
  }

  async createPlan(data: TPaymentPlan): Promise<TPlan> {
    const response = await service.post<ApiResponse<TPlan>>(`/payment-plans/setup`, data);
    return response?.data;
  }

  async updatePlan(id: string, data: Partial<TPaymentPlan>): Promise<TPlan> {
    const response = await service.patch<ApiResponse<TPlan>>(`/payment-plans/${id}`, data);
    return response?.data;
  }

  async createPermissions(data: { description: string; paymentPlanId: string; scopes: string[] }): Promise<TPermission> {
    const response = await service.post<ApiResponse<TPermission>>(`/permissions`, data);
    return response?.data;
  }

  async replicate(id: string): Promise<TPlan> {
    const response = await service.post<ApiResponse<TPlan>>(`/payment-plans/reply/${id}`);
    return response?.data;
  }

  async deletePlan(id: string): Promise<void> {
    await service.delete(`/payment-plans/${id}`);
  }
}
