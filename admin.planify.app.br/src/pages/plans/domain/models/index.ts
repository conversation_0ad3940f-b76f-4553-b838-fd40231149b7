// Adicione seus tipos e interfaces aqui. Exemplos:

export type PeriodType = "3 months" | "6 months" | "year" | "unlimited" | "custom";
export type RecurrencyType = "month" | "year";
export type PlanType = "free" | "start" | "standard" | "premium" | "ultimate" | "ultimate_mentor";

export type TScope = {
  id: string;
  name: string;
  tag: string;
  createdAt: string;
  updatedAt: string;
};

export type TPermission = {
  id: string;
  scopes: string[];
  status: string;
  description: string;
  paymentPlanId: string;
  createdAt: string;
  updatedAt: string;
  paymentPlan: {
    name: string;
    price: string;
  };
};

export type TPaymentPlan = {
  name: string;
  description: string;
  assignDescription: string;
  price: string;
  promotionalPrice: string | null;
  typePlan: PlanType;
  recurrencyType: RecurrencyType;
  recurrencyPeriod: number;
  recurrencyInstallments: number;
  aiObjectivesPeriod: PeriodType;
  aiObjectivesLimit: number | null;
  aiPlanTasksLimits: number | null;
  manualObjectivesPeriod: PeriodType;
  manualObjectivesLimit: number | null;
  manualPlanTasksLimits: number | null;
  radarLimitPeriod: PeriodType;
  radarLimit: number | null;
  allowTasksControl: boolean;
  pendingTasksLimit: number;
  allowNotes: boolean;
  allowEmotionalAnalysis: boolean;
  emotionalAnalysisPeriod: PeriodType;
  emotionalAnalysisLimit: number | null;
  allowNetworking: boolean;
  allowMentoredUserAnalytics: boolean;
  permissionDescription: string;
  scopes: string[];
  scopeTags?: string[];
  installments: number[];
  paymentMethods: string[];
  statementDescriptor: string;
  displayOrder: number | null;
  status?: "active" | "inactive" | "draft";
};

export type TPlan = {
  id: string;
  gatewayPlanId: string | null;
  name: string;
  description: string;
  assignDescription: string;
  status: "active" | "inactive" | "draft";
  displayOrder: number;
  aiObjectivesPeriod: PeriodType;
  aiObjectivesLimit: number | null;
  aiPlanTasksLimits: number | null;
  manualObjectivesPeriod: PeriodType;
  manualObjectivesLimit: number | null;
  manualPlanTasksLimits: number | null;
  radarLimitPeriod: PeriodType;
  radarLimit: number | null;
  allowTasksControl: boolean;
  pendingTasksLimit: number;
  allowNotes: boolean;
  allowEmotionalAnalysis: boolean;
  emotionalAnalysisPeriod: PeriodType;
  emotionalAnalysisLimit: number | null;
  price: string;
  promotionalPrice: string | null;
  typePlan: PlanType;
  recurrencyType: RecurrencyType;
  recurrencyPeriod: number;
  recurrencyInstallments: number;
  allowNetworking: boolean;
  allowMentoredUserAnalytics: boolean;
  createdAt: string;
  updatedAt: string;
  scopes?: string[];
};
