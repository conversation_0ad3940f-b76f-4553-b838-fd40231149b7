import React from "react";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { TPlan } from "../domain/models";

interface Column {
  id: string;
  label: string;
  render: (rowData: TPlan) => React.ReactNode;
  align?: "left" | "center" | "right";
}

interface DraggableTableProps {
  columns: Column[];
  data: TPlan[];
  onReorder: (items: TPlan[]) => void;
}

function DraggableRow({ item, columns }: { item: TPlan; columns: Column[] }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: item.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    backgroundColor: isDragging ? "#F3F4F6" : undefined,
  };

  return (
    <tr ref={setNodeRef} style={style} className={`hover:bg-gray-50 transition-colors ${isDragging ? "shadow-lg" : ""}`}>
      {columns.map((column) => {
        if (column.id === "actions") {
          return (
            <td
              key={column.id}
              className={`px-6 py-4 whitespace-nowrap text-sm ${
                column.align === "center" ? "text-center" : column.align === "right" ? "text-right" : "text-left"
              }`}
            >
              {column.render(item)}
            </td>
          );
        }

        return (
          <td
            key={column.id}
            className={`px-6 py-4 whitespace-nowrap text-sm ${
              column.align === "center" ? "text-center" : column.align === "right" ? "text-right" : "text-left"
            }`}
            style={{ cursor: "move" }}
            {...attributes}
            {...listeners}
          >
            {column.render(item)}
          </td>
        );
      })}
    </tr>
  );
}

export function DraggableTable({ columns, data, onReorder }: DraggableTableProps) {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = data.findIndex((item) => item.id === active.id);
      const newIndex = data.findIndex((item) => item.id === over.id);
      const newItems = arrayMove(data, oldIndex, newIndex);
      onReorder(newItems);
    }
  };

  return (
    <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((column) => (
              <th
                key={column.id}
                scope="col"
                className={`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${
                  column.align === "center" ? "text-center" : column.align === "right" ? "text-right" : "text-left"
                }`}
              >
                {column.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200 z-99">
          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            <SortableContext items={data.map((item) => item.id)} strategy={verticalListSortingStrategy}>
              {data.map((item) => (
                <DraggableRow key={item.id} item={item} columns={columns} />
              ))}
            </SortableContext>
          </DndContext>
        </tbody>
      </table>
    </div>
  );
}
