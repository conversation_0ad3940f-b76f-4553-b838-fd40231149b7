 
import React, { useState } from "react";
import { PlanServices } from "../domain";
import { toast } from "react-toastify";
import { TPaymentPlan, TPlan, TPermission } from "../domain/models";
import { useGlobalStore } from "@/contexts/globalContext";

const initialFormState: TPaymentPlan = {
  name: "",
  status: "active",
  description: "",
  assignDescription: "",
  price: "",
  typePlan: "free",
  promotionalPrice: null,
  recurrencyType: "month",
  recurrencyPeriod: 1,
  recurrencyInstallments: 1,
  aiObjectivesPeriod: "year",
  aiObjectivesLimit: 0,
  aiPlanTasksLimits: 0,
  manualObjectivesPeriod: "year",
  manualObjectivesLimit: 0,
  manualPlanTasksLimits: 0,
  radarLimitPeriod: "year",
  radarLimit: 0,
  allowTasksControl: false,
  pendingTasksLimit: 0,
  allowNotes: false,
  allowEmotionalAnalysis: false,
  emotionalAnalysisPeriod: "year",
  emotionalAnalysisLimit: 0,
  allowNetworking: false,
  allowMentoredUserAnalytics: false,
  permissionDescription: "",
  scopes: [],
  scopeTags: [],
  installments: [1],
  paymentMethods: ["credit_card"],
  statementDescriptor: "PLANFREEMONTHLY",
  displayOrder: null,
};

export function usePlans() {
  const { plans, getPaymentPlans, setLoading, getScopes, scopes } = useGlobalStore();
  const service = new PlanServices();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<TPlan | null>(null);
  const [formData, setFormData] = useState<TPaymentPlan>(initialFormState);

  const [permissions, setPermissions] = useState<TPermission[]>([]);

  const [currentStep, setCurrentStep] = useState(1);

  const resetForm = () => {
    setFormData(initialFormState);
    setEditingPlan(null);
    setIsModalOpen(false);
    setCurrentStep(1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const planData: TPaymentPlan = {
        name: formData.name,
        status: formData.status,
        description: formData.description,
        assignDescription: formData.assignDescription,
        price: formData.price,
        typePlan: formData.typePlan,
        promotionalPrice: formData.promotionalPrice,
        recurrencyType: formData.recurrencyType,
        recurrencyPeriod: formData.recurrencyPeriod,
        recurrencyInstallments: formData.recurrencyInstallments,
        installments: formData.installments,
        statementDescriptor: formData.statementDescriptor,
        allowTasksControl: formData.allowTasksControl,
        allowNotes: formData.allowNotes,
        allowNetworking: formData.allowNetworking,
        allowMentoredUserAnalytics: formData.allowMentoredUserAnalytics,
        allowEmotionalAnalysis: formData.allowEmotionalAnalysis,
        pendingTasksLimit: formData.pendingTasksLimit,
        aiObjectivesPeriod: formData.aiObjectivesPeriod,
        aiObjectivesLimit: formData.aiObjectivesLimit,
        aiPlanTasksLimits: formData.aiPlanTasksLimits,
        manualObjectivesPeriod: formData.manualObjectivesPeriod,
        manualObjectivesLimit: formData.manualObjectivesLimit,
        manualPlanTasksLimits: formData.manualPlanTasksLimits,
        radarLimitPeriod: formData.radarLimitPeriod,
        radarLimit: formData.radarLimit,
        emotionalAnalysisPeriod: formData.emotionalAnalysisPeriod,
        emotionalAnalysisLimit: formData.emotionalAnalysisLimit,
        scopes: formData.scopes,
        permissionDescription: formData.description,
        paymentMethods: ["credit_card"],
        displayOrder: formData.displayOrder,
      };

      if (editingPlan) {
        await service.updatePlan(editingPlan.id, planData);
        toast.success("Plano atualizado com sucesso!");
      } else {
        await service.createPlan(planData);
        toast.success("Plano criado com sucesso!");
      }

      setIsModalOpen(false);
      resetForm();
      await getPaymentPlans();
      await getPermissions();
    } catch (error) {
      console.error("Erro ao salvar plano:", error);
      throw error;
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Tem certeza que deseja excluir este plano?")) {
      try {
        await service.deletePlan(id);
        toast.success("Plano excluído com sucesso!");
        await getPaymentPlans();
        await getPermissions();
      } catch (error) {
        console.error("Erro ao excluir plano:", error);
        toast.error("Erro ao excluir plano");
      }
    }
  };

  const openEditModal = (plan: TPlan) => {
    setEditingPlan(plan);

    const planPermission = permissions.find((p) => p.paymentPlanId === plan.id);
    const planScopeTags = planPermission?.scopes || [];

    const planScopeIds = planScopeTags
      .map((tag) => {
        const scope = scopes.find((s) => s.tag === tag);
        return scope?.id || "";
      })
      .filter((id) => id !== "");

    const formDataEdit: TPaymentPlan = {
      // Dados básicos
      name: plan.name,
      status: plan.status,
      description: plan.description,
      assignDescription: plan.assignDescription || "",
      price: plan.price,
      promotionalPrice: plan.promotionalPrice ?? null,
      typePlan: plan.typePlan,
      permissionDescription: plan.description,
      scopes: planScopeIds,
      scopeTags: planScopeTags,
      displayOrder: plan.displayOrder,
      // Dados de recorrência
      recurrencyType: plan.recurrencyType,
      recurrencyPeriod: plan.recurrencyPeriod,
      recurrencyInstallments: plan.recurrencyInstallments,
      installments: Array.from({ length: plan.recurrencyInstallments }, (_, i) => i + 1),
      paymentMethods: ["credit_card"],
      // Limites e períodos
      aiObjectivesPeriod: plan.aiObjectivesPeriod,
      aiObjectivesLimit: plan.aiObjectivesLimit === null ? null : Number(plan.aiObjectivesLimit),
      aiPlanTasksLimits: plan.aiPlanTasksLimits === null ? null : Number(plan.aiPlanTasksLimits),
      manualObjectivesPeriod: plan.manualObjectivesPeriod,
      manualObjectivesLimit: plan.manualObjectivesLimit === null ? null : Number(plan.manualObjectivesLimit),
      manualPlanTasksLimits: plan.manualPlanTasksLimits === null ? null : Number(plan.manualPlanTasksLimits),
      radarLimitPeriod: plan.radarLimitPeriod,
      radarLimit: plan.radarLimit === null ? null : Number(plan.radarLimit),
      emotionalAnalysisPeriod: plan.emotionalAnalysisPeriod,
      emotionalAnalysisLimit: plan.emotionalAnalysisLimit === null ? null : Number(plan.emotionalAnalysisLimit),
      allowTasksControl: plan.allowTasksControl,
      allowNotes: plan.allowNotes,
      allowNetworking: plan.allowNetworking,
      allowMentoredUserAnalytics: plan.allowMentoredUserAnalytics,
      allowEmotionalAnalysis: plan.allowEmotionalAnalysis,
      pendingTasksLimit: plan.pendingTasksLimit,
      statementDescriptor: generateStatementDescriptor(plan.typePlan, plan.recurrencyType, plan.recurrencyPeriod),
    };

    setFormData(formDataEdit);
    setIsModalOpen(true);
    setCurrentStep(1);
  };

  const generateStatementDescriptor = (typePlan: string, recurrencyType: string, recurrencyPeriod: number) => {
    const planType = typePlan.toUpperCase();
    const period =
      recurrencyType === "month"
        ? recurrencyPeriod === 1
          ? "M"
          : recurrencyPeriod === 6
          ? "6M"
          : recurrencyPeriod === 12
          ? "Y"
          : `${recurrencyPeriod}M`
        : "Y";

    return `PLAN${planType}${period}`.substring(0, 17);
  };

  async function getPermissions() {
    try {
      const response = await service.getPermissions();
      setPermissions(response);
    } catch (error) {
      console.error("Erro ao buscar permissões:", error);
      toast.error("Erro ao carregar as permissões dos planos");
    }
  }

  function togglePlanStatus() {}

  const nextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, 4));
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  async function updatePlanOrder(planId: string, newDisplayOrder: number) {
    try {
      setLoading(true);
      await service.updatePlan(planId, { displayOrder: newDisplayOrder } as Partial<TPaymentPlan>);
      await getPaymentPlans();
    } catch (error) {
      console.error("Erro ao atualizar ordem do plano:", error);
      toast.error("Erro ao atualizar a ordem do plano");
    } finally {
      setLoading(false);
    }
  }

  async function replicatePlan(planId: string) {
    try {
      setLoading(true);
      await service.replicate(planId);
      await getPaymentPlans();
      toast.success("Plano replicado com sucesso!");
    } catch (error) {
      console.error("Erro ao replicar plano:", error);
      toast.error("Erro ao replicar o plano");
    } finally {
      setLoading(false);
    }
  }

  return {
    plans,
    openEditModal,
    handleSubmit,
    handleDelete,
    isModalOpen,
    togglePlanStatus,
    formData,
    editingPlan,
    setFormData,
    setIsModalOpen,
    setEditingPlan,
    getPaymentPlans,
    getScopes,
    getPermissions,
    scopes,
    permissions,
    currentStep,
    nextStep,
    prevStep,
    resetForm,
    updatePlanOrder,
    replicatePlan,
  };
}
