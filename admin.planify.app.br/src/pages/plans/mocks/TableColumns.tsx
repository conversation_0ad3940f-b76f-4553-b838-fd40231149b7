import { TPlan, TScope, TPermission } from "../domain/models";
import { Edit, Trash2, Copy } from "lucide-react";

interface TableColumnsProps {
  onEdit: (plan: TPlan) => void;
  onDelete: (id: string) => void;
  onReplicate: (id: string) => void;
  scopes: TScope[];
  permissions: TPermission[];
}

const formatCurrency = (value: string) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(Number(value));
};

const formatRecurrencyType = (type: "month" | "year", period: number) => {
  if (type === "year") return "Anual";
  if (type === "month") {
    if (period === 1) return "Mensal";
    if (period === 6) return "Semestral";
    return `${period} meses`;
  }
  return type;
};

export function TableColumns({ onEdit, onDelete, onReplicate, scopes, permissions }: TableColumnsProps) {
  return [
    {
      id: "name",
      label: "NOME",
      render: (rowData: TPlan) => (
        <div className="max-w-[200px]">
          <div className="font-medium text-gray-900 truncate">{rowData.name}</div>
          <div className="text-sm text-gray-500 truncate">{rowData.description}</div>
        </div>
      ),
    },
    {
      id: "price",
      label: "PREÇO",
      render: (rowData: TPlan) => <div className="text-gray-900">{formatCurrency(rowData.price)}</div>,
    },

    {
      id: "promotionalPrice",
      label: "PREÇO PROM.",
      render: (rowData: TPlan) => <div className="text-gray-900">{rowData.promotionalPrice ? formatCurrency(rowData.promotionalPrice) : "-"}</div>,
    },
    {
      id: "status",
      label: "STATUS",
      render: (rowData: TPlan) => (
        <span
          className={`px-3 py-1 inline-flex text-sm font-medium rounded-full ${
            rowData.status === "active"
              ? "bg-green-100 text-green-800"
              : rowData.status === "draft"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {rowData.status === "active" ? "Ativo" : rowData.status === "draft" ? "Rascunho" : "Inativo"}
        </span>
      ),
    },
    {
      id: "recurrency",
      label: "VIGÊNCIA",
      render: (rowData: TPlan) => <div className="text-gray-700">{formatRecurrencyType(rowData.recurrencyType, rowData.recurrencyPeriod)}</div>,
    },
    {
      id: "scopes",
      label: "PERMISSÕES",
      render: (rowData: TPlan) => {
        const planPermission = permissions.find((p) => p.paymentPlanId === rowData.id);
        const planScopes = planPermission?.scopes || [];

        const scopeNames = planScopes.map((scopeTag) => {
          const scope = scopes.find((s) => s.tag === scopeTag);
          return scope?.name || scopeTag;
        });

        return (
          <div className="text-sm text-gray-900 max-w-md overflow-hidden">
            <div className="flex flex-wrap gap-1">
              {scopeNames.map((scopeName, index) => (
                <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-[#E11E37]/10 text-[#E11E37]">
                  {scopeName}
                </span>
              ))}
            </div>
          </div>
        );
      },
    },
    {
      id: "order",
      label: "Ordenação",
      render: (rowData: TPlan) => <div className="text-gray-900">{rowData?.displayOrder}</div>,
    },
    {
      id: "actions",
      label: "AÇÕES",
      align: "center",
      render: (rowData: TPlan) => (
        <div className="flex justify-end gap-2">
          <button onClick={() => onReplicate(rowData.id)} className="text-gray-800 hover:text-black" title="Replicar plano">
            <Copy className="w-5 h-5" />
          </button>
          <button onClick={() => onEdit(rowData)} className="text-yellow-600 hover:text-yellow-700" title="Editar plano">
            <Edit className="w-5 h-5" />
          </button>
          <button onClick={() => onDelete(rowData.id)} className="text-red-600 hover:text-red-700" title="Excluir plano">
            <Trash2 className="w-5 h-5" />
          </button>
        </div>
      ),
    },
  ];
}
