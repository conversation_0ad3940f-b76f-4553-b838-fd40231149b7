/* eslint-disable @typescript-eslint/no-explicit-any */
export function hasPermission(formData: any, fieldName: any) {
  const scopeTags = formData.scopeTags || [];
  switch (fieldName) {
    case "aiObjectivesPeriod":
      return scopeTags.includes("aiPlan.write");
    case "manualObjectivesPeriod":
      return scopeTags.includes("manualPlan.write");
    case "radarLimitPeriod":
      return scopeTags.includes("radar.write");
    case "emotionalAnalysisPeriod":
      return scopeTags.includes("emotionalAnalysis.write");
    default:
      return false;
  }
}

export function getUnlimitedLabel(fieldName: any) {
  switch (fieldName) {
    case "aiObjectivesPeriod":
      return "Habilitar objetivos com IA ilimitados";
    case "manualObjectivesPeriod":
      return "Habilitar objetivos manuais ilimitados";
    case "radarLimitPeriod":
      return "Habilitar radar ilimitado";
    case "emotionalAnalysisPeriod":
      return "Habilitar análise emocional ilimitada";
    default:
      return "Limite Ilimitado";
  }
}

export function getBackgroundColor(formData: any, fieldName: any) {
  return hasPermission(formData, fieldName) ? "bg-white" : "bg-gray-200";
}
