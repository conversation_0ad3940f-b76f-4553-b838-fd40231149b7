 
import { Plus, ArrowLeft, ArrowRight } from "lucide-react";
import { HtmlEditor } from "../../../components/HtmlEditor";
import { TableColumns } from "../mocks/TableColumns";
import { TPaymentPlan, TPlan, TScope, PeriodType, TPermission } from "../domain/models";
import { DraggableTable } from "../components/DraggableTable";
import { hasPermission, getBackgroundColor } from "../functions";

export interface TemplateProps {
  plans: TPlan[];
  scopes: TScope[];
  permissions: TPermission[];
  openEditModal: (plan: TPlan) => void;
  handleDelete: (id: string) => void;
  replicatePlan: (id: string) => void;
  isModalOpen: boolean;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  editingPlan: TPlan | null;
  formData: TPaymentPlan;
  setFormData: React.Dispatch<React.SetStateAction<TPaymentPlan>>;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  resetForm: () => void;
  currentStep: number;
  nextStep: () => void;
  prevStep: () => void;
  updatePlanOrder: (id: string, order: number) => Promise<void>;
}

export function Template({
  plans,
  scopes,
  permissions,
  openEditModal,
  handleDelete,
  replicatePlan,
  isModalOpen,
  handleSubmit,
  editingPlan,
  formData,
  setFormData,
  setIsModalOpen,
  resetForm,
  currentStep,
  nextStep,
  prevStep,
  updatePlanOrder,
}: TemplateProps) {
  const generateStatementDescriptor = (typePlan: string, recurrencyType: string, recurrencyPeriod: number) => {
    const planType = typePlan.toUpperCase();
    const period =
      recurrencyType === "month"
        ? recurrencyPeriod === 1
          ? "M"
          : recurrencyPeriod === 6
          ? "6M"
          : recurrencyPeriod === 12
          ? "Y"
          : `${recurrencyPeriod}M`
        : "Y";

    return `PLAN${planType}${period}`.substring(0, 17);
  };

  const handlePlanTypeChange = (value: TPaymentPlan["typePlan"]) => {
    const newFormData = {
      ...formData,
      typePlan: value,
      price: value === "free" ? "0" : formData?.price,
      statementDescriptor: generateStatementDescriptor(value, formData?.recurrencyType, formData?.recurrencyPeriod),
    };
    setFormData(newFormData);
  };

  const handleRecurrencyPeriodChange = (value: number) => {
    if (formData?.recurrencyType === "year" && value !== 1) {
      return;
    }

    const newFormData = {
      ...formData,
      recurrencyPeriod: value,
      statementDescriptor: generateStatementDescriptor(formData?.typePlan, formData?.recurrencyType, value),
    };
    setFormData(newFormData);
  };

  const handleInstallmentsChange = (maxInstallment: number) => {
    const installments = Array.from({ length: maxInstallment }, (_, i) => i + 1);
    setFormData({
      ...formData,
      installments,
      recurrencyInstallments: maxInstallment,
    });
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    resetForm();
  };

  const handleReorder = async (reorderedPlans: TPlan[]) => {
    const updates = reorderedPlans.map((plan, index) => {
      if (plan.displayOrder !== index + 1) {
        return updatePlanOrder(plan.id, index + 1);
      }
      return Promise.resolve();
    });

    await Promise.all(updates);
  };

  const columns = TableColumns({
    onEdit: openEditModal,
    onDelete: handleDelete,
    onReplicate: replicatePlan,
    scopes,
    permissions,
  });

  const renderPeriodSelect = (fieldName: keyof TPaymentPlan, label: string, value: PeriodType, showLimit: boolean = true) => {
    const limitFieldName = fieldName === "radarLimitPeriod" ? "radarLimit" : (`${fieldName.replace("Period", "Limit")}` as keyof TPaymentPlan);
    const isUnlimited = value === "unlimited";

    const getUnlimitedLabel = () => {
      switch (fieldName) {
        case "aiObjectivesPeriod":
          return "Habilitar objetivos com IA ilimitados";
        case "manualObjectivesPeriod":
          return "Habilitar objetivos manuais ilimitados";
        case "radarLimitPeriod":
          return "Habilitar radar ilimitado";
        case "emotionalAnalysisPeriod":
          return "Habilitar análise emocional ilimitada";
        default:
          return "Limite Ilimitado";
      }
    };

    const availablePeriods =
      fieldName === "radarLimitPeriod" ? (["3 months", "6 months", "year"] as PeriodType[]) : (["6 months", "year"] as PeriodType[]);

    const permitido = hasPermission(formData, fieldName);

    return (
      <div
        className={`${getBackgroundColor(
          formData,
          fieldName
        )} rounded-lg border border-gray-200 hover:border-primary transition-colors duration-200 ${
          !permitido ? "opacity-60 pointer-events-none" : ""
        }`}
      >
        <div className="p-3">
          <div className="flex flex-col">
            <div className="mb-2">
              <label htmlFor={fieldName} className="block text-sm font-medium text-gray-700">
                {label}
              </label>
              <select
                name={fieldName}
                id={fieldName}
                required
                disabled={!permitido}
                value={value === "unlimited" ? "year" : value}
                onChange={(e) => {
                  const newValue = e.target.value as PeriodType;
                  setFormData({
                    ...formData,
                    [fieldName]: newValue,
                  });
                }}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
              >
                {availablePeriods.map((period) => (
                  <option key={period} value={period}>
                    {period === "year" ? "Anual" : period === "6 months" ? "Semestral" : period === "3 months" ? "Trimestral" : period}
                  </option>
                ))}
              </select>
            </div>
            {showLimit && (
              <div className="space-y-3">
                <div className="flex items-center group cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors duration-200">
                  <input
                    type="checkbox"
                    id={`${limitFieldName}Unlimited`}
                    checked={isUnlimited}
                    disabled={!permitido}
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        [fieldName]: e.target.checked ? "unlimited" : "year",
                        [limitFieldName]: e.target.checked ? null : formData[limitFieldName],
                      });
                    }}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200"
                  />
                  <label htmlFor={`${limitFieldName}Unlimited`} className="ml-2 block text-sm text-gray-700 cursor-pointer select-none">
                    {getUnlimitedLabel()}
                  </label>
                </div>
                {!isUnlimited && (
                  <div className="bg-gray-50 p-3 rounded-md">
                    <label htmlFor={limitFieldName} className="block text-sm font-medium text-gray-700 mb-1">
                      {`${
                        fieldName === "aiObjectivesPeriod"
                          ? "Limite max. de objetivos"
                          : fieldName === "manualObjectivesPeriod"
                          ? "Limite max. de objetivos manuais"
                          : fieldName === "radarLimitPeriod"
                          ? "Limite max. de radar"
                          : fieldName === "emotionalAnalysisPeriod"
                          ? "Limite max. de análise emocional"
                          : ""
                      }`}
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        name={limitFieldName}
                        id={limitFieldName}
                        min="0"
                        value={formData[limitFieldName] ?? ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          const newValue = value === "" ? null : Number(value);

                          // Criar uma cópia segura do objeto
                          const updatedFormData = { ...formData };

                          // Usar cast seguro para string como chave
                          if (limitFieldName === "aiObjectivesLimit") {
                            updatedFormData.aiObjectivesLimit = newValue;
                          } else if (limitFieldName === "manualObjectivesLimit") {
                            updatedFormData.manualObjectivesLimit = newValue;
                          } else if (limitFieldName === "radarLimit") {
                            updatedFormData.radarLimit = newValue;
                          } else if (limitFieldName === "emotionalAnalysisLimit") {
                            updatedFormData.emotionalAnalysisLimit = newValue;
                          }

                          setFormData(updatedFormData);
                        }}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                      />
                      <p className="mt-1 text-sm text-gray-500">
                        {isUnlimited ? "Ilimitado" : formData[limitFieldName] === 0 ? "Nenhum item disponível" : ""}
                      </p>
                    </div>
                  </div>
                )}
                {fieldName !== "emotionalAnalysisPeriod" && fieldName !== "radarLimitPeriod" && (
                  <div className="bg-gray-50 p-3 rounded-md">
                    <label htmlFor={`${fieldName}TasksLimit`} className="block text-sm font-medium text-gray-700 mb-1">
                      Limite max. de atividades por objetivo
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        name={`${fieldName}TasksLimit`}
                        id={`${fieldName}TasksLimit`}
                        min="0"
                        value={fieldName === "aiObjectivesPeriod" ? formData?.aiPlanTasksLimits ?? "" : formData?.manualPlanTasksLimits ?? ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          const newValue = value === "" ? null : Number(value);

                          const updatedFormData = { ...formData };

                          if (fieldName === "aiObjectivesPeriod") {
                            updatedFormData.aiPlanTasksLimits = newValue;
                          } else {
                            updatedFormData.manualPlanTasksLimits = newValue;
                          }

                          setFormData(updatedFormData);
                        }}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Nome
              </label>
              <input
                type="text"
                name="name"
                id="name"
                required
                value={formData?.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Descrição
              </label>
              <textarea
                name="description"
                id="description"
                required
                value={formData?.description || ""}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                rows={3}
              />
            </div>

            <div>
              <label htmlFor="assignDescription" className="block text-sm font-medium text-gray-700 mb-1">
                Descrição da assinatura
              </label>
              <HtmlEditor
                value={formData?.assignDescription || ""}
                onChange={(value) => setFormData({ ...formData, assignDescription: value })}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="displayOrder" className="block text-sm font-medium text-gray-700 mb-1">
                Ordem de Exibição
              </label>
              <input
                type="number"
                name="displayOrder"
                id="displayOrder"
                required
                min="0"
                value={formData?.displayOrder || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  setFormData({
                    ...formData,
                    displayOrder: value === "" ? 1 : Number(value),
                  });
                }}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status de exibição do plano
              </label>
              <select
                name="status"
                id="status"
                required
                value={formData?.status || "inactive"}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as "active" | "inactive" | "draft" })}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                <option value="active">Ativo</option>
                <option value="inactive">Inativo</option>
                <option value="draft">Rascunho</option>
              </select>
            </div>

            <div>
              <label htmlFor="typePlan" className="block text-sm font-medium text-gray-700 mb-1">
                Tipo do Plano
              </label>
              <select
                name="typePlan"
                id="typePlan"
                required
                value={formData?.typePlan}
                onChange={(e) => handlePlanTypeChange(e.target.value as TPaymentPlan["typePlan"])}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                <option value="free">Free</option>
                <option value="start">Start</option>
                <option value="standard">Standard</option>
                <option value="premium">Premium</option>
                <option value="ultimate">Ultimate</option>
                <option value="ultimate_mentor">Ultimate Mentor</option>
              </select>
            </div>

            {formData?.typePlan !== "free" && (
              <>
                <div>
                  <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                    Preço
                  </label>
                  <input
                    type="number"
                    name="price"
                    id="price"
                    required
                    min="0"
                    step="0.01"
                    value={formData?.price}
                    onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label htmlFor="promotionalPrice" className="block text-sm font-medium text-gray-700 mb-1">
                    Preço Promocional (opcional)
                  </label>
                  <input
                    type="number"
                    name="promotionalPrice"
                    id="promotionalPrice"
                    min="0"
                    step="0.01"
                    value={formData?.promotionalPrice || ""}
                    onChange={(e) => setFormData({ ...formData, promotionalPrice: e.target.value || null })}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    placeholder="0.00"
                  />
                  <p className="mt-1 text-sm text-gray-500">Deixe em branco para não usar preço promocional</p>
                </div>
              </>
            )}
          </div>
        );
      case 2: // Permissões para o plano
        return (
          <div>
            <label htmlFor="scopes" className="block text-sm font-medium text-gray-700">
              Permissões de acesso para o plano
            </label>
            <select
              id="scopes"
              name="scopes"
              required
              onChange={(e) => {
                const selectedOptions = Array.from(e.target.selectedOptions, (option) => option.value);
                const selectedTags = selectedOptions.map((option) => scopes.find((item) => item.id === option)?.tag).filter(Boolean) as string[];

                const newScopes = [...formData?.scopes];
                const newScopeTags = [...(formData?.scopeTags || [])];

                selectedOptions.forEach((option) => {
                  if (!newScopes.includes(option)) {
                    newScopes.push(option);
                  }
                });

                selectedTags.forEach((tag) => {
                  if (!newScopeTags.includes(tag)) {
                    newScopeTags.push(tag);
                  }
                });

                setFormData({
                  ...formData,
                  scopes: newScopes,
                  scopeTags: newScopeTags,
                });
              }}
              multiple
              className="mt-2 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm h-60"
            >
              {scopes
                .filter((scope) => !formData?.scopes.includes(scope.id))
                .map((scope) => (
                  <option key={scope?.id} value={scope?.id}>
                    {scope?.name}
                  </option>
                ))}
            </select>

            <div className="mt-4 flex flex-wrap gap-2">
              {formData?.scopes?.map((scopeId) => {
                const scope = scopes.find((s) => s.id === scopeId);
                return (
                  <span
                    key={scopeId}
                    className="inline-flex items-center px-2 py-1 rounded-full border border-gray-300 bg-gray-100 text-sm font-medium text-gray-700"
                  >
                    {scope ? scope?.name : scopeId}
                    <button
                      type="button"
                      onClick={() => {
                        // Encontrar a tag associada ao escopo
                        const scopeTag = scope?.tag;

                        // Atualizar os arrays de scopes e scopeTags
                        const updatedScopes = formData?.scopes.filter((id) => id !== scopeId);
                        const updatedScopeTags = formData?.scopeTags ? formData?.scopeTags.filter((tag) => tag !== scopeTag) : [];

                        setFormData({
                          ...formData,
                          scopes: updatedScopes,
                          scopeTags: updatedScopeTags,
                        });
                      }}
                      className="ml-1 text-red-500 hover:text-red-700"
                    >
                      &times;
                    </button>
                  </span>
                );
              })}
            </div>
          </div>
        );
      case 3: // Regras do plano
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {renderPeriodSelect("aiObjectivesPeriod", "Período de Objetivos IA", formData?.aiObjectivesPeriod)}
              {renderPeriodSelect("emotionalAnalysisPeriod", "Período de Análise Emocional", formData?.emotionalAnalysisPeriod)}
              {renderPeriodSelect("manualObjectivesPeriod", "Período de Objetivos Manuais", formData?.manualObjectivesPeriod)}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {renderPeriodSelect("radarLimitPeriod", "Período do Radar", formData?.radarLimitPeriod)}
              <div className="md:col-span-2">
                <div className={`bg-white rounded-lg border border-gray-200 hover:border-primary transition-colors duration-200`}>
                  <div className="p-3">
                    <h4 className="text-base font-medium text-gray-900 mb-3 flex items-center">
                      <span className="w-6 h-6 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path
                            fillRule="evenodd"
                            d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01-.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                      Permissões Adicionais
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div
                          className={`flex items-center p-3 ${
                            (formData?.scopeTags || []).includes("task.write") ? "bg-white" : "bg-gray-200"
                          } rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer group ${
                            !(formData?.scopeTags || []).includes("task.write") ? "opacity-60 pointer-events-none" : ""
                          }`}
                        >
                          <input
                            type="checkbox"
                            id="allowTasksControl"
                            checked={formData?.allowTasksControl}
                            disabled={!(formData?.scopeTags || []).includes("task.write")}
                            onChange={(e) => setFormData({ ...formData, allowTasksControl: e.target.checked })}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200"
                          />
                          <label htmlFor="allowTasksControl" className="block text-sm text-gray-700 ml-3 cursor-pointer select-none">
                            Permitir Controle de Tarefas
                          </label>
                        </div>
                        {formData?.allowTasksControl && (
                          <div className="ml-7 p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
                            <label htmlFor="pendingTasksLimit" className="block text-sm font-medium text-gray-700 mb-2">
                              Limite de Tarefas Pendentes Simultâneas
                            </label>
                            <div className="relative">
                              <input
                                type="number"
                                name="pendingTasksLimit"
                                id="pendingTasksLimit"
                                required
                                value={formData?.pendingTasksLimit || 0}
                                onChange={(e) => setFormData({ ...formData, pendingTasksLimit: Number(e.target.value) })}
                                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                              />
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="space-y-4">
                        <div
                          className={`flex items-center p-3 ${
                            (formData?.scopeTags || []).includes("task.write") ? "bg-white" : "bg-gray-200"
                          } rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer group ${
                            !(formData?.scopeTags || []).includes("task.write") ? "opacity-60 pointer-events-none" : ""
                          }`}
                        >
                          <input
                            type="checkbox"
                            id="allowNotes"
                            checked={formData?.allowNotes}
                            disabled={!(formData?.scopeTags || []).includes("task.write")}
                            onChange={(e) => setFormData({ ...formData, allowNotes: e.target.checked })}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200"
                          />
                          <label htmlFor="allowNotes" className="block text-sm text-gray-700 ml-3 cursor-pointer select-none">
                            Permitir Notas
                          </label>
                        </div>

                        <div
                          className={`flex items-center p-3 ${
                            (formData?.scopeTags || []).includes("network.write") ? "bg-white" : "bg-gray-200"
                          } rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer group ${
                            !(formData?.scopeTags || []).includes("network.write") ? "opacity-60 pointer-events-none" : ""
                          }`}
                        >
                          <input
                            type="checkbox"
                            id="allowNetworking"
                            checked={formData?.allowNetworking}
                            disabled={!(formData?.scopeTags || []).includes("network.write")}
                            onChange={(e) => setFormData({ ...formData, allowNetworking: e.target.checked })}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200"
                          />
                          <label htmlFor="allowNetworking" className="block text-sm text-gray-700 ml-3 cursor-pointer select-none">
                            Permitir Networking
                          </label>
                        </div>

                        <div
                          className={`flex items-center p-3 ${
                            (formData?.scopeTags || []).includes("emotionalAnalysis.write") ? "bg-white" : "bg-gray-200"
                          } rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer group ${
                            !(formData?.scopeTags || []).includes("emotionalAnalysis.write") ? "opacity-60 pointer-events-none" : ""
                          }`}
                        >
                          <input
                            type="checkbox"
                            id="allowEmotionalAnalysis"
                            checked={formData?.allowEmotionalAnalysis}
                            disabled={!(formData?.scopeTags || []).includes("emotionalAnalysis.write")}
                            onChange={(e) => setFormData({ ...formData, allowEmotionalAnalysis: e.target.checked })}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200"
                          />
                          <label htmlFor="allowEmotionalAnalysis" className="block text-sm text-gray-700 ml-3 cursor-pointer select-none">
                            Permitir Análise de Sentimentos
                          </label>
                        </div>

                        <div
                          className={`flex items-center p-3 ${
                            (formData?.scopeTags || []).includes("plan.read") ? "bg-white" : "bg-gray-200"
                          } rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer group ${
                            !(formData?.scopeTags || []).includes("plan.read") ? "opacity-60 pointer-events-none" : ""
                          }`}
                        >
                          <input
                            type="checkbox"
                            id="allowMentoredUserAnalytics"
                            checked={formData?.allowMentoredUserAnalytics}
                            disabled={!(formData?.scopeTags || []).includes("plan.read")}
                            onChange={(e) => setFormData({ ...formData, allowMentoredUserAnalytics: e.target.checked })}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded transition-colors duration-200"
                          />
                          <label htmlFor="allowMentoredUserAnalytics" className="block text-sm text-gray-700 ml-3 cursor-pointer select-none">
                            Permitir Analytics de Usuários Mentorados
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 4: // Parcelamento
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="recurrencyType" className="block text-sm font-medium text-gray-700 mb-1">
                Tipo de Recorrência
              </label>
              <select
                name="recurrencyType"
                id="recurrencyType"
                required
                value={formData?.recurrencyType === "month" ? (formData?.recurrencyPeriod === 6 ? "semester" : "month") : "year"}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === "semester") {
                    setFormData({
                      ...formData,
                      recurrencyType: "month",
                      recurrencyPeriod: 6,
                      statementDescriptor: generateStatementDescriptor(formData?.typePlan, "month", 6),
                    });
                  } else if (value === "year") {
                    setFormData({
                      ...formData,
                      recurrencyType: "year",
                      recurrencyPeriod: 1,
                      statementDescriptor: generateStatementDescriptor(formData?.typePlan, "year", 1),
                    });
                  } else {
                    setFormData({
                      ...formData,
                      recurrencyType: "month",
                      recurrencyPeriod: 1,
                      statementDescriptor: generateStatementDescriptor(formData?.typePlan, "month", 1),
                    });
                  }
                }}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                <option value="month">Mensal</option>
                <option value="semester">Semestral</option>
                <option value="year">Anual</option>
              </select>
            </div>

            <div>
              <label htmlFor="recurrencyPeriod" className="block text-sm font-medium text-gray-700 mb-1">
                Período de Recorrência
              </label>
              <input
                type="number"
                name="recurrencyPeriod"
                id="recurrencyPeriod"
                required
                min="1"
                max={formData?.recurrencyType === "year" ? 1 : 12}
                value={formData?.recurrencyPeriod}
                onChange={(e) => handleRecurrencyPeriodChange(Number(e.target.value))}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                disabled={formData?.recurrencyType === "year" || (formData?.recurrencyType === "month" && formData?.recurrencyPeriod === 6)}
              />
            </div>

            <div>
              <label htmlFor="installments" className="block text-sm font-medium text-gray-700 mb-1">
                Quant. Máxima de parcelas disponíveis
              </label>
              <select
                name="installments"
                id="installments"
                required
                value={Math.max(...formData?.installments)}
                onChange={(e) => handleInstallmentsChange(Number(e.target.value))}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((num) => (
                  <option key={num} value={num}>
                    {num}x
                  </option>
                ))}
              </select>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Planos</h1>
        <button
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Novo Plano
        </button>
      </div>

      <DraggableTable columns={columns} data={plans} onReorder={handleReorder} />

      {isModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-4 sm:align-middle sm:max-w-5xl sm:w-full">
              <div className="flex flex-col max-h-[90vh]">
                <div className="px-6 py-3 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">{editingPlan ? `Editar Plano - ${formData?.name}` : "Novo Plano"}</h3>
                    <button onClick={handleCloseModal} className="p-2 hover:bg-gray-100 rounded-full transition-colors" title="Fechar">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Steps Indicator */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between">
                      {[
                        { step: 1, label: "Informações do plano" },
                        { step: 2, label: "Permissionamento" },
                        { step: 3, label: "Regras de uso" },
                        { step: 4, label: "Parcelamento" },
                      ].map(({ step, label }) => (
                        <div key={step} className="flex flex-col items-center">
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                              step === currentStep
                                ? "bg-primary text-white"
                                : step < currentStep
                                ? "bg-green-500 text-white"
                                : "bg-gray-200 text-gray-600"
                            }`}
                          >
                            {step < currentStep ? "✓" : step}
                          </div>
                          <span className="mt-2 text-xs text-gray-500">{label}</span>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 relative">
                      <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gray-200 -translate-y-1/2" />
                      <div
                        className="absolute top-1/2 left-0 h-0.5 bg-primary -translate-y-1/2 transition-all duration-300"
                        style={{ width: `${((currentStep - 1) / 3) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>

                <form onSubmit={handleSubmit} className="flex flex-col flex-1 overflow-hidden">
                  <div className="flex-1 overflow-y-auto p-6">{renderStep()}</div>

                  <div className="flex justify-between items-center px-6 py-4 border-t border-gray-200 bg-white sticky bottom-0">
                    <div>
                      {currentStep > 1 && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            prevStep();
                          }}
                          className="p-2 text-white bg-primary hover:bg-red-600 rounded-full transition-colors"
                          title="Voltar"
                        >
                          <ArrowLeft size={20} />
                        </button>
                      )}
                    </div>

                    {currentStep === 3 && (
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center">
                          <span className="w-2 h-2 bg-gray-300 rounded-full mr-1"></span>
                          Campo vazio: Ilimitado
                        </div>
                        <div className="flex items-center">
                          <span className="w-2 h-2 bg-gray-300 rounded-full mr-1"></span>
                          Valor 0: Nenhum item
                        </div>
                        <div className="flex items-center">
                          <span className="w-2 h-2 bg-gray-300 rounded-full mr-1"></span>
                          Valores &gt; 0: Limite máximo
                        </div>
                      </div>
                    )}

                    <div>
                      {currentStep < 4 ? (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            nextStep();
                          }}
                          className="p-2 text-white bg-primary hover:bg-red-600 rounded-full transition-colors"
                          title="Próximo"
                        >
                          <ArrowRight size={20} />
                        </button>
                      ) : (
                        <button
                          type="submit"
                          className="w-32 inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600"
                        >
                          {editingPlan ? "Atualizar" : "Criar"}
                        </button>
                      )}
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
