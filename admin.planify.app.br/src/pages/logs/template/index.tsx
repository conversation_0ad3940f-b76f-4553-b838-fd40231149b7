import { Table } from "../../../components/Table";
import { TableColumns } from "../mocks/TableColumns";
import { useLogs } from "../hooks/useLogs";

export function LogsTemplate() {
  const { logs, loading } = useLogs();
  const columns = TableColumns();

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Logs de Erro</h1>
      </div>

      <div className="mt-4">
        <Table data={logs} columns={columns} loading={loading} emptyStateText="Nenhum log encontrado" />
      </div>
    </div>
  );
}
