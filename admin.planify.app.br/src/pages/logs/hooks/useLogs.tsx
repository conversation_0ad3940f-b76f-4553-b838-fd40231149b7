import { useState, useEffect } from "react";
import { TLog } from "../domain/models";
import { LogUseCases } from "../domain/services/log.useCases";
import { toast } from "react-toastify";

export function useLogs() {
  const [logs, setLogs] = useState<TLog[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const data = await LogUseCases.getLogs();
      setLogs(data || []);
    } catch (error) {
      console.error("Error fetching logs:", error);
      toast.error("Erro ao carregar logs");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, []);

  return {
    logs,
    loading,
  };
}
