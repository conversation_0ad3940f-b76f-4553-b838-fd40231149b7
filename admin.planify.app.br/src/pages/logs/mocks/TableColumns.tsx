import { TLog } from "../domain/models";
import { format } from "date-fns";

export function TableColumns() {
  const formatCurrency = (value: string) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(Number(value));
  };

  const getRecurrencyText = (type: string, period: number) => {
    if (type === "month") {
      return period === 1 ? "Mensal" : `${period} meses`;
    }
    return period === 1 ? "Anual" : `${period} anos`;
  };

  return [
    {
      id: "name",
      label: "Nome do Usuário",
      render: (rowData: TLog) => <p>{rowData?.user?.name ?? "-"}</p>,
    },
    {
      id: "plan",
      label: "Plano",
      render: (rowData: TLog) => (
        <div className="flex flex-col">
          <p className="font-medium">{rowData?.paymentPlan?.name}</p>
          <p className="text-xs text-gray-500">{getRecurrencyText(rowData?.paymentPlan?.recurrencyType, rowData?.paymentPlan?.recurrencyPeriod)}</p>
        </div>
      ),
    },
    {
      id: "amount",
      label: "Valor",
      align: "center" as const,
      render: (rowData: TLog) => <p>{formatCurrency(rowData?.paymentPlan.price)}</p>,
    },
    {
      id: "error",
      label: "Erro",
      render: (rowData: TLog) => {
        try {
          const logData = JSON.parse(rowData.log);
          return <p className="text-red-500">{logData.error}</p>;
        } catch {
          return <p className="text-red-500">{rowData.log}</p>;
        }
      },
    },
    {
      id: "transactionDate",
      label: "Data da Tentativa",
      align: "center" as const,
      render: (rowData: TLog) => <p>{format(new Date(rowData?.transactionDate), "dd/MM/yyyy HH:mm")}</p>,
    },
  ];
}
