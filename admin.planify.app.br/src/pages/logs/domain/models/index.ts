export interface TLog {
  id: string;
  customerId: string | null;
  customerDocument: string | null;
  cardId: string | null;
  gatewayPlanId: string | null;
  userId: string;
  user: {
    id: string;
    name: string;
  } | null;
  paymentPlan: {
    id: string;
    name: string;
    price: string;
    typePlan: string;
    recurrencyType: string;
    recurrencyPeriod: number;
  };
  hookType: string;
  amount: string;
  status: "failed";
  cycleReference: string | null;
  transactionDate: string;
  log: string;
  createdAt: string;
  updatedAt: string;
  expirationAt: string;
}
