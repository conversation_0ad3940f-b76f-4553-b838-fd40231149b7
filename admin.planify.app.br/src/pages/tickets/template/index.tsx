import { X } from "lucide-react";
import { Table } from "@/components/Table";
import { TableColumns } from "../mocks/TableColumns";
import { useState } from "react";
import { TTicketStatus } from "../domain/models";

type StatusFilterType = "all" | TTicketStatus;

export function Template({ ...sharedProps }) {
  const {
    tickets,
    loading,
    selectedTicket,
    isModalOpen,
    newMessage,
    setNewMessage,
    handleOpenTicket,
    handleCloseTicket,
    handleSendMessage,
    onUpdateStatus,
    getPaginatedTickets,
    handleStatusChange,
  } = sharedProps;

  const [activeStatus, setActiveStatus] = useState<StatusFilterType>("all");

  const statusTabs: { label: string; value: StatusFilterType }[] = [
    { label: "Todos", value: "all" },
    { label: "Abertos", value: "open" },
    { label: "Em Andamento", value: "in_progress" },
    { label: "Fechados", value: "closed" },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Tickets</h1>
      </div>

      <div className="mb-6">
        <nav className="flex" aria-label="Tabs">
          <div className="flex space-x-2">
            {statusTabs.map((tab) => (
              <button
                key={tab.value}
                onClick={() => {
                  handleStatusChange(tab.value === "all" ? null : tab.value);
                  setActiveStatus(tab.value);
                }}
                className={`px-6 py-3 text-sm font-medium rounded-lg ${
                  activeStatus === tab.value
                    ? "bg-red-50 text-red-600 border border-red-200"
                    : "bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100 border border-gray-200"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </nav>
      </div>

      <Table
        showPagination
        columns={TableColumns({
          onView: handleOpenTicket,
          onUpdateStatus,
        })}
        data={tickets?.data}
        defaultRows={tickets?.pageSize || 10}
        totalItems={tickets?.totalRecords}
        changeRowsPerPage={(pageSize) => getPaginatedTickets("pageSize", pageSize)}
        onChangePage={(page) => getPaginatedTickets("page", page)}
      />

      {isModalOpen && selectedTicket && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full sm:w-1/2">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-gray-900">{selectedTicket.title}</h3>
                  </div>
                  <button onClick={handleCloseTicket} className="text-gray-400 hover:text-gray-500">
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg max-h-[80vh] overflow-y-auto">
                    <div className="flex justify-start mb-4">
                      <div className="rounded-lg p-3 max-w-xs lg:max-w-md bg-gray-200">
                        <p className="text-sm">{selectedTicket.message}</p>
                        <p className="text-xs mt-1 opacity-70">{new Date(selectedTicket.createdAt).toLocaleString()}</p>
                      </div>
                    </div>
                    {selectedTicket?.imageUrls && selectedTicket?.imageUrls?.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4 ml-2">
                        {selectedTicket?.imageUrls?.map((imageUrl, index) => (
                          <a key={`${index}-${imageUrl}`} href={imageUrl} target="_blank" rel="noopener noreferrer" className="block">
                            <img
                              src={imageUrl}
                              alt={`Imagem ${index + 1}`}
                              className="h-24 w-auto object-cover rounded-lg border border-gray-300 hover:border-primary transition-colors"
                            />
                          </a>
                        ))}
                      </div>
                    )}
                    {selectedTicket?.conversationHistory?.map((message) => (
                      <div
                        key={`${message.userId}-${message.createdAt}`}
                        className={`flex ${message.userType === "admin" ? "justify-end" : "justify-start"} mb-4`}
                      >
                        <div
                          className={`rounded-lg p-3 max-w-xs lg:max-w-md ${message?.userType === "admin" ? "bg-primary text-white" : "bg-gray-200"}`}
                        >
                          <p className="text-sm">{message?.userMessage}</p>
                          <p className="text-xs mt-1 opacity-70">{new Date(message?.createdAt).toLocaleString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex space-x-4">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Digite sua mensagem..."
                      className="flex-1 min-w-0 rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary"
                      onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                    />
                    <button
                      onClick={handleSendMessage}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      Enviar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
