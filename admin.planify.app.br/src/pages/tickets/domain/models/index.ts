export type TTicketStatus = "open" | "in_progress" | "closed";

export type TTicketMessage = {
  userId: string;
  userName: string;
  userType: string;
  createdAt: string;
  userEmail: string;
  userMessage: string;
};

export type TTicket = {
  id: string;
  userId: string | null;
  name: string;
  email: string;
  cellphone: string;
  title: string;
  message: string;
  description: string | null;
  imageUrls: string[];
  conversationHistory: TTicketMessage[];
  status: TTicketStatus;
  createdAt: string;
  updatedAt: string;
};

export interface ITicketResponse {
  data: TTicket[];
}
