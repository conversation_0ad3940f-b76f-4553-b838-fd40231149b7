import HttpClient from "@infra/httpRequest";
import { TTicket } from "../models";
import { TPaginatedData } from "@/shared/types/paginatedData";

const service = new HttpClient();

export default class TicketsServices {
  async getTickets(): Promise<TPaginatedData> {
    const response = await service.get("/user-support");
    return response?.data;
  }

  async getTicketsPaginated(page: number = 1, pageSize: number = 10): Promise<TPaginatedData> {
    const response = await service.get(`/user-support?page=${page}&pageSize=${pageSize}`);
    return response?.data;
  }

  async getFilteredTicketsPaginated(status: string | null, page: number = 1, pageSize: number = 10): Promise<TPaginatedData> {
    const statusParam = status ? `&status=${status}` : "";
    const response = await service.get(`/user-support?page=${page}&pageSize=${pageSize}${statusParam}`);
    return response?.data;
  }

  async getTicketById(id: string): Promise<TTicket> {
    const response = await service.get(`/tickets/${id}`);
    return response?.data;
  }

  async createTicket(data: Omit<TTicket, "id" | "messages" | "createdAt" | "updatedAt">): Promise<TTicket> {
    const response = await service.post("/tickets", data);
    return response?.data;
  }

  async updateTicket(id: string, data: any): Promise<TTicket> {
    const response = await service.patch(`/user-support/${id}`, data);
    return response?.data;
  }

  async addMessage(ticketId: string, message: string): Promise<TTicket> {
    const response = await service.post(`/user-support/message/${ticketId}`, { message });
    return response?.data;
  }

  async closeTicket(ticketId: string): Promise<TTicket> {
    const response = await service.patch(`/user-support/close/${ticketId}`, null);
    return response?.data;
  }
}
