/* eslint-disable @typescript-eslint/no-explicit-any */
import { MessageCircle } from "lucide-react";
import { TTicket } from "../domain/models";

const statusConfig = {
  open: { label: "Aberto", bgColor: "bg-yellow-100", textColor: "text-yellow-800", borderColor: "border-yellow-200" },
  in_progress: { label: "Em Andamento", bgColor: "bg-blue-100", textColor: "text-blue-800", borderColor: "border-blue-200" },
  closed: { label: "Fechado", bgColor: "bg-green-100", textColor: "text-green-800", borderColor: "border-green-200" },
  pending: { label: "Pendente", bgColor: "bg-orange-100", textColor: "text-orange-800", borderColor: "border-orange-200" },
  canceled: { label: "Cancelado", bgColor: "bg-red-100", textColor: "text-red-800", borderColor: "border-red-200" },
};

export function TableColumns({ onView, onUpdateStatus }: any) {
  return [
    {
      id: "title",
      label: "Título",
      render: (rowData: TTicket) => <p className="font-medium">{rowData.title}</p>,
    },
    {
      id: "description",
      label: "Assunto",
      render: (rowData: TTicket) => (
        <p className="truncate max-w-xs" title={rowData.message}>
          {rowData.message}
        </p>
      ),
    },
    {
      id: "status",
      label: "Status",
      render: (rowData: TTicket) => {
        const config = statusConfig[rowData.status];

        return (
          <div className="flex items-center gap-2">
            <span
              className={`status-badge ${
                rowData.status === "open"
                  ? "warning"
                  : rowData.status === "closed"
                  ? "success"
                  : rowData.status === "in_progress"
                  ? "info"
                  : rowData.status === "pending"
                  ? "pending"
                  : "canceled"
              }`}
            >
              {config.label}
            </span>
            <select
              defaultValue={rowData.status}
              onChange={(e) => {
                const newStatus = e.target.value as TTicket["status"];
                onUpdateStatus({
                  id: rowData?.id,
                  status: newStatus,
                });
              }}
              className="block w-32 text-sm border border-gray-300 rounded-lg shadow-sm py-1.5 px-2 bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
            >
              {Object.entries(statusConfig).map(([value, { label }]) => (
                <option key={value} value={value}>
                  {label}
                </option>
              ))}
            </select>
          </div>
        );
      },
    },
    {
      id: "createdAt",
      label: "Data de Criação",
      render: (rowData: TTicket) => <p>{new Date(rowData.createdAt).toLocaleDateString()}</p>,
    },
    {
      id: "actions",
      label: "Ações",
      render: (rowData: TTicket) => (
        <div className="flex space-x-2">
          <button onClick={() => onView(rowData)} className="text-gray-600 hover:text-primary">
            <MessageCircle className="h-5 w-5" />
          </button>
        </div>
      ),
    },
  ];
}
