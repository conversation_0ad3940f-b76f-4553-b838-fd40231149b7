/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useCallback } from "react";
import { TicketsServices } from "../domain";
import { TTicket, TTicketStatus } from "../domain/models";
import { toast } from "react-toastify";
import { useGlobalStore } from "@/contexts/globalContext";

const ticketsService = new TicketsServices();

export default function useTickets() {
  const { tickets, getTickets, getTicketsPaginated, getFilteredTicketsPaginated } = useGlobalStore();
  const [loading, setLoading] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<TTicket | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  const handleOpenTicket = useCallback(async (ticket: TTicket) => {
    setSelectedTicket(ticket);
    setIsModalOpen(true);
  }, []);

  const handleCloseTicket = useCallback(() => {
    setSelectedTicket(null);
    setIsModalOpen(false);
    setNewMessage("");
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!selectedTicket || !newMessage.trim()) return;

    try {
      const message = newMessage;

      const updatedTicket = await ticketsService.addMessage(selectedTicket.id, message);
      setSelectedTicket(updatedTicket);
      setNewMessage("");
      await getTickets();
    } catch (error) {
      console.error("Error sending message:", error);
    }
  }, [selectedTicket, newMessage, getTickets]);

  const onUpdateStatus = useCallback(async ({ id, status }: any) => {
    try {
      setLoading(true);
      const response = await ticketsService.updateTicket(id, { status });
      await getTickets();

      if (response) {
        toast.success("Ticket atualizado com sucesso!");
      }
    } catch (error) {
      console.error("Error updating ticket status:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleStatusChange = useCallback(async (status: string | null) => {
    setSelectedStatus(status);
    try {
      await getFilteredTicketsPaginated(status, 1, tickets.pageSize);
    } catch (error) {
      console.error("Error fetching filtered tickets:", error);
      toast.error("Erro ao buscar tickets");
    }
  }, [getFilteredTicketsPaginated, tickets.pageSize]);

  const getPaginatedTickets = useCallback(
    (field: string, value: number) => {
      if (selectedStatus) {
        // Se tiver um filtro ativo, usa a função de filtro com paginação
        if (field === "page") {
          getFilteredTicketsPaginated(selectedStatus, value, tickets.pageSize);
        } else if (field === "pageSize") {
          getFilteredTicketsPaginated(selectedStatus, tickets.currentPage, value);
        }
      } else {
        // Se não tiver filtro, usa a paginação normal
        if (field === "page") {
          getTicketsPaginated(value, tickets.pageSize);
        } else if (field === "pageSize") {
          getTicketsPaginated(tickets.currentPage, value);
        }
      }
    },
    [getTicketsPaginated, getFilteredTicketsPaginated, selectedStatus, tickets.currentPage, tickets.pageSize]
  );

  return {
    tickets,
    loading,
    selectedTicket,
    isModalOpen,
    newMessage,
    setNewMessage,
    handleOpenTicket,
    handleCloseTicket,
    handleSendMessage,
    onUpdateStatus,
    getPaginatedTickets,
    selectedStatus,
    handleStatusChange,
  };
}
