import { TDiscount, originLabels, typeLabels } from "../domain/models";
import { Edit, Trash2, Clock, User } from "lucide-react";

interface TableColumnsProps {
  onEdit: (discount: TDiscount) => void;
  onDelete: (id: string) => void;
  onShowHistory: (discount: TDiscount) => void;
}

export function TableColumns({ onEdit, onDelete, onShowHistory }: TableColumnsProps) {
  return [
    {
      id: "discountCode",
      label: "CÓDIGO",
      render: (rowData: TDiscount) => <span className="font-mono text-sm">{rowData.discountCode}</span>,
    },
    {
      id: "type",
      label: "TIPO",
      render: (rowData: TDiscount) => <span className="capitalize">{typeLabels[rowData.type]}</span>,
    },
    {
      id: "value",
      label: "VALOR",
      render: (rowData: TDiscount) => <span>{rowData.type === "percentage" ? `${rowData.value}%` : `R$ ${rowData.value}`}</span>,
    },
    {
      id: "status",
      label: "STATUS",
      render: (rowData: TDiscount) => (
        <span
          className={`px-2 py-1 rounded text-xs font-semibold ${
            rowData.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-200 text-gray-500"
          }`}
        >
          {rowData.status === "active" ? "Ativo" : "Inativo"}
        </span>
      ),
    },
    {
      id: "ownerId",
      label: "CRIADO POR",
      render: (rowData: TDiscount) => (
        <div className="flex items-center">
          <User className="w-4 h-4 mr-1 text-gray-500" />
          <span>{rowData.owner?.name ?? "-"}</span>
        </div>
      ),
    },
    {
      id: "origin",
      label: "ORIGEM",
      render: (rowData: TDiscount) => <span>{originLabels[rowData.origin]}</span>,
    },
    {
      id: "maxUsageByUser",
      label: "LIMITE POR USUÁRIO",
      render: (rowData: TDiscount) => <span>{rowData.maxUsageByUser}</span>,
    },
    {
      id: "maxUsersLimit",
      label: "LIMITE TOTAL",
      render: (rowData: TDiscount) => <span>{rowData.maxUsersLimit}</span>,
    },
    {
      id: "expirationAt",
      label: "EXPIRAÇÃO",
      render: (rowData: TDiscount) => <span>{rowData.expirationAt ? new Date(rowData.expirationAt).toLocaleDateString() : "-"}</span>,
    },
    {
      id: "actions",
      label: "AÇÕES",
      align: "center",
      render: (rowData: TDiscount) => (
        <div className="flex gap-2 justify-center">
          <button onClick={() => onShowHistory(rowData)} className="text-blue-600 hover:text-blue-800" title="Histórico de uso">
            <Clock className="w-5 h-5" />
          </button>
          <button onClick={() => onEdit(rowData)} className="text-yellow-600 hover:text-yellow-700" title="Editar cupom">
            <Edit className="w-5 h-5" />
          </button>
          <button onClick={() => onDelete(rowData.id)} className="text-red-600 hover:text-red-700" title="Excluir cupom">
            <Trash2 className="w-5 h-5" />
          </button>
        </div>
      ),
    },
  ];
}
