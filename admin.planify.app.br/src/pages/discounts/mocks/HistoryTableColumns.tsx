import { TDiscountHistory, typeLabels, originLabels } from "../domain/models";

export function HistoryTableColumns() {
  return [
    {
      id: "allInfo",
      label: "INFORMAÇÕES DO CUPOM",
      render: (item: TDiscountHistory) => (
        <div>
          {item?.user?.name && <div className="font-medium">{item.user.name}</div>}
          {item?.user?.email && <div className="text-xs text-gray-500">{item.user.email}</div>}
          <div className="text-xs text-gray-400">ID: {item.userId}</div>

          <div className="mt-2">
            <div className="font-medium">
              Código: <span className="font-mono">{item?.code}</span>
            </div>
            <div className="text-xs text-gray-500">Tipo: {typeLabels[item?.type] || item?.type}</div>
            <div className="text-xs text-gray-500">Valor: {item?.type === "percentage" ? `${item?.value}%` : `R$ ${item?.value}`}</div>
            <div className="text-xs text-gray-500">Origem: {originLabels[item?.origin] || item?.origin}</div>
            <div className="text-xs text-gray-500">Data de uso: {new Date(item?.createdAt).toLocaleString()}</div>
          </div>
        </div>
      ),
    },
  ];
}
