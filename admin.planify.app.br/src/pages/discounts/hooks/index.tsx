import { useState, useCallback } from "react";
import { DiscountsServices } from "../domain";
import { TDiscount, TDiscountPayload, TDiscountHistory } from "../domain/models";
import { toast } from "react-toastify";

export default function useDiscounts() {
  const service = new DiscountsServices();
  const [pageData, setPageData] = useState<TDiscount[]>([]);
  const [discountHistory, setDiscountHistory] = useState<TDiscountHistory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [editingDiscount, setEditingDiscount] = useState<TDiscount | null>(null);
  const [discountDetails, setDiscountDetails] = useState<TDiscount | null>(null);

  async function getAll() {
    try {
      setIsLoading(true);
      const response = await service.get();
      setPageData(response);
    } catch (error) {
      toast.error("Erro ao carregar cupons de desconto");
    } finally {
      setIsLoading(false);
    }
  }

  async function getDiscountDetails(id: string) {
    try {
      setIsLoading(true);
      const historyResponse = await service.getDiscountHistory(id);
      setDiscountHistory(historyResponse);

      setDiscountDetails(historyResponse);
    } catch (error) {
      toast.error("Erro ao carregar detalhes do cupom");
      setDiscountDetails(null);
      setDiscountHistory([]);
    } finally {
      setIsLoading(false);
    }
  }

  const create = async (data: TDiscountPayload) => {
    try {
      setIsLoading(true);
      await service.create(data);
      toast.success("Cupom criado com sucesso!");
      setIsModalOpen(false);
      getAll();
    } catch (error) {
      toast.error("Erro ao criar cupom");
    } finally {
      setIsLoading(false);
    }
  };

  const edit = async (id: string, data: Partial<TDiscountPayload>) => {
    try {
      setIsLoading(true);
      await service.update(id, data);
      toast.success("Cupom atualizado com sucesso!");
      setIsModalOpen(false);
      setEditingDiscount(null);
      getAll();
    } catch (error) {
      toast.error("Erro ao atualizar cupom");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitDiscount = (data: TDiscountPayload) => {
    if (editingDiscount) {
      edit(editingDiscount.id, data);
    } else {
      create(data);
    }
  };

  const openEditModal = useCallback((discount: TDiscount) => {
    setEditingDiscount(discount);
    setIsModalOpen(true);
  }, []);

  const openCreateModal = useCallback(() => {
    setEditingDiscount(null);
    setIsModalOpen(true);
  }, []);

  const openHistoryModal = useCallback(async (discount: TDiscount) => {
    setIsHistoryModalOpen(true);
    await getDiscountDetails(discount.id);
  }, []);

  const deleteDiscount = async (id: string) => {
    try {
      setIsLoading(true);
      await service.delete(id);
      toast.success("Cupom excluído com sucesso!");
      getAll();
    } catch (error) {
      toast.error("Erro ao excluir cupom");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    pageData,
    discountHistory,
    isModalOpen,
    setIsModalOpen,
    isHistoryModalOpen,
    setIsHistoryModalOpen,
    editingDiscount,
    discountDetails,
    getAll,
    getDiscountDetails,
    create,
    edit,
    deleteDiscount,
    handleSubmitDiscount,
    openEditModal,
    openCreateModal,
    openHistoryModal,
  };
}
