import HttpClient from "@infra/httpRequest";
import { TDiscountPayload } from "../models";

const service = new HttpClient();

export default class DiscountsServices {
  async get() {
    const response = await service.get(`/discounts`);

    return response?.data;
  }

  async getById(id: string) {
    const response = await service.get(`/discounts/${id}`);

    return response?.data;
  }

  async getDiscountHistory(id: string) {
    const response = await service.get(`/discounts/${id}/history`);

    return response?.data;
  }

  async create(data: TDiscountPayload) {
    const response = await service.post(`/discounts`, data);

    return response?.data;
  }

  async update(id: string, data: Partial<TDiscountPayload>) {
    const response = await service.patch(`/discounts/${id}`, data);

    return response?.data;
  }

  async delete(id: string) {
    const response = await service.delete(`/discounts/${id}`);

    return response?.data;
  }
}
