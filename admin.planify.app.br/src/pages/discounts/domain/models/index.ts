// Adicione seus tipos e interfaces aqui. Exemplos:

export type TDiscountsSample = {
  name: string;
  age: number;
};

export interface IDiscountsUserData {
  user: TDiscountsSample;
  email: string;
}

// Tipos e interfaces para Cupons de Desconto e Histórico

export type TDiscount = {
  id: string;
  ownerId: string;
  origin: "internal" | "external" | "mentor" | "user" | "affiliates" | "custom";
  type: "percentage" | "fixed";
  value: number;
  status: "active" | "inactive";
  discountCode: string;
  allowInFirstPurchase: boolean;
  maxUsageByUser: number;
  maxUsersLimit: number;
  expiresIn: number;
  expirationAt: string | null;
  createdAt: string;
  updatedAt: string;
  owner?: {
    id: string;
    name: string;
    email: string;
  };
  history?: TDiscountHistory[];
};

export const originLabels: Record<string, string> = {
  internal: "Interna",
  external: "Externa",
  mentor: "Mentor",
  user: "Usu<PERSON><PERSON>",
  affiliates: "Afiliados",
  custom: "Personalizado",
};

export const typeLabels: Record<string, string> = {
  percentage: "Percentual",
  fixed: "Fixo",
};

export type TDiscountPayload = Omit<TDiscount, "id" | "status" | "ownerId" | "expirationAt" | "createdAt" | "updatedAt">;

export type TDiscountHistory = {
  id: string;
  discountId: string;
  ownerId: string;
  userId: string;
  userName?: string;
  userEmail?: string;
  origin: string;
  type: string;
  value: number;
  code: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
};

export interface TemplatePageProps {
  pageData: TDiscount[];
  isLoading: boolean;
  getDiscounts: () => void;
  discountHistory: TDiscountHistory[];
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isHistoryModalOpen: boolean;
  setIsHistoryModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  editingDiscount: TDiscount | null;
  selectedDiscount: TDiscount | null;
  discountDetails: TDiscount | null;
  handleSubmitDiscount: (data: TDiscountPayload) => void;
  handleDeleteDiscount: (id: string) => void;
  openEditModal: (discount: TDiscount) => void;
  openCreateModal: () => void;
  openHistoryModal: (discount: TDiscount) => void;
}
