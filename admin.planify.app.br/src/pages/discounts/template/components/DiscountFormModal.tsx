import React, { useEffect } from "react";
import { X } from "lucide-react";
import { TDiscount, TDiscountPayload, originLabels, typeLabels } from "../../domain/models";

interface DiscountFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: TDiscountPayload) => void;
  editingDiscount: TDiscount | null;
}

export function DiscountFormModal({ isOpen, onClose, onSubmit, editingDiscount }: DiscountFormModalProps) {
  const [formData, setFormData] = React.useState<TDiscountPayload>({
    origin: "internal",
    type: "percentage",
    value: 0,
    discountCode: "",
    maxUsageByUser: 1,
    maxUsersLimit: 30,
    expiresIn: 60,
    allowInFirstPurchase: true,
  });

  useEffect(() => {
    if (editingDiscount) {
      setFormData({
        origin: editingDiscount.origin,
        type: editingDiscount.type,
        value: editingDiscount.value,
        discountCode: editingDiscount.discountCode,
        maxUsageByUser: editingDiscount.maxUsageByUser,
        maxUsersLimit: editingDiscount.maxUsersLimit,
        expiresIn: editingDiscount.expiresIn,
        allowInFirstPurchase: editingDiscount.allowInFirstPurchase,
      });
    }
  }, [editingDiscount]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "number" ? Number(value) : type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed z-50 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen p-4 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true" onClick={onClose}>
          <div className="absolute inset-0 bg-black bg-opacity-75"></div>
        </div>

        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full sm:p-6"
          onClick={(e) => e.stopPropagation()}
        >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">{editingDiscount ? "Editar Cupom de Desconto" : "Novo Cupom de Desconto"}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
            <X className="h-6 w-6" />
          </button>
        </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="discountCode" className="block text-sm font-medium text-gray-700">
                Código do Cupom
              </label>
              <input
                type="text"
                id="discountCode"
                name="discountCode"
                required
                value={formData.discountCode}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                placeholder="Ex: PLANIFY_10"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                  Tipo de Desconto
                </label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                >
                  <option value="percentage">{typeLabels.percentage} (%)</option>
                  <option value="fixed">{typeLabels.fixed} (R$)</option>
                </select>
              </div>

              <div>
                <label htmlFor="value" className="block text-sm font-medium text-gray-700">
                  Valor
                </label>
                <input
                  type="number"
                  id="value"
                  name="value"
                  required
                  min="1"
                  value={formData.value}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label htmlFor="origin" className="block text-sm font-medium text-gray-700">
                Origem
              </label>
              <select
                id="origin"
                name="origin"
                value={formData.origin}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                <option value="internal">{originLabels.internal}</option>
                <option value="external">{originLabels.external}</option>
                <option value="mentor">{originLabels.mentor}</option>
                <option value="user">{originLabels.user}</option>
                <option value="affiliates">{originLabels.affiliates}</option>
                <option value="custom">{originLabels.custom}</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="maxUsageByUser" className="block text-sm font-medium text-gray-700">
                  Uso máximo por usuário
                </label>
                <input
                  type="number"
                  id="maxUsageByUser"
                  name="maxUsageByUser"
                  required
                  min="1"
                  value={formData.maxUsageByUser}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="maxUsersLimit" className="block text-sm font-medium text-gray-700">
                  Limite de usuários
                </label>
                <input
                  type="number"
                  id="maxUsersLimit"
                  name="maxUsersLimit"
                  required
                  min="1"
                  value={formData.maxUsersLimit}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label htmlFor="expiresIn" className="block text-sm font-medium text-gray-700">
                Expira em (dias)
              </label>
              <input
                type="number"
                id="expiresIn"
                name="expiresIn"
                required
                min="1"
                value={formData.expiresIn}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="allowInFirstPurchase"
                name="allowInFirstPurchase"
                checked={formData.allowInFirstPurchase}
                onChange={handleChange}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label htmlFor="allowInFirstPurchase" className="ml-2 block text-sm text-gray-700">
                Aplicar desconto apenas na primeira cobrança?
              </label>
            </div>

            <div className="mt-5">
              <button
                type="submit"
                className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                {editingDiscount ? "Atualizar Cupom" : "Criar Cupom"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
