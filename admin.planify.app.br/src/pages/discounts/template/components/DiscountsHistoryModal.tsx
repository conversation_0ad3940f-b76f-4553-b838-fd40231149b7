import { X } from "lucide-react";
import { TDiscount, TDiscountHistory, originLabels, typeLabels } from "../../domain/models";
import { Table } from "@/components/Table";
import { HistoryTableColumns } from "../../mocks/HistoryTableColumns";

interface DiscountsHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  discountDetails: TDiscount | null;
  isLoading?: boolean;
}

export function DiscountsHistoryModal({ isOpen, onClose, discountDetails, isLoading = false }: DiscountsHistoryModalProps) {
  if (!isOpen) return null;

  const historyTableColumns = HistoryTableColumns();

  return (
    <div className="fixed z-50 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen p-4 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true" onClick={onClose}>
          <div className="absolute inset-0 bg-black bg-opacity-75"></div>
        </div>

        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Detalhes do Cupom</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>

          {discountDetails && (
            <div className="mt-6 space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-500">Informações do Cupom</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Código</p>
                    <p className="text-sm font-medium font-mono">{discountDetails.discountCode}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">ID</p>
                    <p className="text-sm font-medium font-mono text-xs">{discountDetails.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Origem</p>
                    <p className="text-sm font-medium">{originLabels[discountDetails.origin]}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Tipo</p>
                    <p className="text-sm font-medium">{typeLabels[discountDetails.type]}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Valor</p>
                    <p className="text-sm font-medium">
                      {discountDetails.type === "percentage" ? `${discountDetails.value}%` : `R$ ${discountDetails.value}`}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        discountDetails.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }`}
                    >
                      {discountDetails.status === "active" ? "Ativo" : "Inativo"}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Permitido apenas na primeira compra?</p>
                    <p className="text-sm font-medium">{discountDetails.allowInFirstPurchase ? "Sim" : "Não"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Limite por usuário</p>
                    <p className="text-sm font-medium">{discountDetails.maxUsageByUser}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Limite total de usuários</p>
                    <p className="text-sm font-medium">{discountDetails.maxUsersLimit}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Expira em (dias)</p>
                    <p className="text-sm font-medium">{discountDetails.expiresIn}</p>
                  </div>
                  {discountDetails.expirationAt && (
                    <div>
                      <p className="text-sm text-gray-500">Data de expiração</p>
                      <p className="text-sm font-medium">{new Date(discountDetails.expirationAt).toLocaleDateString()}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-sm text-gray-500">Criado em</p>
                    <p className="text-sm font-medium">{new Date(discountDetails.createdAt).toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-500 mb-4">Histórico de uso do cupom</h4>
            <Table columns={historyTableColumns} data={discountDetails?.history || []} loading={isLoading} emptyStateText="Nenhum uso registrado." />
          </div>
        </div>
      </div>
    </div>
  );
}
