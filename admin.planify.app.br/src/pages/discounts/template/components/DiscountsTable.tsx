import { TDiscount } from "../../domain/models";
import { TableColumns } from "../../mocks/TableColumns";
import { Table } from "@/components/Table";

interface DiscountsTableProps {
  discounts: TDiscount[];
  isLoading: boolean;
  onEdit: (discount: TDiscount) => void;
  onDelete: (id: string) => void;
  onShowHistory: (discount: TDiscount) => void;
}

export function DiscountsTable({ discounts, isLoading, onEdit, onDelete, onShowHistory }: DiscountsTableProps) {
  const columns = TableColumns({ onEdit, onDelete, onShowHistory });

  return <Table columns={columns} data={discounts} loading={isLoading} emptyStateText="Nenhum cupom de desconto encontrado" />;
}
