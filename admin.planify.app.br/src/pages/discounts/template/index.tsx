import { DiscountsTable } from "./components/DiscountsTable";
import { DiscountsHistoryModal } from "./components/DiscountsHistoryModal";
import { DiscountFormModal } from "./components/DiscountFormModal";
import { Plus } from "lucide-react";
import { TemplatePageProps } from "../domain/models";

export default function TemplatePage({
  pageData = [],
  isLoading,
  isModalOpen,
  setIsModalOpen,
  isHistoryModalOpen,
  setIsHistoryModalOpen,
  editingDiscount,
  discountDetails,
  handleSubmitDiscount,
  handleDeleteDiscount,
  openEditModal,
  openCreateModal,
  openHistoryModal,
}: TemplatePageProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Cupons de Desconto</h1>
        <button
          onClick={openCreateModal}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Novo Cupom
        </button>
      </div>

      <DiscountsTable
        discounts={pageData}
        isLoading={isLoading}
        onEdit={openEditModal}
        onDelete={handleDeleteDiscount}
        onShowHistory={openHistoryModal}
      />

      <DiscountsHistoryModal
        isOpen={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        discountDetails={discountDetails}
        isLoading={isLoading}
      />

      <DiscountFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleSubmitDiscount}
        editingDiscount={editingDiscount}
      />
    </div>
  );
}
