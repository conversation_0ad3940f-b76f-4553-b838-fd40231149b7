import { useEffect } from "react";
import TemplatePage from "./template";
import useDiscounts from "./hooks";

export default function Discounts() {
  const hookParams = useDiscounts();
  const { getAll } = hookParams;

  const sharedProps = {
    ...hookParams,
    handleDeleteDiscount: hookParams.deleteDiscount,
  };

  useEffect(() => {
    getAll();
  }, []);

  return (
    <>
      <TemplatePage {...sharedProps} />
    </>
  );
}
