import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { TemplatesServices } from "../domain";
import { Template, CreateTemplateData } from "../domain/models";
import { toast } from "react-toastify";

export default function useTemplates() {
  const service = new TemplatesServices();
  const [pageData, setPageData] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isViewMode, setIsViewMode] = useState(false);
  const [formData, setFormData] = useState<CreateTemplateData>({
    identificationTag: "",
    name: "",
    type: "email",
    description: "",
    content: "",
  });

  const [isSendModalOpen, setIsSendModalOpen] = useState(false);
  const [sendFormData, setSendFormData] = useState({
    templateId: "",
    sendToAll: false,
    sendToGroup: false,
    sendToSpecific: false,
    userGroup: "",
    selectedUsers: [],
  });
  const [users, setUsers] = useState([]);

  const navigate = useNavigate();

  const [paymentPlans, setPaymentPlans] = useState([]);

  useEffect(() => {
    if (isSendModalOpen) {
      fetchUsers();
      fetchPaymentPlans();
    }
  }, [isSendModalOpen]);

  async function fetchPaymentPlans() {
    try {
      const response = await service.get("/payment-plans");
      setPaymentPlans(response || []);
    } catch (error) {
      toast.error("Erro ao buscar planos");
    }
  }

  async function fetchUsers() {
    try {
      setIsLoading(true);
      const response = await service.getUsers();
      setUsers(response);
    } catch (error) {
      toast.error("Erro ao buscar usuários");
    } finally {
      setIsLoading(false);
    }
  }

  const filteredTemplates = pageData.filter((template) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      template.name.toLowerCase().includes(searchLower) ||
      template.description.toLowerCase().includes(searchLower) ||
      template.type.toLowerCase().includes(searchLower)
    );
  });

  async function getTemplates() {
    setIsLoading(true);
    try {
      const response = await service.get();
      setPageData(response);
    } catch (error) {
      console.error("Erro ao buscar templates:", error);
    } finally {
      setIsLoading(false);
    }
  }

  async function createTemplate(data: CreateTemplateData) {
    setIsLoading(true);
    try {
      const response = await service.create(data);
      await getTemplates();
      setIsModalOpen(false);

      if (response) {
        resetForm();
        toast.success("Template criado com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao criar template:", error);
      toast.error("Erro ao criar template");
    } finally {
      setIsLoading(false);
    }
  }

  async function updateTemplate(id: string, data: Partial<CreateTemplateData>) {
    setIsLoading(true);
    try {
      await service.update(id, data);
      await getTemplates();
      setIsModalOpen(false);
      setSelectedTemplate(null);
      resetForm();
      toast.success("Template atualizado com sucesso!");
    } catch (error) {
      console.error("Erro ao atualizar template:", error);
      toast.error("Erro ao atualizar template");
    } finally {
      setIsLoading(false);
    }
  }

  async function deleteTemplate(id: string) {
    setIsLoading(true);
    try {
      await service.delete(id);
      await getTemplates();
      toast.success("Template removido com sucesso!");
    } catch (error) {
      console.error("Erro ao deletar template:", error);
      toast.error("Erro ao remover template");
    } finally {
      setIsLoading(false);
    }
  }

  function handleEdit(template: Template) {
    setSelectedTemplate(template);
    setFormData({
      identificationTag: template.identificationTag,
      name: template.name,
      type: template.type,
      description: template.description,
      content: template.content,
    });
    setIsViewMode(false);
    setIsModalOpen(true);
  }

  function handleView(template: Template) {
    setSelectedTemplate(template);
    setFormData({
      identificationTag: template.identificationTag,
      name: template.name,
      type: template.type,
      description: template.description,
      content: template.content,
    });
    setIsViewMode(true);
    setIsModalOpen(true);
  }

  function handleCreate() {
    setSelectedTemplate(null);
    setFormData({
      identificationTag: "",
      name: "",
      type: "email",
      description: "",
      content: "",
    });
    setIsViewMode(false);
    setIsModalOpen(true);
  }

  function handleClearSearch() {
    setSearchTerm("");
  }

  function resetForm() {
    setFormData({
      identificationTag: "",
      name: "",
      type: "email",
      description: "",
      content: "",
    });
    setSelectedTemplate(null);
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (selectedTemplate) {
        await updateTemplate(selectedTemplate.id, formData);
      } else {
        await createTemplate(formData);
      }
    } catch (error) {
      console.error("Erro ao salvar o template:", error);
    }
  };

  const handleDelete = (id) => {
    if (window.confirm("Tem certeza que deseja excluir este template?")) {
      deleteTemplate(id);
    }
  };

  const handleOpenSendModal = () => {
    setIsSendModalOpen(true);
    setSendFormData({
      templateId: "",
      sendToAll: false,
      sendToGroup: false,
      sendToSpecific: false,
      userGroup: "",
      selectedUsers: [],
    });
  };

  const handleSendTypeChange = (type) => {
    setSendFormData({
      ...sendFormData,
      sendToAll: type === "all",
      sendToGroup: type === "group",
      sendToSpecific: type === "specific",
      userGroup: type === "group" ? sendFormData.userGroup : "",
      selectedUsers: type === "specific" ? sendFormData.selectedUsers : [],
    });
  };

  const handleUserSelection = (userId) => {
    const isSelected = sendFormData.selectedUsers.includes(userId);
    if (isSelected) {
      setSendFormData({
        ...sendFormData,
        selectedUsers: sendFormData.selectedUsers.filter((id) => id !== userId),
      });
    } else {
      setSendFormData({
        ...sendFormData,
        selectedUsers: [...sendFormData.selectedUsers, userId],
      });
    }
  };

  const handleSendTemplate = async (e) => {
    e.preventDefault();
    try {
      // Validações
      if (!sendFormData.templateId) {
        toast.error("Selecione um template para enviar");
        return;
      }

      if (!sendFormData.sendToAll && !sendFormData.sendToGroup && !sendFormData.sendToSpecific) {
        toast.error("Selecione pelo menos um tipo de destinatário");
        return;
      }

      if (sendFormData.sendToGroup && !sendFormData.userGroup) {
        toast.error("Selecione um grupo de usuários");
        return;
      }

      if (sendFormData.sendToSpecific && sendFormData.selectedUsers.length === 0) {
        toast.error("Selecione pelo menos um usuário específico");
        return;
      }

      setIsLoading(true);
      const selectedTemplate = pageData.find((t) => t.id === sendFormData.templateId);
      let payload = {};

      if (sendFormData.sendToSpecific) {
        const selectedEmails = users.filter((u) => sendFormData.selectedUsers.includes(u.id)).map((u) => u.email);
        payload = {
          templateType: selectedTemplate?.identificationTag,
          recipientsType: "specific",
          emails: selectedEmails,
        };
      } else if (sendFormData.sendToAll) {
        payload = {
          templateType: selectedTemplate?.identificationTag,
          recipientsType: "all",
        };
      } else if (sendFormData.sendToGroup) {
        const selectedPlan = paymentPlans.find((p) => p.id === sendFormData.userGroup);
        payload = {
          templateType: selectedTemplate?.identificationTag,
          recipientsType: "group",
          groupType: selectedPlan?.typePlan,
        };
      }

      await service.post("/network-services/batch", payload);
      toast.success("Template enviado com sucesso!");
      setIsSendModalOpen(false);
    } catch (error) {
      console.error("Erro ao enviar template:", error);
      toast.error("Erro ao enviar template");
    } finally {
      setIsLoading(false);
    }
  };

  console.log("formData", formData);
  console.log("sendFormData", sendFormData);

  return {
    isLoading,
    setIsLoading,
    formData,
    setFormData,
    pageData: filteredTemplates,
    selectedTemplate,
    isModalOpen,
    setIsModalOpen,
    isViewMode,
    setIsViewMode,
    handleSubmit,
    navigate,
    getTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    handleEdit,
    handleView,
    handleCreate,
    searchTerm,
    setSearchTerm,
    handleClearSearch,
    resetForm,
    handleDelete,
    // Propriedades e funções para o modal de envio
    isSendModalOpen,
    setIsSendModalOpen,
    sendFormData,
    setSendFormData,
    users,
    handleOpenSendModal,
    handleSendTypeChange,
    handleUserSelection,
    handleSendTemplate,
    paymentPlans,
  };
}
