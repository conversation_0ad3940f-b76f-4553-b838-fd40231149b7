import { Plus, X, Search, Send } from "lucide-react";
import { Table } from "@/components/Table";
import { TableColumns } from "../mocks/TableColumns";
import { HtmlEditor } from "@/components/HtmlEditor";

export default function TemplatePage({ ...sharedProps }) {
  const {
    isLoading,
    formData,
    setFormData,
    pageData,
    selectedTemplate,
    isModalOpen,
    setIsModalOpen,
    isViewMode,
    handleSubmit,
    handleDelete,
    handleEdit,
    handleView,
    handleCreate,
    searchTerm,
    setSearchTerm,
    handleClearSearch,
    isSendModalOpen,
    setIsSendModalOpen,
    sendFormData,
    setSendFormData,
    users,
    handleOpenSendModal,
    handleSendTypeChange,
    handleUserSelection,
    handleSendTemplate,
    paymentPlans,
  } = sharedProps;

  return (
    <div>
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">Templates</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleOpenSendModal}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <Send className="h-4 w-4 mr-2" />
            Enviar Template
          </button>
          <button
            onClick={handleCreate}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600"
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Template
          </button>
        </div>
      </div>

      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Buscar por nome, descrição ou tipo..."
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm"
          />
          {searchTerm && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button onClick={handleClearSearch} className="text-gray-400 hover:text-gray-500 focus:outline-none">
                <X className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="mt-6">
        <Table
          columns={TableColumns({ handleEdit, handleDelete, handleView })}
          data={pageData}
          loading={isLoading}
          emptyStateText="Nenhum template encontrado"
        />
      </div>

      {isSendModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Enviar Template</h2>
              <button onClick={() => setIsSendModalOpen(false)} className="text-gray-500 hover:text-gray-700">
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSendTemplate}>
              <div className="grid grid-cols-1 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Selecione o Template</label>
                  <select
                    value={sendFormData.templateId}
                    onChange={(e) => setSendFormData({ ...sendFormData, templateId: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    required
                  >
                    <option value="">Selecione um template</option>
                    {pageData.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name} ({template.type})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="border-t pt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Destinatários</label>

                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="sendToAll"
                        checked={sendFormData.sendToAll}
                        onChange={() => handleSendTypeChange("all")}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <label htmlFor="sendToAll" className="ml-2 block text-sm text-gray-900">
                        Enviar para todos os usuários
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="sendToGroup"
                        checked={sendFormData.sendToGroup}
                        onChange={() => handleSendTypeChange("group")}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <label htmlFor="sendToGroup" className="ml-2 block text-sm text-gray-900">
                        Enviar para grupo de usuários
                      </label>
                    </div>

                    {sendFormData.sendToGroup && (
                      <div className="ml-6 mt-2">
                        <select
                          value={sendFormData.userGroup}
                          onChange={(e) => setSendFormData({ ...sendFormData, userGroup: e.target.value })}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                          required={sendFormData.sendToGroup}
                        >
                          <option value="">Selecione um grupo</option>
                          {paymentPlans && paymentPlans.length > 0 ? (
                            paymentPlans.map((plan) => {
                              let recurrency = "";

                              if (plan.recurrencyType === "year" && plan.typePlan !== "start" && plan.typePlan !== "view") {
                                recurrency = "Anual";
                              }

                              if (plan.recurrencyType === "month" && plan.recurrencyPeriod === 6) {
                                recurrency = "Semestral";
                              }
                              if (plan.recurrencyType === "month" && plan.recurrencyPeriod === 1) {
                                recurrency = "Mensal";
                              }

                              return (
                                <option key={plan.id} value={plan.id}>
                                  Usuários {plan.name} {recurrency ? `- ${recurrency}` : ""}
                                </option>
                              );
                            })
                          ) : (
                            <>
                              <option value="free">Usuários Free</option>
                              <option value="start">Usuários Start</option>
                              <option value="standard">Usuários Standard</option>
                              <option value="premium">Usuários Premium</option>
                              <option value="ultimate">Usuários Ultimate</option>
                            </>
                          )}
                        </select>
                      </div>
                    )}

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="sendToSpecific"
                        checked={sendFormData.sendToSpecific}
                        onChange={() => handleSendTypeChange("specific")}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <label htmlFor="sendToSpecific" className="ml-2 block text-sm text-gray-900">
                        Enviar para usuários específicos
                      </label>
                    </div>

                    {sendFormData.sendToSpecific && (
                      <div className="ml-6 mt-2 border border-gray-200 rounded-md p-3 max-h-60 overflow-y-auto">
                        <div className="space-y-2">
                          {users.map((user) => (
                            <div key={user.id} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`user-${user.id}`}
                                checked={sendFormData.selectedUsers.includes(user.id)}
                                onChange={() => handleUserSelection(user.id)}
                                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                              />
                              <label htmlFor={`user-${user.id}`} className="ml-2 block text-sm text-gray-900">
                                {user.name} <span className="text-gray-500 text-xs">({user.email})</span>
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => setIsSendModalOpen(false)}
                  disabled={isLoading}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600 disabled:opacity-70 disabled:cursor-not-allowed flex items-center"
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Enviando...
                    </>
                  ) : (
                    "Enviar"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">{isViewMode ? "Visualizar Template" : selectedTemplate ? "Editar Template" : "Novo Template"}</h2>
              <button onClick={() => setIsModalOpen(false)} className="text-gray-500 hover:text-gray-700">
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => !isViewMode && setFormData({ ...formData, name: e.target.value })}
                    disabled={isViewMode}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tag de Identificação</label>
                  <input
                    type="text"
                    value={formData.identificationTag}
                    onChange={(e) => !isViewMode && setFormData({ ...formData, identificationTag: e.target.value })}
                    disabled={isViewMode}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
                  <select
                    value={formData.type}
                    onChange={(e) => !isViewMode && setFormData({ ...formData, type: e.target.value })}
                    disabled={isViewMode}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    required
                  >
                    <option value="email">Email</option>
                    <option value="sms">SMS</option>
                    <option value="push">Push</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => !isViewMode && setFormData({ ...formData, description: e.target.value })}
                    disabled={isViewMode}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Conteúdo</label>
                  <HtmlEditor
                    value={formData.content}
                    onChange={(content) => !isViewMode && setFormData({ ...formData, content })}
                    isPreview={isViewMode}
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  {isViewMode ? "Fechar" : "Cancelar"}
                </button>
                {!isViewMode && (
                  <button
                    type="submit"
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-red-600"
                  >
                    {selectedTemplate ? "Atualizar" : "Criar"}
                  </button>
                )}
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
