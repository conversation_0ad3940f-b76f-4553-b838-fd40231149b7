import HttpClient from "@infra/httpRequest";

const service = new HttpClient();

export interface Template {
  id: string;
  identificationTag: string;
  name: string;
  type: string;
  description: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTemplateData {
  identificationTag: string;
  name: string;
  type: string;
  description: string;
  content: string;
}

export interface ApiResponse<T> {
  data: T;
}

export default class TemplatesServices {
  async get(path?: string) {
    const endpoint = path ? path : `/templates`;
    const response = await service.get(endpoint);

    return response?.data;
  }

  async getById(id: string) {
    const response = await service.get(`/templates/${id}`);

    return response?.data;
  }

  async create(data: CreateTemplateData) {
    const response = await service.post(`/templates`, data);

    return response?.data;
  }

  async update(id: string, data: Partial<CreateTemplateData>) {
    const response = await service.patch(`/templates/${id}`, data);

    return response?.data;
  }

  async delete(id: string) {
    const response = await service.delete(`/templates/${id}`);

    return response?.data;
  }

  async getUsers() {
    const response = await service.get(`/users`);
    return response?.data;
  }

  async post(path: string, data: any) {
    const response = await service.post(path, data);
    return response?.data;
  }
}
