import { Edit, Trash2, Eye } from "lucide-react";
import { Template } from "../domain/models";

interface TableColumnsProps {
  handleEdit: (template: Template) => void;
  handleDelete: (id: string) => void;
  handleView?: (template: Template) => void;
}

export function TableColumns({ handleEdit, handleDelete, handleView }: TableColumnsProps) {
  return [
    {
      id: "name",
      label: "Nome",
      render: (rowData: Template) => <div className="text-sm text-gray-500">{rowData.name}</div>,
    },
    {
      id: "type",
      label: "Tipo",
      render: (rowData: Template) => <div className="text-sm text-gray-500">{rowData.type}</div>,
    },
    {
      id: "identificationTag",
      label: "Tag de Identificação",
      render: (rowData: Template) => <div className="text-sm text-gray-500">{rowData.identificationTag}</div>,
    },
    {
      id: "description",
      label: "Descrição",
      render: (rowData: Template) => (
        <div className="text-sm text-gray-500 max-w-[300px] truncate" title={rowData.description}>
          {rowData.description}
        </div>
      ),
    },
    {
      align: "center",
      id: "actions",
      label: "Ações",
      render: (rowData: Template) => (
        <div className="flex gap-2 justify-center">
          <button onClick={() => (handleView ? handleView(rowData) : handleEdit(rowData))} className="text-gray-800 hover:text-black">
            <Eye size={20} />
          </button>
          <button onClick={() => handleEdit(rowData)} className="text-yellow-600 hover:text-yellow-700">
            <Edit size={20} />
          </button>
          <button onClick={() => handleDelete(rowData.id)} className="text-red-600 hover:text-red-700">
            <Trash2 size={20} />
          </button>
        </div>
      ),
    },
  ];
}
