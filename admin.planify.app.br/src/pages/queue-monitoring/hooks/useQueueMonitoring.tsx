import { useState, useEffect } from "react";
import { QueueData } from "../domain/models";
import { QueueUseCases } from "../domain/services/queue.useCases";

export function useQueueMonitoring() {
  const [queues, setQueues] = useState<QueueData[]>([]);
  const [selectedQueue, setSelectedQueue] = useState<string | null>(null);
  const [queueData, setQueueData] = useState<QueueData | null>(null);
  const [loading, setLoading] = useState(true);

  const getQueues = async () => {
    setLoading(true);
    try {
      const data = await QueueUseCases.getQueues();
      setQueues(data);
    } catch (error) {
      console.error("Error fetching queues:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let cleanup: (() => void) | null = null;

    if (selectedQueue) {
      cleanup = QueueUseCases.monitorQueue(
        selectedQueue,
        (data) => setQueueData(data),
        (error) => {
          console.error("SSE connection error:", error);

          setTimeout(() => {
            if (selectedQueue) {
              setSelectedQueue(selectedQueue);
            }
          }, 5000);
        }
      );
    }

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [selectedQueue]);

  const handleQueueSelect = (queueName: string) => {
    setSelectedQueue(queueName);
  };

  return {
    queues,
    selectedQueue,
    queueData,
    loading,
    handleQueueSelect,
    getQueues,
  };
}
