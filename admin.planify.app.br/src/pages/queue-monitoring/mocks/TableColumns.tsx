import { QueueData } from "../domain/models";

export function TableColumns() {
  return [
    {
      id: "name",
      label: "Queue Name",
      render: (rowData: QueueData) => (
        <span className="font-medium text-gray-900">{rowData.name}</span>
      ),
    },
    {
      id: "active",
      label: "Active",
      align: "center",
      render: (rowData: QueueData) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {rowData.active}
        </span>
      ),
    },
    {
      id: "waiting",
      label: "Waiting",
      align: "center",
      render: (rowData: QueueData) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          {rowData.waiting}
        </span>
      ),
    },
    {
      id: "completed",
      label: "Completed",
      align: "center",
      render: (rowData: QueueData) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          {rowData.completed}
        </span>
      ),
    },
    {
      id: "failed",
      label: "Failed",
      align: "center",
      render: (rowData: QueueData) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          {rowData.failed}
        </span>
      ),
    },
    {
      id: "delayed",
      label: "Delayed",
      align: "center",
      render: (rowData: QueueData) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          {rowData.delayed}
        </span>
      ),
    },
  ];
}