import { useEffect } from "react";
import { useQueueMonitoring } from "./hooks/useQueueMonitoring";
import { Template } from "./template";

export default function QueueMonitoringPage() {
  const hookParams = useQueueMonitoring();

  const { getQueues } = hookParams;
  const sharedProps = {
    ...hookParams,
  };

  useEffect(() => {
    getQueues();
  }, []);

  return <Template {...sharedProps} />;
}
