import { QueueData } from "../domain/models";

import { useState } from "react";

type TemplateProps = {
  queues: QueueData[];
  selectedQueue: string | null;
  queueData: QueueData | null;
  loading: boolean;
  handleQueueSelect: (queueName: string) => void;
};

export function Template({ queues, selectedQueue, queueData, loading, handleQueueSelect }: TemplateProps) {
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  const renderQueueStats = () => {
    if (!queueData) return null;

    const stats = [
      { label: "Todos", value: (queueData.active || 0) + (queueData.waiting || 0) + (queueData.completed || 0) + (queueData.failed || 0), color: "text-blue-600", status: null },
      { label: "Em Espera", value: queueData.waiting || 0, color: "text-yellow-600", status: "pending" },
      { label: "Concluídos", value: queueData.completed || 0, color: "text-green-600", status: "completed" },
      { label: "Falhas", value: queueData.failed || 0, color: "text-red-600", status: "failed" },
    ];

    return (
      <div className="grid grid-cols-4 gap-4 mb-8">
        {stats.map((stat) => (
          <button
            key={stat.label}
            onClick={() => setSelectedStatus(stat.status)}
            className={`bg-white rounded-lg shadow p-4 text-left transition-all ${selectedStatus === stat.status ? 'ring-2 ring-red-600 ring-opacity-50' : 'hover:shadow-md'}`}
          >
            <div className={`text-2xl font-semibold ${stat.color}`}>{stat.value}</div>
            <div className="text-sm text-gray-600">{stat.label}</div>
          </button>
        ))}
      </div>
    );
  };

  const renderMessageCards = () => {
    if (!queueData?.messages) return null;

    const { pending, completed } = queueData.messages;
    let allMessages = [...pending, ...completed];

    if (selectedStatus) {
      allMessages = allMessages.filter(message => message.status === selectedStatus);
    }

    if (allMessages.length === 0) {
      return (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <div className="text-gray-500">Nenhum item encontrado</div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {allMessages.map((message) => (
          <div
            key={message.id}
            className="bg-white rounded-lg shadow p-4 cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => {
              setSelectedMessage(message);
              setIsModalOpen(true);
            }}
          >
            <div className="flex justify-between items-start mb-3">
              <div className="text-sm font-medium text-gray-900">{message.content.title}</div>
              <span
                className={`px-2 py-1 text-xs rounded-full ${
                  message.status === "completed"
                    ? "bg-green-100 text-green-800"
                    : message.status === "failed"
                    ? "bg-red-100 text-red-800"
                    : "bg-yellow-100 text-yellow-800"
                }`}
              >
                {message.status}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-2">{message.content.message}</p>
            <div className="text-xs text-gray-500">
              <div>ID: {message.id}</div>
              <div>Tentativas: {message.attempts}</div>
              <div>Data: {new Date(message.timestamp).toLocaleString()}</div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50">
      {/* Queue List Sidebar */}
      <div className="w-64 flex-shrink-0 bg-white shadow-sm border-r border-gray-200 overflow-y-auto">
        <div className="p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Filas</h2>
          <div className="space-y-1">
            {queues.map((queue) => (
              <button
                key={queue.name}
                onClick={() => handleQueueSelect(queue.name)}
                className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  selectedQueue === queue.name ? "bg-red-600 text-white" : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                {queue.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900">{selectedQueue ? `Fila: ${selectedQueue}` : "Selecione uma Fila"}</h1>
            <p className="mt-1 text-sm text-gray-600">
              {selectedQueue ? "Monitoramento em tempo real da fila selecionada." : "Selecione uma fila no menu lateral para visualizar seus detalhes."}
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : selectedQueue ? (
            <div className="space-y-6">
              {renderQueueStats()}
              {renderMessageCards()}
            </div>
          ) : (
            <div className="text-center py-12 bg-white rounded-lg shadow">
              <div className="text-gray-500">Selecione uma fila para visualizar seus detalhes</div>
            </div>
          )}
        </div>
      </div>

      {/* Message Details Modal */}
      {isModalOpen && selectedMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-3xl max-h-[80vh] overflow-y-auto m-4">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">Detalhes da Mensagem</h3>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <pre className="bg-gray-50 rounded-lg p-4 overflow-x-auto">
                <code className="text-sm">{JSON.stringify(selectedMessage, null, 2)}</code>
              </pre>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
