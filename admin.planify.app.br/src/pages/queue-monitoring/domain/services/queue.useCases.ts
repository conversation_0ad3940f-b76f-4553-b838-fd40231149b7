import { envs } from "@/shared/functions/envsProxy";
import HttpClient from "../../../../infra/httpRequest";
import { QueueData } from "../models";

const BASE_URL = envs.QUEUE_MANAGER_URL;
const httpRequest = new HttpClient(BASE_URL);

export const QueueUseCases = {
  getQueues: async (): Promise<QueueData[]> => {
    const response = await httpRequest.get("/queues");
    if (response?.data?.success) {
      return Object.entries(response.data.data ?? {}).map(([name, stats]: [string, any]) => ({
        name,
        ...stats,
      }));
    }
    return [];
  },

  getQueueMessages: async (queueName: string, status?: string): Promise<any[]> => {
    const endpoint = status ? `/queues/${queueName}/messages?status=${status}` : `/queues/${queueName}/messages`;
    const response = await httpRequest.get(endpoint);
    return response?.data ?? [];
  },

  monitorQueue: (queueName: string, onMessage: (data: any) => void, onError: (error: any) => void) => {
    const url = `${BASE_URL}/queues/${queueName}/monitor`;
    const eventSource = new EventSource(url, { withCredentials: true });

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.success) {
          onMessage(data.data[queueName]);
        }
      } catch (error) {
        onError(error);
      }
    };

    eventSource.onerror = onError;

    return () => {
      eventSource.close();
    };
  },
};
