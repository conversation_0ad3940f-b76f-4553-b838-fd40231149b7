import Dashboard from "@/pages/dashboard";
import Plans from "@/pages/plans";
import Login from "@/pages/signIn";
import Permissions from "@/pages/permissions";
import Subscriptions from "@/pages/subscriptions";
import Prompts from "@/pages/prompts";
import Logs from "@/pages/logs";
import Tickets from "@/pages/tickets";
import ReprocessPayment from "@/pages/reprocess-payment";
import QueueMonitoringPage from "@/pages/queue-monitoring";
import PlannerPrompts from "@/pages/planner-prompts";
import Templates from "@/pages/templates";
import Discounts from "@/pages/discounts";
import Parameterizations from "@/pages/parameterizations";
import Afiliates from "@/pages/afiliates";
import Contracts from "@/pages/contracts";

const routes: {
  path: string;
  element: JSX.Element;
  isPublicRoute: boolean;
}[] = [
  { path: "/", element: <Login />, isPublicRoute: true },
  { path: "/dashboard", element: <Dashboard />, isPublicRoute: false },
  { path: "/plans", element: <Plans />, isPublicRoute: false },
  { path: "/permissions", element: <Permissions />, isPublicRoute: false },
  { path: "/subscriptions", element: <Subscriptions />, isPublicRoute: false },
  { path: "/prompts", element: <Prompts />, isPublicRoute: false },
  { path: "/tickets", element: <Tickets />, isPublicRoute: false },
  { path: "/logs", element: <Logs />, isPublicRoute: false },
  { path: "/reprocess-payment", element: <ReprocessPayment />, isPublicRoute: false },
  { path: "/queue-monitoring", element: <QueueMonitoringPage />, isPublicRoute: false },
  { path: "/planner-prompts", element: <PlannerPrompts />, isPublicRoute: false },
  { path: "/templates", element: <Templates />, isPublicRoute: false },
  { path: "/discounts", element: <Discounts />, isPublicRoute: false },
  { path: "/parameterizations", element: <Parameterizations />, isPublicRoute: false },
  { path: "/afiliates", element: <Afiliates />, isPublicRoute: false },
  { path: "/contracts", element: <Contracts />, isPublicRoute: false },
];

export default routes;
