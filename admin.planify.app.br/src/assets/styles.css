/* Estilos globais para modernizar a UI do Planify */

/* Cores principais */
:root {
  --primary: #df1d35;
  --primary-hover: #c41a2f;
  --primary-light: #ffeaed;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-color: #e5e7eb;
  --background-light: #f9fafb;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
}

/* Melhorias gerais */
body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: var(--text-primary);
}

/* Cards modernos */
.card-modern {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;
}

.card-modern:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Estatísticas */
.stat-card {
  position: relative;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stat-card .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background-color: var(--primary-light);
  margin-bottom: 1rem;
}

.stat-card .icon-container svg {
  color: var(--primary);
}

.stat-card .stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.stat-card .stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Tabelas modernas */
.table-modern {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  overflow: hidden;
}

.table-modern thead th {
  background-color: var(--background-light);
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.table-modern tbody td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 0.875rem;
  vertical-align: middle;
}

.table-modern tbody tr:hover {
  background-color: var(--background-light);
}

.table-modern tbody tr:last-child td {
  border-bottom: none;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-badge.pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.canceled {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.active {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.inactive {
  background-color: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Botões modernos */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-modern.primary {
  background-color: var(--primary);
  color: white;
}

.btn-modern.primary:hover {
  background-color: var(--primary-hover);
}

.btn-modern.secondary {
  background-color: white;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-modern.secondary:hover {
  background-color: var(--background-light);
}

/* Inputs modernos */
.input-modern {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.input-modern:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(223, 29, 53, 0.1);
}

/* Modais modernos */
.modal-modern {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modal-modern .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: white;
}

.modal-modern .modal-body {
  padding: 1.5rem;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.modal-modern .modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.25rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--background-light);
}

/* Navegação lateral moderna */
.sidebar-modern {
  background-color: white;
  border-right: 1px solid var(--border-color);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);
}

.sidebar-modern .nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.sidebar-modern .nav-item:hover {
  background-color: var(--background-light);
  color: var(--text-primary);
}

.sidebar-modern .nav-item.active {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 2px 4px rgba(223, 29, 53, 0.2);
  border-radius: 8px 0 0 8px;
  border-right: none;
}

.sidebar-modern .nav-item svg {
  margin-right: 0.75rem;
  width: 18px;
  height: 18px;
}

/* Estilo padrão para selects */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 1px var(--primary);
}

/* Cabeçalho de página */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.page-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Formulários modernos */
.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

/* Animações suaves */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
