import { useState, useEffect, useRef } from "react";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";

interface PaginationProps {
  onChangePage: (page: number) => void;
  changeRowsPerPage: (pageSize: number) => void;
  defaultRows?: number;
  totalItems?: number;
  loading?: boolean;
  showPagination?: boolean;
  defaultColor?: string;
  currentPageFromAPI?: number;
}

export function Pagination({
  onChangePage,
  changeRowsPerPage,
  defaultRows = 5,
  totalItems = 0,
  loading = false,
  showPagination = true,
  defaultColor = "#dc2626",
  currentPageFromAPI,
}: PaginationProps) {
  const [currentPage, setCurrentPage] = useState(currentPageFromAPI || 1);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRows);
  const [totalPages, setTotalPages] = useState(1);
  const isUserInitiated = useRef(false);

  const rowsOptions = [5, 10, 25, 50, 100];

  useEffect(() => {
    if (totalItems) {
      setTotalPages(Math.max(1, Math.ceil(totalItems / rowsPerPage)));
    }
  }, [totalItems, rowsPerPage]);

  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(totalPages);
      if (isUserInitiated.current) {
        onChangePage(totalPages);
      }
    }
  }, [totalPages, currentPage, onChangePage]);

  useEffect(() => {
    if (currentPageFromAPI && currentPageFromAPI !== currentPage) {
      // Evita chamadas duplicadas à API definindo que esta mudança não foi iniciada pelo usuário
      isUserInitiated.current = false;
      setCurrentPage(currentPageFromAPI);
    }
  }, [currentPageFromAPI, currentPage]);

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || page === currentPage || loading) return;
    // Marca que esta mudança foi iniciada pelo usuário
    isUserInitiated.current = true;
    setCurrentPage(page);
    onChangePage(page);
  };

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newRowsPerPage = parseInt(e.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    changeRowsPerPage(newRowsPerPage);
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = startPage + maxVisiblePages - 1;

      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  if (!showPagination) return null;

  return (
    <div className="flex flex-col sm:flex-row justify-end items-center mt-4 px-2 py-3 border-t border-gray-200">
      {/* <div className="flex items-center mb-4 sm:mb-0">
        <span className="text-sm text-gray-700 mr-2">Linhas por página:</span>
        <select
          value={rowsPerPage}
          onChange={handleRowsPerPageChange}
          disabled={loading}
          className="border border-gray-300 rounded-md text-sm py-1 px-2 focus:outline-none focus:ring-1 focus:ring-primary"
        >
          {rowsOptions.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      </div> */}

      <div className="flex items-center justify-end space-x-4">
        <div className="flex items-center space-x-1">
          <button
            onClick={() => handlePageChange(1)}
            disabled={currentPage === 1 || loading}
            className={`p-1 rounded-md ${
              currentPage === 1 || loading ? "text-gray-400 cursor-not-allowed" : `text-${defaultColor} hover:bg-gray-100`
            }`}
            aria-label="Primeira página"
          >
            <ChevronsLeft size={20} />
          </button>

          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1 || loading}
            className={`p-1 rounded-md ${
              currentPage === 1 || loading ? "text-gray-400 cursor-not-allowed" : `text-${defaultColor} hover:bg-gray-100`
            }`}
            aria-label="Página anterior"
          >
            <ChevronLeft size={20} />
          </button>

          <div className="flex items-center space-x-1">
            {getPageNumbers().map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                disabled={loading}
                className={`w-8 h-8 flex items-center justify-center rounded-full text-sm ${
                  currentPage === page ? `bg-${defaultColor} text-white` : `text-gray-700 hover:bg-gray-100`
                } ${loading ? "cursor-not-allowed" : ""}`}
                aria-label={`Página ${page}`}
                style={currentPage === page ? { backgroundColor: defaultColor } : {}}
              >
                {page}
              </button>
            ))}
          </div>

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages || loading}
            className={`p-1 rounded-md ${
              currentPage === totalPages || loading ? "text-gray-400 cursor-not-allowed" : `text-${defaultColor} hover:bg-gray-100`
            }`}
            aria-label="Próxima página"
          >
            <ChevronRight size={20} />
          </button>

          <button
            onClick={() => handlePageChange(totalPages)}
            disabled={currentPage === totalPages || loading}
            className={`p-1 rounded-md ${
              currentPage === totalPages || loading ? "text-gray-400 cursor-not-allowed" : `text-${defaultColor} hover:bg-gray-100`
            }`}
            aria-label="Última página"
          >
            <ChevronsRight size={20} />
          </button>

          <div className="text-sm text-gray-700 ml-4">
            <span className="font-medium">{currentPage}</span> de <span className="font-medium">{totalPages}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
