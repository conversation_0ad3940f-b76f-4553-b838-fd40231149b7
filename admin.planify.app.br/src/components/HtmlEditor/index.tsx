import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

interface HtmlEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  isPreview?: boolean;
}

export function HtmlEditor({ className, value, onChange, isPreview = false }: HtmlEditorProps) {
  const modules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ["bold", "italic", "underline", "strike"],
      ["color"],
      [{ list: "ordered" }, { list: "bullet" }],
      ["link"],
      ["clean"],
    ],
    clipboard: {
      matchVisual: false,
    },
  };

  const formats = ["header", "bold", "italic", "underline", "strike", "color", "list", "bullet", "link"];

  return (
    <div className={`mt-1 ${className}`}>
      {isPreview ? (
        <div className="prose max-w-none p-4 rounded-md border border-gray-300" dangerouslySetInnerHTML={{ __html: value }} />
      ) : (
        <ReactQuill
          theme="snow"
          value={value}
          onChange={onChange}
          modules={modules}
          formats={formats}
          preserveWhitespace={true}
          className="rounded-md border-gray-300 focus:ring-primary focus:border-primary"
          style={{ whiteSpace: "pre-wrap" }}
        />
      )}
    </div>
  );
}
