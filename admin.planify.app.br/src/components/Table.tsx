/* eslint-disable @typescript-eslint/no-explicit-any */
import { Pagination } from "./Pagination";

interface TableColumn {
  id: string;
  label: string;
  align?: "left" | "center" | "right";
  render: (rowData: any) => React.ReactNode;
}

interface TableProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  emptyStateText?: string;
  showPagination?: boolean;
  totalItems?: number;
  onChangePage?: (page: number) => void;
  changeRowsPerPage?: (pageSize: number) => void;
  defaultRows?: number;
  defaultColor?: string;
  currentPage?: number;
}

export function Table({
  columns,
  data,
  loading,
  emptyStateText,
  showPagination = false,
  totalItems = 0,
  onChangePage = () => {},
  changeRowsPerPage = () => {},
  defaultRows = 5,
  defaultColor = "#FF5A5F",
  currentPage,
}: TableProps) {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!data?.length) {
    return (
      <div className="flex justify-center items-center py-8">
        <p className="text-gray-500">{emptyStateText || "Nenhum registro encontrado"}</p>
      </div>
    );
  }

  return (
    <div className="mt-8 flex flex-col">
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
          <div className="shadow-card overflow-hidden border border-gray-200 sm:rounded-card animate-fade-in bg-white">
            <table className="min-w-full divide-y divide-gray-200 table-modern">
              <thead>
                <tr>
                  {columns?.map((column) => (
                    <th
                      key={column?.id}
                      className={`px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider ${
                        column?.align ? `text-${column?.align}` : ""
                      }`}
                    >
                      {column?.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {data &&
                  data?.map((rowData, index) => (
                    <tr key={index} className="hover:bg-background-light transition-colors duration-150">
                      {columns?.map((column) => (
                        <td
                          key={column?.id}
                          className={`px-6 py-4 whitespace-nowrap text-sm text-text-primary ${column?.align ? `text-${column?.align}` : ""}`}
                        >
                          {column?.render(rowData)}
                        </td>
                      ))}
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {showPagination && (
        <Pagination
          onChangePage={onChangePage}
          changeRowsPerPage={changeRowsPerPage}
          totalItems={totalItems}
          defaultRows={defaultRows}
          loading={loading}
          defaultColor={defaultColor}
          showPagination={true}
          currentPageFromAPI={currentPage}
        />
      )}
    </div>
  );
}
