import React, { useCallback, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, X, Check, AlertCircle } from "lucide-react";
import HttpClient from "@/infra/httpRequest";
import { toast } from "react-toastify";

interface FileUploadProps {
  onFileUploaded: (fileUrl: string) => void;
  label?: string;
  currentFileUrl?: string;
}

interface FileUploadResponse {
  id: string;
  userId: string;
  type: string;
  originalName: string;
  filename: string;
  mimeType: string;
  bucket: string;
  path: string;
  size: number;
  fileUrl: string;
  createdAt: string;
  updatedAt: string;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileUploaded, label = "Ícone", currentFileUrl }) => {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    if (currentFileUrl) {
      setPreviewUrl(currentFileUrl);
    }
  }, [currentFileUrl]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      const file = acceptedFiles[0];

      if (!file.type.startsWith("image/")) {
        setError("Por favor, selecione apenas arquivos de imagem.");
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        setError("O arquivo deve ter no máximo 5MB.");
        return;
      }

      setUploading(true);
      setError(null);
      setSuccess(false);

      try {
        const formData = new FormData();
        formData.append("file", file);

        const service = new HttpClient();
        const response = (await service.post("/files", formData, {
          headers: { "Content-Type": "multipart/form-data" },
        })) as { status: number; data: FileUploadResponse };

        if (response && response.status !== 200 && response.status !== 201) {
          toast.error("Erro ao fazer upload do arquivo");
          throw new Error("Falha ao fazer upload do arquivo");
        }

        const data: FileUploadResponse = response.data;

        setPreviewUrl(data.fileUrl);

        onFileUploaded(data.fileUrl);

        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } catch (err) {
        setError("Erro ao fazer upload do arquivo. Tente novamente.");
        console.error("Erro no upload:", err);
      } finally {
        setUploading(false);
      }
    },
    [onFileUploaded]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif", ".svg"],
    },
    maxFiles: 1,
  });

  const removeFile = () => {
    setPreviewUrl(null);
    onFileUploaded("");
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>

      {previewUrl ? (
        <div className="relative mt-1">
          <div
            className="relative border border-gray-300 rounded-md p-2 flex items-center"
            style={{
              backgroundImage: `url(${previewUrl})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              position: "relative",
            }}
          >
            <div className="absolute inset-0 bg-white bg-opacity-80 backdrop-blur-sm"></div>
            <div className="relative z-10 flex items-center w-full">
              <img src={previewUrl} alt="Preview" className="h-16 w-16 object-contain mr-3" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">Imagem carregada</p>
                <p className="text-sm text-gray-500 truncate">{previewUrl.split("/").pop()}</p>
              </div>
              <button type="button" onClick={removeFile} className="ml-2 flex-shrink-0 p-1 text-gray-400 rounded-full hover:text-gray-500">
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 ${
            isDragActive ? "border-primary border-dashed bg-red-50" : "border-gray-300 border-dashed"
          } rounded-md cursor-pointer hover:bg-gray-50 transition-colors`}
        >
          <input {...getInputProps()} />
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <p className="relative cursor-pointer font-medium text-primary hover:text-red-500">
                Clique para selecionar
                <span className="text-gray-500"> ou arraste e solte</span>
              </p>
            </div>
            <p className="text-xs text-gray-500">PNG, JPG, GIF até 5MB</p>
          </div>
        </div>
      )}

      {uploading && (
        <div className="mt-2 flex items-center text-sm text-gray-500">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
          Enviando arquivo...
        </div>
      )}

      {error && (
        <div className="mt-2 flex items-center text-sm text-red-500">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}

      {success && (
        <div className="mt-2 flex items-center text-sm text-green-500">
          <Check className="h-4 w-4 mr-1" />
          Arquivo enviado com sucesso!
        </div>
      )}
    </div>
  );
};

export default FileUpload;
