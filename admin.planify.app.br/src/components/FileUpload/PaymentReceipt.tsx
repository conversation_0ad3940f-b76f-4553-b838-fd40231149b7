import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, X, AlertCircle } from "lucide-react";

interface FileUploadProps {
  onUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  selectedFile: File | null;
}

const PaymentReceipt: React.FC<FileUploadProps> = ({ onUpload, fileInputRef, selectedFile }) => {
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      const file = acceptedFiles[0];

      if (file.size > 5 * 1024 * 1024) {
        setError("O arquivo deve ter no máximo 5MB.");
        return;
      }

      // Criar um evento de mudança simulado
      const fakeEvent = {
        target: {
          files: [file],
        },
      } as unknown as React.ChangeEvent<HTMLInputElement>;

      onUpload(fakeEvent);
      setError(null);
    },
    [onUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 1,
  });

  const removeFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    // Criar um evento de mudança simulado com arquivo vazio
    const fakeEvent = {
      target: {
        files: null,
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    onUpload(fakeEvent);
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-1">Comprovante de Pagamento</label>

      {selectedFile ? (
        <div className="relative mt-1">
          <div className="relative border border-gray-300 rounded-md p-4 flex items-center">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">Arquivo selecionado</p>
              <p className="text-sm text-gray-500 truncate">
                {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
              </p>
            </div>
            <button type="button" onClick={removeFile} className="ml-2 flex-shrink-0 p-1 text-gray-400 rounded-full hover:text-gray-500">
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 ${
            isDragActive ? "border-primary border-dashed bg-red-50" : "border-gray-300 border-dashed"
          } rounded-md cursor-pointer hover:bg-gray-50 transition-colors`}
        >
          <input {...getInputProps()} ref={fileInputRef} onChange={onUpload} />
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <p className="relative cursor-pointer font-medium text-primary hover:text-red-500">
                Clique para selecionar
                <span className="text-gray-500"> ou arraste e solte</span>
              </p>
            </div>
            <p className="text-xs text-gray-500">PDF, PNG, JPG até 5MB</p>
          </div>
        </div>
      )}

      {error && (
        <div className="mt-2 flex items-center text-sm text-red-500">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
};

export default PaymentReceipt;
