import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  Users,
  Package,
  LogOut,
  Shield,
  AlertTriangle,
  MessageCircle,
  Bell,
  RefreshCw,
  Calendar,
  FileText,
  Percent,
  Bot,
  Settings2,
  <PERSON>r<PERSON><PERSON>,
  Handshake,
} from "lucide-react";
import { useAuthStore } from "@/contexts/authContext";
import { useGlobalStore } from "@/contexts/globalContext";
import { envs } from "@/shared/functions/envsProxy";
import logoImage from "../assets/logo.jpg";
interface LayoutProps {
  children: React.ReactNode;
}

export default function RootTemplate({ children }: LayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuthStore();
  const { openTickets } = useGlobalStore();

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  return (
    <div className="min-h-screen bg-background-light">
      <nav className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10 shadow-card">
        <div className="w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <img src={logoImage} height="50px" width="50px" className="rounded-lg" />
                <span className="ml-3 text-xl font-bold text-primary">Planify Admin</span>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-text-primary mr-4 font-medium">{user?.name}</span>
              <button
                onClick={() => navigate("/tickets")}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-lg text-text-secondary hover:text-text-primary hover:bg-background-light mr-2 relative transition-all duration-200"
              >
                <div className="relative">
                  <Bell className="h-4 w-4" />
                  {openTickets > 0 && <span className="absolute -top-1 -right-1 h-3 w-3 bg-primary rounded-full" />}
                </div>
              </button>
              <button
                onClick={handleLogout}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-lg text-text-secondary hover:text-text-primary hover:bg-background-light transition-all duration-200"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sair
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex relative pt-16">
        <aside className="fixed top-16 w-64 bg-white border-r border-gray-200 h-[calc(100vh-4rem)] flex flex-col sidebar-modern shadow-sm">
          <div className="flex flex-col h-full justify-between overflow-y-auto">
            <nav className="mt-5 px-2">
              <button
                onClick={() => navigate("/dashboard")}
                className={`nav-item ${
                  location.pathname === "/dashboard" ? "active" : ""
                } group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <LayoutDashboard className="mr-3 h-5 w-5" />
                Painel
              </button>
              <button
                onClick={() => navigate("/plans")}
                className={`nav-item ${
                  location.pathname === "/plans" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Package className="mr-3 h-6 w-6" />
                Configuração de planos
              </button>
              <button
                onClick={() => navigate("/permissions")}
                className={`nav-item ${
                  location.pathname === "/permissions" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Shield className="mr-3 h-6 w-6" />
                Permissões
              </button>
              <button
                onClick={() => navigate("/subscriptions")}
                className={`nav-item ${
                  location.pathname === "/subscriptions" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Users className="mr-3 h-6 w-6" />
                Assinaturas
              </button>
              <button
                onClick={() => navigate("/prompts")}
                className={`nav-item ${
                  location.pathname === "/prompts" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Bot className="mr-3 h-6 w-6" />
                Treinamento
              </button>
              <button
                onClick={() => navigate("/planner-prompts")}
                className={`nav-item ${
                  location.pathname === "/planner-prompts" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Calendar className="mr-3 h-6 w-6" />
                Planner Prompts
              </button>
              <button
                onClick={() => navigate("/tickets")}
                className={`nav-item ${
                  location.pathname === "/tickets" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <MessageCircle className="mr-3 h-6 w-6" />
                Tickets
              </button>
              <button
                onClick={() => navigate("/logs")}
                className={`nav-item ${
                  location.pathname === "/logs" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <AlertTriangle className="mr-3 h-6 w-6" />
                Logs de Erro
              </button>
              <button
                onClick={() => navigate("/reprocess-payment")}
                className={`nav-item ${
                  location.pathname === "/reprocess-payment" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <RefreshCw className="mr-3 h-6 w-6" />
                Reprocessamento
              </button>
              <button
                onClick={() => navigate("/queue-monitoring")}
                className={`nav-item ${
                  location.pathname === "/queue-monitoring" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Bell className="mr-3 h-6 w-6" />
                Monitoramento de Filas
              </button>
              <button
                onClick={() => navigate("/discounts")}
                className={`nav-item ${
                  location.pathname === "/discounts" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Percent className="mr-3 h-6 w-6" />
                Cupons de Desconto
              </button>
              <button
                onClick={() => navigate("/templates")}
                className={`nav-item ${
                  location.pathname === "/templates" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <FileText className="mr-3 h-6 w-6" />
                Templates
              </button>
              <button
                onClick={() => navigate("/afiliates")}
                className={`nav-item ${
                  location.pathname === "/afiliates" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <UserPlus className="mr-3 h-6 w-6" />
                Afiliados
              </button>
              <button
                onClick={() => navigate("/contracts")}
                className={`nav-item ${
                  location.pathname === "/contracts" ? "active" : ""
                } mt-1 group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 w-full`}
              >
                <Handshake className="mr-3 h-6 w-6" />
                Negociações
              </button>
              {/* <button
                onClick={() => navigate("/parameterizations")}
                className={`mt-1 group flex items-center px-2 py-2 text-base font-medium rounded-md ${
                  location.pathname === "/parameterizations" ? "bg-red-600 text-white" : "text-gray-600"
                } ${location.pathname === "/parameterizations" ? "hover:bg-red-700" : "hover:bg-gray-50"} w-full`}
              >
                <Settings2 className="mr-3 h-6 w-6" />
                Parametrizações
              </button> */}
            </nav>

            <div>
              <div className="py-2 text-sm text-white bg-red-600 text-center">{envs.APP_VERSION}</div>
            </div>
          </div>
        </aside>

        <main className="flex-1 p-8 ml-64">{children}</main>
      </div>
    </div>
  );
}
