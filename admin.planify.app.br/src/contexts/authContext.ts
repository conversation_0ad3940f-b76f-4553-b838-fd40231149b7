import { create } from "zustand";
import Cookies from "js-cookie";

interface AuthState {
  token: string | null;
  isAuthenticated: boolean;
  setToken: (token: string) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  token: Cookies.get("auth-token") || null,
  isAuthenticated: !!Cookies.get("auth-token"),
  setToken: (token: string) => {
    Cookies.set("auth-token", token);
    set({ token, isAuthenticated: true });
  },
  logout: () => {
    Cookies.remove("auth-token");
    set({ token: null, isAuthenticated: false });
  },
}));
