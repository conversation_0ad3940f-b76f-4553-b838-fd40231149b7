/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";
import { TSubscription } from "@/pages/subscriptions/domain/models";
import { PlanServices } from "@/pages/plans/domain";
import { TPermission, TPlan, TScope } from "@/pages/plans/domain/models";
import { TicketsServices } from "@/pages/tickets/domain";
import { PermissionService } from "@/pages/permissions/domain/services/permission.useCases";
import { SubscriptionService } from "@/pages/subscriptions/domain/services/subscription.useCases";
import { TPaginatedData } from "@/shared/types/paginatedData";
interface GlobalState {
  subscriptions: TSubscription[];
  subscriptionsPaginated: TPaginatedData;
  loading: boolean;
  getSubscriptions: () => void;
  getSubscriptionsPaginated: (page?: number, pageSize?: number) => void;
  getFilteredSubscriptionsPaginated: (status?: string, page?: number, pageSize?: number) => void;
  plans: TPlan[];
  getPaymentPlans: () => void;
  setPlans: (data: any) => void;
  setLoading: (loading: boolean) => void;
  tickets: TPaginatedData;
  getTickets: () => void;
  getTicketsPaginated: (page?: number, pageSize?: number) => void;
  getFilteredTicketsPaginated: (status?: string, page?: number, pageSize?: number) => void;
  scopes: TScope[];
  getScopes: () => void;
  permissions: any[];
  getPermissions: () => void;
  planPermissions: TPermission[];
  getPlanPermissions: () => void;
  openTickets: number;
  setTablePagination: (key: string, data: any) => void;
}

const planServices = new PlanServices();
const ticketsService = new TicketsServices();
const permissionsService = new PermissionService();
const subscriptionService = new SubscriptionService();

export const useGlobalStore = create<GlobalState>((set) => ({
  subscriptions: [],
  subscriptionsPaginated: {
    data: [],
    totalRecords: 0,
    totalPages: 1,
    currentPage: 1,
    pageSize: 10,
  },
  plans: [],
  loading: true,
  tickets: {
    data: [],
    totalRecords: 0,
    totalPages: 1,
    currentPage: 1,
    pageSize: 10,
  },
  scopes: [],
  permissions: [],
  planPermissions: [],
  openTickets: 0,

  getSubscriptions: async () => {
    const data = await subscriptionService.getSubscriptions();
    set({ subscriptions: data, loading: false });
  },
  getSubscriptionsPaginated: async (page = 1, pageSize = 10) => {
    try {
      set({ loading: true });
      const response = await subscriptionService.getSubscriptionsPaginated(page, pageSize);

      const result: TPaginatedData = {
        data: response.data,
        totalRecords: response.totalRecords,
        totalPages: response.totalPages,
        currentPage: page,
        pageSize: pageSize,
      };

      set({ subscriptionsPaginated: result, loading: false });
    } catch (error) {
      console.error("Error fetching paginated subscriptions:", error);
      set({ loading: false });
    }
  },
  getFilteredSubscriptionsPaginated: async (status = null, page = 1, pageSize = 10) => {
    try {
      set({ loading: true });
      const response = await subscriptionService.getFilteredSubscriptions(status, page, pageSize);

      const result: TPaginatedData = {
        data: response.data,
        totalRecords: response.totalRecords,
        totalPages: response.totalPages,
        currentPage: page,
        pageSize: pageSize,
      };

      set({ subscriptionsPaginated: result, loading: false });
    } catch (error) {
      console.error("Error fetching filtered subscriptions:", error);
      set({ loading: false });
    }
  },
  getPaymentPlans: async () => {
    const data = await planServices.get();
    set({ plans: data, loading: false });
  },
  getScopes: async () => {
    const data = await planServices.getScopes();
    set({ scopes: data, loading: false });
  },
  setPlans: (data: any) => {
    set({ plans: data });
  },
  setLoading: (loading: boolean) => {
    set({ loading });
  },
  setTablePagination: (key: string, data: any) => {
    set({ [key]: data });
  },
  getTickets: async () => {
    try {
      const response = await ticketsService.getTickets();
      const openTickets = response?.data?.filter((item) => item?.status !== "closed").length;

      const result: TPaginatedData = {
        data: response.data,
        totalRecords: response.totalRecords,
        totalPages: response.totalPages,
        currentPage: response.currentPage || 1,
        pageSize: response.pageSize || 10,
      };

      set({ tickets: result, openTickets, loading: false });
    } catch (error) {
      console.error("Error fetching tickets:", error);
      set({ loading: false });
    }
  },

  getTicketsPaginated: async (page = 1, pageSize = 10) => {
    try {
      set({ loading: true });
      const response = await ticketsService.getTicketsPaginated(page, pageSize);

      const openTickets = response?.data?.filter((item) => item?.status !== "closed").length;

      const result: TPaginatedData = {
        data: response.data,
        totalRecords: response.totalRecords,
        totalPages: response.totalPages,
        currentPage: page,
        pageSize: pageSize,
      };

      set({ tickets: result, openTickets, loading: false });
    } catch (error) {
      console.error("Error fetching paginated tickets:", error);
      set({ loading: false });
    }
  },

  getFilteredTicketsPaginated: async (status = null, page = 1, pageSize = 10) => {
    try {
      set({ loading: true });
      const response = await ticketsService.getFilteredTicketsPaginated(status, page, pageSize);

      const openTickets = response?.data?.filter((item) => item?.status !== "closed").length;

      const result: TPaginatedData = {
        data: response.data,
        totalRecords: response.totalRecords,
        totalPages: response.totalPages,
        currentPage: page,
        pageSize: pageSize,
      };

      set({ tickets: result, openTickets, loading: false });
    } catch (error) {
      console.error("Error fetching filtered tickets:", error);
      set({ loading: false });
    }
  },
  getPermissions: async () => {
    const response = await permissionsService.getPermissions();
    set({ permissions: response, loading: false });
  },
  getPlanPermissions: async () => {
    const response = await planServices.getPermissions();
    set({ planPermissions: response, loading: false });
  },
}));
