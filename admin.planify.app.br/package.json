{"name": "planify-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "commit": "git add . && git-cz && git push", "gen": "plop"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@rollup/rollup-linux-x64-gnu": "^4.34.9", "axios": "^1.8.4", "date-fns": "^4.1.0", "esbuild": "^0.25.1", "git-cz": "^4.9.0", "js-cookie": "^3.0.5", "lucide-react": "^0.344.0", "plop": "^4.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-number-format": "^5.4.2", "react-quill": "^2.0.0", "react-router-dom": "^6.22.3", "react-toastify": "^11.0.2", "zustand": "^4.5.2"}, "devDependencies": {"@babel/helpers": "^7.27.0", "@eslint/js": "^9.9.1", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.15"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}